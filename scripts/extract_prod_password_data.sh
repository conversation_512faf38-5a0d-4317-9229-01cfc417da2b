#!/bin/sh
####################################
## Extract production password history data for performance testing
## Safe read-only operations on production reader
####################################

PROD_HOST="nymbl-prod.cluster-ro-cloyxcrhwxb4.us-east-2.rds.amazonaws.com"
UTILITY="************"
PEM_FILE="~/.ssh/nymbl_key"

echo "=== Extracting Production Password History Data ==="
echo "Getting data from user with most password changes for local testing"
echo ""

# Create dumps directory if it doesn't exist
mkdir -p ~/dumps

# Extract password history data for the heaviest user (20798)
echo "Extracting password history for user 20798 (49,930 password changes)..."

ssh -i $PEM_FILE ubuntu@$UTILITY "mysql -h $PROD_HOST -u dbadmin -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master -e \"
SELECT 
  ua.id,
  ua.revision_id,
  ua.password,
  ar.rev_timestamp
FROM user_audit ua
JOIN audit_revision ar ON ua.revision_id = ar.revision_id
WHERE ua.id = 20798 
  AND ua.password IS NOT NULL
  AND ar.rev_timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 365 DAY)) * 1000
ORDER BY ar.rev_timestamp DESC
LIMIT 1000;
\" > /tmp/user_20798_passwords.sql"

# Download the data
echo "Downloading password history data..."
scp -i $PEM_FILE ubuntu@$UTILITY:/tmp/user_20798_passwords.sql ~/dumps/

# Also get some audit revision data
echo "Extracting audit revision data..."
ssh -i $PEM_FILE ubuntu@$UTILITY "mysql -h $PROD_HOST -u dbadmin -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master -e \"
SELECT DISTINCT
  ar.revision_id,
  ar.rev_timestamp,
  ar.user
FROM audit_revision ar
JOIN user_audit ua ON ua.revision_id = ar.revision_id
WHERE ua.id = 20798 
  AND ua.password IS NOT NULL
  AND ar.rev_timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 365 DAY)) * 1000
ORDER BY ar.rev_timestamp DESC
LIMIT 1000;
\" > /tmp/audit_revisions_20798.sql"

# Download audit revision data
scp -i $PEM_FILE ubuntu@$UTILITY:/tmp/audit_revisions_20798.sql ~/dumps/

# Clean up remote files
ssh -i $PEM_FILE ubuntu@$UTILITY "rm /tmp/user_20798_passwords.sql /tmp/audit_revisions_20798.sql"

echo ""
echo "=== Creating Local Test Data ==="

# Create a test user locally with the production password history
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master -e "
-- Create test user
INSERT IGNORE INTO user (id, username, email, password, first_name, last_name, created_at, active, last_password_change_date)
VALUES (999998, 'prod_test_user', '<EMAIL>', '\$2a\$10\$example.hash.for.testing', 'Production', 'Test', NOW(), 1, NOW());

-- Show current state
SELECT 'Before import' as status, COUNT(*) as password_audits FROM user_audit WHERE id = 999998 AND password IS NOT NULL;
"

echo "Data extraction complete. Files saved to ~/dumps/"
echo "- user_20798_passwords.sql: Password history data"
echo "- audit_revisions_20798.sql: Audit revision data"
echo ""
echo "Next: Import this data locally to reproduce the performance issue"
