#!/bin/sh
####################################
## Setup local environment to reproduce production password reset performance issue
## This creates a test user with production-scale password history
####################################

echo "=== Setting Up Local Password Performance Test ==="
echo "Creating test user with production-scale password history data"
echo ""

# First, let's import the audit revision data
echo "Step 1: Importing audit revision data..."
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master << 'EOF'
-- Create temporary table to load audit revision data
CREATE TEMPORARY TABLE temp_audit_revisions (
    revision_id BIGINT,
    rev_timestamp BIGINT,
    user VARCHAR(255)
);

-- Load the data (we'll do this manually after)
EOF

# Convert the production data to SQL INSERT statements
echo "Step 2: Converting production data to SQL format..."

# Process the audit revisions file
tail -n +2 ~/dumps/audit_revisions_20798.sql | head -100 | while IFS=$'\t' read -r revision_id rev_timestamp user; do
    echo "INSERT IGNORE INTO audit_revision (revision_id, rev_timestamp, user) VALUES ($revision_id, $rev_timestamp, '$user');"
done > /tmp/insert_audit_revisions.sql

# Process the user audit file  
tail -n +2 ~/dumps/user_20798_passwords.sql | head -100 | while IFS=$'\t' read -r id revision_id password rev_timestamp; do
    # Escape the password hash for SQL
    escaped_password=$(echo "$password" | sed "s/'/\\\\'/g")
    echo "INSERT IGNORE INTO user_audit (id, revision_id, revision_type, username, email, password, first_name, last_name, created_at, active, last_password_change_date) VALUES ($id, $revision_id, 1, 'test_perf_user', '<EMAIL>', '$escaped_password', 'Test', 'Performance', FROM_UNIXTIME($rev_timestamp/1000), 1, FROM_UNIXTIME($rev_timestamp/1000));"
done > /tmp/insert_user_audits.sql

echo "Step 3: Creating test user and importing data..."
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master << 'EOF'
-- Create the test user
INSERT IGNORE INTO user (id, username, email, password, first_name, last_name, created_at, active, last_password_change_date)
VALUES (20798, 'test_perf_user', '<EMAIL>', '$2a$10$jngFwZCVTPKHhaXbB3TO.OveYgwA4EkUMUhEXLLOAvz7CVu6DAEt.', 'Test', 'Performance', NOW(), 1, NOW());

-- Show before state
SELECT 'Before import' as status, COUNT(*) as password_audits FROM user_audit WHERE id = 20798 AND password IS NOT NULL;
EOF

# Import the audit revisions
echo "Step 4: Importing audit revisions..."
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master < /tmp/insert_audit_revisions.sql

# Import the user audits
echo "Step 5: Importing user audit records..."
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master < /tmp/insert_user_audits.sql

# Show final state
echo "Step 6: Verifying import..."
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master -e "
SELECT 'After import' as status, COUNT(*) as password_audits FROM user_audit WHERE id = 20798 AND password IS NOT NULL;

SELECT 'Password history summary' as info,
       COUNT(*) as total_passwords,
       COUNT(DISTINCT password) as unique_passwords,
       MIN(ar.rev_timestamp) as earliest_change,
       MAX(ar.rev_timestamp) as latest_change
FROM user_audit ua
JOIN audit_revision ar ON ua.revision_id = ar.revision_id
WHERE ua.id = 20798 AND ua.password IS NOT NULL;
"

# Clean up temp files
rm -f /tmp/insert_audit_revisions.sql /tmp/insert_user_audits.sql

echo ""
echo "=== Setup Complete ==="
echo "Test user created: test_perf_user (ID: 20798)"
echo "You can now test password changes in the UI to reproduce the performance issue"
echo ""
echo "To test:"
echo "1. Start the application locally"
echo "2. Login as test_perf_user"
echo "3. Try to change password - should take 1+ minutes"
echo "4. Monitor logs to see the password history validation delay"
