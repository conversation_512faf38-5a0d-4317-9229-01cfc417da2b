#!/bin/sh
####################################
## Safe production analysis script
## Only runs READ queries on production reader
## No impact on production performance
####################################

PROD_HOST="nymbl-prod.cluster-ro-cloyxcrhwxb4.us-east-2.rds.amazonaws.com"
DB_USER="dbadmin"
DB_PASS="P49ikJr8rjH4udoK4rhH4jdpBH"

echo "=== Safe Production Password History Analysis ==="
echo "Running READ-ONLY queries on production reader database"
echo "This will help identify the password reset performance issue"
echo ""

# Function to run safe read-only queries
run_prod_query() {
    local query="$1"
    local description="$2"
    
    echo "--- $description ---"
    docker exec -i nymbl_mysql8 mysql -h $PROD_HOST -u $DB_USER -p$DB_PASS nymbl_master --connect-timeout=30 -e "$query" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "Query failed or connection timeout"
    fi
    echo ""
}

# 1. Check overall audit table sizes (very fast query)
run_prod_query "
SELECT 
  'Production Audit Table Sizes' as analysis,
  (SELECT COUNT(*) FROM user_audit WHERE password IS NOT NULL) as total_password_audits,
  (SELECT COUNT(*) FROM audit_revision) as total_audit_revisions,
  (SELECT COUNT(DISTINCT id) FROM user_audit WHERE password IS NOT NULL) as users_with_password_history;
" "Overall Password History Statistics"

# 2. Find users with most password changes (potential performance culprits)
run_prod_query "
SELECT 
  ua.id as user_id,
  COUNT(*) as password_changes,
  MIN(ar.rev_timestamp) as first_change,
  MAX(ar.rev_timestamp) as last_change
FROM user_audit ua 
JOIN audit_revision ar ON ua.revision_id = ar.revision_id
WHERE ua.password IS NOT NULL
GROUP BY ua.id
HAVING password_changes > 100
ORDER BY password_changes DESC 
LIMIT 20;
" "Users with Most Password Changes (>100)"

# 3. Check password changes in last 365 days (what the validation checks)
run_prod_query "
SELECT 
  'Recent Password Activity' as analysis,
  COUNT(*) as password_changes_last_365_days,
  COUNT(DISTINCT ua.id) as users_changed_password_last_365_days
FROM user_audit ua
JOIN audit_revision ar ON ua.revision_id = ar.revision_id
WHERE ua.password IS NOT NULL
  AND ar.rev_timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 365 DAY)) * 1000;
" "Password Activity Last 365 Days"

# 4. Sample a specific user's password history (if we find a heavy user)
run_prod_query "
SELECT 
  ua.id as user_id,
  COUNT(*) as total_passwords,
  COUNT(CASE WHEN ar.rev_timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 365 DAY)) * 1000 THEN 1 END) as passwords_last_365_days,
  COUNT(CASE WHEN ar.rev_timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 90 DAY)) * 1000 THEN 1 END) as passwords_last_90_days
FROM user_audit ua
JOIN audit_revision ar ON ua.revision_id = ar.revision_id
WHERE ua.password IS NOT NULL
  AND ua.id IN (
    SELECT ua2.id 
    FROM user_audit ua2 
    WHERE ua2.password IS NOT NULL 
    GROUP BY ua2.id 
    ORDER BY COUNT(*) DESC 
    LIMIT 1
  )
GROUP BY ua.id;
" "Detailed Analysis of Heaviest User"

echo "=== Analysis Complete ==="
echo "This data will help us understand the production password history performance issue"
echo "without impacting production systems."
