#!/bin/sh
####################################
## Import the full 1000 password history records to better simulate production
####################################

echo "=== Importing Full Password History Dataset ==="
echo "This will import 1000 password history records (vs the current 100)"
echo "Expected password change time: ~70 seconds (1000 × 70ms)"
echo ""

# Convert the full audit revisions file
echo "Step 1: Converting full audit revision data..."
tail -n +2 ~/dumps/audit_revisions_20798.sql | while IFS=$'\t' read -r revision_id rev_timestamp user; do
    echo "INSERT IGNORE INTO audit_revision (revision_id, rev_timestamp, user) VALUES ($revision_id, $rev_timestamp, '$user');"
done > /tmp/insert_all_audit_revisions.sql

# Convert the full user audit file  
echo "Step 2: Converting full user audit data..."
tail -n +2 ~/dumps/user_20798_passwords.sql | while IFS=$'\t' read -r id revision_id password rev_timestamp; do
    # Escape the password hash for SQL
    escaped_password=$(echo "$password" | sed "s/'/\\\\'/g")
    echo "INSERT IGNORE INTO user_audit (id, revision_id, revision_type, username, email, password, first_name, last_name, created_at, active, last_password_change_date) VALUES ($id, $revision_id, 1, 'test_perf_user', '<EMAIL>', '$escaped_password', 'Test', 'Performance', FROM_UNIXTIME($rev_timestamp/1000), 1, FROM_UNIXTIME($rev_timestamp/1000));"
done > /tmp/insert_all_user_audits.sql

echo "Step 3: Clearing existing test data..."
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master -e "
-- Show current state
SELECT 'Before full import' as status, COUNT(*) as password_audits FROM user_audit WHERE id = 20798 AND password IS NOT NULL;

-- Clear existing audit data for this user to avoid duplicates
DELETE FROM user_audit WHERE id = 20798;
"

echo "Step 4: Importing full audit revisions..."
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master < /tmp/insert_all_audit_revisions.sql

echo "Step 5: Importing full user audit records..."
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master < /tmp/insert_all_user_audits.sql

echo "Step 6: Verifying full import..."
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master -e "
SELECT 'After full import' as status, COUNT(*) as password_audits FROM user_audit WHERE id = 20798 AND password IS NOT NULL;

SELECT 'Full password history summary' as info,
       COUNT(*) as total_passwords,
       COUNT(DISTINCT password) as unique_passwords,
       MIN(ar.rev_timestamp) as earliest_change,
       MAX(ar.rev_timestamp) as latest_change,
       ROUND(COUNT(*) * 0.07, 1) as estimated_validation_time_seconds
FROM user_audit ua
JOIN audit_revision ar ON ua.revision_id = ar.revision_id
WHERE ua.id = 20798 AND ua.password IS NOT NULL;

-- Show recent password changes (what the validation will check)
SELECT 'Recent password activity' as info,
       COUNT(*) as passwords_last_365_days,
       ROUND(COUNT(*) * 0.07, 1) as estimated_time_seconds
FROM user_audit ua
JOIN audit_revision ar ON ua.revision_id = ar.revision_id
WHERE ua.id = 20798 
  AND ua.password IS NOT NULL
  AND ar.rev_timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 365 DAY)) * 1000;
"

# Clean up temp files
rm -f /tmp/insert_all_audit_revisions.sql /tmp/insert_all_user_audits.sql

echo ""
echo "=== Full Import Complete ==="
echo "Test user now has ~1000 password history records"
echo "Expected password change time: ~70 seconds"
echo ""
echo "Try changing the password again - it should take much longer now!"
echo "This better simulates the production issue where users have 10k-50k password changes"
