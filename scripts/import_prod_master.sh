#!/bin/sh
####################################
## Modified script to import production nymbl_master database
## for password history performance analysis
####################################

UTILITY=************
PEM_FILE="~/.ssh/nymbl_key"
DATABASE="nymbl_master"

echo "=== Importing Production nymbl_master Database ==="
echo "This will help us analyze the password history performance issue"
echo ""

importDB() {
  echo "Step 1: Exporting and compressing $1 database from production RDS...."
  ssh -i $PEM_FILE ubuntu@$UTILITY /home/<USER>/mysqld.sh $1

  echo "Step 2: Downloading $1 database...."
  scp -i $PEM_FILE ubuntu@$UTILITY:/mnt/nymbl-prod/sql_exports/$1.sql.zip ~/dumps/$1.sql.zip

  echo "Step 3: Inflating $1 database...."
  unzip -o ~/dumps/$1.sql.zip -d ~/dumps/
  rm -f ~/dumps/$1.sql.zip

  echo "Step 4: Importing into local MySQL container...."
  docker exec -it nymbl_mysql8 /bin/bash /tmp/sh/importDB.sh $1
  
  if [[ -z $2 || $2 != "keep" ]] ; then
    echo "Step 5: Cleaning up local SQL file...."
    rm -f ~/dumps/$1.sql
  else
    echo "Step 5: Keeping local SQL file as requested...."
  fi

  echo "Step 6: Cleaning up remote SQL file...."
  ssh -i $PEM_FILE ubuntu@$UTILITY rm /mnt/nymbl-prod/sql_exports/$1.sql.zip
  
  echo ""
  echo "=== Import Complete ==="
  echo "You can now analyze password history performance with production data"
}

# Import nymbl_master database
importDB $DATABASE keep

echo ""
echo "=== Post-Import Analysis ==="
echo "Running quick analysis of imported data..."

# Show audit table sizes
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master -e "
SELECT 
  'Production Data Analysis' as info,
  (SELECT COUNT(*) FROM user_audit WHERE password IS NOT NULL) as total_password_audits,
  (SELECT COUNT(*) FROM audit_revision) as total_audit_revisions,
  (SELECT COUNT(DISTINCT id) FROM user_audit WHERE password IS NOT NULL) as users_with_password_history;
"

# Show top users with most password changes
docker exec -i nymbl_mysql8 mysql -u root -pP49ikJr8rjH4udoK4rhH4jdpBH nymbl_master -e "
SELECT 
  ua.id as user_id,
  u.username,
  COUNT(*) as password_changes
FROM user_audit ua 
LEFT JOIN user u ON ua.id = u.id
WHERE ua.password IS NOT NULL
GROUP BY ua.id, u.username
ORDER BY password_changes DESC 
LIMIT 10;
"

echo ""
echo "=== Ready for Performance Testing ==="
echo "You can now test password history validation with production-scale data"
