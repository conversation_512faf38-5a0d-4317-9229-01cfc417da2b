-- <PERSON>ript to generate production-scale password history test data
-- This simulates the performance issue seen in production

USE nymbl_master;

-- Create a test user for password history performance testing
INSERT IGNORE INTO user (id, username, email, password, first_name, last_name, created_at, active, last_password_change_date)
VALUES (999999, 'perf_test_user', '<EMAIL>', '$2a$10$example.hash.for.testing', 'Performance', 'Test', NOW(), 1, NOW());

-- Get the starting revision ID
SET @start_revision = (SELECT COALESCE(MAX(revision_id), 0) FROM audit_revision);

-- Create a procedure to generate password history data
DELIMITER $$

DROP PROCEDURE IF EXISTS GeneratePasswordHistory$$

CREATE PROCEDURE GeneratePasswordHistory(
    IN user_id BIGINT,
    IN num_password_changes INT,
    IN days_back INT
)
BEGIN
    DECLARE i INT DEFAULT 0;
    DECLARE current_revision BIGINT;
    DECLARE change_timestamp BIGINT;
    DECLARE password_hash VARCHAR(255);
    
    -- Start from current max revision + 1
    SET current_revision = @start_revision + 1;
    
    WHILE i < num_password_changes DO
        -- Calculate timestamp (spread changes over the specified days)
        SET change_timestamp = UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL (days_back * i / num_password_changes) DAY)) * 1000;
        
        -- Generate a unique password hash for each change
        SET password_hash = CONCAT('$2a$10$', MD5(CONCAT('password_', i, '_', user_id)), '.hash.for.testing.performance');
        
        -- Insert audit revision
        INSERT INTO audit_revision (revision_id, rev_timestamp, user)
        VALUES (current_revision, change_timestamp, CONCAT('system_perf_test_', i));
        
        -- Insert user audit record
        INSERT INTO user_audit (
            id, revision_id, revision_type, username, email, password, 
            first_name, last_name, created_at, active, last_password_change_date
        )
        VALUES (
            user_id, current_revision, 1, 'perf_test_user', '<EMAIL>', 
            password_hash, 'Performance', 'Test', change_timestamp, 1, change_timestamp
        );
        
        SET i = i + 1;
        SET current_revision = current_revision + 1;
        
        -- Show progress every 100 records
        IF i % 100 = 0 THEN
            SELECT CONCAT('Generated ', i, ' password history records...') as progress;
        END IF;
    END WHILE;
    
    SELECT CONCAT('Completed: Generated ', num_password_changes, ' password history records for user ', user_id) as result;
END$$

DELIMITER ;

-- Generate test data similar to production scale
-- User with 2000 password changes over 365 days (similar to heavy production users)
CALL GeneratePasswordHistory(999999, 2000, 365);

-- Show summary of generated data
SELECT 
    'Test user password history summary' as info,
    COUNT(*) as total_password_audits,
    MIN(ar.rev_timestamp) as earliest_change,
    MAX(ar.rev_timestamp) as latest_change,
    DATEDIFF(FROM_UNIXTIME(MAX(ar.rev_timestamp)/1000), FROM_UNIXTIME(MIN(ar.rev_timestamp)/1000)) as days_span
FROM user_audit ua
JOIN audit_revision ar ON ua.revision_id = ar.revision_id
WHERE ua.id = 999999 AND ua.password IS NOT NULL;

-- Show distribution of password changes by month
SELECT 
    DATE_FORMAT(FROM_UNIXTIME(ar.rev_timestamp/1000), '%Y-%m') as month,
    COUNT(*) as password_changes
FROM user_audit ua
JOIN audit_revision ar ON ua.revision_id = ar.revision_id
WHERE ua.id = 999999 AND ua.password IS NOT NULL
GROUP BY DATE_FORMAT(FROM_UNIXTIME(ar.rev_timestamp/1000), '%Y-%m')
ORDER BY month DESC
LIMIT 12;

-- Clean up the procedure
DROP PROCEDURE GeneratePasswordHistory;
