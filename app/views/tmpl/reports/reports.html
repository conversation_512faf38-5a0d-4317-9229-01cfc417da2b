<ng-include src="'views/tmpl/maintenance/_view_denied.html'"></ng-include>
<div class="page page-forms-common" ng-if="hasPermission(page.view + '_view')">
    <!--script src="/bower_components/dist/ng-table-to-csv.js"></script-->
    <!-- page header -->
    <div class="pageheader">
        <h2>{{page.title}} <span ng-bind-html="page.subtitle"></span></h2>
        <div class="alert" id="header-alert-container" style="display: none;"></div>
        <div class="page-bar">
            <ul class="page-breadcrumb">
                <li>
                    <a branch-dropdown></a>
                </li>
                <li>
                    <a ui-sref="app.reports">{{page.title}}</a>
                </li>
                <li>
                    <a ui-sref="app.reports">{{page.subtitle}}</a>
                </li>
            </ul>
        </div>
    </div>
    <div>
        <div class="row" ng-show="reportService.report == null">
            <div class="col-sm-3" ng-if="hasAnyFinancialReportPermissions()">

                <h2>Financial Reports</h2>
                <h4 style="padding-left: 10px;" ng-if="showGLReports()"><u><i>General Ledger & Financial Close</i></u></h4>
                <div  style="padding-left: 20px;" ng-if="showGLReports()">
                    <span ng-click="openGLLink('accounts-receivable')" role="button">Accounts Receivable</span>
                </div>

                <div  style="padding-left: 20px;" ng-if="showGLReports()">
                    <span ng-click="openGLLink('sales')" role="button">Sales</span>
                </div>
                <div  style="padding-left: 20px;" ng-if="showGLReports()">
                    <span ng-click="openGLLink('payments')" role="button">Payments</span>
                </div>

                <div  style="padding-left: 20px;" ng-if="showGLReports()">
                    <span ng-click="openGLLink('adjustments')" role="button">Adjustments</span>
                </div>
                <div  style="padding-left: 20px;" ng-if="showGLReports()">
                    <span ng-click="openGLLink('hcpc-sales')" role="button">HCPC Sales</span>
                </div>
                <div  style="padding-left: 20px;" ng-if="showGLReports()">
                    <span ng-click="openGLLink('hcpc-payments')" role="button">HCPC Payments</span>
                </div>
                <div  style="padding-left: 20px;" ng-if="showGLReports()">
                    <span ng-click="openGLLink('claims-activity')" role="button">Claims Activity</span>
                </div>

                <h4 style="padding-left: 10px;" ng-if="showBalanceReports()"><i><u>Balances</u></i></h4>
                <div ng-repeat="report in reportService.reportsList().balance" style="padding-left: 20px;" ng-if="showBalanceReports()">
                    <span ng-click="reportService.report = report" role="button">{{report.label}} <b ng-if="report.isDeprecated">(deprecated)</b></span>
                </div>
                <div  style="padding-left: 20px;" ng-if="showBalanceReports()">
                    <span ng-click="openBalancesLink('outstanding-balances')" role="button">Outstanding Balances</span>
                </div>
                <h4 style="padding-left: 10px;" ng-if="showSalesReports()"><u><i>Sales</i></u></h4>
                <div ng-repeat="report in reportService.reportsList().sales" style="padding-left: 20px;" ng-if="showSalesReports()">
                    <span ng-click="reportService.report = report" role="button">{{report.label}} <b ng-if="report.isDeprecated">(deprecated)</b></span>
                </div>
                <h4 style="padding-left: 10px;" ng-if="showPaymentReports()"><u><i>Payments</i></u></h4>
                <div ng-repeat="report in reportService.reportsList().payments" style="padding-left: 20px;" ng-if="showPaymentReports()">
                    <span ng-click="reportService.report = report" role="button">{{report.label}} <b ng-if="report.isDeprecated">(deprecated)</b></span>
                </div>
                <div  style="padding-left: 20px;" ng-if="showPaymentReports()">
                    <span ng-click="openPaymentsLink('zero-dollar-payments')" role="button">Zero Dollar Payments</span>
                </div>
                <div  style="padding-left: 20px;" ng-if="showPaymentReports()">

                    <span ng-show="hasV2CashAccess" ng-click="openPaymentsLink('cash')" role="button">Cash Report</span>
                </div>
            </div>
            <div class="col-sm-3"
                 ng-if="hasAnyProductivityReportPermissions()">
                <h2>Productivity</h2>
                <h4 style="padding-left: 10px;" ng-if="showPIReports()"><i><u>Purchasing and Inventory <b>(NEW)</b></u></i></h4>
                <div  style="padding-left: 20px;" ng-if="showPIReports()">
                    <span ng-click="openPurchasingLink('purchasing-history')" role="button">Purchasing History</span>
                </div>
                <h4 style="padding-left: 10px;" ng-if="showActionableReports()"><i><u>Actionable</u></i></h4>
                <div ng-repeat="report in reportService.reportsList().actionable" style="padding-left: 20px;" ng-if="showActionableReports()">
                    <span ng-click="reportService.report = report" role="button">{{report.label}}</span>
                </div>
                <div style="padding-left: 20px;" ng-if="showActionableReports()">
                    <span ng-click="openActionableLink('rental-prescription-summary')" role="button">Rental {{utilService.prescriptionNoun}} Summary Report</span>
                </div>
                <h4 style="padding-left: 10px;" ng-if="showEfficiencyReports()"><i><u>Efficiency</u></i></h4>
                <div ng-repeat="report in reportService.reportsList().efficiency" style="padding-left: 20px;" ng-if="showEfficiencyReports()">
                    <span ng-if="report.type && report.type === 'v2'"
                          ng-click="openV2ReportLink('efficiency', report.id)" role="button">{{report.label}}</span>
                    <span ng-if="report.type!=='v2'" ng-click="reportService.report = report" role="button">{{report.label}}</span>
                </div>
                <h4 style="padding-left: 10px;" ng-if="showFeedbackReports()"><i><u>Feedback</u></i></h4>
                <div ng-repeat="report in reportService.reportsList().feedback" style="padding-left: 20px;" ng-if="showFeedbackReports()">
                    <span ng-click="reportService.report = report" role="button">{{report.label}}</span>
                </div>
                <h4 style="padding-left: 10px;" ng-if="showSchedulingReports()"><i><u>Scheduling</u></i></h4>
                <div ng-repeat="report in reportService.reportsList().scheduling" style="padding-left: 20px;" ng-if="showSchedulingReports()">
                    <span ng-click="reportService.report = report" role="button">{{report.label}}</span>
                </div>
                <div  style="padding-left: 20px;" ng-if="showSchedulingReports()">
                    <span ng-click="openSchedulingLink('practitioner-appointments')" role="button">{{utilService.practitionerNoun + ' & Care Extender Appointments'}}</span>
                </div>
            </div>
            <div class="col-sm-3" ng-if="hasPermission('misc_reports_view') || hasPermission('export_reports_view')
                                            || hasPermission('quick_sight_dashboards_view')">
                <h2>Miscellaneous</h2>
                <div ng-if="hasPermission('export_reports_view')">
                    <h4 style="padding-left: 10px;"><i><u>Data Export</u></i></h4>
                    <div ng-repeat="report in reportService.reportsList().export_reports" style="padding-left: 20px;">
                        <span ng-click="reportService.report = report" role="button">{{report.label}}</span>
                    </div>
                </div>
                <div ng-if="hasPermission('misc_reports_view')">
                    <h4 style="padding-left: 10px;"><i><u>Misc.</u></i></h4>
                    <div ng-repeat="report in reportService.reportsList().misc_reports" style="padding-left: 20px;">
                        <span ng-click="reportService.report = report" role="button">{{report.label}}</span>
                    </div>
                </div>
                <div ng-if="hasPermission('quick_sight_dashboards_view')">
                    <h2>Quicksight</h2>
                    <div ng-repeat="report in reportService.reportsList().dashboards_by_quicksight_reports">
                        <span ng-click="reportService.report = report" role="button">{{report.label}}</span>
                    </div>
                </div>
                <div class="col-sm-3"></div>
            </div>
            <div class="col-sm-3" ng-if="hasPermission('super_admin_only_view')">
                <h2>Super Admin Only</h2>
                <h4 style="padding-left: 10px;"><i><u>Super Admin Only</u></i></h4>
                <div ng-repeat="report in reportService.reportsList().superadminonly" style="padding-left: 20px;">
                    <span ng-click="reportService.report = report" role="button">{{report.label}}</span>
                </div>
            </div>
        </div>
    <div class="panel panel-primary mb-10" id="filter-form" ng-show="reportService.report != null">
        <div class="panel-body pt-10 pb-10" ng-hide="reportService.report.id == 'dashBoardsByQuickSight'">
            <div class="row">
                <div class="col-sm-2"
                     ng-hide="['exportAll','salesTaxReport','sentTextMessageDetails','checkSmsCompliancePatient','checkSmsComplianceUser'].includes(reportService.report.id)">
                    <div class="col-sm-12 form-group">
                        <label for="branch">Branch</label>
                        <div class="select" id="branch-class-handler">
                            <select chosen="{width: '240px'}"
                                    class="form-control input-sm chosen-select"
                                    id="branch"
                                    ng-model="reportService.branchId"
                                    ng-options="b.id as b.name for b in branchService.userBranches">
                                <option ng-if="branchService.enableFilterAll()" value="">All</option>
                            </select>
                        </div>
                        <span id="branch-errors-container"></span>
                    </div>
                </div>

                <!-- Report Data Parameters -->
                <div class="col-sm-10" ng-hide="reportService.report.id == 'exportAll'">

                    <ng-include src="'views/tmpl/reports/_appointments_list_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_unapproved_notes_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_date_between_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_diagnosis_code_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_patient_birthday_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_patients_by_category_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_note_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_patient_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_physician_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_yearly_billings_collected_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_monthly_billings_collected_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_monthly_new_prescriptions_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_monthly_percent_collections_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_billings_by_practitioner_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_use_branch_from_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_un_claimed_prescriptions_list_params.html'"></ng-include>

                    <ng-include src="'/views/tmpl/reports/_billing_summary_params.html'"></ng-include>

                    <ng-include src="'/views/tmpl/reports/date_type_params.html'"></ng-include>

                    <ng-include src="'/views/tmpl/reports/_sales_summary_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_insurance_companies_l_code_alerts_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_user_batch_summary_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_daily_close_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/billing/_payment_modal_form.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_practitioner_commission_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_cash_summary_params.html'"></ng-include>

                    <ng-inslude src="'views/tmpl/reports/_device_type_list.html'"></ng-inslude>

                    <ng-include src="'views/tmpl/reports/_payment_date_option_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_submission_or_service_date_params.html'"></ng-include>

                    <ng-include
                        src="'views/tmpl/reports/_survey_text_messages_for_appointment_types_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_sent_text_message_details_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_check_sms_compliance_user_params.html'"></ng-include>

                    <ng-include src="'views/tmpl/reports/_check_sms_compliance_patient_params.html'"></ng-include>

                </div>
                <!-- Done for UI layout purposes -->
                <div class="col-sm-6" ng-show="reportService.report.id == 'exportAll'">

                    <ng-include src="'views/tmpl/reports/_exportAll_params.html'"></ng-include>

                </div>

                <!--</div>-->
            </div>
        </div>
    </div>
    <div id="pref-form" ng-if="reportService.report.id == 'exportAll'">
        <ng-include src="'views/tmpl/reports/_export_preferences.html'"></ng-include>
    </div>
    <div id="body-container" ng-show="reportService.report != null">
        <div class="row pl-15" ng-if="reportService.report.isDeprecated || reportService.report.isLegacy">
            <div class="panel panel-danger mb-10" style="border: 2px solid red">
                <div class="panel-body pt-10 pb-10">
                    <div class="row col-sm-12">
                        <p style="color: black; font-size: 14px">{{ reportService.deprecatedOrLegacyWarning }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="row pl-15" ng-if="reportService.report.description">
            <div class="panel panel-primary mb-10" id="descriptions">
                <div class="panel-body pt-10 pb-10">
                    <div class="row col-sm-12">
                        <p style="color:black; font-size: 12px" ng-bind-html="reportService.report.description"></p>
                        <p style="color: grey; font-size: 12px" ng-bind-html="reportService.report.mainDetail"></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="row pl-15">
            <form class="col-sm-12 ajax-crud-form" id="branch-form" method="post">
                <div class="row">
                    <div class="col-sm-4 p-0" ng-show="reportService.report != null">
                        <button class="btn btn-rounded btn-default" ng-click="reportService.back()"
                                type="button">
                            <i class="fa fa-arrow-left"></i> Back
                        </button>
                    </div>
                    <div class="col-sm-4 p-0 text-center" ng-show="reportService.report != null">
                        <h2 class="m-0"><strong>{{reportService.report.label}}</strong></h2>
                    </div>
                    <div class="col-sm-4 text-right">
                        <a class="btn btn-sm btn-rounded btn-warning" id="pivot-table-export"
                           ng-click='reportService.claimsSummaryPivotTableExport()'
                           ng-show="reportService.report.id == 'claimsSummary'"
                           role="button">
                            <i class="fa fa-download"></i> Pivot Table Export
                        </a>
                        <a class="btn btn-sm btn-rounded btn-warning"
                           download="{{reportService.report.id}}.csv"
                           id="export-excel"
                           ng-click='reportService.csv.generate()' ng-href="{{ reportService.csv.link() }}"
                           ng-show="reportService.report.id != 'exportAll' && reportService.report.id != 'dashBoardsByQuickSight' && reportService.report.id !='uncollected'"
                           role="button">
                            <i class="fa fa-download"></i> Export to Excel
                        </a>
                        <!--<button type="button" id="print-report" class="btn btn-sm btn-rounded btn-primary">Print Report</button>-->
                        <button class="btn btn-sm btn-rounded btn-info" id="print-claims" ng-click="printInvoices()"
                                ng-if="canPrintInvoices" type="button">Print Invoice(s)
                        </button>

                        <button class="btn btn-sm btn-rounded btn-success"
                                data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Exporting..."
                                id="uncollected-export"
                                ng-show="reportService.report.id === 'uncollected'"
                                ng-click="uncollectedExport()"
                                type="button">
                            Sent to Collections Export
                        </button>

                        <button class="btn btn-sm btn-rounded btn-success"
                                data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Generating..."
                                id="generate-report"
                                ng-hide="reportService.report.id === 'dashBoardsByQuickSight'"
                                ng-click="report.seen = !report.seen; reportService[reportService.report.id]()"
                                type="button">
                            Generate Report
                        </button>
                    </div>
                </div>
                <div class="alert" id="alert-container" style="display: none;"></div>
                <div class="row mt-20">
                    <div class="tile">
                        <div class="tile-body p-0" id="reports-list-container">
                            <div class="tile-body p-0" id="reports-list-container-blank"
                                 ng-if="reportService.report.id === undefined">
                                <table class="table table-condensed" id="blank-list">
                                    <thead>
                                    <tr>
                                        <th></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td class="bg-warning">No report selected.</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- uncollected list report -->
                            <ng-include src="'views/tmpl/reports/_uncollected.html'"></ng-include>

                            <!-- unapproved note list report -->
                            <ng-include src="'views/tmpl/reports/_unapproved_notes.html'"></ng-include>

                            <!-- appointment list report -->
                            <ng-include src="'views/tmpl/reports/_appointments_list.html'"></ng-include>

                            <!-- diagnosis code list report -->
                            <ng-include src="'views/tmpl/reports/_diagnosis_code_list.html'"></ng-include>

                            <!-- patient list report -->
                            <ng-include src="'views/tmpl/reports/_patient_list.html'"></ng-include>

                            <!-- physician list container -->
                            <ng-include src="'views/tmpl/reports/_physician_list.html'"></ng-include>

                            <!-- adjustments report reports-financial-container-adjustments -->
                            <ng-include src="'views/tmpl/reports/_billing_summary.html'"></ng-include>

                            <!-- adjustments report reports-financial-container-adjustments -->
                            <ng-include src="'views/tmpl/reports/_sales_summary.html'"></ng-include>

                            <!-- Sales Detail Report -->
                            <ng-include src="'views/tmpl/reports/_sales_detail.html'"></ng-include>

                            <!-- yearly billings collected chart -->
                            <ng-include src="'views/tmpl/reports/_yearly_billings_collected_chart.html'"></ng-include>

                            <!-- monthly billings collected chart -->
                            <ng-include src="'views/tmpl/reports/_monthly_billings_collected_chart.html'"></ng-include>

                            <!--Billings By Delivery Date-->
                            <ng-include src="'views/tmpl/reports/_billings_by_delivery_date.html'"></ng-include>

                            <!--Claims Summary By Claim Submission Date-->
                            <ng-include src="'views/tmpl/reports/_claims_summary.html'"></ng-include>

                            <!-- WIP Billings and Estimated Values -->
                            <ng-include src="'views/tmpl/reports/_wip_billings_collected_list.html'"></ng-include>

                            <!-- New Patient Prescriptions by Month chart-->
                            <ng-include src="'views/tmpl/reports/_monthly_new_prescriptions_chart.html'"></ng-include>

                            <!-- Percent Collections by Month chart -->
                            <!--<ng-include src="'views/tmpl/reports/_monthly_percent_collections_chart.html'"></ng-include>-->

                            <!-- Billings By Practitioner List -->
                            <ng-include src="'views/tmpl/reports/_billings_by_practitioner_list.html'"></ng-include>

                            <!-- Billings By User List -->

                            <!-- Practitioner Billings by Category-->
                            <ng-include src="'views/tmpl/reports/_practitioner_billings_by_category.html'"></ng-include>

                            <!-- Un-Claimed Prescription Report -->
                            <ng-include src="'views/tmpl/reports/_un_claimed_prescriptions_list.html'"></ng-include>

                            <!-- Patients with positive total patient responsibility balance -->
                            <ng-include src="'views/tmpl/reports/_outstanding_balance_list.html'"></ng-include>

                            <!--Survey text messages for appointment types-->
                            <ng-include
                                src="'views/tmpl/reports/_survey_text_messages_for_appointment_types_list.html'"></ng-include>

                            <!--Sent Text Message Details-->
                            <ng-include src="'views/tmpl/reports/_sent_text_message_details_list.html'"></ng-include>

                            <!--Check SMS Compliance-->
                            <ng-include src="'views/tmpl/reports/_check_sms_compliance_user_list.html'"></ng-include>

                            <!--Check SMS Compliance-->
                            <ng-include src="'views/tmpl/reports/_check_sms_compliance_patient_list.html'"></ng-include>

                            <!--Prescriptions Without Survey Text-->
                            <ng-include src="'views/tmpl/reports/_prescriptions_without_survey_text.html'"></ng-include>

                            <!--Survey Responses-->
                            <ng-include src="'views/tmpl/reports/_survey_responses.html'"></ng-include>

                            <!--Insurances with outstanding balances-->
                            <ng-include
                                src="'views/tmpl/reports/_insurance_outstanding_balance_list.html'"></ng-include>

                            <!-- Patients with negative total patient responsibility balance -->
                            <ng-include src="'views/tmpl/reports/_patient_refund_list.html'"></ng-include>

                            <!-- Billings by Referring Physician Report -->
                            <ng-include src="'views/tmpl/reports/_billings_referring_physician_list.html'"></ng-include>

                            <!-- Physician Monthly Visit Count Report -->
                            <ng-include
                                src="'views/tmpl/reports/_monthly_physician_visit_count_list.html'"></ng-include>

                            <!-- Appointments Canceled Report -->
                            <ng-include src="'views/tmpl/reports/_appointments_by_status_list.html'"></ng-include>

                            <!-- No Show Appointments Report -->
                            <ng-include src="'views/tmpl/reports/_appointments_no_show_list.html'"></ng-include>

                            <!-- Billings by HCPCS List Report -->
                            <ng-include src="'views/tmpl/reports/_l_code_billings_list.html'"></ng-include>

                            <!-- Total Billed List Report -->
                            <ng-include src="'views/tmpl/reports/_total_billed_list.html'"></ng-include>

                            <!-- Patients with Critical Message -->
                            <ng-include
                                src="'views/tmpl/reports/_patients_with_critical_message_list.html'"></ng-include>

                            <!--Claims with Additional Comment-->
                            <ng-include src="'views/tmpl/reports/_claims_with_additional_comment.html'"></ng-include>

                            <!-- User Batch Summary -->
                            <ng-include src="'views/tmpl/reports/_general_batch_summary_list.html'"></ng-include>

                            <!-- Delivered Prescriptions Report -->
                            <ng-include src="'views/tmpl/reports/_delivered_prescriptions_list.html'"></ng-include>

                            <!-- Insurance Companies Billed Report -->
                            <ng-include src="'views/tmpl/reports/_insurance_companies_billed.html'"></ng-include>

                            <!-- Insurance Companies With Attached HCPCS Alerts Report -->
                            <ng-include
                                src="'views/tmpl/reports/_insurance_companies_l_code_alerts_list.html'"></ng-include>

                            <!-- Low Inventory Stock -->
                            <ng-include src="'views/tmpl/reports/_low_inventory_stock_list.html'"></ng-include>

                            <!-- Note -->
                            <ng-include src="'views/tmpl/reports/_note_list.html'"></ng-include>

                            <!-- Patients by Category List -->
                            <ng-include src="'views/tmpl/reports/_patients_by_category_list.html'"></ng-include>

                            <ng-include src="'views/tmpl/reports/_user_batch_summary_list.html'"></ng-include>

                            <!--Pre-authorization Expiration Report-->
                            <ng-include src="'views/tmpl/reports/_auth_expiration.html'"></ng-include>

                            <!-- Daily Close Summary Report -->
                            <ng-include src="'views/tmpl/reports/_daily_close_list.html'"></ng-include>
                            <ng-include src="'views/tmpl/reports/_daily_close_list_new.html'"></ng-include>

                            <!-- Prescriptions On HoldReport -->
                            <ng-include src="'views/tmpl/reports/_prescriptions_on_hold.html'"></ng-include>

                            <!-- Practitioner Commission Report -->
                            <ng-include src="'views/tmpl/reports/_practitioner_commission_list.html'"></ng-include>

                            <!-- Representative (formerly User) Commission Report -->
                            <ng-include src="'views/tmpl/reports/_user_commission_list.html'"></ng-include>

                            <!-- Cash Summary Report -->
                            <ng-include src="'views/tmpl/reports/_cash_summary.html'"></ng-include>

                            <ng-include src="'views/tmpl/reports/_device_type_list.html'"></ng-include>

                            <!-- Payments by Payer Report -->
                            <ng-include src="'views/tmpl/reports/_payments_by_payer.html'"></ng-include>

                            <!-- Fabrication Past Due Date Report -->
                            <ng-include src="'views/tmpl/reports/_fabrications_past_due_date.html'"></ng-include>

                            <!-- Ar Aging Chart -->
                            <ng-include src="'views/tmpl/reports/_ar_aging_chart.html'"></ng-include>

                            <ng-include src="'views/tmpl/reports/_quicksight_dashboards.html'"></ng-include>

                            <!-- Sales Tax Report -->
                            <ng-include src="'views/tmpl/reports/_sales_tax.html'"></ng-include>

                            <ng-include src="'views/tmpl/reports/_clerical_productivity.html'"></ng-include>

                            <ng-include src="'views/tmpl/reports/_overdue_rental.html'"></ng-include>

                            <!--Prescriptions with Referral Source Report-->
                            <ng-include
                                    src="'views/tmpl/reports/_prescriptions_with_referral_source.html'"></ng-include>

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
