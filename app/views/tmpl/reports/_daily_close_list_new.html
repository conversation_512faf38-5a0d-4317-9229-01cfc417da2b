<div id="reports-list-container-daily-close-branch" class="tile-body p-0 claims-table-responsive"
     ng-if="reportService.report.id === 'dailyCloseNew'" xmlns="http://www.w3.org/1999/html"
     xmlns="http://www.w3.org/1999/html">
    <table id="daily-close-branch-list" class="table table-condensed" export-csv="reportService.csv" separator=",">
        <thead>
        <tr class="bg-primary">
            <th></th>
            <th colspan="4">Branch</th>
            <th class="text-right">Cash</th>
            <th class="text-right">Checks</th>
            <th class="text-right">Credit</th>
            <th class="text-right">Electronic</th>
            <th class="text-right">ACH</th>
            <th class="text-right">ERA</th>
            <th class="text-right">Cash Adjustments</th>
            <th class="text-right">Total Deposited</th>
            <th class="text-right">Total Unapplied</th>
            <th colspan="3"></th>
        </tr>
        </thead>
        <tbody ng-repeat="bcd in reportService.dailyCloseReportDto.branchCloseDtos | orderBy: reportService.sortColumn:reportService.reverseOrder track by bcd.branchId"
               ng-if="bcd.paymentListNew.length > 0 || bcd.appliedPaymentListNew.length > 0">
        <tr>
            <td>
                <a class="btn btn-link" ng-click="bcd.$[branchId].$open = !bcd.$[branchId].$open">
                    <span class="glyphicon glyphicon-plus-sign" ng-if="!bcd.$[branchId].$open"></span>
                    <span class="glyphicon glyphicon-minus-sign" ng-if="bcd.$[branchId].$open"></span>
                </a>
            </td>
            <td colspan="4" ng-bind-html="bcd.branchName"></td>
            <td align="right" ng-bind-html="bcd.branchCash | currency:'$':2"></td>
            <td align="right" ng-bind-html="bcd.branchCheck | currency:'$':2"></td>
            <td align="right" ng-bind-html="bcd.branchCredit | currency:'$':2"></td>
            <td align="right" ng-bind-html="bcd.branchElectronic | currency:'$':2"></td>
            <td align="right" ng-bind-html="bcd.branchAch | currency:'$':2"></td>
            <td align="right" ng-bind-html="bcd.branchEra | currency:'$':2"></td>
            <td align="right" ng-bind-html="bcd.branchAdjustment | currency:'$':2"></td>
            <td align="right" ng-bind-html="bcd.branchTotal | currency:'$':2"></td>
            <td align="right" ng-bind-html="bcd.branchTotalUnApplied | currency:'$':2"></td>
            <td colspan="3"></td>
        </tr>
        <tr></tr>
        <tr ng-if="bcd.$[branchId].$open" bgcolor="grey">
            <th class="text-white">Payment ID</th>
            <th class="text-white"
                ng-bind-html="reportService.dateOption === 'payment' ? 'Payment Date' : 'Deposit Date'"></th>
            <th colspan="3" class="text-white">Patient Name</th>
            <th colspan="2" class="text-white">Payer Name</th>
            <th class="text-white">Payment Type</th>
            <th class="text-white">Check Number</th>
            <th class="text-white text-right">Applied Date</th>
            <th class="text-white text-right">Adjustment</th>
            <th class="text-white text-right">Payment Amount</th>
            <th class="text-white text-right">Applied</th>
            <th class="text-white text-right">Unapplied</th>
            <th class="text-white">Created By</th>
        </tr>
        <tr ng-if="bcd.$[branchId].$open" ng-repeat="p in bcd.paymentListNew track by $index" bgcolor="lightgrey">
            <td><a ui-sref="app.billing.posted_payments({paymentId: p.paymentId})" target="_blank" ng-bind-html="'#'+p.paymentId"></a>
            </td>
            <td ng-if="reportService.dateOption == 'payment'"
                ng-bind-html="p.paymentDate ? moment(p.paymentDate).format('L') : 'No Payment Date'">
            </td>
            <td ng-if="reportService.dateOption == 'deposit'"
                ng-bind-html="p.depositDate ? moment(p.depositDate).format('L') : 'No Deposit Date'">
            </td>
            <td colspan="3">
                <a ui-sref="app.patient.profile({patientId: p.patientId})" target="_blank" ng-bind-html="p.patientId p.patientNam"></a></td>
            <td colspan="2" ng-bind-html="p.payerName"></td>
            <td ng-if="p.paymentType.toString().indexOf(' - ') < 0"
                ng-bind-html="reportService.paymentTypes[p.paymentType]['text'] + !!p.adjustmentTypeId ? ' - ' +  reportService.getAdjustmentTypeLabel(p.adjustmentTypeId) : ''">
            </td>
            <td ng-if="p.paymentType.indexOf(' - ') > 0 && p.paymentType.indexOf(' ZZ ') < 0"
                ng-bind-html="reportService.paymentTypes[p.paymentType.substring(0, p.paymentType.indexOf(' - '))]['text'] + ' - ' +
                reportService.paymentTypes[p.paymentType.substring(p.paymentType.indexOf(' - ') + 3)]['text']">
            </td>
            <td ng-if="p.paymentType.indexOf(' ZZ ') > 0"
            ng-bind-html="reportService.paymentTypes[p.paymentType.substring(0, p.paymentType.indexOf(' - '))]['text'] + ' - ' +
                reportService.paymentTypes[p.paymentType.substring(p.paymentType.indexOf(' - ') + 3, p.paymentType.indexOf(' ZZ '))]['text'] + ' - ' +
                p.paymentType.substring(p.paymentType.indexOf(' ZZ ') + 1)">
            </td>
            <td ng-bind-html="p.checkNumber"></td>
            <td ng-bind-html="p.appliedDate ? moment(p.appliedDate).format('L') : 'No Applied Date'"></td>
            <td align="right" ng-bind-html="p.adjustmentAmount | currency:'$':2"></td>
            <td align="right" ng-bind-html="p.paymentAmount | currency:'$':2"></td>
            <td align="right" ng-bind-html="p.paymentApplied | currency:'$':2"></td>
            <td align="right" ng-bind-html="p.paymentUnapplied | currency:'$':2"></td>
            <td ng-bind-html="p.createdByName"></td>
        </tr>
        <tr></tr>
        <tr ng-if="bcd.$[branchId].$open && bcd.appliedPaymentListNew.length" bgcolor="#65a1cf">
            <th class="text-white">Payment ID</th>
            <th class="text-white"
                ng-bind-html="reportService.dateOption == 'payment' ? 'Payment Date' : 'Deposit Date'"></th>
            <th colspan="3" class="text-white">Patient Name</th>
            <th colspan="2" class="text-white">Payer Name</th>
            <th class="text-white">Payment Type</th>
            <th class="text-white">Check Number</th>
            <th class="text-white text-right">Applied Date</th>
            <th class="text-white">Adjustment</th>
            <th class="text-white text-right">Payment Amount</th>
            <th class="text-white text-right">Applied</th>
            <th class="text-white text-right">Unapplied</th>
            <th class="text-white">Created By</th>
        </tr>
        <tr ng-if="bcd.$[branchId].$open" ng-repeat="ap in bcd.appliedPaymentListNew track by $index" bgcolor="#c9e7ff">
            <td ng-bind-html="'#'+ap.paymentId"></td>
            <td ng-if="reportService.dateOption === 'payment'" ng-bind-html="ap.paymentDate ? moment(ap.paymentDate).format('L') : 'No Payment Date'"></td>
            <td ng-if="reportService.dateOption === 'deposit'" ng-bind-html="ap.depositDate ? moment(ap.depositDate).format('L') : 'No Deposit Date'"></td>
            <td colspan="3">
                <a ui-sref="app.patient.profile({patientId: ap.patientId})" target="_blank" ng-bind-html="'#'+ap.patientId + ' ' + ap.patientName"></a>
            </td>
            <td colspan="2" ng-bind-html="ap.payerName"></td>
            <td ng-bind-html="reportService.paymentTypes[ap.paymentType]['text']"></td>
            <td ng-bind-html="ap.checkNumber"></td>
            <td align="right" ng-bind-html="ap.appliedDate ? moment(ap.appliedDate).format('L') : 'No Applied Date'"></td>
            <td align="right" ng-bind-html="ap.adjustmentAmount | currency:'$':2"></td>
            <td align="right" ng-bind-html="ap.paymentAmount | currency:'$':2"></td>
            <td align="right" ng-bind-html="ap.paymentApplied | currency:'$':2"></td>
            <td align="right" ng-bind-html="ap.paymentUnapplied | currency:'$':2"></td>
            <td ng-bind-html="ap.createdByName"></td>
        </tr>
        </tbody>
        <tfoot>
        <tr></tr>
        <tr class="bg-primary">
            <td colspan="4">Report Totals</td>
            <td></td>
            <td align="right" ng-bind-html="reportService.dailyCloseReportDto.reportCash | currency:'$':2"></td>
            <td align="right" ng-bind-html="reportService.dailyCloseReportDto.reportCheck | currency:'$':2"></td>
            <td align="right" ng-bind-html="reportService.dailyCloseReportDto.reportCredit | currency:'$':2"></td>
            <td align="right" ng-bind-html="reportService.dailyCloseReportDto.reportElectronic | currency:'$':2"></td>
            <td align="right" ng-bind-html="reportService.dailyCloseReportDto.reportAch | currency:'$':2"></td>
            <td align="right" ng-bind-html="reportService.dailyCloseReportDto.reportEra | currency:'$':2"></td>
            <td align="right" ng-bind-html="reportService.dailyCloseReportDto.reportAdjustment | currency:'$':2"></td>
            <td align="right"
                ng-bind-html="reportService.dailyCloseReportDto.reportTotalDeposited | currency:'$':2"></td>
            <td align="right" ng-bind-html="reportService.dailyCloseReportDto.reportUnApplied | currency:'$':2"></td>
            <td colspan="3"></td>
        </tr>
        </tfoot>
    </table>
</div>
