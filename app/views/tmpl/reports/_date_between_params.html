<div ng-if="['totalBilled',
             'generalBatchSummaryList',
             'userBatchSummaryList',
             'insuranceCompaniesBilledList',
             'billingsByReferringPhysician',
             'surveyResponse',
             'patientsByCategoryList',
             'deliveredPrescriptionsList',
             'appointmentList',
             'appointmentListCanceled',
             'appointmentListNoShow',
             'billingSummary',
             'salesSummary',
             'salesDetail',
             'lCodeBilledCharges',
             'billingsByDateOfService',
             'authExpiration',
             'claimsSummary',
             'prescriptionsOnHold',
             'surveyTextMessagesForAppointmentTypes',
             'prescriptionsWithoutSurveyText',
             'dailyClose', 'dailyCloseNew',
             'practitionerBillingsByCategory',
             'billingsByPractitioner',
             'billingsByRepresentative',
             'practitionerCommissions',
             'representativeCommissions',
             'uncollected',
             'cashSummary',
             'paymentsByPayer',
             'averageDaysToDeliverByDeviceType',
             'patientsWithCriticalMessageList',
             'arReport',
             'arAgingChart',
             'clericalProductivity',
             'salesTaxReport',
             'unapprovedNotes',
             'arAgingChart',
             'patientByReferralSource',
             'fabricationsPastDueDate',
             'overdueRental',
             'monthlyPhysicianVisitCount',
             'unClaimedPrescriptions',
             'noteList'].includes(reportService.report.id)">
    <div class="col-sm-3 form-group mb-0">
        <label for="startDate"
               ng-hide="reportService.report.id === 'arAgingChart' ||
                        reportService.report.id ==='fabricationsPastDueDate' ||
                        reportService.report.id ==='monthlyPhysicianVisitCount' ||
                        reportService.report.id ==='unClaimedPrescriptions' ||
                        reportService.report.id ==='overdueRental'">Between</label>
        <label for="startDate"
               ng-if="reportService.report.id === 'arAgingChart' ||
                      reportService.report.id === 'fabricationsPastDueDate' ||
                      reportService.report.id === 'monthlyPhysicianVisitCount' ||
                      reportService.report.id === 'unClaimedPrescriptions' ||
                      reportService.report.id === 'overdueRental'">As
            of</label>
        <p class="input-group">
            <input type="text"
                   id="startDate"
                   name="startDate"
                   uib-datepicker-popup="MM/dd/yyyy"
                   class="form-control"
                   ng-model="reportService.startDate"
                   is-open="calendar.opened.startDate"
                   datepicker-options="calendar.dateOptions"
                   close-text="Close" readonly
                   ng-cloak
                   as-date nymbl-date>
            <span class="input-group-btn">
                <button type="button" class="btn btn-default" ng-click="calendar.open($event, 'startDate')"><i
                    class="fa fa-calendar"></i></button>
            </span>
        </p>
    </div>
    <div class="col-sm-3 form-group mb-0"
         ng-hide="reportService.report.id === 'arAgingChart' ||
                  reportService.report.id === 'fabricationsPastDueDate' ||
                  reportService.report.id === 'monthlyPhysicianVisitCount' ||
                  reportService.report.id === 'unClaimedPrescriptions' ||
                  reportService.report.id === 'overdueRental'">
        <label for="endDate">And</label>
        <p class="input-group">
            <input type="text"
                   id="endDate"
                   name="endDate"
                   uib-datepicker-popup="MM/dd/yyyy"
                   class="form-control"
                   ng-model="reportService.endDate"
                   is-open="calendar.opened.endDate"
                   datepicker-options="calendar.dateOptions"
                   close-text="Close" readonly
                   ng-cloak
                   as-date nymbl-date>
            <span class="input-group-btn">
                <button type="button" class="btn btn-default" ng-click="calendar.open($event, 'endDate')"><i
                    class="fa fa-calendar"></i></button>
            </span>
        </p>
    </div>
</div>
