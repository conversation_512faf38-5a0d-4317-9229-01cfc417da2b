<!--div class="alert" id="alert-container" style="display: none;"></div-->
<!--hr style="border-width: 2px; border-color: #337ab7;"-->
<div class="row mt-10">
    <!-- Manufacturer Item -->
    <div class="col-sm-3" title="{{'ItemByManufacturer #' + activeItemByManufacturer.itemByManufacturerId}}">
        <h4>Manufacturer Item</h4>
    </div>
    <div class="col-sm-9">
        <div class="form-group text-right">
            <div class="col-sm-6" ng-class="{ 'has-error' : (submitted || submittedItemByManufacturer) && !itemByManufacturerValid, 'has-success' : (submitted || submittedItemByManufacturer) && itemByManufacturerValid}"
                 title="{{'ItemByManufacturer #' + activeItemByManufacturer.itemByManufacturerId}}">
                <div class="search">
                    <input class="form-control"
                           id="itemByManufacturer"
                           name="itemByManufacturer"
                           ng-blur="itemByManufacturerBlur()"
                           ng-disabled="!editing || editingItemByManufacturer || !hasPermission('item_add') || !hasPermission('item_edit') || cannotEverEdit()"
                           ng-model="activeItemByManufacturer.itemByManufacturer"
                           placeholder="Search for Manufacturer Item"
                           size="40"
                           type="text"
                           typeahead-min-length="2"
                           typeahead-on-select="selectItemByManufacturer($item)"
                           typeahead-template-url="views/tmpl/_searchItemTemplate.html"
                           typeahead-wait-ms="1000"
                           uib-typeahead="item as item.name for item in itemService.getItemsByManufacturer($viewValue, activeItemByManufacturer.manufacturerId)"
                           ng-paste="onItemByManufacturerPaste()"/>
                </div>
                <div ng-messages="form.item.itemByManufacturer.$error"
                     ng-show="!itemByManufacturerValid && (submitted || submittedItemByManufacturer)"
                     role="alert">
                    <div class="help-block">Valid Manufacturer Item is required</div>
                </div>
            </div>
            <div class="col-sm-6">
                <ng-hide ng-if="activeItemByManufacturer.itemByManufacturerId" ng-cloak>
                    <audit-button entity="item-by-manufacturer"
                                  ng-model="activeItemByManufacturer.itemByManufacturerId"
                                  showlabel="{{true}}">
                    </audit-button>
                    <button class="btn btn-sm btn-rounded btn-info"
                            id="manufacturer-edit-form"
                            ng-click="editItemByManufacturer()"
                            ng-disabled="!hasPermission('item_edit') || cannotEverEdit()"
                            ng-if="!editingItemByManufacturer"
                            type="button">
                        <i class="fa fa-edit"></i> Edit
                    </button>
                </ng-hide>
                <button class="btn btn-sm btn-rounded btn-primary"
                        id="manufacturer-new-form"
                        ng-click="addItemByManufacturer()"
                        ng-disabled="!hasPermission('item_add') || cannotEverAdd()"
                        ng-if="!editingItemByManufacturer"
                        type="button">
                    <i class="fa fa-plus"></i> New
                </button>
                <button class="btn btn-sm btn-rounded btn-success"
                        data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Saving..."
                        id="save-item-by-manufacturer"
                        ng-click="saveItemByManufacturer(form.item.$valid)"
                        ng-if="editingItemByManufacturer"
                        type="button">
                    <i class="fa fa-save"></i> Save
                </button>
                <button class="btn btn-sm btn-rounded btn-default"
                        id="cancel-item-by-manufacturer"
                        ng-click="cancelItemByManufacturer()"
                        ng-if="editingItemByManufacturer"
                        type="button"><i class="fa fa-times"></i> Cancel
                </button>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <!-- Manufacturer -->
    <div class="col-sm-5 form-group" title="{{'Vendor #' + activeItemByManufacturer.manufacturerId}}"
         ng-class="{ 'has-error' : (submitted || submittedItemByManufacturer) && form.item.manufacturerId.$invalid, 'has-success' : (submitted || submittedItemByManufacturer) && form.item.manufacturerId.$valid}">
        <label for="manufacturerId">Manufacturer</label>
        <select
                class="form-control input-sm"
                id="manufacturerId"
                name="manufacturerId"
                ng-change="manufacturerChanged()"
                ng-disabled="!(editing || editingItemByManufacturer) || !hasPermission('item_add') || !hasPermission('item_edit') || cannotEverAdd() || cannotEverEdit()"
                ng-model="activeItemByManufacturer.manufacturerId"
                ng-options="vendor.id as vendor.name for vendor in vendors"
                required>
            <option value="">All</option>
        </select>
        <div ng-messages="form.item.manufacturerId.$error" ng-show="(submitted || submittedItemByManufacturer)" role="alert">
            <div class="help-block" ng-message="required">Manufacturer is required</div>
        </div>
    </div>
    <!-- Category -->
    <div class="col-sm-5 form-group" title="{{'LCodeCategoryId #' + activeItemByManufacturer.itemByManufacturer.LCodeCategoryId}}"
         ng-class="{ 'has-error' : submittedItemByManufacturer && form.item.LCodeCategoryId.$invalid, 'has-success' : submittedItemByManufacturer && form.item.LCodeCategoryId.$valid}">
        <label>Category</label>
        <select
                class="form-control input-sm"
                id="LCodeCategoryId"
                name="LCodeCategoryId"
                ng-change="updateLabel()"
                ng-disabled="!editingItemByManufacturer"
                ng-model="activeItemByManufacturer.itemByManufacturer.LCodeCategoryId"
                ng-options="category.id as category.category for category in LCodeCategoryOptions"
                ng-required="editingItemByManufacturer">
        </select>
        <div ng-messages="form.item.LCodeCategoryId.$error" ng-show="submittedItemByManufacturer" role="alert">
            <div class="help-block" ng-message="required">Category is required</div>
        </div>
    </div>
    <div class="col-sm-2 form-group pt-25">
    </div>
</div>
<div class="row">
    <!-- Name -->
    <div class="col-sm-6 form-group"
         ng-class="{ 'has-error' : submittedItemByManufacturer && form.item.name.$invalid, 'has-success' : submittedItemByManufacturer && form.item.name.$valid}">
        <label>Name</label>
        <input class="form-control input-sm"
               id="name"
               name="name"
               ng-disabled="!editingItemByManufacturer"
               ng-model="activeItemByManufacturer.itemByManufacturer.name"
               ng-required="editingItemByManufacturer"
               type="text">
        <div ng-messages="form.item.name.$error" ng-show="submittedItemByManufacturer" role="alert">
            <div class="help-block" ng-message="required">Name is required.</div>
        </div>
    </div>
    <!-- Part Number -->
    <div class="col-sm-4 form-group"
         ng-class="{ 'has-error' : submittedItemByManufacturer && form.item.partNumber.$invalid, 'has-success' : submittedItemByManufacturer && form.item.partNumber.$valid}">
        <label>Part Number</label>
        <input class="form-control input-sm"
               id="partNumber"
               name="partNumber"
               ng-disabled="!editingItemByManufacturer"
               ng-model="activeItemByManufacturer.itemByManufacturer.partNumber"
               ng-required="editingItemByManufacturer"
               type="text">
        <div ng-messages="form.item.partNumber.$error" ng-show="submittedItemByManufacturer" role="alert">
            <div class="help-block" ng-message="required">Part Number is required.</div>
        </div>
    </div>
    <!-- MSRP -->
    <div class="col-sm-2 form-group">
        <label>MSRP</label>
        <input type="number"
               id="msrp"
               name="msrp"
               class="form-control input-sm"
               ng-disabled="!editingItemByManufacturer"
               ng-model="activeItemByManufacturer.itemByManufacturer.msrp"
               min="{{0.00}}" ng-pattern="/^[0-9]+(\.[0-9]{1,2})?$/" step="0.01">
    </div>
</div>
<!-- Description -->
<div class="row">
    <div class="col-sm-12 form-group">
        <label>Description</label>
        <textarea class="form-control input-sm no-resize"
                  name="description"
                  ng-disabled="!editingItemByManufacturer"
                  ng-model="activeItemByManufacturer.itemByManufacturer.description"
                  rows="5"></textarea>
        <!--<input type="textarea" id="description" name="description" class="form-control input-sm" ng-disabled="!editingItemByManufacturer" ng-model="activeItemByManufacturer.itemByManufacturer.description">-->
    </div>
</div>
<div class="row">
    <!-- Keywords -->
    <div class="col-sm-5 form-group">
        <label>Keywords</label>
        <input class="form-control input-sm" id="keywords" name="keywords" ng-disabled="!editingItemByManufacturer"
               ng-model="activeItemByManufacturer.itemByManufacturer.keywords"
               type="text">
    </div>
    <!-- UPC -->
    <div class="col-sm-3 form-group" title="{{editingItemByManufacturer && activeItemByManufacturer.itemByManufacturer && !activeItemByManufacturer.itemByManufacturer.upcType ? 'Please select a UPC type' : ''}}"
         ng-class="{ 'has-error' : submittedItemByManufacturer && form.item.upc.$invalid, 'has-success' : submittedItemByManufacturer && form.item.upc.$valid}">
        <label>
            <label class="radio-inline">
                <input id="upcTypeA"
                       name="upcType"
                       ng-disabled="!editingItemByManufacturer"
                       ng-model="activeItemByManufacturer.itemByManufacturer.upcType"
                       type="radio"
                       value="A">UPC-A
            </label>
            <label class="radio-inline">
                <input id="upcTypeE"
                       name="upcType"
                       ng-disabled="!editingItemByManufacturer"
                       ng-model="activeItemByManufacturer.itemByManufacturer.upcType"
                       type="radio"
                       value="E">UPC-E
            </label>
        </label>
        <input class="form-control input-sm"
               id="upc"
               name="upc"
               ng-disabled="!editingItemByManufacturer || !activeItemByManufacturer.itemByManufacturer.upcType"
               ng-model="activeItemByManufacturer.itemByManufacturer.upc"
               ng-pattern="isUpcE() ? '^[0-9]{6}$' : '^[0-9]{12}$'"
               type="text">
        <div ng-messages="form.item.upc.$error" ng-show="submittedItemByManufacturer" role="alert">
            <div class="help-block" ng-message="pattern">UPC-{{activeItemByManufacturer.itemByManufacturer ? activeItemByManufacturer.itemByManufacturer.upcType : ''}} must contain {{isUpcE() ? '6' : '12'}} digits exactly</div>
        </div>
    </div>
    <!-- UPN -->
    <div class="col-sm-2 form-group"
         title="Select an Option">
        <label for="upnQualifier">UPN Qualifier (24A)</label>
        <select class="form-control input-sm"
                id="upnQualifier"
                name="upnQualifier"
                ng-change="upnQualifierChanged()"
                ng-disabled="!editingItemByManufacturer"
                ng-model="activeItemByManufacturer.itemByManufacturer.upnQualifier">
            <option value=""> </option>
            <option value="EN">EN</option>
            <option value="EO">EO</option>
            <option value="HI">HI</option>
            <option value="ON">ON</option>
            <option value="UK">UK</option>
            <option value="UP">UP</option>
        </select>
    </div>
    <div class="col-sm-2 form-group"
         ng-class="{ 'has-error' : submittedItemByManufacturer && form.item.upn.$invalid, 'has-success' : submittedItemByManufacturer && form.item.upn.$valid}">
        <label for="upn">UPN Number (24A)</label>
        <input
                class="form-control input-sm"
                id="upn"
                name="upn"
                ng-change="upnNumberChanged()"
                ng-disabled="!editingItemByManufacturer"
                ng-model="activeItemByManufacturer.itemByManufacturer.upn"
                ng-pattern="/^[0-9]{12}$/"
                type="text">
        <div ng-messages="form.item.upn.$error" ng-show="submittedItemByManufacturer" role="alert">
            <div class="help-block" ng-message="pattern">UPN must contain 12 digits exactly</div>
        </div>
    </div>
</div>
<!-- depreciation -->
<div class="row">
    <div class="col-sm-4">
        <label for="depreciationType">Depreciation Type</label>
        <select chosen class="form-control input-sm chosen-select"
                ng-disabled="!editingItemByManufacturer"
                id="depreciationType"
                name="depreciationType"
                ng-change="depreciationTypeChanged()"
                ng-model="activeItemByManufacturer.itemByManufacturer.depreciationType">
            <option value=""></option>
            <option value="ongoing_upon_receipt">Ongoing upon Receipt</option>
            <option value="sale_distributed">Sale Distributed</option>
        </select>
    </div>
    <div class="col-sm-4">
        <label for="depreciationUnit">Depreciation Unit</label>
        <select chosen class="form-control input-sm chosen-select"
                ng-disabled="!editingItemByManufacturer"
                id="depreciationUnit"
                name="depreciationUnit"
                ng-change="depreciationUnitChanged()"
                ng-model="activeItemByManufacturer.itemByManufacturer.depreciationUnit">
            <option value=""></option>
            <option value="rental_cycle">Rental Cycle</option>
            <option value="month">Month</option>
        </select>
    </div>
    <div class="col-sm-4">
        <label for="depreciationUnitsToZero">
            {{(activeItemByManufacturer.depreciationUnit == 'month' ? 'Months' : 'Units') + ' to Zero'}}
        </label>
        <input class="form-control input-sm"
               ng-disabled="!editingItemByManufacturer"
               id="depreciationUnitsToZero"
               min="0"
               name="depreciationUnitsToZero"
               ng-model="activeItemByManufacturer.itemByManufacturer.depreciationUnitsToZero"
               step="1"
               type="number"/>
    </div>
</div>
<div class="row mt-10">
    <div class="col-sm-12 form-group">
        <label>Related to the following HCPCS Codes:</label>
        <div class="form-group">
            <input autocomplete="off"
                   class="form-control input-sm text-uppercase"
                   ng-if="editingItemByManufacturer"
                   ng-model="lCode"
                   placeholder="Enter l-code and select to add..."
                   size="40"
                   type="text"
                   typeahead-append-to-body="true"
                   typeahead-min-length="2"
                   typeahead-on-select="addLCode($item, $model, $label)"
                   typeahead-wait-ms="500"
                   uib-typeahead="selected as lCode.name+' - '+lCode.description for lCode in refreshLCodes($viewValue)"/>
        </div>
    </div>
</div>
<div class="col-sm-12 custom-table mb-15" id="lcodes-list" ng-style="{height: '120px'}">
    <div class="col-sm-12 header-fixed">
        <div class="col-sm-2">HCPCS</div>
        <div class="col-sm-9">Description</div>
        <div class="col-sm-1" ng-show="editingItemByManufacturer">Action</div>
    </div>
    <div class="col-sm-12 custom-table-body" ng-style="{height: '86px'}">
        <div class="col-sm-12 address-container" ng-repeat="lCode in activeItemByManufacturer.itemByManufacturer.lCodes track by $index">
            <div class="col-sm-2" ng-bind-html="lCode.name" title="LCode #{{lCode.id}}"></div>
            <div class="col-sm-9" title="LCode #{{lCode.id}}">
                <div class="text-sm-overflow"
                     ng-bind-html="lCode.description">
                </div>
            </div>
            <div class="col-sm-1">
                <a class="text-danger text-md pull-right mr-10"
                   ng-click="deleteLCode(lCode.id)"
                   ng-show="editingItemByManufacturer"
                   role="button">
                    <i class="fa fa-trash"></i>
                </a>
            </div>
        </div>
    </div>
</div>
