<div class="modal-content" id="itemPhysicalModal" modal-movable>
    <form id="itemPhysicalForm" name="itemPhysicalForm" ng-submit="save(itemPhysicalForm)" novalidate>
        <div class="modal-header">
            <h3 class="modal-title" title="ItemPhysical #{{itemPhysical.id}}">Physical Item</h3>
        </div>
        <div class="modal-body">
            <!-- Item-related stuff -->
            <div class="row mb-10">
                <div class="col-sm-6"
                     ng-class="{ 'has-error' : (submitted && itemPhysicalForm.itemByVendor.$invalid), 'has-success' : submitted && itemPhysicalForm.itemByVendor.$valid}"
                     title="{{'Item #' + itemPhysical.itemId}}">
                    <label for="itemByVendor">Vendor Item</label>
                    <input autocomplete="off"
                           class="form-control input-sm"
                           id="itemByVendor"
                           name="itemByVendor"
                           ng-change="itemChanged()"
                           ng-model="itemPhysical.item"
                           placeholder="Search for Item"
                           required
                           size="40"
                           type="text"
                           typeahead-min-length="2"
                           typeahead-on-select="selectItem($item)"
                           typeahead-template-url="views/tmpl/_searchItemTemplate.html"
                           typeahead-wait-ms="500"
                           uib-typeahead="item as item.name for item in getItems($viewValue)"/>
                    <div ng-messages="itemPhysicalForm.itemByVendor.$error" ng-show="submitted" role="alert">
                        <div class="help-block" ng-message="required">Vendor Item is required.</div>
                    </div>
                </div>
                <div class="col-sm-3" title="{{'Vendor #' + itemPhysical.item.vendorId}}">
                    <label for="vendor">Vendor</label>
                    <input class="form-control input-sm"
                           disabled
                           id="vendor"
                           name="vendor"
                           ng-model="itemPhysical.item.vendor.name"
                           readonly
                           type="text"/>
                </div>
                <div class="col-sm-3" title="{{'Vendor #' + itemPhysical.item.manufacturerId}}">
                    <label for="Manufacturer">Manufacturer</label>
                    <input class="form-control input-sm"
                           disabled
                           id="manufacturer"
                           name="manufacturer"
                           ng-model="itemPhysical.item.manufacturer.name"
                           readonly
                           type="text"/>
                </div>
            </div>
            <div class="row mb-10">
                <div class="col-sm-12">
                    <label for="description">Description</label>
                    <textarea class="form-control input-sm no-resize"
                              id="description"
                              name="description"
                              ng-model="itemPhysical.item.itemByManufacturer.description"
                              readonly
                              rows="4"/>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-4"
                     ng-class="{ 'has-error' : (submitted && itemPhysicalForm.branchId.$invalid), 'has-success' : submitted && itemPhysicalForm.branchId.$valid}"
                     title="{{'Branch #' + itemPhysical.branchId}}">
                    <label for="branchId">Branch</label>
                    <select chosen class="form-control input-sm chosen-select"
                            disable-search="{{isMobile}}"
                            id="branchId"
                            name="branchId"
                            ng-model="itemPhysical.branchId"
                            ng-options="b.id as b.name for b in branchService.branches | orderBy: 'name'"
                            required>
                        <option value=""></option>
                    </select>
                    <div ng-messages="itemPhysicalForm.branchId.$error" ng-show="submitted" role="alert">
                        <div class="help-block" ng-message="required">Branch is required.</div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <label for="serialNumber">Serial Number</label>
                    <input class="form-control input-sm"
                           id="serialNumber"
                           name="serialNumber"
                           ng-model="itemPhysical.serialNumber"
                           type="text"/>
                </div>
                <div class="col-sm-4">
                    <label for="status">Status</label>
                    <select chosen class="form-control input-sm chosen-select"
                            id="status"
                            name="status"
                            ng-change="statusChanged()"
                            ng-model="itemPhysical.status">
                        <option value=""></option>
                        <option value="inventory">Inventory</option>
                        <option value="prescription">Prescription</option>
                        <option value="loaned">Loaned</option>
                        <option value="rented">Rented</option>
                        <option value="reserved">Reserved</option>
                        <option value="cleaning">(needs) Cleaning</option>
                        <option value="repair">(needs) Repair</option>
                        <option value="delivered">Delivered</option>
                        <option value="discarded">Discarded</option>
                    </select>
                </div>
            </div>
            <!-- Rental-related stuff -->
            <div class="row">
                <div class="col-sm-6" ng-click="rentalPanelOpen = !rentalPanelOpen">
                    <h4 ng-if="rentalPanelOpen">
                        &blacktriangledown; Prescription
                    </h4>
                    <h4 ng-if="!rentalPanelOpen">
                        &blacktriangleright; Prescription
                    </h4>
                </div>
            </div>
            <div class="span" ng-if="rentalPanelOpen">
                <div class="row">
                    <div class="col-sm-4 form-group" title="Patient #{{itemPhysical.patientId}}">
                        <label for="patient">Patient</label>
                        <input autocomplete="off"
                               class="form-control input-sm"
                               id="patient"
                               name="patient"
                               ng-change="patientChanged(true)"
                               ng-model="itemPhysical.patient"
                               size="40"
                               type="text"
                               typeahead-min-length="2"
                               typeahead-on-select="selectPatient($item, $model, $label);"
                               typeahead-template-url="patientWithDOBSearchTemplate.html"
                               typeahead-wait-ms="500"
                               uib-typeahead="patient as '#' + patient.id + ' ' + utilService.formatName(patient) for patient in patientService.getPatients($viewValue)"/>
                    </div>
                    <div class="col-sm-4 form-group" title="Prescription #{{itemPhysical.prescriptionId}}">
                        <label for="prescription">Prescription</label>
                        <select class="form-control input-sm chosen-select"
                                id="prescription"
                                ng-change="prescriptionChanged(true)"
                                ng-model="itemPhysical.prescriptionId"
                                ng-options="prescription.id as '#' + prescription.id + ' ' + prescription.deviceType.name for prescription in prescriptions">
                            <option value=""></option>
                        </select>
                    </div>
                    <div class="col-sm-4 form-group" title="PrescriptionLCode #{{itemPhysical.prescriptionLCodeId}}">
                        <label for="prescriptionLCode">Prescription HCPCS</label>
                        <select class="form-control input-sm chosen-select"
                                id="prescriptionLCode"
                                ng-model="itemPhysical.prescriptionLCodeId"
                                ng-options="plc.id as plc.lCode.name + ' - ' + plc.lCode.description for plc in prescriptionLCodes">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-2 form-group">
                        <label for="condition">Condition</label>
                        <input class="form-control input-sm"
                               id="condition"
                               name="condition"
                               ng-disabled="true"
                               ng-model="itemPhysical.condition"
                               type="text">
                    </div>
                    <div class="col-sm-4 form-group" title="Start typing for the list of options">
                        <label for="reason">Reason</label>
                        <datalist id="reasonList">
                            <option value="Existing Hard Down"/>
                            <option value="Home Trial-Assessment"/>
                            <option value="Lost Or Stolen Chair"/>
                            <option value="Pending Auth For New Equipment"/>
                        </datalist>
                        <input autocomplete="off"
                               class="input-sm form-control"
                               id="reason"
                               list="reasonList"
                               name="reason"
                               ng-model="itemPhysical.reason"
                               type="text">
                    </div>
                    <div class="col-sm-3 form-group">
                        <label for="rentalStartDate">Start Date</label>
                        <div class="input-group">
                            <input class="form-control input-sm"
                                   id="rentalStartDate"
                                   name="rentalStartDate"
                                   ng-model="itemPhysical.dateOfServiceStart"
                                   ng-pattern="/^(2[0-9]{3}).*$/"
                                   type="date"/>
                        </div>
                    </div>
                    <div class="col-sm-3 form-group">
                        <label for="rentalEndDate">End Date</label>
                        <div class="input-group">
                            <input class="form-control input-sm"
                                   id="rentalEndDate"
                                   name="rentalEndDate"
                                   ng-model="itemPhysical.dateOfServiceEnd"
                                   ng-pattern="/^(2[0-9]{3}).*$/"
                                   type="date"/>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 form-group">
                        <label for="notes">Notes</label>
                        <input class="form-control input-sm"
                               id="notes"
                               name="notes"
                               ng-model="itemPhysical.notes"
                               type="text">
                    </div>
                </div>
            </div>
            <!-- Depreciation-related stuff -->
            <div class="row">
                <div class="col-sm-6" ng-click="depreciationPanelOpen = !depreciationPanelOpen">
                    <h4 ng-if="depreciationPanelOpen">
                        &blacktriangledown; Depreciation
                    </h4>
                    <h4 ng-if="!depreciationPanelOpen">
                        &blacktriangleright; Depreciation
                    </h4>
                </div>
            </div>
            <div class="span" ng-if="depreciationPanelOpen">
                <div class="row">
                    <div class="col-sm-4">
                        <label for="depreciationType">Depreciation Type</label>
                        <select chosen class="form-control input-sm chosen-select"
                                id="depreciationType"
                                name="depreciationType"
                                ng-change="depreciationTypeChanged()"
                                ng-model="itemPhysical.depreciationType">
                            <option value=""></option>
                            <option value="ongoing_upon_receipt">Ongoing upon Receipt</option>
                            <option value="sale_distributed">Sale Distributed</option>
                        </select>
                    </div>
                    <div class="col-sm-4">
                        <label for="depreciationUnit">Depreciation Unit</label>
                        <select chosen class="form-control input-sm chosen-select"
                                id="depreciationUnit"
                                name="depreciationUnit"
                                ng-change="depreciationUnitChanged()"
                                ng-model="itemPhysical.depreciationUnit">
                            <option value=""></option>
                            <option value="rental_cycle">Rental Cycle</option>
                            <option value="month">Month</option>
                        </select>
                    </div>
                    <div class="col-sm-4" ng-if="itemPhysical.depreciationUnit == 'month'">
                        <label for="receivedDate">Received Date</label>
                        <p class="input-group">
                            <input class="form-control input-sm"
                                   id="receivedDate"
                                   name="receivedDate"
                                   ng-change="receivedDateChanged()"
                                   ng-model="itemPhysical.startDate"
                                   ng-model-options="{updateOn: 'blur change'}"
                                   ng-pattern="/^(2[0-9]{3}).*$/"
                                   type="date"/>
                    </div>
                </div>
                <div class="row mb-10">
                    <div class="col-sm-4">
                        <label for="depreciationUnitsToZero">
                            {{(itemPhysical.depreciationUnit == 'month' ? 'Months' : 'Units') + ' to Zero'}}
                        </label>
                        <input class="form-control input-sm"
                               id="depreciationUnitsToZero"
                               min="0"
                               name="depreciationUnitsToZero"
                               ng-change="calculateDepreciation(true)"
                               ng-model="itemPhysical.depreciationUnitsToZero"
                               ng-model-options="{updateOn: 'default blur', debounce: {default: 1000, blur: 0}}"
                               step="1"
                               type="number"/>
                    </div>
                    <div class="col-sm-4" title="{{itemPhysical.depreciationUnit == 'month' ? 'Depreciation months are auto-calculated from Received Date' : ''}}">
                        <label for="depreciationUnitsCount">
                            {{(itemPhysical.depreciationUnit == 'month' ? 'Months' : 'Units') + ' Count'}}
                        </label>
                        <input class="form-control input-sm"
                               id="depreciationUnitsCount"
                               min="0"
                               name="depreciationUnitsCount"
                               ng-disabled="itemPhysical.depreciationUnit == 'month'"
                               ng-change="calculateDepreciation(true)"
                               ng-model="itemPhysical.depreciationUnitsCount"
                               step="1"
                               type="number"/>
                    </div>
                    <div class="col-sm-4">
                        <label for="depreciationUnitsRemain">
                            {{(itemPhysical.depreciationUnit == 'month' ? 'Months' : 'Units') + ' Remaining'}}
                        </label>
                        <input class="form-control input-sm"
                               id="depreciationUnitsRemain"
                               min="0"
                               name="depreciationUnitsRemain"
                               ng-readonly="true"
                               ng-value="itemPhysical.depreciationUnitsToZero && itemPhysical.depreciationUnitsCount ? itemPhysical.depreciationUnitsToZero - itemPhysical.depreciationUnitsCount : null"
                               step="1"
                               type="number"/>
                    </div>
                </div>
                <div class="row mb-10">
                    <div class="col-sm-3"
                         ng-class="{ 'has-error' : (submitted && itemPhysicalForm.initialValue.$invalid), 'has-success' : submitted && itemPhysicalForm.initialValue.$valid}">
                        <label for="initialValue">Initial Value</label>
                        <div class="col-sm-12 input-group">
                            <div class="input-group-addon">$</div>
                            <input class="form-control input-sm"
                                   id="initialValue"
                                   min="0.00"
                                   name="initialValue"
                                   ng-change="calculateDepreciation(true)"
                                   ng-model="itemPhysical.initialValue"
                                   placeholder="0.00"
                                   required="itemPhysical.depreciationUnitsCount > 0 || (itemPhysical.depreciationUnit == 'month' && itemPhysical.startDate)"
                                   step="0.01"
                                   type="number"/>
                        </div>
                        <div ng-messages="itemPhysicalForm.initialValue.$error" ng-show="submitted" role="alert">
                            <div class="help-block" ng-message="required">Initial Value is required when depreciation units count is present</div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <label for="depreciationPercentage">Depreciation Prct</label>
                        <div class="col-sm-12 input-group">
                            <div class="input-group-addon">%</div>
                            <input class="form-control input-sm"
                                   id="depreciationPercentage"
                                   name="depreciationPercentage"
                                   ng-model="itemPhysical.depreciationPercentage"
                                   placeholder="0.00"
                                   readonly
                                   type="number"/>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <label for="depreciationAmount">Depreciation Amt</label>
                        <div class="col-sm-12 input-group">
                            <div class="input-group-addon">$</div>
                            <input class="form-control input-sm"
                                   id="depreciationAmount"
                                   name="depreciationAmount"
                                   ng-model="itemPhysical.depreciationAmount"
                                   placeholder="0.00"
                                   readonly
                                   type="number"/>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <label for="currentValue">Current Value</label>
                        <div class="col-sm-12 input-group">
                            <div class="input-group-addon">$</div>
                            <input class="form-control input-sm"
                                   id="currentValue"
                                   name="currentValue"
                                   ng-model="itemPhysical.currentValue"
                                   placeholder="0.00"
                                   readonly
                                   type="number"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-sm btn-rounded btn-default" ng-click="$dismiss()"
                        type="button"><i class="fa fa-times"></i> Cancel
                </button>
                <button class="btn btn-sm btn-rounded btn-success"
                        id="itemSerializedFormSubmit"
                        type="submit"><i class="fa fa-save"></i> Save
                </button>
            </div>
        </div>
    </form>
</div>

<ng-include src="'views/tmpl/patient/_search_result_templates.html'"></ng-include>
