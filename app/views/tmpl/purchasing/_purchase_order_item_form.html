<div class="col-xs-12">
    <div class="row hidden">
        <pre>{{ purchaseOrderItems[i].item | prettyJSON }}</pre>
    </div>
    <div class="row">
        <div class="col-sm-4 form-group"
             ng-class="{'has-error' : submitted && purchaseOrderForm['poiPatient' + i].$invalid, 'has-success' :  submitted && purchaseOrderForm['poiPatient' + i].$valid}"
             style="padding-right: 0px;">
            <label>Patient</label>
            <a ng-if="purchaseOrderItems[i].patientId"
               target="_blank"
               ui-sref="app.patient.profile({patientId: purchaseOrderItems[i].patientId})">
                (#{{purchaseOrderItems[i].patientId}})
            </a>
            <div class="search">
                <div>
                    <input class="form-control"
                           id="poiPatient{{i}}"
                           name="poiPatient{{i}}"
                           ng-disabled="poiDisablePatient(i)"
                           ng-model="purchaseOrderItems[i].patient"
                           size="40"
                           type="text"
                           typeahead-min-length="2"
                           typeahead-on-select="poiPatientChanged(i)"
                           typeahead-template-url="patientWithDOBSearchTemplate.html"
                           typeahead-wait-ms="500"
                           uib-typeahead="patient as utilService.formatNameWithDOB(patient) for patient in patientService.getPatients($viewValue)"/>
                </div>
            </div>
            <div ng-show="purchaseOrderItems[i].patient && !purchaseOrderItems[i].patientId && submitted"
                 role="alert">
                <div class="help-block">A patient can only be selected from the dropdown menu</div>
            </div>
        </div>
        <div class="col-sm-2 form-group"
             ng-class="{'has-error' : submitted && purchaseOrderForm['poiPrescription' + i].$invalid, 'has-success' : submitted && purchaseOrderForm['poiPrescription' + i].$valid}"
             ng-if="purchaseOrderItems[i].patientId || (purchaseOrder.singlePatient && purchaseOrder.patientId)"
             title="{{'Prescription #' + purchaseOrderItems[i].prescriptionId}}">
            <label>{{utilService.prescriptionNoun}}</label>
            <select class="form-control input-sm"
                    id="poiPrescription{{i}}"
                    name="poiPrescription{{i}}"
                    ng-change="poiPrescriptionChanged(i)"
                    ng-disabled="poiDisablePrescription(i)"
                    ng-model="purchaseOrderItems[i].prescriptionId"
                    ng-options="prescription.id as (prescription.id + ' ' + prescription.deviceType.name) for prescription in purchaseOrderItems[i].prescriptions"
                    ng-required="!(purchaseOrder.singlePatient && purchaseOrder.prescriptionId) && purchaseOrderItems[i].patientId">
                <option value="">Select a {{utilService.prescriptionNoun}}</option>
            </select>
            <div ng-messages="purchaseOrderForm['poiPrescription' + i].$error" ng-show="submitted"
                 role="alert">
                <div class="help-block" ng-message="required">{{utilService.prescriptionNoun}} is required</div>
            </div>
        </div>
        <div class="col-sm-2 form-group"
             ng-class="{'has-error' : submitted && purchaseOrderForm['poiPrescriptionLCode' + i].$invalid, 'has-success' : submitted && purchaseOrderForm['poiPrescriptionLCode' + i].$valid}"
             ng-if="purchaseOrderItems[i].patientId"
             title="{{'PrescriptionLCode #' + purchaseOrderItems[i].prescriptionLCodeId}}">
            <label>{{utilService.prescriptionNoun}} HCPCS</label>
            <select class="form-control input-sm"
                    id="poiPrescriptionLCode{{i}}"
                    name="poiPrescriptionLCode{{i}}"
                    ng-disabled="poiDisablePrescriptionLCode(i)"
                    ng-model="purchaseOrderItems[i].prescriptionLCodeId"
                    ng-options="plc.id as (plc.lCode.name +' '+plc.lCode.description) for plc in purchaseOrderItems[i].prescription.prescriptionLCodes">
                <option value="">Select a {{utilService.prescriptionNoun}} HCPCS</option>
            </select>
        </div>
        <div class="col-sm-2 form-group"
             ng-class="{'has-error' : purchaseOrderForm['poiBranch' + i].$invalid, 'has-success' : purchaseOrderForm['poiBranch' + i].$valid}"
             ng-cloak
             ng-if="!purchaseOrder.singleBranch"
             title="{{'Branch #' + purchaseOrderItems[i].branchId}}">
            <label>Branch</label>
            <select class="form-control input-sm"
                    id="poiBranch{{i}}"
                    name="poiBranch{{i}}"
                    ng-change="poiBranchChanged(i)"
                    ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i]) || ((purchaseOrderItems[i].vendor.name == null || (purchaseOrderItems[i].vendor.name != null && purchaseOrderItems[i].vendor.name != 'Empire'))&&['inventory','ordered'].includes(purchaseOrder.status))"
                    ng-model="purchaseOrderItems[i].branchId"
                    ng-options="branch.id as branch.name for branch in branches | orderBy: 'name'"
                    ng-required="!purchaseOrder.singleBranch">
                <option value="">Select a branch</option>
            </select>
            <div ng-messages="purchaseOrderForm['poiBranch' + i].$error"
                 role="alert">
                <div class="help-block" ng-message="required">Branch is required.</div>
            </div>
        </div>
        <div class="col-sm-2 form-group"
             ng-if="!purchaseOrder.singlePractitioner"
             title="{{utilService.practitionerNoun + ' #' + purchaseOrderItems[i].practitionerId}}">
            <label>{{utilService.practitionerNoun}}</label>
            <select class="form-control input-sm"
                    name="poiPractitioner{{i}}"
                    ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i]) || ((purchaseOrderItems[i].vendor.name == null || (purchaseOrderItems[i].vendor.name != null && purchaseOrderItems[i].vendor.name != 'Empire')) && ['inventory','ordered'].includes(purchaseOrder.status))"
                    ng-model="purchaseOrderItems[i].practitionerId"
                    ng-options="practitioner.id as utilService.formatName(practitioner, 'FL') for practitioner in practitioners">
                <option value="">{{'Select a ' + utilService.practitionerNoun}}</option>
            </select>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-10 form-group">
            <div class="row">
                <div class="col-sm-4 form-group"
                     ng-class="{'has-error' : (purchaseOrderForm['item' + i].$invalid || (purchaseOrderItems[i].item && !purchaseOrderItems[i].itemId)), 'has-success' : purchaseOrderForm['item' + i].$valid && purchaseOrderItems[i].itemId}"
                     title="{{'Item #' + purchaseOrderItems[i].itemId}}">
                    <label for="item{{i}}" style="width: 100%">Item
                        <a class="action-link text-greensea text-sm ml-5" id="new-item{{i}}"
                           ng-click="createNewItem(i)"
                           ng-if="hasPermission('item_add') && !purchaseOrder.poom && !((purchaseOrderItems[i].vendor.name == null || (purchaseOrderItems[i].vendor.name != null && purchaseOrderItems[i].vendor.name != 'Empire')) && ['inventory','ordered'].includes(purchaseOrder.status))"
                           role="button">
                            <i class="fa fa-plus"></i>
                        </a>
                        <i class="fa fa-spinner fa-spin fa-2x" ng-if="purchasingService.searching"
                           ng-style="{ 'color': '#16A085' }"></i>
                        <a class="hidden action-link text-sm ml-5" id="search-item{{i}}"
                           ng-click="purchasingService.openSearchItemModal(i)"
                           ng-if="!purchaseOrder.poom && !((purchaseOrderItems[i].vendor.name == null || (purchaseOrderItems[i].vendor.name != null && purchaseOrderItems[i].vendor.name != 'Empire')) && ['inventory','ordered'].includes(purchaseOrder.status))"
                           role="button">
                            <i class="fa fa-search"></i>
                        </a>
                        <span class="pull-right" ng-if="purchaseOrderItems[i].id">
                            <audit-button entity="purchase_order_item" id="audit_info{{i}}"
                                          name="audit_info{{i}}"
                                          ng-model="purchaseOrderItems[i].id"
                                          showlabel="{{false}}"
                                          type="icon"></audit-button>
                        </span>
                    </label>
                    <div class="search">
                        <div>
                            <input class="form-control"
                                   id="item{{i}}"
                                   name="item{{i}}"
                                   ng-disabled="!hasPermission('purchase_order_add') || purchaseOrder.poom || ((purchaseOrderItems[i].vendor.name == null || (purchaseOrderItems[i].vendor.name != null && purchaseOrderItems[i].vendor.name != 'Empire')) && ['inventory','ordered'].includes(purchaseOrder.status))"
                                   ng-model="purchaseOrderItems[i].item"
                                   placeholder="Search for Item"
                                   required
                                   size="40"
                                   type="text"
                                   typeahead-min-length="0"
                                   typeahead-on-select="poiItemChanged(i)"
                                   typeahead-template-url="views/tmpl/_searchItemTemplate.html"
                                   typeahead-wait-ms="1000"
                                   uib-typeahead="item as item.name for item in purchasingService.fullTextSearch($viewValue, null, false, purchaseOrder.vendorId)"/>
                        </div>
                    </div>
                    <div ng-messages="purchaseOrderForm['item' + i].$error" role="alert">
                        <div class="help-block" ng-message="required">Item is required.</div>
                    </div>
                    <!-- div ng-show="purchaseOrderItems[i].item && !purchaseOrderItems[i].itemId && submitted"
                         role="alert">
                        <div class="help-block">An item can only be selected from the dropdown menu</div>
                    </div -->
                </div>
                <div class="col-sm-2 form-group">
                    <label for="partNumber{{i}}">Part Number</label>
                    <input class="form-control"
                           id="partNumber{{i}}"
                           name="partNumber{{i}}"
                           ng-model="purchaseOrderItems[i].item.partNumber"
                           readonly
                           type="text">
                </div>
                <div class="col-sm-2 form-group">
                    <label for="category{{i}}">Category</label>
                    <input class="form-control"
                           id="category{{i}}"
                           name="category{{i}}"
                           ng-model="purchaseOrderItems[i].item.itemByManufacturer.LCodeCategory.category"
                           readonly
                           type="text">
                </div>
                <div class="col-sm-2 form-group"
                     ng-class="{'has-error' : submitted && purchaseOrderForm['patientSalePrice' + i].$invalid, 'has-success' : submitted && purchaseOrderForm['patientSalePrice' + i].$valid}">
                    <label for="patientSalePrice{{i}}">Patient Sale Price</label>
                    <input class="form-control input-sm"
                           id="patientSalePrice{{i}}"
                           min="{{0.00}}"
                           ng-change="calculateTotals()"
                           ng-disabled="purchaseOrder.poom || !userCanEditPurchaseOrder(purchaseOrderItems[i]) || ((purchaseOrderItems[i].vendor.name == null || (purchaseOrderItems[i].vendor.name != null && purchaseOrderItems[i].vendor.name != 'Empire'))&&['inventory','ordered'].includes(purchaseOrder.status))"
                           ng-model="purchaseOrderItems[i].patientSalePrice"
                           placeholder="0.00"
                           step="0.01"
                           style="text-align: right"
                           type="number">
                </div>
                <div class="col-sm-2 form-group">
                    <label for="uom{{i}}">Unit of Measure</label>
                    <select class="form-control input-sm chosen-select"
                            id="uom{{i}}"
                            ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i]) || purchaseOrder.status === 'inventory'"
                            ng-model="purchaseOrderItems[i].unitOfMeasure"
                            ng-options="t as t for t in unitsOfMeasure">
                        <option value="">Select Unit</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-4 form-group">
                    <label for="manufacturer{{i}}">Manufacturer</label>
                    <input class="form-control"
                           id="manufacturer{{i}}"
                           name="manufacturer{{i}}"
                           ng-value="purchaseOrderItems[i].item.itemByManufacturer.manufacturer.name"
                           readonly
                           type="text">
                </div>
                <div class="col-sm-2 form-group">
                    <label>Manufacturer Phone</label>
                    <input class="form-control"
                           id="manufacturerPhoneNumber{{i}}"
                           name="manufacturerPhoneNumber{{i}}"
                           ng-value="utilService.formatPhone(purchaseOrderItems[i].item.itemByManufacturer.manufacturer.phoneNumber)"
                           readonly
                           type="text">
                </div>
                <div class="col-sm-4 form-group"
                     ng-class="{'has-error' : submitted && (purchaseOrderForm['poiVendor' + i].$invalid || (purchaseOrderItems[i].vendor && !purchaseOrderItems[i].vendorId)), 'has-success' : submitted && purchaseOrderForm['poiVendor' + i].$valid && purchaseOrderItems[i].vendorId}"
                     ng-if="!purchaseOrder.singleVendor"
                     title="{{'Vendor #' + purchaseOrderItems[i].item.vendorId}}">
                    <label class="input-label" for="poiVendor{{i}}">Vendor</label>
                    <div class="search">
                        <div id="scrollable-dropdown-menu{{i}}">
                            <input class="form-control"
                                   id="poiVendor{{i}}"
                                   name="poiVendor{{i}}"
                                   ng-disabled="disableVendor(i, purchaseOrder.poom) ||
                                   !userCanEditPurchaseOrder(purchaseOrderItems[i]) || ((purchaseOrderItems[i].vendor.name == null || (purchaseOrderItems[i].vendor.name != null && purchaseOrderItems[i].vendor.name != 'Empire')) && ['inventory','ordered'].includes(purchaseOrder.status))"
                                   ng-model="purchaseOrderItems[i].item.vendor"
                                   readonly
                                   size="40"
                                   type="text"
                                   typeahead-on-select="poiVendorChanged(i)"
                                   typeahead-template-url="searchVendorTemplate.html"
                                   typeahead-wait-ms="500"
                                   uib-typeahead="vendor as vendor.name for vendor in purchasingService.getVendors($viewValue)"/>
                        </div>
                    </div>
                    <!--                <div ng-messages="purchaseOrderForm.poiVendor.$error" role="alert"-->
                    <!--                     ng-show="purchaseOrderForm.$submitted">-->
                    <!--                    <div class="help-block" ng-message="required">Vendor is required.</div>-->
                    <!--                </div>-->
                    <!--                <div role="alert"-->
                    <!--                     ng-show="purchaseOrderItems[i].vendor && !purchaseOrderItems[i].vendorId && submitted">-->
                    <!--                    <div class="help-block">A vendor can only be selected from the dropdown menu</div>-->
                    <!--                </div>-->
                </div>
                <div class="col-sm-2 form-group" ng-if="!purchaseOrder.singleVendor">
                    <label>Vendor Phone</label>
                    <input class="form-control"
                           id="vendorPhoneNumber{{i}}"
                           name="vendorPhoneNumber{{i}}"
                           ng-value="utilService.formatPhone(purchaseOrderItems[i].vendor.phoneNumber)"
                           readonly
                           type="text">
                </div>
            </div>
            <div class="row">
                <div class="col-sm-2 form-group"
                     title="For multiple items, use comma-separated list">
                    <label for="serialNumber{{i}}">Serial Number</label>
                    <div class="input-group">
                        <input class="form-control input-sm"
                               id="serialNumber{{i}}"
                               name="serialNumber{{i}}"
                               ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i]) || purchaseOrder.status === 'inventory'"
                               ng-model="purchaseOrderItems[i].serialNumber">
                    </div>
                </div>
                <div class="col-sm-2 form-group" title="PurchaseOrderItem #{{purchaseOrderItems[i].id}}">
                    <label for="poiStatus{{i}}">Status</label>
                    <select class="form-control input-sm chosen-select"
                            id="poiStatus{{i}}"
                            name="poiStatus{{i}}"
                            ng-change="poiStatusChanged(i, purchaseOrderItems[i].status, '{{purchaseOrderItems[i].status}}', false)"
                            ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i]) || purchaseOrder.status === 'inventory'"
                            ng-model="purchaseOrderItems[i].status"
                            ng-options="key as data for (key, data) in statuses">
                        <option value="">Select a status</option>
                    </select>
                </div>
                <div class="col-sm-2 form-group">
                    <label for="type{{i}}">Type</label>
                    <select class="form-control input-sm chosen-select"
                            id="type{{i}}"
                            name="type{{i}}"
                            ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i])"
                            ng-model="purchaseOrderItems[i].type"
                            ng-options="t as t for t in itemTypes | orderBy: 't'">
                        <option value="">Select a type</option>
                    </select>
                </div>
                <div class="col-sm-2 form-group">
                    <label class="checkbox-inline checkbox-custom pt-25">
                        <input id="onetime"
                               name="onetime"
                               ng-disabled="true"
                               ng-model="purchaseOrderItems[i].item.oneTimePurchase"
                               type="checkbox"><i></i>One Time Purchase
                    </label>
                </div>
                <div class="col-sm-2 form-group"
                     ng-hide="purchaseOrderItems[i].purchaseOrder.poom && !purchaseOrderItems[i].purchaseOrder.referenceNumber">
                    <label class="checkbox-inline checkbox-custom pt-25">
                        <input name="split-item-by-quantity" ng-disabled="purchaseOrderItems[i].quantity === undefined ||
                       purchaseOrderItems[i].quantity === null ||
                       purchaseOrderItems[i].quantity <= 1"
                               ng-model="purchaseOrderItems[i].splitPurchaseOrderItems"
                               type="checkbox">
                        <i></i>
                        Split Item
                    </label>
                </div>
                <div class="col-sm-2 form-group" ng-if="purchaseOrder.status !== 'inventory'">
                    <label for="rma_info{{i}}">RMA #</label>
                    <input class="form-control input-sm" id="rma_info{{i}}"
                           name="rma_info{{i}}"
                           ng-blur="updateStatusForRMA(purchaseOrderItems[i])"
                           ng-model="purchaseOrderItems[i].rma"
                           type="text"></textarea>
                </div>
            </div>
        </div>
        <div class="col-sm-2 form-group">
            <div class="row"
                 title="Unit Price will be multiplied by Quantity">
                <div class="col-sm-5">
                    <label for="price{{i}}">Unit Price</label>
                </div>
                <div class="col-sm-7"
                     ng-class="{'has-error' : purchaseOrderForm['price' + i].$invalid, 'has-success' : purchaseOrderForm['price' + i].$valid}">
                    <div class="input-group">
                        <div class="input-group-addon">$</div>
                        <input class="form-control input-sm"
                               id="price{{i}}"
                               name="price{{i}}"
                               ng-change="calculateTotals()"
                               ng-disabled="purchaseOrder.poom || !userCanEditPurchaseOrder(purchaseOrderItems[i]) || ((purchaseOrderItems[i].vendor.name == null || (purchaseOrderItems[i].vendor.name != null && purchaseOrderItems[i].vendor.name != 'Empire')) && ['inventory','ordered'].includes(purchaseOrder.status))"
                               ng-model="purchaseOrderItems[i].itemCost"
                               placeholder="0.00"
                               required
                               step="0.01"
                               style="text-align: right"
                               type="number"
                               ng-model-options="{updateOn: 'blur'}"
                               clear-zero-on-focus>
                    </div>
                    <div ng-messages="purchaseOrderForm['price' + i].$error" role="alert">
                        <div class="help-block" ng-message="required">Price is required</div>
                        <div class="help-block" ng-message="pattern">A leading digit before the decimal is required
                            (e.g. '0.95')
                        </div>
                    </div>
                </div>
            </div>
            <div class="row"
                 title="Number of Units">
                <div class="col-sm-5">
                    <label for="poiQuantity{{i}}">Quantity</label>
                </div>
                <div class="col-sm-7"
                     ng-class="{'has-error' : submitted && purchaseOrderForm['poiQuantity' + i].$invalid, 'has-success' : submitted && purchaseOrderForm['poiQuantity' + i].$valid}">
                    <div class="input-group">
                        <div class="input-group-addon">
                            {{purchaseOrderItems[i].unitOfMeasure ? purchaseOrderItems[i].unitOfMeasure.substring(0, 1) : '&nbsp;&nbsp;'}}
                        </div>
                        <input class="form-control input-sm"
                               id="poiQuantity{{i}}"
                               min="0"
                               name="poiQuantity{{i}}"
                               ng-change="poiQuantityChanged(i, purchaseOrderItems[i].quantity, '{{purchaseOrderItems[i].quantity}}')"
                               ng-disabled="searching || !hasPermission('purchase_order_add') || purchaseOrder.poom || ((purchaseOrderItems[i].vendor.name == null || (purchaseOrderItems[i].vendor.name != null && purchaseOrderItems[i].vendor.name != 'Empire')) && ['inventory','ordered'].includes(purchaseOrder.status))"
                               ng-model="purchaseOrderItems[i].quantity"
                               placeholder="0"
                               required
                               style="text-align: right"
                               type="number">
                    </div>
                    <div ng-messages="purchaseOrderForm['poiQuantity' + i].$error" ng-show="submitted" role="alert">
                        <div class="help-block" ng-message="required">Quantity is required.</div>
                        <div class="help-block" ng-message="min">Quantity cannot be less than 0</div>
                    </div>
                </div>
            </div>
            <div class="row"
                 title="Total PO discount prorated for all units of this item combined">
                <div class="col-sm-5">
                    <label for="discount{{i}}">Discount</label>
                </div>
                <div class="col-sm-7">
                    <div class="input-group">
                        <div class="input-group-addon">-$</div>
                        <input class="form-control input-sm"
                               disabled
                               id="discount{{i}}"
                               ng-model="purchaseOrderItems[i].$discount"
                               ng-readonly="true"
                               placeholder="0.00"
                               style="text-align: right"
                               type="number">
                    </div>
                </div>
            </div>
            <div class="row"
                 title="Total Additional Charges prorated for all units of this item">
                <div class="col-sm-5">
                    <label for="add-charges{{i}}">Additional</label>
                </div>
                <div class="col-sm-7">
                    <div class="input-group">
                        <div class="input-group-addon">$</div>
                        <input class="form-control input-sm"
                               disabled
                               id="add-charges{{i}}"
                               ng-model="purchaseOrderItems[i].$additionalCharges"
                               ng-readonly="true"
                               placeholder="0.00"
                               style="text-align: right"
                               type="number">
                    </div>
                </div>
            </div>
            <div class="row"
                 title="Sales Tax for all units of this item combined">
                <div class="col-sm-5">
                    <label for="tax{{i}}">Sales Tax</label>
                </div>
                <div class="col-sm-7">
                    <div class="input-group">
                        <div class="input-group-addon">$</div>
                        <input class="form-control input-sm"
                               disabled
                               id="tax{{i}}"
                               ng-model="purchaseOrderItems[i].$salesTax"
                               ng-readonly="true"
                               placeholder="0.00"
                               style="text-align: right"
                               type="number">
                    </div>
                </div>
            </div>
            <div class="row"
                 title="Total Shipping Cost prorated for all units of this item combined">
                <div class="col-sm-5">
                    <label for="shipping{{i}}">Shipping</label>
                </div>
                <div class="col-sm-7">
                    <div class="input-group">
                        <div class="input-group-addon">$</div>
                        <input class="form-control input-sm"
                               disabled
                               id="shipping{{i}}"
                               ng-model="purchaseOrderItems[i].$shippingCharges"
                               ng-readonly="true"
                               placeholder="0.00"
                               style="text-align: right"
                               type="number">
                    </div>
                </div>
            </div>
            <div class="row"
                 title="Total Price for all units of this item combined">
                <div class="col-sm-5">
                    <label for="totalPrice{{i}}" style="font-weight: bold">Total Price</label>
                </div>
                <div class="col-sm-7">
                    <div class="input-group">
                        <div class="input-group-addon" style="font-weight: bold">$</div>
                        <input class="form-control input-sm"
                               disabled
                               id="totalPrice{{i}}"
                               ng-model="purchaseOrderItems[i].totalCost"
                               placeholder="0.00"
                               style="font-weight: bold; text-align: right"
                               type="number">
                    </div>
                </div>
            </div>
            <div class="row"
                 title="COG for all units of this item combined">
                <div class="col-sm-5">
                    <label for="COG{{i}}" style="color: #020da7; font-weight: bold">COG</label>
                </div>
                <div class="col-sm-7">
                    <div class="input-group">
                        <div class="input-group-addon" style="color: #020da7; font-weight: bold;">$</div>
                        <input class="form-control input-sm"
                               disabled
                               id="COG{{i}}"
                               ng-model="purchaseOrderItems[i].cog"
                               placeholder="0.00"
                               style="font-weight: bold; text-align: right"
                               type="number">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-5 form-group">
            <label for="additional_comments{{i}}">Additional Comments</label>
            <textarea class="form-control input-sm"
                      id="additional_comments{{i}}"
                      name="additional_comments{{i}}"
                      ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i]) || purchaseOrder.status === 'inventory'"
                      ng-model="purchaseOrderItems[i].additionalComments"></textarea>
        </div>
        <div class="col-sm-4 form-group">
            <label for="warranty_info{{i}}">Warranty Information</label>
            <textarea class="form-control input-sm"
                      id="warranty_info{{i}}"
                      name="warrant_info{{i}}"
                      ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i]) || purchaseOrder.status === 'inventory'"
                      ng-model="purchaseOrderItems[i].warrantyInfo"></textarea>
        </div>
        <div class="col-sm-2 form-group mt-40"
             ng-if="!isCascade && !isSPS && ['approved', 'awaiting_approval', 'denied', 'from_inventory', 'on_hold', 'open', 'pending_review', 'purchase_order_error', 'transfer_order', 'transfer_pending', 'transfer_received'].includes(purchaseOrder.status)">
            <button class="btn btn-rounded btn-sm btn-warning"
                    id="to-shopping-cart"
                    ng-click="deletePOItem(purchaseOrderForm, i, true)"
                    ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i])"
                    title="Return the item to shopping cart"
                    type="button">
                <i class="fa fa-refresh"></i> Return to Shopping Cart
            </button>
        </div>
        <div class="col-sm-1 form-group mt-40"
             ng-if="!isCascade && !isSPS && ['approved', 'awaiting_approval', 'denied', 'from_inventory', 'on_hold', 'open', 'pending_review', 'purchase_order_error', 'transfer_order', 'transfer_pending', 'transfer_received'].includes(purchaseOrder.status)">
            <button class="btn btn-rounded btn-sm btn-danger"
                    id="delete-item"
                    ng-click="deletePOItem(purchaseOrderForm, i, false)"
                    ng-disabled="!userCanEditPurchaseOrder(purchaseOrderItems[i])"
                    title="Remove the item from the PO"
                    type="button">
                <i class="fa fa-trash"></i>&nbsp;&nbsp;&nbsp;Delete&nbsp;
            </button>
        </div>
    </div>
    <div class="row">
        <ng-include src="'views/tmpl/purchasing/_purchase_order_item_invoice.html'"></ng-include>
    </div>
</div>
<ng-include src="'views/tmpl/patient/_search_result_templates.html'"></ng-include>
