<div class="clearfix content-holder" xmlns="http://www.w3.org/1999/html">
    <form autocomplete="off" name="purchaseOrderForm" novalidate>
        <div class="row pb-10 sticky-row">
            <div class="col-sm-12 text-right">
                <button class="btn btn-rounded btn-sm btn-warning mr-10"
                        id="display-packing-slip"
                        ng-if="purchaseOrder.id !== undefined"
                        ng-click="openPurchaseOrderInvoice(true)"
                        title="Packing Slip"
                        type="button">
                    <i class="fa fa-print fa-lg"></i>
                    Packing Slip
                </button>
                <button class="btn btn-success btn-sm btn-rounded"
                        data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Saving..."
                        id="save-form"
                        ng-click="save(purchaseOrderForm)"
                        ng-disabled="(!hasPermission('purchase_order_add') && !hasPermission('purchase_order_edit')) || loading || purchaseOrder.status === 'inventory'  || (submitted && purchaseOrderForm.$invalid)"
                        type="submit">
                    <i class="fa fa-save"></i>
                    Save
                </button>
            </div>
        </div>
        <div class="panel panel-primary">
            <div class="panel-heading">
                <h1 class="panel-title" ng-if="purchaseOrder.id !== undefined">
                    <strong>Purchase Order # </strong>{{purchaseOrderPrefix}}{{ purchaseOrder.id }}
                    <i class="fa fa-print fa-lg pull-right" ng-click="openPurchaseOrderInvoice()" title="Print PO"></i>
                </h1>
                <h1 class="panel-title" ng-if="purchaseOrder.id === undefined">
                    <strong>New Purchase Order</strong>
                </h1>
            </div>
            <div class="panel-body">
                <div class="row text-center" ng-if="purchasingService.loading">
                    <div class="col-sm-12">
                        <i class="fa fa-spinner fa-spin fa-5x text-greensea pt-20"></i>
                    </div>
                </div>
                <div ng-if="!purchasingService.loading">
                    <div class="row">
                        <div class="col-sm-2 form-group">
                            <label for="referenceNumber">Reference #</label>
                            <input class="form-control"
                                   id="referenceNumber"
                                   name="referenceNumber"
                                   ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.poom"
                                   ng-model="purchaseOrder.referenceNumber">
                        </div>
                        <div class="col-sm-2 form-group">
                            <label for="orderedAt">Date Ordered</label>
                            <p class="input-group">
                                <input as-date
                                       class="form-control"
                                       close-text="Close"
                                       datepicker-options="calendar.dateOptions"
                                       id="orderedAt"
                                       is-open="calendar.opened.orderedAt"
                                       name="orderedAt"
                                       ng-cloak
                                       ng-disabled="!userCanEditPurchaseOrder() || ['inventory','ordered'].includes(purchaseOrder.status)"
                                       ng-model="purchaseOrder.orderedAt"
                                       placeholder="MM/dd/yyyy"
                                       type="text"
                                       uib-datepicker-popup="MM/dd/yyyy">
                                <span class="input-group-btn">
                                    <button class="btn btn-default" ng-click="calendar.open($event, 'orderedAt')"
                                            ng-disabled="!userCanEditPurchaseOrder()"
                                            type="button"><i
                                            class="fa fa-calendar"></i></button>
                                </span>
                            </p>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label for="expectedAt">Date Expected</label>
                            <p class="input-group">
                                <input as-date
                                       class="form-control"
                                       close-text="Close"
                                       datepicker-options="calendar.dateOptions"
                                       id="expectedAt"
                                       is-open="calendar.opened.expectedAt"
                                       name="expectedAt"
                                       ng-cloak
                                       ng-disabled="!userCanEditPurchaseOrder() || ['inventory','ordered'].includes(purchaseOrder.status)"
                                       ng-model="purchaseOrder.expectedAt"
                                       placeholder="MM/dd/yyyy"
                                       type="text"
                                       uib-datepicker-popup="MM/dd/yyyy">
                                <span class="input-group-btn">
                                    <button class="btn btn-default" ng-click="calendar.open($event, 'expectedAt')"
                                            ng-disabled="!userCanEditPurchaseOrder()"
                                            type="button"><i
                                            class="fa fa-calendar"></i></button>
                                </span>
                            </p>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label for="poStatus">Manual Status Selection</label>
                            <div class="input-group">
                                <span class="input-group-addon check">
                                    <input class="m-0" id="manual_status_checkbox" ng-model="purchaseOrder.manualStatusSelection"
                                           type="checkbox">
                                </span>
                                <select class="form-control input-sm chosen-select"
                                        disable-search="{{isMobile}}"
                                        id="poStatus"
                                        ng-change="poStatusChanged(purchaseOrder.status, '{{purchaseOrder.status}}')"
                                        ng-disabled="!purchaseOrder.manualStatusSelection"
                                        ng-model="purchaseOrder.status"
                                        ng-options="key as data for (key, data) in statuses"
                                        ng-required="purchaseOrder.manualStatusSelection">
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group" ng-if="purchaseOrder.vendorId">
                            <label for="vendorEmail">Vendor Email</label>
                            <input class="form-control"
                                   id="vendorEmail"
                                   name="vendorEmail"
                                   ng-disabled="true"
                                   ng-model="purchaseOrder.vendor.email">
                        </div>
                        <div class="col-sm-2 form-group" ng-if="purchaseOrder.vendorId">
                            <label for="vendorPhone">Vendor Customer ID</label>
                            <input class="form-control"
                                   id="vendorPhone"
                                   name="vendorPhone"
                                   ng-disabled="true"
                                   ng-model="purchaseOrder.vendor.vendorCustomerId">
                        </div>
                        <div class="col-sm-12">
                            <audit-button class="pull-right"
                                          entity="purchase_order"
                                          ng-if="purchaseOrder && purchaseOrder.id"
                                          ng-model="purchaseOrder.id"
                                          showlabel="{{true}}"></audit-button>
                        </div>
                    </div>
                    <div class="row pl-15 pb-15">
                        <ng-include src="'views/tmpl/purchasing/_purchase_order_item_invoice.html'"></ng-include>
                    </div>
                    <div class="row">
                        <div class="col-sm-2 form-group">
                            <label class="checkbox checkbox-custom">
                                <input name="single-patient" ng-click="singlePatientChanged()"
                                       ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'inventory'"
                                       ng-model="purchaseOrder.singlePatient"
                                       type="checkbox">
                                <i></i>
                                Single Patient
                            </label>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label class="checkbox checkbox-custom">
                                <input name="single-branch"
                                       ng-click="singleBranchChanged()"
                                       ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'inventory'"
                                       ng-model="purchaseOrder.singleBranch"
                                       type="checkbox">
                                <i></i>
                                Single Branch
                            </label>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label class="checkbox checkbox-custom">
                                <input name="single-vendor" ng-click="singleVendorChanged()"
                                       ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'inventory'"
                                       ng-model="purchaseOrder.singleVendor"
                                       type="checkbox">
                                <i></i>
                                Single Vendor
                            </label>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label class="checkbox checkbox-custom">
                                <input name="single-practitioner" ng-click="singlePractitionerChanged()"
                                       ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'inventory'"
                                       ng-model="purchaseOrder.singlePractitioner"
                                       type="checkbox">
                                <i></i>
                                Single Practitioner
                            </label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-2 form-group"
                             ng-class="{'has-error' : submitted && purchaseOrderForm.poPatient.$invalid, 'has-success' : submitted && purchaseOrderForm.poPatient.$valid}"
                             ng-if="purchaseOrder.singlePatient">
                            <label for="poPatient">Patient</label>
                            <div class="search">
                                <div id="scrollable-dropdown-menu">
                                    <input class="form-control"
                                           id="poPatient"
                                           name="poPatient"
                                           ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'inventory'"
                                           ng-model="purchaseOrder.patient"
                                           ng-required="purchaseOrder.singlePatient"
                                           placeholder="Search Patient by Name, Id, or SSN"
                                           size="40"
                                           type="text"
                                           typeahead-min-length="2"
                                           typeahead-on-select="poPatientChanged()"
                                           typeahead-template-url="patientWithDOBSearchTemplate.html"
                                           typeahead-wait-ms="500"
                                           uib-typeahead="patient as utilService.formatNameWithDOB(patient) for patient in patientService.getPatients($viewValue)"/>
                                </div>
                            </div>
                            <div ng-messages="purchaseOrderForm.poPatient.$error" ng-show="submitted" role="alert">
                                <div class="help-block" ng-message="required">Patient is required</div>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group" ng-if="purchaseOrder.singlePatient">
                            <label class="input-label" for="poPrescription">{{utilService.prescriptionNoun}}</label>
                            <select chosen
                                    class="form-control input-sm chosen-select"
                                    disable-search="{{isMobile}}"
                                    id="poPrescription"
                                    name="poPrescription"
                                    ng-change="poPrescriptionChanged()"
                                    ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'inventory'"
                                    ng-model="purchaseOrder.prescriptionId"
                                    ng-options="prescription.id as (prescription.deviceType.name + ' #' + prescription.id) for prescription in purchaseOrder.prescriptions">
                                <option value="">Select a {{utilService.prescriptionNoun}}</option>
                            </select>
                        </div>
                        <div class="col-sm-2 form-group" ng-if="purchaseOrder.singlePatient">
                            <label class="input-label" for="poPrescriptionLCode">{{utilService.prescriptionNoun}} HCPCS</label>
                            <select chosen
                                    class="form-control input-sm chosen-select"
                                    disable-search="{{isMobile}}"
                                    id="poPrescriptionLCode"
                                    name="poPrescriptionLCode"
                                    ng-change="poPrescriptionLCodeChanged()"
                                    ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'inventory'"
                                    ng-model="purchaseOrder.prescriptionLCodeId"
                                    ng-options="plc.id as (plc.lCode.name +' '+plc.lCode.description) for plc in purchaseOrder.prescription.prescriptionLCodes">
                                <option value="">Select a {{utilService.prescriptionNoun}} HCPCS</option>
                            </select>
                        </div>
                        <div class="col-sm-2 form-group"
                             ng-class="{'has-error' : purchaseOrderForm.poBranch.$invalid, 'has-success' : purchaseOrderForm.poBranch.$valid}"
                             ng-cloak
                             ng-if="purchaseOrder.singleBranch"
                             title="{{'Branch #' + purchaseOrder.branchId}}">
                            <label for="poBranch">Branch</label>
                            <select class="form-control input-sm"
                                    disable-search="{{isMobile}}"
                                    id="poBranch"
                                    name="poBranch"
                                    ng-change="poBranchChanged()"
                                    ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'inventory'"
                                    ng-model="purchaseOrder.branchId"
                                    ng-options="branch.id as branch.name for branch in branches | orderBy: 'name'"
                                    ng-required="purchaseOrder.singleBranch">
                                <option value="">Select a branch</option>
                            </select>
                            <div ng-messages="purchaseOrderForm.poBranch.$error"
                                 role="alert">
                                <div class="help-block" ng-message="required">Branch is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group"
                             ng-class="{ 'has-error' : submitted && purchaseOrderForm.poVendor.$invalid, 'has-success' : submitted && purchaseOrderForm.poVendor.$valid}"
                             ng-if="purchaseOrder.singleVendor">
                            <label>Vendor
                                <a class="action-link text-greensea text-sm ml-5"
                                   id="vendor_add"
                                   ng-click="purchasingService.openNewVendorModal()"
                                   ng-if="hasPermission('vendor_add')"
                                   role="button">
                                    <i class="fa fa-plus"></i>
                                </a>
                            </label>
                            <div class="search">
                                <div id="scrollable-dropdown-menu1">
                                    <input class="form-control"
                                           id="poVendor"
                                           name="poVendor"
                                           ng-model="purchaseOrder.vendor"
                                           placeholder="Search for Vendor"
                                           required
                                           size="40"
                                           type="text"
                                           typeahead-on-select="poVendorChanged()"
                                           typeahead-template-url="searchVendorTemplate.html"
                                           typeahead-wait-ms="500"
                                           uib-typeahead="vendor as vendor.name for vendor in purchasingService.getVendors($viewValue)"/>
                                </div>
                            </div>
                            <div ng-messages="purchaseOrderForm.poVendor.$error" ng-show="submitted"
                                 role="alert">
                                <div class="help-block" ng-message="required">Vendor is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group" ng-if="purchaseOrder.singlePractitioner">
                            <label for="poPractitioner">{{utilService.practitionerNoun}}</label>
                            <select chosen
                                    class="form-control input-sm chosen-select"
                                    disable-search="{{isMobile}}"
                                    id="poPractitioner"
                                    name="poPractitioner"
                                    ng-change="poPractitionerChanged()"
                                    ng-disabled="!userCanEditPurchaseOrder()|| purchaseOrder.status === 'inventory'"
                                    ng-model="purchaseOrder.practitionerId"
                                    ng-options="practitioner.id as utilService.formatName(practitioner, 'FL') for practitioner in practitioners">
                                <option value="">{{'Select a ' + utilService.practitionerNoun}}</option>
                            </select>
                        </div>
                    </div>
                    <div class="row text-center" ng-if="loading">
                        <i class="fa fa-spinner fa-spin fa-5x" style="color: #16A085;"></i>
                    </div>
                    <div ng-class="{'row purchase-order-item-row': !loading, 'purchase-order-item-last-row': !loading && i === (purchaseOrderItems.length - 1)}"
                         ng-if="!loading"
                         ng-repeat="(i, purchaseOrderItem) in purchaseOrderItems track by $index">
                        <ng-include src="'views/tmpl/purchasing/_purchase_order_item_form.html'"></ng-include>
                    </div>
                    <div class="row" ng-if="!purchaseOrder.poom">
                        <div class="text-center m-20">
                            <button class="btn btn-primary btn-rounded" ng-click="addLineItem()"
                                    ng-disabled="!hasPermission('purchase_order_add') || ['inventory','ordered'].includes(purchaseOrder.status)"
                                    type="button">Add Line Item
                            </button>
                        </div>
                    </div>
                    <div class="row purchase-order-item-row">
                        <div class="row">
                            <div class="col-sm-3 form-group" ng-if="!isCascade">
                                <label>Delivery Location</label>
                                <select class="form-control input-sm chosen-select"
                                        name="delivery_location"
                                        ng-change="updateDeliveryLocation(false, delivery_location)"
                                        ng-disabled="['inventory','ordered'].includes(purchaseOrder.status)"
                                        ng-model="delivery_location">
                                    <option value="">Select Shipping Location</option>
                                    <option ng-repeat="branch in branches | orderBy: 'name'" value="{{branch}}">
                                        {{branch.name}}
                                    </option>
                                    <option ng-if="purchaseOrder.patient"
                                            ng-selected="delivery_location === 'primary_branch'"
                                            value="primary_branch">
                                        Patient Primary Branch
                                    </option>
                                    <option ng-if="purchaseOrder.patient"
                                            ng-selected="delivery_location === 'patient_address'"
                                            value="patient_address">
                                        Patient Address
                                    </option>
                                    <option ng-if="purchaseOrder.patient.streetAddress2"
                                            ng-selected="delivery_location === 'patient_alternate_address'"
                                            value="patient_alternate_address">
                                        Patient Alternate Address
                                    </option>
                                    <!--                                    <option value="shipping_branch"-->
                                    <!--                                            ng-selected="delivery_location === 'shipping_branch'">Ship To Branch-->
                                    <!--                                    </option>-->
                                    <option ng-selected="delivery_location === 'other'"
                                            value="other">Other
                                    </option>
                                </select>
                            </div>
                            <!--                            <div class="col-sm-3 form-group"-->
                            <!--                                 ng-if="isSPS && delivery_location === 'shipping_branch' && userCanEditPurchaseOrder() && !['inventory','ordered'].includes(purchaseOrder.status)">-->
                            <!--                                <label>Branch</label>-->
                            <!--                                <select id="branchId"-->
                            <!--                                        name="branchId"-->
                            <!--                                        class="form-control input-sm"-->
                            <!--                                        ng-model="shipping.branch"-->
                            <!--                                        ng-options="branch as branch.name for branch in branches">-->
                            <!--                                    <option value="">Select a branch</option>-->
                            <!--                                </select>-->
                            <!--                            </div>-->
                            <div class="col-sm-8 form-group" ng-class="{'has-error' : submitted && purchaseOrderForm.cascade_location.$invalid, 'has-success' : submitted && purchaseOrderForm.cascade_location.$valid}"
                                 ng-if="isCascade">
                                <label for="cascade_location"
                                       ng-if="!purchaseOrderForm.cascade_location.$invalid || !submitted">Shipping Location (* Shipping Location is required when
                                    creating order with
                                    Cascade)</label>
                                <label class="text-strong text-red"
                                       for="cascade_location" ng-if="purchaseOrderForm.cascade_location.$invalid && submitted">Shipping Location (* Shipping
                                    Location is required when creating order with
                                    Cascade)</label>
                                <select chosen
                                        class="form-control input-sm chosen-select"
                                        disable-search="{{isMobile}}" id="cascade_location"
                                        name="cascade_location"
                                        ng-change="updateDeliveryLocation(true, selected.location)"
                                        ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'ordered'"
                                        ng-model="selected.location"
                                        ng-options="location as '(' + location.name + ') - ' + location.fullAddress for location in cascadeLocations"
                                        required>
                                    <option value="">Select Shipping Location</option>
                                </select>
                                <div ng-messages="purchaseOrderForm.cascade_location.$error" ng-show="submitted"
                                     role="alert">
                                    <div class="help-block" ng-message="required">Shipping Location is required.</div>
                                </div>
                            </div>
                            <div class="col-sm-2 form-group" ng-if="isSPS">
                                <label class="input-label">Shipping Method</label>
                                <select chosen
                                        class="form-control input-sm chosen-select"
                                        disable-search="{{isMobile}}"
                                        id="shipping_method" name="shipping_method"
                                        ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'ordered'"
                                        ng-model="purchaseOrder.thirdPartyShippingMethodId"
                                        ng-options="sm.id as sm.vendorShippingMethod for sm in shippingMethods"
                                        required>
                                    <option value="">Select a Shipping Method</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6 form-group">
                                <label for="street_address">Attn</label>
                                <input class="form-control input-sm" id="attn" name="attn" ng-disabled="['inventory','ordered'].includes(purchaseOrder.status) || isCascade"
                                       ng-model="purchaseOrder.attn"
                                       type="text">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 form-group">
                                <label for="street_address">Street Address</label>
                                <input class="form-control input-sm" id="street_address" name="street_address"
                                       ng-disabled="['inventory','ordered'].includes(purchaseOrder.status) || isCascade" ng-model="purchaseOrder.streetAddress"
                                       type="text">
                            </div>
                        </div>
                        <div class="row" ng-if="purchaseOrder.streetAddress2">
                            <div class="col-sm-12 form-group">
                                <label for="street_address">Street Address Line 2</label>
                                <input class="form-control input-sm" id="street_address2" name="street_address2"
                                       ng-disabled="['inventory','ordered'].includes(purchaseOrder.status) || isCascade" ng-model="purchaseOrder.streetAddress2"
                                       type="text">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-5 form-group">
                                <label for="city">City</label>
                                <input class="form-control input-sm" id="city" name="city" ng-disabled="['inventory','ordered'].includes(purchaseOrder.status) || isCascade"
                                       ng-model="purchaseOrder.city"
                                       type="text">
                            </div>
                            <div class="col-sm-2 form-group">
                                <label for="state">State</label>
                                <div class="select" id="state-class-handler">
                                    <select chosen="{width: '240px'}" class="form-control input-sm chosen-select" id="state"
                                            name="state"
                                            ng-disabled="['inventory','ordered'].includes(purchaseOrder.status) || isCascade"
                                            ng-model="purchaseOrder.state">
                                        <option ng-repeat="(key, data) in states" ng-selected="key === purchaseOrder.state"
                                                value="{{key}}">{{data}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-3 form-group"
                                 ng-class="{ 'has-error' : submitted && !patientForm.zipcode.$pristine && patientForm.zipcode.$invalid, 'has-success' : submitted && !patientForm.zipcode.$pristine && patientForm.zipcode.$valid}">
                                <label for="zipcode">Zipcode</label>
                                <input class="form-control input-sm bfh-phone"
                                       data-format="ddddd-dddd"
                                       id="zipcode"
                                       name="zipcode"
                                       ng-disabled="['inventory','ordered'].includes(purchaseOrder.status) || isCascade"
                                       ng-model="purchaseOrder.zipcode"
                                       ng-pattern="/^[0-9]{5}([-][0-9]{1,4}){0,1}$/"
                                       type="text">
                            </div>
                            <div class="col-sm-2 form-group">
                                <label>Country</label>
                                <input class="form-control input-sm"
                                       disabled
                                       ng-model="purchaseOrder.country"
                                       placeholder="US"
                                       type="text"
                                       value="US">
                            </div>
                        </div>
                    </div>
                    <div class="row purchase-order-item-row" ng-if="purchaseOrder.id">
                        <div class="row">
                            <div class="col-sm-10 bg-primary p-10 text-strong">
                                <i class="fa fa-files-o"></i> Attachments
                            </div>
                            <div class="col-sm-2 bg-primary p-5 text-strong">
                                <button class="btn btn-danger btn-sm btn-rounded text-sm mr-5 pull-right"
                                        id="files-and-docs-upload"
                                        ng-click="fileService.openUploadModal(null, purchaseOrder.id)">Upload
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="panel-body pb-0 b-0 files-uploaded">
                                <div class="single-note media mt-0 p-10 b-{{file.fileType.color}}"
                                     ng-repeat="file in fileService.poFiles"
                                     ng-show="fileService.poFiles.length && !fileService.fileLoading">
                                    <div class="col-sm-1 pl-0 mt-10">
                                        <div class="thumb thumb-sm" file-id="{{file.id}}" view-attachment>
                                            <!--                                             ng-click="utilService.openPrintScreen(file.url + '/' + file.fileName)">-->
                                            <img alt=""
                                                 class="img-circle size-30x30"
                                                 id="user-profile-photo"
                                                 onerror="return false;" profile-photo
                                                 user-id="{{file.providedById}}">
                                        </div>
                                    </div>
                                    <div class="col-sm-11 pl-0">
                                        <div class="col-sm-12">
                                            <div class="row">
                                                <span class="pull-right">
                                                    <a class="text-primary ml-5" ng-click="fileService.openUploadModal(file, purchaseOrder.id)"
                                                       role="button">
                                                        <i alt="Edit File" class="fa fa-edit"></i>
                                                    </a>
                                                    <a download="{{file.fileName}}" file-id="{{file.id}}"
                                                       href="" view-attachment>
                                                        <i alt="Download File" class="fa fa-download text-success"></i>
                                                    </a>
                                                    <a class="text-danger ml-5 delete-file" ng-click="fileService.deleteFile(file)"
                                                       ng-if="hasPermission('file_delete')"
                                                       role="button">
                                                        <i alt="Delete File" class="fa fa-trash"></i>
                                                    </a>
                                                </span>
                                            </div>
                                            <div class="row" file-id="{{file.id}}" view-attachment>
                                                <span class="full-note col-sm-10 pl-0 text-nowrap posted-by"
                                                      ng-bind-html="file.description"></span>
                                                <span class="pull-right text-sm text-muted">
                                                    <i class="fa fa-clock-o"></i>
                                                    {{ moment.utc(file.createdAt).local().format('L') }}
                                                </span>
                                            </div>
                                            <div class="row" file-id="{{file.id}}" view-attachment>
                                                <span class="col-sm-9 text-primary pl-0">
                                                    {{utilService.formatName(file.providedBy, "FLMC")}}
                                                </span>
                                                <div class="note-type label pull-right bg-{{file.fileType.color}}">
                                                    {{file.fileType.name}}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="filled bg-warning"
                                     ng-hide="fileService.poFiles.length && !fileService.fileLoading">No files found.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row purchase-order-item-row">
                        <div class="col-sm-9">
                            <label for="note">Notes</label>
                            <textarea class="form-control input-sm"
                                      id="note"
                                      name="note"
                                      ng-disabled="!userCanEditPurchaseOrder() || purchaseOrder.status === 'inventory'"
                                      ng-model="purchaseOrder.note"
                                      rows="5">
                            </textarea>
                        </div>
                        <div class="col-sm-3">
                            <div class="row mb-5">
                                <div class="col-sm-6">
                                    <label>Sub Total</label>
                                </div>
                                <div class="col-sm-6">
                                    <div class="input-group">
                                        <div class="input-group-addon">$</div>
                                        <input class="form-control input-sm"
                                               disabled
                                               id="subTotal"
                                               name="subTotal"
                                               ng-model="purchaseOrder.subTotal"
                                               style="text-align: right"
                                               type="number">
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-5">
                                <div class="col-sm-6">
                                    <label>Discount</label>
                                </div>
                                <div class="col-sm-6"
                                     ng-class="{'has-error' : submitted && purchaseOrderForm.discount.$invalid, 'has-success' : submitted && purchaseOrderForm.discount.$valid}">
                                    <div class="input-group">
                                        <div class="input-group-addon">-$</div>
                                        <input class="form-control input-sm"
                                               id="discount"
                                               min="{{ 0 }}"
                                               name="discount"
                                               ng-change="calculateTotals()"
                                               ng-disabled="!userCanEditPurchaseOrder()|| ['inventory','ordered'].includes(purchaseOrder.status)"
                                               ng-model="purchaseOrder.discount"
                                               placeholder="0.00"
                                               step="0.01"
                                               style="text-align: right"
                                               type="number">
                                    </div>
                                    <div ng-messages="purchaseOrderForm.discount.$error" ng-show="submitted"
                                         role="alert">
                                        <div class="help-block" ng-message="min">Discount cannot be less than 0</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-5">
                                <div class="col-sm-6">
                                    <label>Add. Charges</label>
                                </div>
                                <div class="col-sm-6"
                                     ng-class="{'has-error' : submitted && purchaseOrderForm.additionalCharges.$invalid, 'has-success' : submitted && purchaseOrderForm.additionalCharges.$valid}">
                                    <div class="input-group">
                                        <div class="input-group-addon">$</div>
                                        <input class="form-control input-sm"
                                               id="additionalCharges"
                                               min="{{ 0 }}"
                                               name="additionalCharges"
                                               ng-change="calculateTotals()"
                                               ng-disabled="!userCanEditPurchaseOrder() || ['inventory','ordered'].includes(purchaseOrder.status)"
                                               ng-model="purchaseOrder.additionalCharges"
                                               placeholder="0.00"
                                               step="0.01"
                                               style="text-align: right"
                                               type="number">
                                    </div>
                                    <div ng-messages="purchaseOrderForm.additionalCharges.$error" ng-show="submitted"
                                         role="alert">
                                        <div class="help-block" ng-message="min">
                                            Additional Charges cannot be less than 0
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-5">
                                <div class="col-sm-6">
                                    <label>Sales Tax</label>
                                </div>
                                <div class="col-sm-6"
                                     ng-class="{'has-error' : submitted && purchaseOrderForm.salesTax.$invalid, 'has-success' : submitted && purchaseOrderForm.salesTax.$valid}">
                                    <div class="input-group">
                                        <div class="input-group-addon">$</div>
                                        <input class="form-control input-sm"
                                               id="salesTax"
                                               min="{{ 0 }}"
                                               name="salesTax"
                                               ng-change="calculateTotals()"
                                               ng-disabled="!userCanEditPurchaseOrder() || ['inventory','ordered'].includes(purchaseOrder.status)"
                                               ng-model="purchaseOrder.salesTax"
                                               ng-model-options="{debounce: 1000}"
                                               placeholder="0.00"
                                               step="0.01"
                                               style="text-align: right"
                                               type="number">
                                    </div>
                                    <div ng-messages="purchaseOrderForm.salesTax.$error" ng-show="submitted"
                                         role="alert">
                                        <div class="help-block" ng-message="min">Sales Tax cannot be less than 0</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-5">
                                <div class="col-sm-6">
                                    <label>Shipping Charges</label>
                                </div>
                                <div class="col-sm-6"
                                     ng-class="{'has-error' : submitted && purchaseOrderForm.shippingCharges.$invalid, 'has-success' : submitted && purchaseOrderForm.shippingCharges.$valid}">
                                    <div class="input-group">
                                        <div class="input-group-addon">$</div>
                                        <input class="form-control input-sm"
                                               id="shippingCharges"
                                               min="{{ 0 }}"
                                               name="shippingCharges"
                                               ng-change="calculateTotals()"
                                               ng-disabled="!userCanEditPurchaseOrder() || ['inventory','ordered'].includes(purchaseOrder.status)"
                                               ng-model="purchaseOrder.shippingCharges"
                                               placeholder="0.00"
                                               step="0.01"
                                               style="text-align: right"
                                               type="number">
                                    </div>
                                    <div ng-messages="purchaseOrderForm.shippingCharges.$error" ng-show="submitted"
                                         role="alert">
                                        <div class="help-block" ng-message="min">Shipping Charges cannot be less than 0
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-5">
                                <div class="col-sm-6">
                                    <label style="font-weight: bold">Total</label>
                                </div>
                                <div class="col-sm-6">
                                    <div class="input-group">
                                        <div class="input-group-addon" style="font-weight: bold">$</div>
                                        <input class="form-control input-sm"
                                               disabled
                                               id="totalCost"
                                               name="totalCost"
                                               ng-model="purchaseOrder.totalCost"
                                               style="font-weight: bold; text-align: right"
                                               type="number">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-footer">
            </div>
        </div>
    </form>
    <button ng-click="showPOOM()" ng-if="purchaseOrder.poom" novalidate>Show POOM cXML</button>
    <pre id="cxml" ng-bind-html="purchaseOrder.poom | escape" ng-if="showXML"></pre>
</div>
<ng-include src="'views/tmpl/files/_file_upload_modal.html'"></ng-include>
