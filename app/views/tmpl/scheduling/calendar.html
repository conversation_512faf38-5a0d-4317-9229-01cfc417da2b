<div class="page page-forms-common">
	<meta name="viewport" content="width=1024">
    <div id="body-container" class="mt-5 mb-5 no-print">
		<div id="header-alert-container" class="alert" style="display: none;"></div>
		<div id="filter-toggle" class="panel panel-greensea mb-10">
			<div class="panel-body pt-10 pb-10">
				<div class="row">
					<div class="col-sm-7">
						<div class="form-group mb-0">
							<div id="filter-location-id-class-handler" class="select">
								<label>Company Branch</label>
								<ui-select multiple ng-model="calendarTemplateBranches.selected"
										   close-on-select="false" style="height: auto; min-height: 30px; padding: .3em;"
										   on-select="selectCalendarTemplateBranch($item)"
										   on-remove="deselectCalendarTemplateBranch($item)">
									<ui-select-match placeholder="...">{{''+$item.name}}</ui-select-match>
									<ui-select-choices repeat="branch in calendarTemplateBranches | filter:$select.search track by $index">
										<div ng-bind-html="''+branch.name | highlight: $select.search"></div>
									</ui-select-choices>
								</ui-select>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group mb-0">
							<div id="filter-template-class-handler" class="select">
								<label>Calendar Template</label>
								<select name="filter_template"
										id="filter_template"
										chosen
										ng-model="filter.template"
										class="form-control input-sm chosen-select"
										ng-options="t.id as t.name for t in userCalendarTemplates"
										ng-selected="t.id === filter.template"
										ng-change="loadCalendarValues(filter.template)">
									<option value="">Select Template</option>
								</select>
							</div>
						</div>
					</div>
                    <div class="col-sm-1">
                        <div class="form-group mb-0">
                            <div align="center">
                                <label>Cancelled</label>
                                <div class="col-sm-12 pl-0">
                                    <label class="checkbox checkbox-custom">
										<input type="checkbox" ng-model="filter.cancelled">
                                        <i></i>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
				</div>
                <div class="row">
                    <div class="col-sm-7">
                        <div class="form-group mb-0">
                            <div id="filter-user-id-class-handler" class="select">
                                <label>Users</label>
								<ui-select multiple ng-model="users.selected"
										   close-on-select="false" style="height: auto; min-height: 30px; padding: .3em;"
										   on-select="selectCalendarTemplateUser($item, filter.template)"
										   on-remove="deselectCalendarTemplateUser($item, filter.template)">
									<ui-select-match placeholder="...">{{''+$item.firstAndLastName}}</ui-select-match>
									<ui-select-choices repeat="user in users | filter:$select.search track by $index">
										<div ng-bind-html="''+user.firstAndLastName | highlight: $select.search"></div>
									</ui-select-choices>
								</ui-select>
                            </div>
                        </div>
                    </div>
					<div class="col-sm-3">
						<div class="form-group mb-0">
							<div id="filter-status-class-handler" class="select">
								<label>Appointment Status</label>
								<select name="filter_status"
										id="filter_status"
										chosen
										ng-model="filter.status"
										class="form-control input-sm chosen-select"
										ng-options="key as data.name for (key, data) in appointmentStatus">
									<option value="">All Appointments</option>
								</select>
							</div>
						</div>
					</div>
                    <div class="col-sm-2 form-group mb-0">
                        <label>Filter</label>
                        <button class="btn btn-primary btn-rounded form-control" ng-click="applyFilter()"> Search</button>
                    </div>
                </div>
<!--				<div class="row col-sm-12 mt-5" ng-if="userService.getCurrentUser().nylasActive">-->
<!--					<button class="btn btn-secondary btn-sm btn-rounded" ng-click="initialNylasSync()" ng-disabled="nylasSyncDisabled">Initial Calendar Sync</button>-->
<!--					<i class="fa fa-info-circle text-info ml-3" uib-popover-template="'nylasSync.html'" popover-placement="bottom" popover-trigger="mouseenter"></i>-->
<!--				</div>-->
<!--				<div class="col-1">-->
<!--					<i class="fa fa-spinner fa-spin" ng-if="nylasLoading"></i>-->
<!--				</div>-->
			</div>
		</div>
		<div class="row" style="margin-bottom: 0px; padding-bottom: 0px">
			<div class="col-sm-6 xs-mb-15">
                <button ng-click="addAppointmentModal()" type="button" id="add-appointment"
                        class="btn btn-greensea btn-sm btn-rounded text-uppercase ml-15" style="display:none">Add
                    Appointment
                    <i class="fa fa-plus"></i>
                </button>
                <i class="fa fa-spinner fa-spin fa-2x" ng-show="filter.loading"></i>
            </div>
			<div class="col-sm-6 form-inline form-group">
                <div class="pull-right" style="margin-right:15px">
                    <button id="calendar-date-picker" class="btn btn-info btn-rounded view"
                            ng-click="miniCalendar.open($event)" style="display:none">
                        <i class="fa fa-calendar"></i> <span>Go To Date</span>
                    </button>
                </div>
                <div class="pull-right" style="margin-right:10px">
                    <input type="hidden"
						   ng-model="miniCalendarDate"
						   uib-datepicker-popup="MM/dd/yyyy"
						   ng-change="selectedDay(miniCalendarDate, true, null, null)"
						   is-open="miniCalendar.opened"
						   datepicker-options="miniCalendar.dateOptions"
						   close-text="Close"
						   class="input input-sm form-control"
						   readonly/>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-12 text-right">
				Calendar shown in {{ dateService.getLocalTimeZoneAbbreviation() }}
			</div>
		</div>
	</div>
	<div class="container-fluid" id="nymblCal" style="margin-bottom: 100px; margin-top: 0px; padding-top: 0px"></div>

</div>
<div class="modal fade" id="cannot-add-appointment-popup" role="dialog">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Past Date</h4>
			</div>
			<div class="modal-body">
				<div class="filled bg-warning">Cannot add appointment to past dates.</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default btn-sm btn-rounded" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="confirm-appointment-date-change" role="dialog" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog modal-sm">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">Update Appointment</h5>
			</div>
			<div class="modal-body">
				<p>Appointment time already full. This appointment will be moved to a later time. Continue appointment date change?
				</p>
			</div>
			<div class="modal-footer">
				<button type="button" id="change-date" class="btn btn-primary btØn-sm btn-rounded">Save</button>
				<button type="button" class="btn btn-default btn-sm btn-rounded" data-dismiss="modal">Cancel</button>
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="cannot-change-appointment-time" role="dialog" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog modal-sm">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">Change Appointment Time</h5>
			</div>
			<div class="modal-body">
				<p>Appointment time exceeds limit. Adjust time.</p>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default btn-sm btn-rounded" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>
<ng-include src="'views/tmpl/scheduling/appointments/_modal_form.html'"></ng-include>

