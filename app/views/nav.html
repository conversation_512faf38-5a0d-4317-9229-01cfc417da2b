<div id="sidebar-wrap" ng-controller="NavCtrl">
    <uib-accordion class="nav-accordion main-nav-accordion" close-others="status.oneAtTime"
                   slimscroll="{height: '100%'}">

        <div class="nav-flex-1">
            <uib-accordion-group is-open="status.isFirstOpen">
                <uib-accordion-heading>
                    Navigation
                    <i class="fa fa-angle-up pull-right" ng-show="status.isFirstOpen"></i>
                    <i class="fa fa-angle-down pull-right" ng-show="!status.isFirstOpen"></i>
                </uib-accordion-heading>
                <ul class="nav-accordion-list" id="navigation" nav-collapse ng-if="status.isFirstOpen" ripple>
                    <li ui-sref-active="active">
                        <a ui-sref="app.dashboard">
                            <i class="fa fa-dashboard"></i>
                            <span>{{ 'Menu.DASHBOARD' | translate }}</span>
                        </a>
                    </li>
                    <li ng-class="{'open':$state.includes('app.patient')}" ui-sref-active="active">
                        <a ui-sref="app.patient">
                            <i class="fa fa-users"></i>
                            <span>{{ 'Menu.PATIENTS' | translate }}</span>
                        </a>
                        <ul>
                            <li ng-show="tabService.recentPatients.length" ui-sref-active="active">
                                <a>
                                    <i class="fa fa-caret-right"></i>
                                    {{ 'Menu.RECENT_PATIENTS' | translate }}
                                </a>
                                <ul>
                                    <li ng-repeat="recentPatient in tabService.recentPatients">

                                        <a ng-click="patientProfileLink(recentPatient.id)">
                                            <i class="fa fa-caret-right"></i>
                                            {{recentPatient.name}}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li ui-sref-active="active">
                                <a ui-sref="app.patient.all">
                                    <i class="fa fa-caret-right"></i>
                                    {{ 'Menu.ALL_PATIENTS' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a ui-sref="app.patient.add">
                                    <i class="fa fa-caret-right"></i>
                                    {{ 'Menu.NEW_PATIENT' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a ng-click="openV2Link('patients/patient/new')">
                                    <i class="fa fa-caret-right"></i>
                                    {{ 'Menu.V2_NEW_PATIENT' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a ui-sref="app.patient.summary">
                                    <i class="fa fa-caret-right"></i>
                                    {{ 'Menu.PATIENT_SUMMARY' | translate }}
                                </a>
                            </li>
                            <!--                            ng-show="hasV2RxSummaryAccess"-->
                            <li ui-sref-active="active">
                                <a ng-click="openV2Link('patients/summary')">
                                    <i class="fa fa-caret-right"></i>
                                    {{ 'Menu.V2_PATIENT_SUMMARY' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                            <a><i class="fa fa-caret-right"></i>Notes</a>
                            <ul>
                                <li ui-sref-active="active">
                                    <a ui-sref="app.patient.notes">
                                        <i class="fa fa-caret-right"></i>
                                        {{ 'Menu.MISSING_NOTES' | translate }}
                                    </a>
                                </li>
                                <li ui-sref-active="active" ng-show="aiNotesOn">
                                    <a ng-click="openV2Link('patients/ai-notes')">
                                        <i class="fa fa-caret-right"></i>
                                        {{ 'Menu.AI_NOTES' | translate }}
                                    </a>
                                </li>
                            </ul>
                            </li>
                            <li ui-sref-active="active">
                                <a ui-sref="app.patient.checklist">
                                    <i class="fa fa-caret-right"></i>
                                    {{ 'Menu.CHECKLIST' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a><i class="fa fa-caret-right"></i>Patient Intake</a>
                                <ul>
                                    <li ui-sref-active="active">
                                        <a ui-sref="app.patient.intake.queue">
                                            <i class="fa fa-caret-right"></i>
                                            {{ 'Menu.PATIENT_INTAKE_QUEUE' | translate }}
                                        </a>
                                    </li>
                                    <li ui-sref-active="active">
                                        <a ui-sref="app.patient.intake.create">
                                            <i class="fa fa-caret-right"></i>
                                            {{ 'Menu.PATIENT_INTAKE_CREATE' | translate }}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li ui-sref-active="active" ng-if="enableBatchSign">
                                <a ui-sref="app.patient.swo-batch-sign">
                                    <i class="fa fa-caret-right"></i>
                                    {{ 'Menu.SWO_BATCH_SIGN' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active" ng-show="lmnOn">
                                <a ng-click="openV2Link('patients/lmn')">
                                    <i class="fa fa-caret-right"></i>
                                    {{ 'Menu.LMN_DASHBOARD' | translate }}
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li ng-class="{'open':$state.includes('app.scheduling')}" ui-sref-active="active">
                        <a ui-sref="app.scheduling">
                            <i class="fa fa-clock-o"></i>
                            <span>{{ 'Menu.SCHEDULING' | translate }}</span>
                        </a>
                        <ul>
                            <li ui-sref-active="active">
                                <a ui-sref="app.appointments.all">
                                    <i class="fa fa-list-ol"></i>
                                    <span>{{ 'Menu.APPOINTMENTS' | translate }}</span>
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a ui-sref="app.calendar">
                                    <i class="fa fa-calendar"></i>
                                    <span>{{ 'Menu.CALENDAR' | translate }}</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li ng-class="{'open':$state.includes('app.billing')}"
                        ng-show="hasPermissionDirect['billing_view'] || hasPermissionDirect['superadmin']"
                        ui-sref-active="active">
                        <a ui-sref="app.billing">
                            <i class="fa fa-money"></i>
                            <span>{{ 'Menu.BILLING' | translate }}</span></a>
                        <ul>
                            <li ui-sref-active="active">
                                <a ui-sref="app.billing.claims">
                                    <i class="fa fa-file-text-o"></i>
                                    {{ 'Menu.CLAIMS' | translate }}
                                </a>
                            </li>
                            <li ng-show="directPayOn" ui-sref-active="active">
                                <a ui-sref="app.billing.nymbl_payments">
                                    <i class="fa fa-file-code-o"></i>
                                    {{ 'Menu.NYMBL_PAYMENTS' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a ui-sref="app.billing.era_payments">
                                    <i class="fa fa-file-code-o"></i>
                                    {{ 'Menu.ERA_PAYMENTS' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a ui-sref="app.billing.posted_payments">
                                    <i class="fa fa-file-archive-o"></i>
                                    {{ 'Menu.POSTED_PAYMENTS' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a ui-sref="app.billing.statements">
                                    <i class="fa fa-file-archive-o"></i>
                                    {{ 'Menu.PATIENT_STATEMENTS' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a ng-click="openV2Link('reports/financial/balances/outstanding-balances')">
                                    <i class="fa fa-file-archive-o"></i>
                                    {{ 'Menu.OUTSTANDING_BALANCES' | translate }}
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li ng-class="{'open':$state.includes('app.questionnaire')}" ui-sref-active="active">
                        <a ui-sref="app.questionnaire">
                            <i class="fa fa-file-text-o"></i>
                            <span> {{ 'Menu.NYMBL_QUESTIONNAIRE' | translate }}</span></a>
                        <ul>
                            <li ui-sref-active="active">
                                <a ui-sref="app.questionnaire.forms">
                                    <i class="fa fa-file-text-o"></i>
                                    {{ 'Menu.NYMBL_FORMS' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a ui-sref="app.questionnaire.questions">
                                    <i class="fa fa-question"></i>
                                    {{ 'Menu.NYMBL_QUESTIONS' | translate }}
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li ng-class="{'open':$state.includes('app.inventory')}"
                        ng-show="userService.hasInventory() && (hasPermissionDirect['inventory_view'] || hasPermissionDirect['loaner_view'] || hasPermissionDirect['superadmin'])"
                        ui-sref-active="active">
                        <a ui-sref="app.inventory">
                            <i class="fa fa-clipboard"></i>
                            <span>{{ 'Menu.INVENTORY' | translate }}</span></a>
                        <ul>
                            <li ng-if="userService.hasInventory() && (hasPermissionDirect['inventory_view'] || hasPermissionDirect['superadmin'])"
                                ui-sref-active="active">
                                <a ui-sref="app.inventory.all">
                                    <i class="fa fa-industry"></i>
                                    {{ 'Menu.ALL_INVENTORY' | translate }}
                                </a>
                            </li>
                            <li ng-if="userService.hasInventory() && (hasPermissionDirect['inventory_transfer_view'] || hasPermissionDirect['superadmin'])"
                                ui-sref-active="active">
                                <a ui-sref="app.inventory.transfer">
                                    <i class="fa fa-exchange"></i>
                                    {{ 'Menu.INVENTORY_TRANSFERS' | translate }}
                                </a>
                            </li>
                            <li ng-if="(hasPermissionDirect['loaner_view'] || hasPermissionDirect['superadmin']) && enableLoaners"
                                ui-sref-active="active">
                                <a ui-sref="app.inventory.loaner">
                                    <i class="fa fa-wheelchair"></i>
                                    {{ 'Menu.LOANERS' | translate }}
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li ng-class="{'open':$state.includes('app.purchasing')}"
                        ng-show="hasPermissionDirect['purchase_order_view'] || hasPermissionDirect['superadmin']"
                        ui-sref-active="active">
                        <a ui-sref="app.purchasing">
                            <i class="fa fa-cart-plus"></i>
                            <span>{{ 'Menu.PURCHASING' | translate }}</span>
                        </a>
                        <ul>
                            <li ui-sref-active="active">
                                <a ui-sref="app.purchasing.external">
                                    <i class="fa fa-shopping-cart"></i>
                                    {{ 'Menu.THIRD_PARTY_RETAIL' | translate }}
                                </a>
                            </li>
                            <li ng-if="userService.getCurrentUser().empireRefreshToken != null"
                                ui-sref-active="active">
                                <a ui-sref="app.purchasing.empire_orders">
                                    <i class="fa fa-shopping-cart"></i>
                                    {{ 'Menu.EMPIRE_PURCHASE_ORDERS' | translate }}
                                </a>
                            </li>
                            <li ng-if="userService.getCurrentUser().pelToken != null"
                                ui-sref-active="active">
                                <a ui-sref="app.purchasing.pel_orders">
                                    <i class="fa fa-shopping-cart"></i>
                                    {{ 'Menu.PEL_PURCHASE_ORDERS' | translate }}
                                </a>
                            </li>

                            <li ng-if="hasPermissionDirect['purchase_order_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                <a ui-sref="app.purchasing.all">
                                    <i class="fa fa-clipboard"></i>
                                    {{ 'Menu.ALL_PURCHASE_ORDERS' | translate }}
                                </a>
                            </li>
                            <li ng-if="hasPermissionDirect['purchase_order_add'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                <a ui-sref="app.purchasing.add">
                                    <i class="fa fa-credit-card"></i>
                                    {{ 'Menu.NEW_PURCHASE_ORDER' | translate }}
                                </a>
                            </li>
                            <li ng-if="hasPermissionDirect['purchase_order_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                <a ui-sref="app.purchasing.vendors">
                                    <i class="fa fa-truck"></i>
                                    {{ 'Menu.VENDORS' | translate }}
                                </a>
                            </li>
                            <li ng-if="hasPermissionDirect['shopping_cart_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                <a ui-sref="app.purchasing.shopping_cart">
                                    <i class="fa fa-shopping-cart"></i>
                                    {{ 'Menu.SHOPPING_CART' | translate }}
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li ui-sref-active="active">
                        <a ng-click="openV2Link('crm/activities')" role="button">
                            <i class="fa fa-fax"></i>
                            <span>{{ 'Menu.CRM' | translate }}</span>
                        </a>
                    </li>
                    <li ui-sref-active="active">
                        <a ui-sref="app.reports">
                            <i class="fa fa-bar-chart"></i>
                            <span>{{ 'Menu.REPORTS' | translate }}</span>
                        </a>
                    </li>

                    <li ng-class="{'open':$state.includes('app.maintenance')}" ui-sref-active="active">
                        <a ui-sref="app.maintenance">
                            <i class="fa fa-list"></i>
                            <span>{{ 'Menu.MAINTENANCE' | translate }}</span>
                        </a>
                        <ul>
                            <li ui-sref-active="active">
                                <a><i class="fa fa-caret-right"></i>Billing</a>
                                <ul>
                                    <li ng-if="hasPermissionDirect['adjustment_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.adjustment">
                                            <i class="fa fa-sliders"></i>
                                            {{ 'Menu.ADJUSTMENTS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['claim_status_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.claimStatus">
                                            <i class="fa fa-sliders"></i>
                                            {{ 'Menu.CLAIM_STATUS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['clearing_house_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.clearing_house">
                                            <i class="fa fa-sliders"></i>
                                            {{ 'Menu.CLEARING_HOUSE' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['adjustment_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.era_codes">
                                            <i class="fa fa-sliders"></i>
                                            {{ 'Menu.ERA_CODES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['prescription_status_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.prescriptionStatus">
                                            <i class="fa fa-sliders"></i>
                                            {{ 'Menu.PRESCRIPTION_STATUS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['device_type_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.deviceType">
                                            <i class="fa fa-flask"></i>
                                            {{ 'Menu.DEVICE_TYPES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['diagnosis_code_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.diagnosis_code">
                                            <i class="fa fa-stethoscope"></i>
                                            {{ 'Menu.DIAGNOSIS_CODES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['insurance_company_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.insurance_company">
                                            <i class="fa fa-money"></i>
                                            {{ 'Menu.INSURANCE_COMPANIES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['procedure_code_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.procedure_code">
                                            <i class="fa fa-barcode"></i>
                                            {{ 'Menu.PROCEDURE_CODES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['gl_period_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.gl_period">
                                            <i class="fa fa-barcode"></i>
                                            {{ 'Menu.GL_PERIODS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['gl_account_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.gl_account">
                                            <i class="fa fa-barcode"></i>
                                            {{ 'Menu.GL_ACCOUNTS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['nymbl_pay_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.nymbl_pay">
                                            <i class="fa fa-barcode"></i>
                                            {{ 'Menu.NYMBL_PAY' | translate }}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li ui-sref-active="active">
                                <a><i class="fa fa-caret-right"></i>Branch/Company</a>
                                <ul>
                                    <li ng-if="hasPermissionDirect['delivery_location_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.delivery_location">
                                            <i class="fa fa-truck"></i>
                                            {{ 'Menu.DELIVERY_LOCATIONS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['branch_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.branch">
                                            <i class="fa fa-building"></i>
                                            {{ 'Menu.BRANCHES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['general_contact_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.general_contact">
                                            <i class="fa fa-phone"></i>
                                            {{ 'Menu.GENERAL_CONTACTS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['medical_group_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.medical_group">
                                            <i class="fa fa-users"></i>
                                            {{ 'Menu.MEDICAL_GROUP' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['group_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.group">
                                            <i class="fa fa-lock"></i>
                                            {{ 'Menu.GROUPS' | translate }}
                                        </a>
                                    </li>
                                    <!--<li ng-if="hasPermissionDirect['physician_view')" ui-sref-active="active"><a-->
                                    <!--ui-sref="app.maintenance.physician">-->
                                    <!--<i class="fa fa-user-md"></i>-->
                                    <!--{{ 'Menu.PHYSICIANS' | translate }}</a></li>-->
                                    <li ng-if="hasPermissionDirect['user_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.user">
                                            <i class="fa fa-user"></i>
                                            {{ 'Menu.USERS' | translate }}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li ui-sref-active="active">
                                <a><i class="fa fa-caret-right"></i>Purchasing</a>
                                <ul>
                                    <li ng-if="hasPermissionDirect['item_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.item">
                                            <i class="fa fa-gift"></i>
                                            {{ 'Menu.ITEMS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['vendor_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.vendor">
                                            <i class="fa fa-users"></i>
                                            {{ 'Menu.VENDORS' | translate }}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li ui-sref-active="active">
                                <a><i class="fa fa-caret-right"></i>Referral Sources</a>
                                <ul>
                                    <li ng-if="hasPermissionDirect['physician_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.physician">
                                            <i class="fa fa-user-md"></i>
                                            {{ 'Menu.PHYSICIANS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['therapist_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.therapist">
                                            <i class="fa fa-user-md"></i>
                                            {{ 'Menu.THERAPISTS' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['facility_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.facility">
                                            <i class="fa fa-building"></i>
                                            {{ 'Menu.FACILITIES' | translate }}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li ui-sref-active="active">
                                <a><i class="fa fa-caret-right"></i>Scheduling</a>
                                <ul>
                                    <li ng-if="hasPermissionDirect['appointment_types_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.appointment_types.all">
                                            <i class="fa fa-tags"></i>
                                            {{ 'Menu.APPOINTMENT_TYPES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['work_schedule_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.work_schedule">
                                            <i class="fa fa-calendar"></i>
                                            {{ 'Menu.WORK_SCHEDULES' | translate }}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li ng-if="hasPermissionDirect['system_setting_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                <a ui-sref="app.maintenance.system_setting">
                                    <i class="fa fa-cog"></i>
                                    {{ 'Menu.SYSTEM_SETTINGS' | translate }}
                                </a>
                            </li>
                            <li ui-sref-active="active">
                                <a><i class="fa fa-caret-right"></i>Templates</a>
                                <ul>
                                    <li ng-if="hasPermissionDirect['checklist_template_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.checklist_template">
                                            <i class="fa fa-clone"></i>
                                            {{ 'Menu.CHECKLIST_TEMPLATES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['common_procedure_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.common_procedure">
                                            <i class="fa fa-medkit"></i>
                                            {{ 'Menu.HCPCS_TEMPLATES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['fabrication_template_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.fabrication_template">
                                            <i class="fa fa-clone"></i>
                                            {{ 'Menu.FABRICATION_TEMPLATES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['file_type_template_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.file_type_template">
                                            <i class="fa fa-clone"></i>
                                            {{ 'Menu.FILE_TYPE_TEMPLATES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['justification_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.justification">
                                            <i class="fa fa-paragraph"></i>
                                            {{ 'Menu.JUSTIFICATION' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['print_screen_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.print_screen">
                                            <i class="fa fa-print"></i>
                                            {{ 'Menu.PRINT_SCREEN' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['template_view'] || hasPermissionDirect['superadmin']"
                                        ui-sref-active="active">
                                        <a ui-sref="app.maintenance.template">
                                            <i class="fa fa-file-text"></i>
                                            {{ 'Menu.TEMPLATES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['task_template_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.task_template">
                                            <i class="fa fa-file-text"></i>
                                            {{ 'Menu.TASK_TEMPLATES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['custom_form_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ui-sref="app.maintenance.form_template">
                                            <i class="fa fa-file-text"></i>
                                            {{ 'Menu.FORM_TEMPLATES' | translate }}
                                        </a>
                                    </li>
                                    <li ng-if="hasPermissionDirect['cms_1500_form_template_view'] || hasPermissionDirect['superadmin']" ui-sref-active="active">
                                        <a ng-click="openV2Link('maintenance/1500-form')">
                                            <i class="fa fa-file-text"></i>
                                            {{ 'Menu.FORM_1500_TEMPLATES' | translate }}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                </ul>
            </uib-accordion-group>
        </div>

        <uib-accordion-group is-open="status.isSecondOpen">
            <uib-accordion-heading>
                Support
                <i class="fa fa-angle-up pull-right" ng-show="status.isSecondOpen"></i>
                <i class="fa fa-angle-down pull-right" ng-show="!status.isSecondOpen"></i>
                <i class="fa fa-circle text-danger pull-right mr-3" ng-hide="!newPatch || status.isSecondOpen"></i>
            </uib-accordion-heading>
            <div ng-if="status.isSecondOpen">
                <ul class="nav-accordion-list" id="support" nav-collapse ripple>
                    <li ui-sref-active="active">
                        <a href>
                            <i class="fa fa-phone"></i>
                            <span>(*************</span>
                        </a>
                    </li>
                    <li>
                        <a href="https://nymblsystems.zendesk.com/hc/en-us/requests/new" target="_blank">
                            <i class="fa fa-ticket"></i>
                            <span>Create a Support Ticket</span>
                        </a>
                    </li>
                    <li>
                        <a ng-click="toggleZendeskWidget()">
                            <i class="fa fa-comment"></i>
                            <span ng-if="zendeskWidgetOpen">Hide Chat Widget</span>
                            <span ng-if="!zendeskWidgetOpen">Start a Chat with Support</span>
                        </a>
                    </li>
                    <li>
                        <a href="https://nymblsystems.zendesk.com/hc/en-us/community/posts/new" target="_blank">
                            <i class="fa fa-lightbulb-o"></i>
                            <span>Feature Suggestion</span>
                        </a>
                    </li>
                    <li>
                        <a href="https://nymblsystems.zendesk.com/hc/en-us" target="_blank">
                            <i class="fa fa-h-square"></i>
                            <span>Help Center</span>
                        </a>
                    </li>
                    <!--<li ui-sref-active="active">-->
                    <!--<a href="https://nymblsystems.zendesk.com/hc/en-us/sections/360008943894-Announcements"-->
                    <!--target="_blank">-->
                    <!--<i class="fa fa-bullhorn"></i>-->
                    <!--<span>Announcements</span>-->
                    <!--<i class="fa fa-circle" class="text-danger pull-right" ng-hide="!newPatch"></i>-->
                    <!--</a>-->
                    <!--</li>-->
                    <li ui-sref-active="active">
                        <a ui-sref="app.release-notes.all">
                            <i class="fa fa-file-text"></i>
                            <span>{{ 'Menu.RELEASE_NOTES' | translate }}</span>
                        </a>
                    </li>
                    <li ui-sref-active="active">
                        <a ui-sref="app.announcements.all">
                            <i class="fa fa-bullhorn"></i>
                            <span>{{ 'Menu.NYMBL_ANNOUNCEMENTS' | translate }}</span>
                        </a>
                    </li>
                </ul>
                <!--<iframe frameborder="0" height="150px" src="https://assist.zoho.com/login/embed-remote-support.jsp"-->
                <!--        width="100%"></iframe>-->
            </div>
        </uib-accordion-group>
    </uib-accordion>
</div>
