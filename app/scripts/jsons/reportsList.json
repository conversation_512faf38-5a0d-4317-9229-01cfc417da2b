{"balance": [{"id": "patientRefunds", "label": "Patients Owed Refund", "description": "List of patients with negative patient responsibility balances.", "mainDetail": "Total Patient Responsibility Balance, Patient details.", "isDeprecated": false, "isLegacy": false}], "sales": [{"id": "billingsByDateOfService", "label": "Billings By Date of Service", "description": "List of claims by Date of Service. Claim does not have to be submitted.", "mainDetail": "Last Applied Date, <PERSON>laim Details.", "isDeprecated": false, "isLegacy": true}, {"id": "billingsByPractitioner", "label": "Billings by <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Summation of total claim amounts by Practitioner. The date filter goes by first claim submission date.", "mainDetail": "Total Billed", "isDeprecated": false, "isLegacy": true}, {"id": "billingsByRepresentative", "label": "Billings by Representative", "description": "Summation of total claim amounts by Representative. The date filter goes by first claim submission date.", "mainDetail": "Total Billed", "isDeprecated": false, "isLegacy": true}, {"id": "billingsByReferringPhysician", "label": "Billings by Referral Source", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": true}, {"id": "wipBillingsCollected", "label": "Incomplete Prescriptions", "description": "List of incomplete prescriptions along with their estimated value amounts.", "mainDetail": "Prescription details,Estimated value", "isDeprecated": false, "isLegacy": false}, {"id": "salesDetail", "label": "Sales Detail", "description": "Detailed list of claims and outstanding balances filtered by date and device type. Claims must have a submission date and are filtered by the FIRST time the claim was sent. Manual claims to the hospital MUST HAVE a “manual submission” date.", "mainDetail": "Total Charges, Total Allowable, Outstanding Balances.<br><br>Sales Detail Export will export all sales to a csv file. Filters will be ignored.", "isDeprecated": false, "isLegacy": true}, {"id": "salesSummary", "label": "Sales Summary", "description": "Summary of claims and outstanding balances filtered by date and device type. Claims must have a  submission date and are filtered by the FIRST time the claim was sent. Manual claims to the hospital MUST HAVE a “manual submission” date. The Total Patient Outstanding and Total Insurance Outstanding are calculated from remaining balances on the claims.", "mainDetail": "Total Charges, Total Allowable, Outstanding Balances.", "isDeprecated": false, "isLegacy": true}, {"id": "salesTaxReport", "label": "Sales Tax Report", "description": "Lists all branches with total billable, allowable, and sales tax numbers for chosen date range.", "mainDetail": "Branches, Billable, Allowable, Sales Tax Percentage, Total Sales Tax", "isDeprecated": false, "isLegacy": true}, {"id": "uncollected", "label": "<PERSON><PERSON><PERSON> to Collections", "description": "List of claims that have been sent to collection. ", "mainDetail": "Claim <PERSON>, <PERSON><PERSON>, Amount <PERSON>t To Collections, Total Claim Amount", "isDeprecated": false, "isLegacy": false}, {"id": "unClaimedPrescriptions", "label": "Un-Claimed Prescriptions", "description": "List of prescriptions that have not been sent to billing.", "mainDetail": "Prescription details, Estimated value.", "isDeprecated": false, "isLegacy": false}], "payments": [{"id": "dailyClose", "label": "Daily Close Summary", "description": "Summary of payments by Payment Date or by Deposit Date with expandable list of associated payments(grey) and associated applied details from bulk payments(light blue)", "mainDetail": "Payment Type, Payment/Deposit Date, Applied and Unapplied Payment Totals.<br><br>Daily Close Export will export all payments to a csv file. Filters will be ignored.", "isDeprecated": false, "isLegacy": true}, {"id": "dailyCloseNew", "label": "Daily Close Summary **New and Improved**", "description": "Summary of payments by Payment Date or by Deposit Date with expandable list of associated payments(grey) and associated applied details from bulk payments(light blue)", "mainDetail": "Payment Type, Payment/Deposit Date, Applied and Unapplied Payment Totals.<br><br>Daily Close Export will export all payments to a csv file. Filters will be ignored.", "isDeprecated": false, "isLegacy": true}, {"id": "practitionerCommissions", "label": "Practitioner Commissions", "description": "List of practitioners with payments made toward claims in which they were the prescribing physician. Refunds are calculated by the specific adjustments selected.", "mainDetail": "Practitioners, Payments, Refunds", "isDeprecated": false, "isLegacy": true}, {"id": "representativeCommissions", "label": "Representative Commissions", "description": "List of users with payments made toward claims in which they were the assisting user. Refunds are calculated by the specific adjustments selected.", "mainDetail": "Representatives, Payments, Refunds", "isDeprecated": false, "isLegacy": true}], "superadminonly": [{"id": "sentTextMessageDetails", "label": "Appointment Confirmation Request Sent Text Message Details", "description": "List of records detailing the information about appointment confirmation request text messages that have been sent to patients. Date filters are based off of confirmation request date.  The patients list is restricted to the branch selection.", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "checkSmsComplianceUser", "label": "User Phone Number SMS Compliance Failures", "description": "List of records detailing phone number SMS compliance failures.", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "checkSmsCompliancePatient", "label": "Patient Phone Number SMS Compliance Failures", "description": "List of records detailing phone number SMS compliance failures.", "mainDetail": "", "isDeprecated": false, "isLegacy": false}], "actionable": [{"id": "surveyTextMessagesForAppointmentTypes", "label": "Survey Text Messages for Appointment Types", "description": "List of appointments with the ability to send a SMS text with survey link. Date filters are based off of Appointment Date.  The Survey Sent Date range filter is only available if the Survey Sent selection is Sent.", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "prescriptionsWithoutSurveyText", "label": "Delivered Prescriptions Without Survey Text Message", "description": "List of patients that have not been sent a patient satisfaction survey. Ability to send a SMS text with survey link. Date filters are based off of Date of Service. The delivered on column is based on the last time the proof of delivery was printed.", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "fabricationsPastDueDate", "label": "Fabrications Past Due Date", "description": "List of Prescriptions that Past Fabrication Due Date", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "overdueRental", "label": "Overdue Rental", "description": "List of all HCPCS Codes that have a Rental Status = 'OUT' and the Date Of Service End is `less than` or `equal to` today.", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "authExpiration", "label": "Prescriptions With Expiring Pre-Auth", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "unapprovedNotes", "label": "Unapproved Notes", "description": "Generates a list of clinical notes written by care extenders that have not yet been approved.", "mainDetail": "", "isDeprecated": false, "isLegacy": false}], "scheduling": [{"id": "appointmentList", "label": "List of Appointments", "description": "Limit of 10,000 Records.", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "appointmentListCanceled", "label": "Cancelled Appointments to Reschedule", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "appointmentListNoShow", "label": "No Show Appointments to Reschedule", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}], "feedback": [{"id": "surveyResponse", "label": "Survey Responses", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}], "efficiency": [{"id": "averageDaysToDeliverByDeviceType", "label": "Average Days To Deliver By Device Type", "description": "Displays list of device types with a delivery date within the chosen date range. The report shows how many prescriptions the device was used for and the minimum, maximum, and average amount of days it took to deliver the prescription.", "mainDetail": "Device Type, Number of Prescriptions, Average Days to Deliver, Minimum Days to Deliver, Maximum Days to Deliver", "isDeprecated": false, "isLegacy": false}, {"id": "clericalProductivity", "label": "Clerical Productivity", "description": "Displays the number of patients, prescriptions, claims, and notes created by a user. Notes are categorized by Note Type. The Branch filter uses Patient's Primary Branch not the User's branch.", "mainDetail": "Patients, prescriptions, claims, notes", "isDeprecated": false, "isLegacy": false}, {"id": "clerical-productivity", "label": "Clerical Productivity (New)", "description": "Displays the number of patients, prescriptions, claims, and notes created by a user. Notes are categorized by Note Type. The Branch filter uses Patient's Primary Branch not the User's branch.", "mainDetail": "Patients, prescriptions, claims, notes", "type": "v2", "isDeprecated": false, "isLegacy": false}, {"id": "new-patients", "label": "New Patients", "description": "Displays list of patients created within the date range specified.", "mainDetail": "Patient demographics and prescription information.", "type": "v2", "isDeprecated": false, "isLegacy": false}, {"id": "note-completion-timelines", "label": "Note Completion Timelines", "description": "Displays information on clinical note status for patient appointments", "mainDetail": "Appointment note information", "type": "v2", "isDeprecated": false, "isLegacy": false}, {"id": "rx-delivered-not-billed", "label": "Delivered Prescriptions Not Billed", "description": "Displays prescriptions that have been delivered but not yet billed", "mainDetail": "Prescription information", "type": "v2", "isDeprecated": false, "isLegacy": false}, {"id": "rx-timelines", "label": "Rx Timelines", "description": "Displays prescription information", "mainDetail": "Prescription information", "type": "v2", "isDeprecated": false, "isLegacy": false}, {"id": "rx-status-change-tracking", "label": "Rx Status Change Tracking", "description": "Displays prescription status information", "mainDetail": "Prescription information", "type": "v2", "isDeprecated": false, "isLegacy": false}, {"id": "claim-status-change-tracking", "label": "Claim Status Change Tracking", "description": "Displays claim status information", "mainDetail": "Claim information", "type": "v2", "isDeprecated": false, "isLegacy": false}, {"id": "task-tracking", "label": "Task Tracking", "description": "Displays detailed information about tasks including associated patients, rxs, and participants", "mainDetail": "Tasks", "type": "v2", "isDeprecated": false, "isLegacy": false}, {"id": "monthlyPhysicianVisitCount", "label": "Practitioner Monthly Visit Counts", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "patient-last-touch", "label": "Patient Last Touch", "type": "v2", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}], "misc_reports": [{"id": "lowInventoryStockList", "label": "Low Inventory Stock", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "noteList", "label": "Company Notes", "description": "Notes written by a User and categorized by Note Type in the Notes section of the Patient Profile.  NOTE:  This report is limited to 10,000 rows", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "patientBirthdayList", "label": "Patient Birthday", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "patientsWithCriticalMessageList", "label": "Patients with Critical Message", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}, {"id": "patientByReferralSource", "label": "Prescriptions with Referral Source", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}], "dashboards_by_quicksight_reports": [{"id": "dashBoardsByQuickSight", "label": "Dashboards By QuickSight", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}], "export_reports": [{"id": "exportAll", "label": "Export Table", "description": "", "mainDetail": "", "isDeprecated": false, "isLegacy": false}]}