/**
 * @ngdoc overview
 * @name minovateApp
 * @description
 * # minovateApp
 *
 * Main module of the application.
 */
// var currentRequests = [];

var app = angular
	.module('minovateApp', [
		'ngAnimate',
		'ngCookies',
		'ngResource',
		'ngSanitize',
		'ngMessages',
		'ui.bootstrap',
		'ui.router',
		'ui.utils',
		'ui.uploader',
		'angular-loading-bar',
		'angular-momentjs',
		'FBAngular',
		'lazyModel',
		'toastr',
		'angularBootstrapNavTree',
		'oc.lazyLoad',
		'ui.select',
		'ui.tree',
		'textAngular',
		'colorpicker.module',
		'angularFileUpload',
		'ui.grid',
		'ui.grid.resizeColumns',
		'ui.grid.edit',
		'ui.grid.moveColumns',
		'ngTable',
		'smart-table',
		'summernote',
		'signature',
		'angular-flot',
		'angular-rickshaw',
		'easypiechart',
		'uiGmapgoogle-maps',
		'ngTagsInput',
		'ngStomp',
		'pascalprecht.translate',
		'localytics.directives',
		'leaflet-directive',
		'wu.masonry',
		'ipsum',
		'angular-intro',
		'dragularModule',
		'ngBootstrap',
		'ngTableToCsv',
		'uiCropper',
		'angular-cache',
		'timer',
		'templateCache',
	])

	.run(['$rootScope', '$state', '$stateParams', 'UserService', 'BranchService', 'SessionService', 'PatientService', '$moment', 'MomentTimezonesFactory', '$timeout',
		function ($rootScope, $state, $stateParams, UserService, BranchService, SessionService, PatientService, moment, MomentTimezonesFactory, $timeout) {

			$rootScope.branchId = SessionService.getCurrentBranchId();
			$rootScope.branch = null;
			$rootScope.$state = $state;
			$rootScope.$stateParams = $stateParams;
			$rootScope.isMobile = false;
			try {
				var isMobile = (function (a) {
					return (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4)));
				})(navigator.userAgent || navigator.vendor || window.opera);
				if (isMobile || (window.navigator.userAgent.match('CriOS')) ||
					(/Android/i.test(window.navigator.userAgent))) {
					$rootScope.isMobile = true;
				}
			} catch (e) {
				$rootScope.isMobile = false;
			}


			//2831: use with-data.js
			//https://stackoverflow.com/questions/33258908/angular-moment-moment-timezone-has-no-data-for-america-new-york
			// MomentTimezonesFactory.query().$promise.then(function (result) {
			// 	moment.tz.add(result);
			// });

		PatientService.getPatientCookie();
		$rootScope.$on("$stateChangeStart", function (event, toState, toParams, fromState, fromParams) {

			//This check is necessary if the user refreshed the page or opened another tab.

			var userId = SessionService.getCurrentUserId();
			// no user, short circuit out of here...to the login page, batman!
			// console.log("userId: " + userId); // PDF LOGGING
			if (userId && SessionService.isAuthenticated()) {

				// console.log("if(userId) entered"); // PDF LOGGING
				UserService.populateUserFromLocalStoragePromise()
					.then(BranchService.loadUserBranchesPromise)
					.then(UserService.populateCompanyFromLocalStoragePromise)
					.then(UserService.populateBranchFromLocalStoragePromise)
					.then(function(){

						// patch for 06.01: first click on 'recent patients' does not receive the message
						UserService.hasFeatureAccess('prescription_summary');
						UserService.hasFeatureAccess('cash_report');
						UserService.hasFeatureAccess('rental_prescriptions_summary');
						UserService.hasFeatureAccess('claim_level_1500_overrides');

						UserService.authenticateFromLocalStorage(function () {
							if (toState.authRequired && !SessionService.isAuthenticated()) {
								event.preventDefault();
								$state.transitionTo("core.login");
								$("#pageloader").removeClass('animate');
							} else if (toState.name !== "core.login") {
								setTimeout(function () {
									//2781 console.log("^^^^^^^^^^^^^BROADCAST^^^^^^^^^^^^^");
									$rootScope.$broadcast('localStorageObjectLoadComplete', toState, toParams, fromState, fromParams);
								}, 500);
							}
						});
					})
					.catch(function (error) {
						if (toState.authRequired && !SessionService.isAuthenticated()) {
							event.preventDefault();
							$state.transitionTo("core.login");
							$("#pageloader").removeClass('animate');
						}
					});
			} else {
				// console.log("No userId, clear the session"); // PDF LOGGING
				SessionService.clearSession(); // always, always get rid of cookies, etc
				if (toState.authRequired && !SessionService.isAuthenticated()) {
					event.preventDefault();
					$state.transitionTo("core.login");
					$("#pageloader").removeClass('animate');
				}
			}

			//Cancel all pending requests, we are navigating away from the current page
			// angular.forEach(currentRequests, function (request) {
			//   request.resolve(); // cancel
			// });
			// currentRequests = [];
		});

		$rootScope.$on('$stateChangeSuccess', function (event, toState, toParams, fromState, fromParams) {
			// only used for the login page
			if(!toState.authRequired) {
				event.targetScope.$watch('$viewContentLoaded', function () {
					angular.element('html, body, #content').animate({scrollTop: 0}, 200);
					$timeout(function () {
						angular.element('#wrap').css('visibility', 'visible');
						if (!angular.element('.dropdown').hasClass('open')) {
							angular.element('.dropdown').find('>ul').slideUp();
						}
					}, 200);
				});
				event.preventDefault();
				$rootScope.containerClass = toState.containerClass;
			}
		});

		// Sync to the object load event
		$rootScope.$on('localStorageObjectLoadComplete', function (event, toState, toParams, fromState, fromParams) {

			event.targetScope.$watch('$viewContentLoaded', function () {
				angular.element('html, body, #content').animate({scrollTop: 0}, 200);
				$timeout(function () {
					angular.element('#wrap').css('visibility', 'visible');
					if (!angular.element('.dropdown').hasClass('open')) {
						angular.element('.dropdown').find('>ul').slideUp();
					}
					var chosenSelects = $('.ui-select').find('.chosen-select, [chosen]'),
						$select, $option;

					if (chosenSelects) {
						chosenSelects.chosen();
						// Chosen touch support.
						if ($rootScope.isMobile) {
							chosenSelects.each(function () {
								$select = $(this);
								$select.addClass('chosen-mobile');

								$select.find('option').each(function () {
									$option = $(this);

									if ( $option.val() == '' ) {
										$option
											.attr('selected', 'selected')
											.attr('disabled', 'disabled')
											.text( $select.data('placeholder') );
									}
								});
							});
						} /*else{
							window.alert("SKIPPING chosen fix");
						}*/
					}

					$rootScope.$broadcast('readyToLoadLeftNav');
				}, 500);
			});
			event.preventDefault();
			if (!!toState) {
				if (toState.name === 'app.patient.profile')
					PatientService.savePatientCookie(toParams.patientId);
				$rootScope.containerClass = toState.containerClass;
			}
		});

		// Listen for company changes in the token
		$rootScope.$on('companyChangedInToken', function(event, companyId) {
			// Fetch the company details
			CompanyFactory.get({id: companyId}).$promise.then(function(company) {
				// Update the company in the session
				SessionService.setCurrentCompany(company);
				console.log("Company updated in session to match token: " + company.name + " (ID: " + company.id + ")");
			});
		});

			document.addEventListener("wheel", function(event){
				if(document.activeElement.type === "number")
					//&&  document.activeElement.classList.contains("financial-noscroll")) // for future to filter by class (SCRUM-1694)
				{
					document.activeElement.blur();
				}
			});

		$rootScope.$on("$stateChangeError", function (event, toState, toParams, fromState, fromParams) {
			if (toState.authRequired && !SessionService.isAuthenticated()) {
				event.preventDefault();
				$state.transitionTo("core.login");
				$("#pageloader").removeClass('animate');
			} else {
				event.preventDefault();
				$state.transitionTo('app.dashboard');
				$("#pageloader").removeClass('animate');
			}
		});
	}])

	.config(['$httpProvider', function ($httpProvider) {
		$httpProvider.defaults.xsrfCookieName = 'CSRF-TOKEN';
		$httpProvider.defaults.xsrfHeaderName = 'X-CSRF-TOKEN';
		$httpProvider.interceptors.push("sessionInjector");
		$httpProvider.interceptors.push("httpInterceptor");
		$httpProvider.defaults.headers.common["X-Requested-With"] = 'XMLHttpRequest';
	}])

	.config(['$provide', function ($provide) {
		$provide.decorator('$state', ['$delegate', '$window',
			function ($delegate, $window) {
				var extended = {
					goNewTab: function (stateName, params) {
						$window.open(
							$delegate.href(stateName, params, {absolute: true}), '_blank');
					}
				};
				angular.extend($delegate, extended);
				return $delegate;
			}]);
	}])

	.config(['uiSelectConfig', function (uiSelectConfig) {
		uiSelectConfig.theme = 'bootstrap';
	}])

	//angular-language
	.config(['$translateProvider', function ($translateProvider) {
		$translateProvider.useStaticFilesLoader({
			prefix: 'languages/',
			suffix: '.json'
		});
		$translateProvider.useLocalStorage();
		$translateProvider.preferredLanguage('en');
		$translateProvider.useSanitizeValueStrategy(null);
	}])

	.config(['$compileProvider', function ($compileProvider) {
		$compileProvider.aHrefSanitizationWhitelist(/^\s*(https?|ftp|mailto|tel|sms|data):/);
	}])

	.config(['$stateProvider', '$urlRouterProvider', function ($stateProvider, $urlRouterProvider) {

		$urlRouterProvider.otherwise('/app/dashboard');
		// $urlRouterProvider.otherwise('/core/login');

		$stateProvider

			.state('core', {
				abstract: true,
				url: '/core',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('core.login', {
				url: '/login',
				templateUrl: 'views/login.html',
				controller: 'LoginCtrl',
				authRequired: false
			})
			.state('core.cancel', {
				url: '/cancel',
				templateUrl: 'views/cancel.html',
				authRequired: false
			})
			.state('core.browser_compatibility', {
				url: '/browser-compatibility',
				templateUrl: 'views/browser-compatibility.html',
				controller: 'BrowserCompatibilityCtrl',
				authRequired: false
			})
			.state('core.form', {
				abstract: true,
				url: '/forms',
				template: '<div ui-view class="text-center"></div>',
				authRequired: true
			})
			//WARNING*** DO NOT CHANGE STATE NAMES IN core.form, THEY ARE USED IN REVERSE LOOK-UPS
			.state('core.form.medical_screening_ped', {
				url: '/medical_screening_ped/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.medical_screening', {
				url: '/medical_screening/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.orthotic_delivery_cro', {
				url: '/orthotic_delivery_cro/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.orthotic_delivery_ped_le', {
				url: '/orthotic_delivery_ped_le/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.orthotic_delivery_le', {
				url: '/orthotic_delivery_le/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.orthotic_delivery_spinal', {
				url: '/orthotic_delivery_spinal/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true,
			})
			.state('core.form.orthotic_evaluation_cro', {
				url: '/orthotic_evaluation_cro/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true,
			})
			.state('core.form.orthotic_evaluation_ped_le', {
				url: '/orthotic_evaluation_ped_le/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true,
			})
			.state('core.form.orthotic_evaluation_le', {
				url: '/orthotic_evaluation_le/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true,
			})
			.state('core.form.orthotic_evaluation_spinal', {
				url: '/orthotic_evaluation_spinal/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true,
			})
			.state('core.form.orthotic_evaluation_ped_fo', {
				url: '/orthotic_evaluation_ped_fo/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true,
			})
			.state('core.form.orthotic_interim_note', {
				url: '/orthotic_interim_note/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true,
			})
			.state('core.form.orthotic_interim_note_ped', {
				url: '/orthotic_interim_note_ped/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true,
			})
			.state('core.form.orthotic_ots_custom_fit', {
				url: '/orthotic_ots_custom_fit/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true,
			})
			.state('core.form.prosthetic_delivery_le', {
				url: '/prosthetic_delivery_le/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.prosthetic_delivery_ue', {
				url: '/prosthetic_delivery_ue/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.prosthetic_evaluation_le', {
				url: '/prosthetic_evaluation_le/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.prosthetic_evaluation_ue', {
				url: '/prosthetic_evaluation_ue/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.prosthetic_interim_note_le', {
				url: '/prosthetic_interim_note_le/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.prosthetic_interim_note_ue', {
				url: '/prosthetic_interim_note_ue/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('core.form.therapeutic_shoes_evaluation', {
				url: '/therapeutic_shoes_evaluation/?patientId?prescriptionId?evaluationFormId',
				templateUrl: 'views/form/index.html',
				controller: 'EvaluationFormCtrl',
				authRequired: true
			})
			.state('app', {
				abstract: true,
				url: '/app',
				templateUrl: 'views/tmpl/app.html',
				authRequired: true
			})
			.state('app.dashboard', {
				url: '/dashboard',
				controller: 'DashboardCtrl',
				templateUrl: 'views/tmpl/dashboard.html',
				authRequired: true,
				params: {
					releaseNote: null
				},
				resolve: {
					plugins: ['$ocLazyLoad', function ($ocLazyLoad) {
						return $ocLazyLoad.load([
							'scripts/vendor/datatables/datatables.bootstrap.min.css',
							'scripts/vendor/datatables/datatables.bootstrap.min.css'
						]);
					}]
				}
			})
			.state('app.tasks', {
				url: '/tasks',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.tasks.all', {
				url: '/all',
				controller: 'TasksCtrl',
				templateUrl: 'views/tmpl/tasks/index.html',
				authRequired: true

			})
			.state('app.patient', {
				url: '/patient',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.patient.all', {
				url: '/all',
				controller: 'PatientAllCtrl',
				templateUrl: 'views/tmpl/patient/index.html',
				authRequired: true
			})
			.state('app.patient.profile', {
				url: '/profile/:patientId',
				controller: 'PatientProfileCtrl',
				templateUrl: 'views/tmpl/patient/profile.html',
				authRequired: true
			})
			.state('app.patient.profile.tab', {
				url: '?tab?prescriptionId',
				controller: 'PatientProfileCtrl',
				templateUrl: 'views/tmpl/patient/profile.html',
				authRequired: true
			})
			.state('app.patient.add', {
				url: '/add',
				controller: 'PatientAddCtrl',
				templateUrl: 'views/tmpl/patient/add.html',
				authRequired: true
			})
			.state('app.patient.summary', {
				url: '/summary',
				controller: 'PrescriptionSummaryCtrl',
				templateUrl: 'views/tmpl/patient/summary.html',
				authRequired: true
			})
			.state('app.patient.notes', {
				url: '/notes',
				controller: 'NotesSummaryCtrl',
				templateUrl: 'views/tmpl/patient/notes-summary.html',
				authRequired: true
			})
			.state('app.patient.checklist', {
				url: '/checklist',
				controller: 'ChecklistCtrl',
				templateUrl: 'views/tmpl/patient/checklist.html',
				authRequired: true
			})
			.state('app.patient.intake', {
				url: '/patient/intake',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state("app.patient.intake.queue", {
				url: '/queue',
				controller: 'PatientIntakeQueueCtrl',
				templateUrl: 'views/tmpl/patient/intake/queue.html',
				authRequired: true
			})
			.state("app.patient.intake.create", {
				url: '/intake/create',
				controller: 'PatientIntakeCreateCtrl',
				templateUrl: 'views/tmpl/patient/intake/create.html',
				authRequired: true
			})
			.state("app.patient.swo-batch-sign", {
				url: '/swo',
				controller: 'SWOBatchSignCtrl',
				templateUrl: 'views/tmpl/patient/swo_sign.html',
				authRequired: true
			})
			.state('app.scheduling', {
				url: '/scheduling',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.appointments', {
				url: '/scheduling/appointments',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.appointments.all', {
				url: '/all',
				controller: 'AppointmentsCtrl',
				templateUrl: 'views/tmpl/scheduling/appointments/index.html',
				authRequired: true
			})
			.state('app.appointments.add', {
				url: '/add',
				controller: 'AppointmentCtrl',
				templateUrl: 'views/tmpl/scheduling/appointments/add.html',
				authRequired: true,
				resolve: {
					isModal: function () {
						return false;
					},
					$uibModalInstance: function () {
						return null;
					},
					apptDetails: function () {
						var apptDetails = {
							apptId: false,
							profilePatientId: false,
							startTime: false,
							userId: false
						};
						return apptDetails;
					}
				}
			})
			.state('app.appointments.edit', {
				url: '/:appointmentId/edit',
				controller: 'AppointmentCtrl',
				templateUrl: 'views/tmpl/scheduling/appointments/edit.html',
				authRequired: true,
				resolve: {
					isModal: function () {
						return false;
					},
					$uibModalInstance: function () {
						return null;
					},
					apptDetails: function () {
						var apptDetails = {
							apptId: false,
							profilePatientId: false,
							startTime: false,
							userId: false
						};
						return apptDetails;
					}
				}
			})
			.state('app.calendar', {
				url: '/scheduling/calendar',
				controller: 'CalendarCtrl',
				templateUrl: 'views/tmpl/scheduling/calendar.html',
				authRequired: true
			})
			.state('app.crm', {
				url: '/crm',
				controller: 'CrmCtrl',
				templateUrl: 'views/tmpl/crm/index.html',
				authRequired: true
			})
			.state('app.billing', {
				url: '/billing',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.billing.claims', {
				url: '/claims',
				controller: 'BillingClaimsCtrl',
				templateUrl: 'views/tmpl/billing/claims.html',
				authRequired: true
			})
			.state('app.billing.statements', {
				url: '/view/statements',
				controller: 'PatientStatementCtrl',
				templateUrl: 'views/tmpl/billing/patient_statements.html'
			})
			.state('app.billing.claim', {
				url: '/view/:claimId',
				controller: 'ClaimCtrl',
				templateUrl: 'views/tmpl/claim/claim_view_index.html',
				authRequired: true
			})
			.state('app.billing.era_payments', {
				url: '/era_payments',
				controller: 'BillingEraPaymentsCtrl',
				templateUrl: 'views/tmpl/billing/era_payments.html',
				authRequired: true
			})
			.state('app.billing.nymbl_payments', {
				url: '/nymbl_payments',
				controller: 'BillingNymblPaymentsCtrl',
				templateUrl: 'views/tmpl/billing/nymbl_payments.html',
				authRequired: true
			})
			.state('app.billing.posted_payments', {
				url: '/posted_payments?:paymentId',
				controller: 'BillingPostedPaymentsCtrl',
				templateUrl: 'views/tmpl/billing/posted_payments.html',
				authRequired: true
			})
			.state('app.billing.success', {
				url: '/success/:stripeIdentifier/:source',
				controller: 'BillingSuccessCtrl',
				templateUrl: 'views/tmpl/billing/success.html',
				authRequired: true
			})
			.state('app.questionnaire', {
				url: '/questionnaire',
				template: '<div ui-view></div>'
			})
			.state('app.questionnaire.forms', {
				url: '/forms',
				controller: 'NymblFormsCtrl',
				templateUrl: 'views/tmpl/nymbl_questionnaire/nymbl_forms/forms_index.html',
				authRequired: true
			})
			.state('app.questionnaire.form', {
				url: '/details/:formId',
				controller: 'NymblFormDetailCtrl',
				templateUrl: 'views/tmpl/nymbl_questionnaire/nymbl_forms/form_details.html',
				authRequired: true
			})
			.state('app.questionnaire.questions', {
				url: '/questions',
				controller: 'NymblQuestionsCtrl',
				templateUrl: 'views/tmpl/nymbl_questionnaire/nymbl_questions/questions_index.html',
				authRequired: true
			})
			.state('app.purchasing', {
				url: '/purchasing',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.purchasing.all', {
				url: '/all',
				controller: 'PurchasingCtrl',
				templateUrl: 'views/tmpl/purchasing/index.html',
				authRequired: true
			})
			.state('app.purchasing.external', {
				url: '/third-party',
				controller: 'PurchasingCtrl',
				templateUrl: 'views/tmpl/purchasing/third_party/index.html',
				authRequired: true
			})
			.state('app.purchasing.external.receive_code', {
				url: '/receive-code?:code',
				controller: 'PurchasingCtrl',
				templateUrl: 'views/tmpl/purchasing/third_party/index.html',
				authRequired: true
			})
			.state('app.purchasing.empire_orders', {
				url: '/empire-orders?:order_id',
				controller: 'EmpirePurchasingCtrl',
				templateUrl: 'views/tmpl/purchasing/empire_orders/index.html',
				authRequired: true
			})
			.state('app.purchasing.pel_orders', {
				url: '/pel-orders?:order_id',
				controller: 'PelPurchasingCtrl',
				templateUrl: 'views/tmpl/purchasing/pel_orders/index.html',
				authRequired: true
			})
			.state('app.purchasing.add', {
				url: '/add',
				controller: 'PurchasingOrderCtrl',
				templateUrl: 'views/tmpl/purchasing/add.html',
				authRequired: true,
				resolve: {
					purchaseOrderDto: function () {
						return null;
					},
					isModal: function () {
						return false;
					}
				}
			})
			.state('app.purchasing.addEmpire', {
				url: '/add/:referenceNumber/:orderedAt/:expectedAt/:orderStatus/:price/:quantity/:customer_note/:description/:part_number/:vendor/:is_for_stock/:shipping_address/:job_number',
				controller: 'PurchasingOrderCtrl',
				templateUrl: 'views/tmpl/purchasing/add.html',
				authRequired: true,
				resolve: {
					purchaseOrderDto: function () {
						return null;
					},
					isModal: function () {
						return false;
					}
				}
			})
			.state('app.purchasing.order', {
				url: '/order/:purchaseOrderId',
				controller: 'PurchasingOrderCtrl',
				templateUrl: 'views/tmpl/purchasing/order.html',
				authRequired: true,
				resolve: {
					purchaseOrderDto: function () {
						return null;
					},
					isModal: function () {
						return false;
					},
				}
			})
			.state('app.purchasing.vendors', {
				url: '/vendors',
				controller: 'PurchasingVendorsCtrl',
				templateUrl: 'views/tmpl/purchasing/vendors_list.html',
				authRequired: true
			})
			.state('app.purchasing.shopping_cart', {
				url: '/shopping-cart',
				controller: 'PurchasingShoppingCartCtrl',
				templateUrl: 'views/tmpl/purchasing/shopping_cart.html',
				authRequired: true
			})
			.state('app.reports', {
				url: '/reports',
				controller: 'ReportsCtrl',
				templateUrl: 'views/tmpl/reports/reports.html',
				authRequired: true
			})
			.state('app.maintenance', {
				url: '/maintenance',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.maintenance.user', {
				url: '/user',
				controller: 'UserCtrl',
				templateUrl: 'views/tmpl/maintenance/user.html',
				authRequired: true
			})
			.state('app.maintenance.group', {
				url: '/group',
				controller: 'GroupCtrl',
				templateUrl: 'views/tmpl/maintenance/group.html',
				authRequired: true
			})
			.state('app.maintenance.work_schedule', {
				url: '/work_schedule',
				controller: 'WorkScheduleCtrl',
				templateUrl: 'views/tmpl/maintenance/work_schedule.html',
				authRequired: true
			})
			.state('app.maintenance.user.user_profile', {
				url: '/profile',
				controller: 'UserProfileCtrl',
				templateUrl: 'views/tmpl/maintenance/user_profile.html',
				authRequired: true
			})
			.state('app.maintenance.branch', {
				url: '/branch',
				controller: 'BranchCtrl',
				templateUrl: 'views/tmpl/maintenance/branch.html',
				authRequired: true
			})
			.state('app.maintenance.physician', {
				url: '/physician',
				controller: 'PhysicianCtrl',
				templateUrl: 'views/tmpl/maintenance/physician.html',
				authRequired: true
			})
			.state('app.maintenance.general_contact', {
				url: '/general_contact',
				controller: 'GeneralContactCtrl',
				templateUrl: 'views/tmpl/maintenance/general_contact.html',
				authRequired: true
			})
			.state('app.maintenance.insurance_company', {
				url: '/insurance_company',
				controller: 'InsuranceCompanyCtrl',
				templateUrl: 'views/tmpl/maintenance/insurance_company.html',
				authRequired: true
			})
			.state('app.maintenance.delivery_location', {
				url: '/delivery_location',
        controller: 'DeliveryLocationCtrl',
        templateUrl: 'views/tmpl/maintenance/delivery_location.html',
        authRequired: true
      })
      .state('app.maintenance.fabrication_template', {
        url: '/fabrication_template',
        controller: 'FabricationTemplateCtrl',
        templateUrl: 'views/tmpl/maintenance/fabrication_template.html',
        authRequired: true
      })
      .state('app.maintenance.file_type_template', {
        url: '/file_type_template',
        controller: 'FileTypeTemplateCtrl',
        templateUrl: 'views/tmpl/maintenance/file_type_template.html',
        authRequired: true
      })
      .state('app.maintenance.checklist_template', {
        url: '/checklist_template',
        controller: 'ChecklistTemplateCtrl',
        templateUrl: 'views/tmpl/maintenance/checklist_template.html',
        authRequired: true
      })
			.state('app.maintenance.task_template', {
				url: '/task_template',
				controller: 'TaskTemplateCtrl',
				templateUrl: 'views/tmpl/maintenance/task_template.html',
				authRequired: true
			})
			.state('app.maintenance.diagnosis_code', {
				url: '/diagnosis_code',
				controller: 'DiagnosisCodeCtrl',
				templateUrl: 'views/tmpl/maintenance/diagnosis_code.html',
				authRequired: true
			})
			.state('app.maintenance.procedure_code', {
				url: '/procedure_code',
				controller: 'ProcedureCodeCtrl',
				templateUrl: 'views/tmpl/maintenance/procedure_code.html',
				authRequired: true
			})
			.state('app.maintenance.common_procedure', {
				url: '/HCPC_template',
				controller: 'CommonProcedureCtrl',
				templateUrl: 'views/tmpl/maintenance/common_procedure.html',
				authRequired: true
			})
			.state('app.maintenance.justification', {
				url: '/justification',
				controller: 'JustificationCtrl',
				templateUrl: 'views/tmpl/maintenance/justification.html',
				authRequired: true
			})
			.state('app.maintenance.template', {
				url: '/template',
				controller: 'TemplateCtrl',
				templateUrl: 'views/tmpl/maintenance/template.html',
				authRequired: true
			})
			.state('app.maintenance.form_template', {
				url: '/form_template',
				controller: 'FormTemplateCtrl',
				templateUrl: 'views/tmpl/maintenance/form_template.html',
				authRequired: true
			})
			.state('app.maintenance.print_screen', {
				url: '/print_screen',
				controller: 'PrintScreenCtrl',
				templateUrl: 'views/tmpl/maintenance/print_screen.html',
				authRequired: true
			})
			.state('app.maintenance.adjustment', {
				url: '/adjustment',
				controller: 'AdjustmentCtrl',
				templateUrl: 'views/tmpl/maintenance/adjustment.html',
				authRequired: true
			})
			.state('app.maintenance.era_codes', {
				url: '/era_reason_codes',
				controller: 'EraReasonCodeCtrl',
				templateUrl: 'views/tmpl/maintenance/era_reason_codes.html',
				authRequired: true
			})
			.state('app.maintenance.clearing_house', {
				url: '/clearing_house',
				controller: 'ClearingHouseCtrl',
				templateUrl: 'views/tmpl/maintenance/clearing_house.html',
				authRequired: true
			})
			.state('app.maintenance.appointment_types', {
				url: '/appointment_types',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.maintenance.appointment_types.all', {
				url: '/all',
				controller: 'AppointmentTypesCtrl',
				templateUrl: 'views/tmpl/maintenance/appointment_types/index.html',
				authRequired: true
			})
			.state('app.maintenance.appointment_types.add', {
				url: '/add',
				controller: 'AppointmentTypesAddCtrl',
				templateUrl: 'views/tmpl/maintenance/appointment_types/add.html',
				authRequired: true
			})
			.state('app.maintenance.appointment_types.edit', {
				url: '/:id/edit',
				controller: 'AppointmentTypesCtrl',
				templateUrl: 'views/tmpl/maintenance/appointment_types/edit.html',
				authRequired: true
			})
			.state('app.maintenance.system_setting', {
				url: '/system_setting',
				controller: 'SystemSettingCtrl',
				templateUrl: 'views/tmpl/maintenance/system_setting.html',
				authRequired: true
			})
			.state('app.maintenance.medical_group', {
				url: '/medical_group',
				controller: 'InsuranceGroupCtrl',
				templateUrl: 'views/tmpl/maintenance/insurance_group.html',
				authRequired: true
			})
			.state('app.maintenance.vendor', {
				url: '/vendor',
				controller: 'VendorCtrl',
				templateUrl: 'views/tmpl/maintenance/vendor.html',
				authRequired: true,
				resolve: {
					isModal: function () {
						return false;
					},
					$uibModalInstance: function () {
						return null;
					}
				}
			})
			.state('app.maintenance.item', {
				url: '/item',
				controller: 'ItemCtrl',
				templateUrl: 'views/tmpl/maintenance/item.html',
				authRequired: true,
				resolve: {
					isModal: function () {
						return false;
					},
					oneTimePurchase: function () {
						return false;
					},
					prescriptionPurchaseOrdersDto: function () {
						return null;
					},
					$uibModalInstance: function () {
						return null;
					}
				}
			})
			.state('app.maintenance.category', {
				url: '/category',
				controller: 'CategoryCtrl',
				templateUrl: 'views/tmpl/maintenance/category.html',
				authRequired: true
			})
			.state('app.maintenance.deviceType', {
				url: '/device-type',
				controller: 'DeviceTypeCtrl',
				templateUrl: 'views/tmpl/maintenance/device_type.html',
				authRequired: true
			})
			.state('app.maintenance.claimStatus', {
				url: '/claim-status',
				controller: 'ClaimStatusCtrl',
				templateUrl: 'views/tmpl/maintenance/claim_status.html',
				authRequired: true
			})
			.state('app.maintenance.prescriptionStatus', {
				url: '/prescription-status',
				controller: 'PrescriptionStatusCtrl',
				templateUrl: 'views/tmpl/maintenance/prescription_status.html',
				authRequired: true
			})
			.state('app.maintenance.therapist', {
				url: '/therapist',
        controller: 'TherapistCtrl',
        templateUrl: 'views/tmpl/maintenance/therapist.html',
        authRequired: true
      })
      .state('app.maintenance.facility', {
        url: '/facility',
        controller: 'FacilityCtrl',
        templateUrl: 'views/tmpl/maintenance/facility.html',
        authRequired: true
      })
      .state('app.maintenance.gl_period', {
        url: '/gl-period',
        controller: 'GL_PeriodCtrl',
        templateUrl: 'views/tmpl/maintenance/gl_period.html',
        authRequired: true
      })
      .state('app.maintenance.gl_account', {
        url: '/gl-account',
	      controller: 'GL_AccountCtrl',
	      templateUrl: 'views/tmpl/maintenance/gl_account.html',
	      authRequired: true
      })
			.state('app.maintenance.nymbl_pay', {
				url: '/nymbl_pay',
				controller: 'NymblPayCtrl',
				templateUrl: 'views/tmpl/maintenance/nymbl_pay_setting.html',
				authRequired: true
			})
			// .state('app.maintenance.loaner', {
			// 	url: '/loaners',
			// 	controller: 'LoanerCtrl',
			// 	templateUrl: 'views/tmpl/maintenance/loaner.html',
			// 	authRequired: true
			// })
			.state('app.user', {
				url: '/user/profile',
				controller: 'ProfileCtrl',
				templateUrl: 'views/tmpl/user/profile.html',
				authRequired: true
			})
			.state('app.tutorials', {
				url: '/tutorials',
				template: '<div ui-view></div>',
				authRequired: true

			})
			.state('app.tutorials.demos', {
				url: '/demos',
				controller: 'DemosCtrl',
				templateUrl: 'views/tmpl/tutorials/demos.html',
				authRequired: true
			})
			.state('app.tutorials.training', {
				url: '/training',
				controller: 'TrainingCtrl',
				templateUrl: 'views/tmpl/tutorials/training.html',
				authRequired: true
			})
			.state('app.notifications', {
				url: '/view_all_notifications',
				controller: 'NotificationsCtrl',
				templateUrl: 'views/tmpl/notifications/view_all.html',
				authRequired: true
			})
			.state('app.inventory', {
				url: '/inventory',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.inventory.all', {
				url: '/all',
				controller: 'InventoryCtrl',
				templateUrl: 'views/tmpl/inventory/index.html',
				authRequired: true
			})
			.state('app.inventory.transfer', {
				url: '/transfers',
				controller: 'InventoryTransferCtrl',
				templateUrl: 'views/tmpl/inventory/transfer/index.html',
				authRequired: true
			})
			.state('app.inventory.qr', {
				url: '/inventory/qr-scanner',
				controller: 'QRCtrl',
				templateUrl: 'views/tmpl/inventory/qr-scanner.html',
				authRequired: true
			})
			.state('app.inventory.loaner', {
				url: '/loaners',
				controller: 'InventoryLoanerCtrl',
				templateUrl: 'views/tmpl/inventory/loaner/index.html',
				authRequired: true
			})
			.state('app.release-notes', {
				url: '/release-notes',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.release-notes.all', {
				url: '/release-notes/all',
				controller: 'ReleaseNotesCtrl',
				templateUrl: 'views/tmpl/support/release_notes/index.html',
				authRequired: true
			})
			.state('app.announcements', {
				url: '/announcements',
				template: '<div ui-view></div>',
				authRequired: true
			})
			.state('app.announcements.all', {
				url: '/announcements/all',
				controller: 'AnnouncementsCtrl',
				templateUrl: 'views/tmpl/support/announcements/index.html',
				authRequired: true
			});

	}]);
