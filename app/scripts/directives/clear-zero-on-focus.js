app.directive("clearZeroOnFocus", clearZeroOnFocus);

function clearZeroOnFocus($timeout) {
    return {
      restrict: 'A',
      require: 'ngModel',
      link: function(scope, element, attrs, ngModelCtrl) {
        element.on('focus', function() {
          scope.$apply(function() {
            if (ngModelCtrl.$modelValue === 0) {
              ngModelCtrl.$setViewValue('');
              ngModelCtrl.$render();
            }
          });
        });

        element.on('blur', function() {
            scope.$apply(function() {
            $timeout(function() {
                const value = ngModelCtrl.$modelValue;
                if (value === '' || value === null || angular.isUndefined(value)) {
                    ngModelCtrl.$setViewValue(0);
                    ngModelCtrl.$render();
                }
            }, 100);
          });
        });
      }
    };
  }
