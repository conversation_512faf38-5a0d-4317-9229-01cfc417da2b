app.service('PrescriptionService', PrescriptionService);
PrescriptionService.$inject = [
	'$rootScope', '$moment', '$uibModal', '$state', '$stateParams', '$http', '$q', '$filter', '$timeout', 'PrescriptionFactory',
	'PrescriptionSectionFactory', 'PrescriptionFileFactory',
	'PrescriptionDiagnosisCodeFactory', 'PrescriptionLCodeFactory', 'DeliveryLocationFactory', 'TemplateFactory',
	'DetailedWrittenOrderFactory', 'InsuranceVerificationFactory', 'PatientInsuranceFactory', 'PurchaseOrderItemFactory',
	'LCodeFactory', 'InsuranceLCodeAlertFactory', 'UserService', 'PatientService', 'TabService', 'UtilService',
	'InsuranceService', 'FinancialResponsibilityService', 'LCodeNodeFactory', 'SectionsFactory', 'EvaluationFormFactory',
	'UserFactory', 'ClaimService', 'NotificationService', 'ClaimFactory', 'EmpirePurchaseOrderFactory', 'SystemSettingFactory',
	'AppointmentService', 'UserNotificationService', 'OutcomeMeasureService', 'ChecklistService', 'PurchasingService',
	'SessionService', 'DateService', 'DiffModalService',
	'DeviceTypeFileTypeFactory', 'ClaimFileFactory', 'NymblStatusFactory', 'PhoneNumberFactory', 'AdvanceBeneficiaryNoticeFactory',
	'CustomFormService', 'ShoppingCartService', 'GL_PeriodFactory', 'ItemFactory', 'DiagnosisCodeLCodeAlertFactory',
	'AppointmentFactory', 'toastr'];

function PrescriptionService(
	$rootScope, $moment, $uibModal, $state, $stateParams, $http, $q, $filter, $timeout, PrescriptionFactory,
	PrescriptionSectionFactory, PrescriptionFileFactory,
	PrescriptionDiagnosisCodeFactory, PrescriptionLCodeFactory, DeliveryLocationFactory, TemplateFactory,
	DetailedWrittenOrderFactory, InsuranceVerificationFactory, PatientInsuranceFactory, PurchaseOrderItemFactory,
	LCodeFactory, InsuranceLCodeAlertFactory, UserService, PatientService, TabService, UtilService,
	InsuranceService, FinancialResponsibilityService, LCodeNodeFactory, SectionsFactory, EvaluationFormFactory,
	UserFactory, ClaimService, NotificationService, ClaimFactory, EmpirePurchaseOrderFactory, SystemSettingFactory,
	AppointmentService, UserNotificationService, OutcomeMeasureService, ChecklistService, PurchasingService,
	SessionService, DateService, DiffModalService,
	DeviceTypeFileTypeFactory, ClaimFileFactory, NymblStatusFactory, PhoneNumberFactory, AdvanceBeneficiaryNoticeFactory,
	CustomFormService, ShoppingCartService, GL_PeriodFactory, ItemFactory, DiagnosisCodeLCodeAlertFactory,
	AppointmentFactory, toastr) {

	var _this = this;

	this.init = function () {
		_this.loading = false;
		_this.prescriptionSectionsLoading = false;
		_this.prescriptionDiagnosisCodeLoading = false;
		_this.prescriptionLCodesLoading = false;
		_this.detailedWrittenOrderLoading = false;
		_this.detailedWrittenOrderFaceToFaceDateWarning = false;
		_this.proofOfDeliveryLoading = false;
		_this.lCodeAlertsLoading = false;
		_this.prescriptions = null;
		_this.prescription = undefined;
		_this.prescriptionSections = [];
		_this.prescriptionDiagnosisCodes = [];
		_this.prescriptionLCodes = [];
		_this.financialResponsibility = {};
		_this.primaryInsuranceContribution = 0;
		_this.detailedWrittenOrder = undefined;
		_this.percentCompleted = [];
		_this.uncompletedSections = [];
		_this.evaluationForms = [];
		_this.latestPrescription = {};
		_this.patientPrimaryInsurance = {};
		_this.medicalGroup = null;
		_this.diagnosis = [];
		_this.diagnosisCodes = [];
		_this.codeset = [];
		_this.lCodeAlerts = [];
		_this.lCodeAlertsTrackingArray = [];
		_this.diagnosisCodeLCodeAlerts = [];
		_this.prescriptionLCodeAlerts = [];
		_this.prescriptionLCodeAlertsTrackingArray = [];
		_this.noteTemplateId = "";
		_this.plcodes = [];
		_this.sections = SectionsFactory.query();
		_this.allUsers = [];
		_this.hideLcodeFee = false;
		_this.vaHospitalFormat = false;
		_this.showPodModifiers = true;
		_this.hideMods = false;
		_this.isPrimaryCarePhysician = false;
		_this.isReferringPhysician = true;
		_this.toggleShowOnDelivery = false;
		_this.appointmentServiceLoaded = false;
		_this.userNotificationServiceLoaded = false;
		_this.skipClaimSubmissionForRental = false;
		_this.showPrescriptionDetailsFiles = false;
		_this.showPrescriptionStandardWrittenOrderFiles = false;
		_this.initialEvaluationAppointment = null;
		_this.shouldReviewHcpcAddition = false;
		_this.reviewHcpcAdditionChange = false;

		_this.checklistServiceLoaded = false;
		_this.outcomeMeasureServiceLoaded = false;
		_this.forbinServiceLoaded = false;
		_this.patientMedicalHistoryLoaded = false;
		_this.evalFormsLoaded = false;
		_this.patientContactsLoaded = false;
		_this.patientFilesLoaded = false;
		_this.patientAlertsLoaded = false;
		_this.purchaseOrdersLoaded = false;
		_this.hasClaimFileWIP = false;
		_this.claimSubmissions = 'undefined';
		_this.claimIds = [];
		_this.prescriptionStatuses = [];
		_this.physicianDocumentationStatuses = [];
		_this.prescriptionCogByLCode = new Map();
		_this.prescriptionDetailsLoading = false;
		_this.prescriptionTotalCost = 0;
		_this.prescriptionTotalMap = new Map();
		_this.cogByRxIdAndLCode = new Map();
		_this.cogFromPoiMap = new Map();
		_this.cogTotal = 0;
		_this.surveyWrapper = null;
		_this.serviceEstimateHideAllowable = false;
		_this.prescriptionAbnDTOs = [];
		_this.prescriptionDateInLockedBillingCycle = false;
		_this.lockPrescriptionBranch = false;
		_this.lastSurgeryDate = null;
		_this.insuranceService = InsuranceService;
		SystemSettingFactory.findBySectionAndField({
			section: "general",
			field: "show_detailed_justifications"
		}).$promise.then(function (response) {
			_this.showDetailedJustifications = response.value === 'Y';
		});
		SystemSettingFactory.findBySectionAndField({
			section: "purchasing",
			field: "hcpcs_item_link"
		}).$promise.then(function (response) {
			_this.hcpcsItemLink = response.value === '1';
		});
		SystemSettingFactory.findBySectionAndField({
			section: "general",
			field: "hcpc_change_review"
		}).$promise.then(function (response) {
			_this.hcpcsSelectionAuditEnabled = response.value === 'Y';
		});
		SystemSettingFactory.findBySectionAndField({
			section: "general",
			field: "show_llpr_fields"
		}).$promise.then(function (response) {
			if(response.value) {
				_this.showLLPRFields = response.value;
			}
		});
	};

	// this is the CRT flag, FYI...
	SystemSettingFactory.findBySectionAndField({
		section: "general",
		field: "hide_device_type"
	}).$promise.then(function (response) {
		_this.CRT_hideDeviceType = response.value === 'Y';
	});

	this.numberOfInactivePrescriptions = 0;
	this.numberOfArchivedPrescriptions = 0;
	this.hasActivePrescriptions = false;
	this.showInactivePrescriptions = false;
	this.showArchivedItems = false;
	this.promiseList = [];
	this.promiseRunning = false;
	this.voidStatus = ['canceled', 'denied', 'purchase_order_error', 'returned'];
	var executeSequentially = function () {
		var tasks = _this.promiseList;
		_this.promiseRunning = true;
		if (tasks && tasks.length > 0) {
			var task = tasks.shift();
			return task().then(function (output) {
				return executeSequentially(tasks).then(function (outputs) {
					outputs.push(output);

					_this.promiseRunning = false;
					return Promise.resolve(outputs);
				});
			}).catch(function (error) {
				console.log("PrescriptionService Profile Load Issue");
				console.log(error);
			});
		}

		return Promise.resolve([]);
	};

	this.profileLoad = function (patientId) {
		if (patientId === null || patientId === undefined) {
			console.log('Patient Id is null or undefined, possible race condition');
			return;
		}
		_this.init();

		var newPromise = function () {
			return profileLoadPromise(patientId);
		};

		_this.promiseList.push(newPromise);

		if (!_this.promiseRunning) {

			executeSequentially().then(function (response) {
				_this.promiseRunning = false;
				// re-fetch because the fetch is profileLoadPromise isn't sequential and UserService may not yet be done
				_this.currentUser = UserService.getCurrentUser();
				_this.currentBranch = SessionService.getCurrentBranch();
			}).catch(function (error) {
				_this.promiseRunning = false;
			});
		}

	};

	function profileLoadPromise(patientId) {

		_this.loading = true;
		_this.hasActivePrescriptions = false;
		_this.currentUser = UserService.getCurrentUser();
		_this.currentBranch = SessionService.getCurrentBranch();
		//$stateParams.patientId = patientId;

		$rootScope.$broadcast('loadPatientPayments', {patientId: patientId});
		// PERF: Move this and all similar to USERSERVICE AND LET IT KEEP A LIST OF USERS...AND THEN DTO USERS
		UserFactory.getActiveUsersForCompanyByCompanyIdNoSuperAdmin({companyId: UserService.getCompanyId()}).$promise.then(function (users) {
			angular.forEach(users, function (user) {
				_this.allUsers.push(user);
			});
		});

		if(_this.showArchivedItems) {
			_this.archiveQueryParam = "?getArchived=true";
		} else {
			_this.archiveQueryParam = "";
		}

		var prescriptionAPI = 'api/prescription/patient/active-true/';
		if (_this.showInactivePrescriptions) {
			prescriptionAPI = 'api/prescription/patient/';
		}

		return $http.get(prescriptionAPI + patientId + _this.archiveQueryParam).then(function (prescriptionData) {
			// PrescriptionFactory.findByPatientId({patientId: patientId}).$promise.then(function (prescriptions) {
			var prescriptions = prescriptionData.data;
			if (prescriptions.length === 0) {
				_this.loading = false;
				_this.prescriptions = [];
				$http.get('api/prescription/inactive-prescription-count/' + patientId).then(function (archivedCount) {
					_this.numberOfInactivePrescriptions = archivedCount.data;
				});
				$http.get('api/prescription/archived-prescription-count/' + patientId).then(function (archivedCount) {
					_this.numberOfArchivedPrescriptions = archivedCount.data;
				});
				return $q.when([]);
			}
			PrescriptionFactory.getLatestPrescription({patientId: patientId}).$promise.then(function (latestPrescription) {
				if (latestPrescription) {
					_this.latestPrescription = latestPrescription;
					var insurances = InsuranceService.patientInsurances;
					if (InsuranceService.loading) {
						console.log("need async control 3165");
					}

					if (insurances && insurances.length === 1) {
						_this.patientPrimaryInsurance = insurances[0];
						if (_this.latestPrescription.patientInsuranceId === undefined && _this.latestPrescription.patientId !== undefined && _this.latestPrescription.patientId !== null && _this.latestPrescription.active) {
							_this.latestPrescription.patientInsuranceId = insurances[0].id;

							PrescriptionFactory.save(_this.latestPrescription).$promise.then(function (response) {
								_this.latestPrescription = response;
							});
						}
					}
					angular.forEach(insurances, function (insurance, index) {
						if (_this.latestPrescription.patientInsurance && insurance.insuranceCompanyId === _this.latestPrescription.patientInsurance.insuranceCompany.id) {
							_this.medicalGroup = insurance.insuranceCompany.insuranceGroup;
						}
					});
				}
			});

			angular.forEach(prescriptions, function (prescription, index) {
				_this.loadSectionInfo(prescription.id);
				AdvanceBeneficiaryNoticeFactory.findByPrescriptionId({prescriptionId: prescriptions[index].id}).$promise.then(function (response) {
					_this.prescriptionAbnDTOs = response;
				});
			});

			var lCodesPromiseList = [];
			var claimsPromiseList = [];

			function getPrescriptionClaims(prescriptionId) {
				return $http.get('api/claim/prescription/' + prescriptionId).then(function (response) {
					return response.data;
				}).catch(function (error) {
					return $q.when([]);
				});
			}

			function getPrescriptionLCodes(prescriptionId) {
				return $http.get('api/prescription-l-code/by-prescription?prescriptionId=' + prescriptionId).then(function (response) {
					return response.data;
				}).catch(function (error) {
					return $q.when([]);
				});
			}

			angular.forEach(prescriptions, function (prescription, index) {
				// lCodesPromiseList.push(PrescriptionLCodeFactory.findByPrescriptionId({prescriptionId: prescription.id}).$promise);
				lCodesPromiseList.push(getPrescriptionLCodes(prescription.id));
				claimsPromiseList.push(getPrescriptionClaims(prescription.id));
				if (prescription.surgeryDate != null && ($moment(prescription.surgeryDate) > $moment(_this.lastSurgeryDate) || _this.lastSurgeryDate == null)) {
					_this.lastSurgeryDate = prescription.surgeryDate;
				}
				if (prescription.active) {
					_this.hasActivePrescriptions = true;
				}
			});

			return $q.all(lCodesPromiseList)
				.then(function (results) {
					angular.forEach(results, function (lCode, index) {
						if (_this.plcodes[lCode.prescriptionId] === undefined) {
							_this.plcodes[lCode.prescriptionId] = [];
						}
						_this.plcodes[lCode.prescriptionId] = lCode;
						angular.forEach(lCode, function (lc) {
							if ('K0462' === lc.lCode.name) {
								prescriptions[index].$isLoanerBilled = true;
							}
						});
					});
					return $q.all(claimsPromiseList)
						.then(function (results) {

							angular.forEach(results, function (prescriptionClaim) {
								angular.forEach(prescriptionClaim, function (claim) {
									var index = _.findIndex(prescriptions, ['id', claim.prescriptionId]);
									prescriptions[index].claimId = claim.id;
									prescriptions[index].dos = claim.dateOfService;
									prescriptions[index].zeroBalance = false;
									prescriptions[index].patientRefund = false;
									prescriptions[index].negativeInsBalance = false;
									if (claim.totalPtResponsibilityBalance == 0.00 && claim.totalClaimBalance == 0.00) {
										prescriptions[index].zeroBalance = true;
									} else if (claim.totalPtResponsibilityBalance < 0 && claim.totalClaimBalance == 0.00) {
										prescriptions[index].patientRefund = true;
									} else if (claim.totalClaimBalance < 0) {
										prescriptions[index].negativeInsBalance = true;
									}
								});
							});
							_this.loading = false;
							_this.prescriptions = prescriptions;

							$http.get('api/prescription/inactive-prescription-count/' + patientId).then(function (inactiveCount) {
								_this.numberOfInactivePrescriptions = inactiveCount.data;
							});

							$http.get('api/prescription/archived-prescription-count/' + patientId).then(function (archivedCount) {
								_this.numberOfArchivedPrescriptions = archivedCount.data;
							});
						});
				});
		});
	}

	this.getPrescriptionPurchaseOrders = function () {
		angular.forEach(_this.prescriptions, function (prescription) {
			EmpirePurchaseOrderFactory.getPurchaseOrderDTOs({prescriptionId: prescription.id, getArchived: !!_this.showArchivedItems}).$promise.then(function (response) {
				prescription.purchaseOrderDtos = response;
			});
		});
	};

	_this.appointmentServiceLoaded = false;
	_this.userNotificationServiceLoaded = false;
	_this.checklistServiceLoaded = false;
	_this.outcomeMeasureServiceLoaded = false;
	_this.forbinServiceLoaded = false;
	_this.patientMedicalHistoryLoaded = false;
	_this.evalFormsLoaded = false;
	_this.patientContactsLoaded = false;
	_this.patientFilesLoaded = false;
	_this.patientAlertsLoaded = false;
	_this.prescriptionId = null;
	_this.purchaseOrdersLoaded = false;
	_this.surveyLinkType = null;

	this.openTab = function (tab, patientId) {
		switch (tab) {
			// case 'patient_summary':
			// 	break;
			// case 'personal_information':
			// 	break;
			case 'forms':
				if (!_this.evalFormsLoaded) {
					_this.evalFormsLoaded = true;
					CustomFormService.loadForms();
					PatientService.getEvaluationForms(patientId);
				}
				break;

			case 'contacts':
				if (!_this.patientContactsLoaded) {
					_this.patientContactsLoaded = true;
					PatientService.getPatientContacts(patientId);
				}
				break;

			case 'medical_history':
				if (!_this.patientMedicalHistoryLoaded) {
					_this.patientMedicalHistoryLoaded = true;
					PatientService.getPatientMedicalHistory(patientId);
				}
				break;
			case 'work_in_progress':
				if (!_this.patientContactsLoaded) {
					_this.patientContactsLoaded = true;
					PatientService.getPatientContacts(patientId);
				}
				ShoppingCartService.findByPatientId(patientId); // show shopping cart items in proof of delivery section
				break;
			// case 'transaction_history':
			// 	break;
			// case 'notes':
			// 	if (!noteServiceLoaded) {
			// 		noteServiceLoaded = true;
			// 		AppointmentService.profileLoad(patientId);
			// 		NoteService.profileLoad(patientId);
			// 	}
			// 	break;

			case 'appointments':
				if (!_this.appointmentServiceLoaded) {
					_this.appointmentServiceLoaded = true;
					AppointmentService.profileLoad(patientId, _this.prescriptionNotes);
				}
				//Future this will need! AppointmentService.loadAppointmentNotes(_this.prescriptionNotes);
				break;

			// case 'files_and_documents':
			// 	if (!patientFilesLoaded) {
			// 		patientFilesLoaded = true;
			// 		NoteService.getPatientFiles(patientId);
			// 	}
			// 	break;

			case 'purchasing':
				if (!_this.purchaseOrdersLoaded) {
					_this.purchaseOrdersLoaded = true;
					PurchasingService.profileLoad(patientId, _this.showArchivedItems);
				}
				break;
			case 'shopping_cart' :
				ShoppingCartService.findByPatientId(patientId);
				break;
			case 'fabrication':
				if (!_this.checklistServiceLoaded) {
					_this.checklistServiceLoaded = true;
					ChecklistService.profileLoad(patientId, _this.prescriptions);
				}
				break;

			case 'checklist':
				if (!_this.checklistServiceLoaded) {
					_this.checklistServiceLoaded = true;
					ChecklistService.profileLoad(patientId, _this.prescriptions);
				}
				break;

			case 'outcome_measures':
				if (!_this.outcomeMeasureServiceLoaded) {
					_this.outcomeMeasureServiceLoaded = true;
					OutcomeMeasureService.profileLoad(patientId, _this.prescriptions);
				}
				break;

			// case 'fma':
			// 	break;

			case 'alerts':
				if (!_this.patientAlertsLoaded) {
					_this.patientAlertsLoaded = true;
					PatientService.getPatientAlerts(patientId);
					UserNotificationService.profileLoad(patientId);
				}
				break;
			// case 'insurance':
			// 	if (!patientInsurancesLoaded) {
			// 		patientInsurancesLoaded = true;
			// 		InsuranceService.profileLoad(patientId);
			// 	}
			// 	break;

			default:
				break;
		}
	};

	this.loadSectionInfo = function (prescriptionId) {
		PrescriptionSectionFactory.getPercentageComplete({prescriptionId: prescriptionId}).$promise.then(function (response) {
			if (_this.percentCompleted[prescriptionId] === undefined)
				_this.percentCompleted[prescriptionId] = "";
			_this.percentCompleted[prescriptionId] = Math.ceil(response.complete) + "%";
		});
		PrescriptionSectionFactory.getUncompletedSectionNames({prescriptionId: prescriptionId}).$promise.then(function (response) {
			if (_this.uncompletedSections[prescriptionId] === undefined)
				_this.uncompletedSections[prescriptionId] = "";
			_this.uncompletedSections[prescriptionId] = response.join(", ");
		});
	};

	this.changeDeliveryLocation = function () {
		if (_this.prescription === undefined) return;
		var location = _this.prescription.deliveryLocation;
		var patient = PatientService.patient || _this.prescription.patient;
		if (location === 'primary_branch') {
			var primaryBranch = patient.primaryBranch;
			_this.prescription.deliveryLocationAddress = primaryBranch.streetAddress + ", " + primaryBranch.city + ", " + primaryBranch.state + " " + primaryBranch.zipcode;
			_this.prescription.deliveryLocationPhoneNumber = primaryBranch.phoneNumber ? UtilService.formatPhone(primaryBranch.phoneNumber) : '';
			_this.prescription.deliveryLocationFaxNumber = primaryBranch.faxNumber ? UtilService.formatPhone(primaryBranch.faxNumber) : '';
		} else if (location === 'prescription_branch') {
			var prescriptionBranch = _this.prescription.branch;
			_this.prescription.deliveryLocationAddress = prescriptionBranch.streetAddress + ", " + prescriptionBranch.city + ", " + prescriptionBranch.state + " " + prescriptionBranch.zipcode;
			_this.prescription.deliveryLocationPhoneNumber = prescriptionBranch.phoneNumber ? UtilService.formatPhone(prescriptionBranch.phoneNumber) : '';
			_this.prescription.deliveryLocationFaxNumber = prescriptionBranch.faxNumber ? UtilService.formatPhone(prescriptionBranch.faxNumber) : '';
		} else if (location === 'patient_address') {
			_this.prescription.deliveryLocationAddress = patient.streetAddress + ", " + patient.city + ", " + patient.state + " " + patient.zipcode;
		} else if (location === 'patient_alternate_address') {
			if (patient.streetAddress2) {
				_this.prescription.deliveryLocationAddress = patient.streetAddress2 + ", " + patient.city2 + ", " + patient.state2 + " " + patient.zipcode2;
			} else {
				_this.prescription.deliveryLocationAddress = "No alternate address on file";
			}
		} else if ($.isNumeric(location)) {
			DeliveryLocationFactory.get({id: location}).$promise.then(function (response) {
				_this.prescription.deliveryLocationName = response.name;
				_this.prescription.deliveryLocationAddress = response.streetAddress + ", " + response.city + ", " + response.state + " " + response.zipcode;
				_this.prescription.deliveryLocationPhoneNumber = response.phoneNumber ? UtilService.formatPhone(response.phoneNumber) : '';
				_this.prescription.deliveryLocationFaxNumber = response.faxNumber ? UtilService.formatPhone(response.faxNumber) : '';
			});
		} else if (location == null) {
			_this.prescription.deliveryLocationName = "";
			_this.prescription.deliveryLocationAddress = "";
			_this.prescription.deliveryLocationPhoneNumber = "";
			_this.prescription.deliveryLocationFaxNumber = "";
		} else {
			_this.prescription.deliveryLocationAddress = _this.prescription.deliveryLocationAddress === undefined || _this.prescription.deliveryLocationAddress === "" ? "" : _this.prescription.deliveryLocationAddress;
		}
	};

	this.openPrescription = function (prescription, parentRx) {

		if (!prescription) {
			_this.prescriptionDateInLockedBillingCycle = false;
			_this.lockPrescriptionBranch = false;
		}
		var modalInstance = $uibModal.open({
			animation: true,
			// templateUrl: 'views/tmpl/prescription/_add_update_modal_form.html',
			templateUrl: '_prescription_modal.html',
			controller: 'PrescriptionCtrl',
			backdrop: 'static',
			size: 'lg',
			keyboard: false,
			resolve: {
				prescription: function () {
					return prescription;
				},
				parentRx: function () {
					return parentRx;
				},
				prescriptionDiagnosisCodes: function () {

					// SCRUM-4390: previously we were returning the patient Dx codes as prescription Dx codes...with the ID, making them move between new Rx's as
					// they are created.
					if (prescription === undefined) {
						_this.prescriptionDiagnosisCodes = [];
						_this.prescriptionDiagnosisCodes = angular.copy(PatientService.patientDiagnosisCodes);

						angular.forEach(_this.prescriptionDiagnosisCodes, function (dxCode) {
							dxCode.id = undefined;
						});
					}

					return _this.prescriptionDiagnosisCodes;
				},
				sentToBilling: function () {
					return prescription ? ClaimService.prescriptionClaim[prescription.id] !== undefined : false;
				},
				initialEvaluationAppointment: function () {
					return prescription ? _this.initialEvaluationAppointment : undefined;
				}
			}
		});

		modalInstance.result.then(function (resultRx) {
			if (prescription === undefined) {
				_this.profileLoad(PatientService.patient.id);
				ClaimService.profileLoad(PatientService.patient.id);
			}
			if (resultRx.surgeryDate != null && ($moment(resultRx.surgeryDate) > $moment(_this.lastSurgeryDate) || _this.lastSurgeryDate == null)) {
				_this.lastSurgeryDate = resultRx.surgeryDate;
			}
			if (resultRx.id !== undefined && resultRx.id !== 'cancel') {
				_this.setActivePrescription(resultRx.id);
				$rootScope.$broadcast('openTab', 'work_in_progress'); // SCRUM-5408: ensure we properly set the WIP tab
			}
		});
	};

	this.showNoteTypeOption = function (prescription, noteType) {
		var isPrescription = (prescription !== undefined && prescription.id !== undefined && prescription.id > 0);
		var isBilling = (noteType === 'billing' && (UserService.isAdmin() || UserService.isClerical() || UserService.isBilling()));
		var isClinical = (noteType === 'clinical' && (currentUser.id === prescription.residentId || currentUser.id === prescription.treatingPractitionerId));
		var isGeneral = (noteType === 'general'); // ((noteType === 'general' || noteType === 'primary_device') && isPrescription && (UserService.getCurrentUserId() === prescription.clericalUserId || UserService.getCurrentUser().id === prescription.residentId || UserService.getCurrentUser().id === prescription.treatingPractitionerId));
		return isBilling || isClinical || isGeneral;
	};

	this.canAddNote = function (prescription) {
		if (UserService.getCurrentUser() === null || UserService.getCurrentUser() === undefined) return false;
		var currentUserId = UserService.getCurrentUserId();
		// var canAdd = (currentUserId === prescription.clericalUserId || currentUserId === prescription.residentId || currentUserId === prescription.treatingPractitionerId);
		var canAdd = [prescription.clericalUserId, prescription.residentId, prescription.treatingPractitionerId].indexOf(currentUserId) > -1;
		return canAdd;
	};

	this.markPrescriptionInactive = function (prescription) {
		if (!UserService.hasPermission('prescription_deactivation')) {
			alert("You do not have permission to mark prescriptions inactive.");
			return;
		}
		if (ClaimService.prescriptionClaim[prescription.id] !== undefined && ClaimService.prescriptionClaim[prescription.id].disableMarkingPrescriptionInactive === true) {
			alert("This prescription can not be inactivated because there is a claim submission attached. This is blocked for reporting purposes.");
			return;
		}
		var subPrescriptions = $filter('filter')(_this.prescriptions, {parentId: prescription.id}, true);
		var hasActiveSubPrescriptions = false;
		if (subPrescriptions && subPrescriptions.length > 0) {
			angular.forEach(subPrescriptions, function (sp) {
				if (sp.active) {
					hasActiveSubPrescriptions = true;
				}
			});
			if (hasActiveSubPrescriptions) {
				alert("This prescription can not be inactivated because it contains active sub-prescriptions");
				return;
			}
		}
		// SCRUM-2319: Prescriptions with a claim submission in a locked period are not allowed to be deactivated.
		// if (ClaimService.prescriptionClaim[prescription.id] !== undefined && ClaimService.prescriptionClaim[prescription.id].disableMarkingPrescriptionInactive === true) {
		//   if (confirm("WARNING: There is a claim submission attached.  This could cause historical data to change.  Are you sure you want to mark this prescription as inactive?")) {
		//     PrescriptionFactory.markInactive({id: prescription.id}).$promise.then(function (response) {
		//       _this.closePrescription(0);
		//       _this.profileLoad(PatientService.patient.id);
		//     });
		//   }
		// } else {
		if (confirm(ClaimService.prescriptionClaim[prescription.id] && ClaimService.prescriptionClaim[prescription.id].prescriptionClaimSubmissions ? "This Rx has a submission and could have payments attached to it. If you deactivate this Rx it will no longer be included in any financial reporting. Are you sure you wish to deactivate?" : "Are you sure you wish to deactivate this prescription?")) {
			PrescriptionFactory.markInactive({id: prescription.id}).$promise.then(function (response) {
				_this.closePrescription(0);
				_this.profileLoad(PatientService.patient.id);
			});
		}
		// }
	};

	this.markPrescriptionActive = function (prescription) {
		if (confirm("Are you sure you want to mark prescription as Active?")) {
			PrescriptionFactory.markActive({id: prescription.id}).$promise.then(function (response) {
				_this.closePrescription(0);
				_this.profileLoad(PatientService.patient.id);
			});
		}
	};

	this.processArchive = function(prescription) {
		PrescriptionFactory.archivePrescription({id: prescription.id}).$promise.then(function (response) {
			_this.closePrescription(0);
			_this.profileLoad(PatientService.patient.id);
			ClaimService.profileLoad(PatientService.patient.id, _this.showArchivedItems);
			PurchasingService.profileLoad(PatientService.patient.id, _this.showArchivedItems);
		});
	}

	this.processUnarchive = function (prescription) {
		PrescriptionFactory.unarchivePrescription({id: prescription.id}).$promise.then(function (response) {
			_this.closePrescription(0);
			_this.profileLoad(PatientService.patient.id);
			ClaimService.profileLoad(PatientService.patient.id, _this.showArchivedItems);
			PurchasingService.profileLoad(PatientService.patient.id, _this.showArchivedItems);
		});
	}

	this.archivePrescription = function (prescription) {
		_this.isParentPrescription = false;
		for(const p of _this.prescriptions) {
			if(prescription.id === p.parentId) {
				_this.isParentPrescription = true;
				break;
			}
		}
		if(_this.isParentPrescription) {
			confirm("There are un-archived sub-prescriptions under this parent. The parent cannot be archived without the sub-prescription also Archived. Are you sure you want to Archive the Parent Rx and all Sub-Prescriptions?")
		}

		$http.get('api/claim/prescription/' + prescription.id).then(function (response) {
			if (response.data.length === 0 || !(response.data[0].totalPtResponsibilityBalance === 0.00 && response.data[0].totalClaimBalance === 0.00)) {
				if(confirm("Prescription/Claim are Not Zero Balanced, are you sure you’d like to Archive this Prescription?")) {
					_this.processArchive(prescription)
				}

			} else if (response.data[0].totalPtResponsibilityBalance === 0.00 && response.data[0].totalClaimBalance === 0.00) {
				if(confirm("Are you sure you'd like to archive this prescription?")){
					_this.processArchive(prescription)
				}
			} else {
				_this.processArchive(prescription)
			}
		}).catch(function (error) {
			return $q.when([]);
		});
	}

	this.unArchivePrescription = function (prescription) {
		if(undefined !== prescription.parentId) {
			if(confirm("Since this is a Sub-Rx, the Parent Rx will also have to be Un-Archived. Are you sure you want to un-archive this prescription?")) {
				_this.processUnarchive(prescription);
			}
		} else {
			if (confirm("Are you sure you want to un-archive this prescription?")) {
				_this.processUnarchive(prescription);
			}
		}
	};

	this.duplicatePrescription = function (prescription) {
		if (confirm("Are you sure you want to duplicate prescription #" + prescription.id + " for patient " + UtilService.formatName(prescription.patient, 'FMiL') + "? Please note all documents requiring a signature will be reset (SWO, POD, etc). ")) {

			PrescriptionFactory.duplicatePrescription({prescriptionId: prescription.id}).$promise.then(function (response) {
				_this.closePrescription(0);
				_this.profileLoad(PatientService.patient.id);
			});
		}
	};

	this.closePrescription = function (prescriptionId) {
		// console.log("Entered");
		// var pid = undefined;
		// var el = $("[data-prescription-id='" + prescriptionId + "']");
		// console.log(el);
		// if ($("[data-prescription-id] i.fa-folder-open").length === 0) {
		//   console.log("if");
		//   /* No prescription file opened */
		//   el.find("i").removeClass("fa-folder").addClass("fa-folder-open");
		//   pid = prescriptionId;
		// } else {
		//   console.log("else");
		//   if (el.find("i").hasClass("fa-folder-open")) {
		//     console.log("else if");
		//     el.find("i").removeClass("fa-folder-open").addClass("fa-folder");
		//     pid = undefined;
		//   } else {
		//     console.log("else else");
		//     $("[data-prescription-id] i.fa-folder-open").removeClass("fa-folder-open").addClass("fa-folder");
		//     el.find("i").removeClass("fa-folder").addClass("fa-folder-open");
		//     pid = prescriptionId;
		//   }
		// }

		_this.setActivePrescription(prescriptionId);
	};

	this.checkRxPatientInsuranceValue = function () {
		// proper full safety check with profile reload if the patientInsuranceId is ever off
		if (_this.prescription) {
			InsuranceVerificationFactory.findByPrescriptionIdAndCarrierType({
				prescriptionId: _this.prescription.id,
				carrierType: 'primary'
			}).$promise.then(function (primaryInsuranceVerification) {
				if (_this.prescription.active && primaryInsuranceVerification && primaryInsuranceVerification.patientInsuranceId && primaryInsuranceVerification.patientInsuranceId !== _this.prescription.patientInsuranceId) {
					_this.prescription.patientInsuranceId = primaryInsuranceVerification.patientInsuranceId;
					PrescriptionFactory.save(_this.prescription).$promise.then(function (response) {
						_this.prescription = response;
						// SCRUM-3484: necessary?
						// full reload required:
						//_this.profileLoad(PatientService.patient.id);
					});
				}
			}, function (err) {
				console.log(err);
			});
		}
	};

	this.loadPrescriptionDetails = function (prescriptionId) {
		_this.shouldReviewHcpcAddition = false;
		_this.prescriptionId = prescriptionId;
		if (_this.prescriptionDetailsLoading && _this.prescription && prescriptionId === _this.prescription.id) {
			return;
		}
		_this.prescriptionDetailsLoading = true;

		PrescriptionFactory.get({id: prescriptionId}).$promise.then(function (response) {
			_this.prescription = response;

			_this.checkRxPatientInsuranceValue();

			if (_this.prescription.patientRecall) {
				var recallDate = $moment(_this.prescription.deliveredOn).add(_this.prescription.recallDays, 'days');
				_this.prescription.$recallDaysRemaining = recallDate.diff($moment(), 'days');
			}
			_this.showPrescriptionDetailsFiles = false;
			_this.showPrescriptionStandardWrittenOrderFiles = false;
			_this.changeDeliveryLocation();
			_this.prescriptionLCodesLoading = true;
			PrescriptionLCodeFactory.findByPrescriptionId({prescriptionId: prescriptionId, stripDown: true}).$promise.then(function (response) {
				_this.prescriptionLCodesLoading = false;
				_this.prescriptionLCodes = response;
				_this.prescriptionLCodeAlerts = [];
				_this.prescriptionLCodeAlertsTrackingArray = [];
				_this.diagnosisCodeLCodeAlerts = [];


				for(let code of _this.prescriptionLCodes) {
					if(code.lCode.create_review_task === true) {
						_this.shouldReviewHcpcAddition = true;
						_this.reviewHcpcAdditionChange = true;
						break;
					}
				}
				PrescriptionLCodeFactory.findHCPCSSelectionByPrescriptionId({prescriptionId : prescriptionId}).$promise.then(function (response) {
					if(response["ADD_AUTH_AUDIT"] !== undefined && response["ADD_AUTH_AUDIT"].addAuthorized === true) {
						_this.shouldReviewHcpcAddition = false;
					}
				});

				_this.loadPrescriptionDiagnosisCodes(prescriptionId);
				InsuranceService.loadPatientInsurancesVerification(prescriptionId);

				SystemSettingFactory.findBySectionAndField({
					section: "general",
					field: "survey_link_type"
				}).$promise.then(function (response) {
					_this.surveyLinkType = response.value;
					if (_this.surveyLinkType === 'device_type') {
						if (_this.prescription.deviceType) {
							_this.surveyWrapper = {
								surveyName: null,
								surveyLink: _this.prescription.deviceType.survey_link
							};
						} else {
							console.log("null deviceType on prescription");
						}
					}
				}, function (err) {
					console.log(err);
				});
				NymblStatusFactory.search({type: 'prescription', active: true}).$promise.then(function (response) {
					_this.prescriptionStatuses = response;
				}, function (err) {
					console.log(err);
				});
				NymblStatusFactory.search({type: 'physician_documentation', active: true}).$promise.then(function (response) {
					_this.physicianDocumentationStatuses = response;
				}, function (err) {
					console.log(err);
				});
			}, function (err) {
				console.log(err);
			});
			SystemSettingFactory.findBySectionAndField({
				section: "billing",
				field: "sn_on_pod"
			}).$promise.then(function (response) {
				_this.useSerialNumberOnPOD = response;
			}, function (err) {
				console.log(err);
			});
			DeviceTypeFileTypeFactory.findByDeviceTypeId({deviceTypeId: _this.prescription.deviceTypeId}).$promise.then(function (response) {
				_this.deviceTypeFileTypes = response;
			}, function (err) {
				console.log(err);
			});
			if (_this.prescription.referringPhysicianId) {
				PhoneNumberFactory.getPhysicianPhoneNumber({personId: _this.prescription.referringPhysicianId}).$promise.then(function (response) {
					for (var i = 0; i < response.length; i++) {
						if (response[i].type === 'Work Phone') {
							_this.prescription.referringPhysician.workPhone = UtilService.formatPhone(response[i].phoneNumber);
						} else if (response[i].type === 'Fax') {
							_this.prescription.referringPhysician.faxNumber = UtilService.formatPhone(response[i].phoneNumber);
						}
					}
				});
			}
			if (_this.prescription.referringPhysicianId != null && _this.prescription.referringPhysicianId > 0) {
				_this.isPrimaryCarePhysician = false;
				_this.isReferringPhysician = true;
			} else if (_this.prescription.primaryCarePhysicianId != null && _this.prescription.primaryCarePhysicianId > 0) {
				_this.isPrimaryCarePhysician = true;
				_this.isReferringPhysician = false;
			}
			if (_this.prescription.primaryCarePhysicianId) {
				PhoneNumberFactory.getPhysicianPhoneNumber({personId: _this.prescription.primaryCarePhysicianId}).$promise.then(function (response) {
					for (var i = 0; i < response.length; i++) {
						if (response[i].type === 'Work Phone') {
							_this.prescription.primaryCarePhysician.workPhone = UtilService.formatPhone(response[i].phoneNumber);
						} else if (response[i].type === 'Fax') {
							_this.prescription.primaryCarePhysician.faxNumber = UtilService.formatPhone(response[i].phoneNumber);
						}
					}
				});
			}
			AdvanceBeneficiaryNoticeFactory.findByPrescriptionId({prescriptionId: _this.prescription.id}).$promise.then(function (response) {
				_this.prescriptionAbnDTOs = response;
			});
			GL_PeriodFactory.getLastClosedPeriod().$promise.then(function (response) {
				if (response.endDate) {
					PrescriptionFactory.getFirstClaimSubmissionForPrescription({prescriptionId: prescriptionId}).$promise.then(function (firstClaimSubmission) {
						if (firstClaimSubmission.id) {
							var minDate = $moment(response.endDate).add(1, "days").format("YYYY-MM-DD");
							var firstClaimSubmissionDate = $moment(firstClaimSubmission.submissionDate).format("YYYY-MM-DD");
							_this.prescriptionDateInLockedBillingCycle = $moment(firstClaimSubmissionDate).isBefore($moment(minDate), 'day');
						} else {
							_this.prescriptionDateInLockedBillingCycle = false;
						}
					});
				}
			});
			AppointmentFactory.getEarliestAppointmentForPrescription({prescriptionId: _this.prescription.id}).$promise.then(function (response) {
				_this.initialEvaluationAppointment = response;
			});

			_this.loadPrescriptionPurchaseOrders(prescriptionId);
			_this.prescriptionDetailsLoading = false;
		});

		_this.prescriptionSectionsLoading = true;

		PrescriptionSectionFactory.findByPrescriptionId({prescriptionId: prescriptionId}).$promise.then(function (response) {
			_this.prescriptionSectionsLoading = false;
			_this.prescriptionSections = response;
		}, function (err) {
			console.log(err);
		});

		_this.detailedWrittenOrderLoading = true;
		DetailedWrittenOrderFactory.findByPrescriptionId({prescriptionId: prescriptionId}).$promise.then(function (response) {
			_this.detailedWrittenOrderLoading = false;
			_this.detailedWrittenOrder = response;
			_this.detailedWrittenOrder.startDate = _this.detailedWrittenOrder.startDate ? $moment(_this.detailedWrittenOrder.startDate).format("YYYY-MM-DD") : undefined;
			_this.detailedWrittenOrder.$startDateForDisplay = _this.detailedWrittenOrder.startDate ? $moment(_this.detailedWrittenOrder.startDate).toDate() : undefined;
		}, function (err) {
			console.log(err);
		});

		FinancialResponsibilityService.loadFinancialResponsibility(prescriptionId);

		EvaluationFormFactory.findByPrescriptionId({prescriptionId: prescriptionId}).$promise.then(function (response) {
			_this.evaluationForms = response;
		}, function (err) {
			console.log(err);
		});

		PrescriptionFileFactory.findByPrescriptionId({prescriptionId: prescriptionId}).$promise.then(function (response) {
			_this.prescriptionFiles = response;
		}, function (err) {
			console.log(err);
		});
	};

	this.loadPrescriptionDiagnosisCodes = function (prescriptionId) {
		_this.prescriptionDiagnosisCodeLoading = true;
		PrescriptionDiagnosisCodeFactory.findByPrescriptionId({prescriptionId: prescriptionId}).$promise.then(function (response) {
			_this.prescriptionDiagnosisCodeLoading = false;
			_this.prescriptionDiagnosisCodes = response;
			var diagnosis = [];
			var codeSet = [];
			angular.forEach(_this.prescriptionDiagnosisCodes, function (code, index) {
				diagnosis.push(code.diagnosisCode.name + ' (' + code.diagnosisCode.code + ')');
				codeSet.push(code.diagnosisCode.code);
			});
			_this.diagnosis = diagnosis.join(', ');
			_this.codeSet = codeSet[0] ? codeSet[0].toUpperCase() : undefined;
			_this.loadDiagnosisCodeLCodeAlerts();
		}, function (error) {
			console.log(error);
			console.log("loadPrescriptionDiagnosisCodes failed for Rx: " + prescriptionId);
		});
	};

	this.loadDiagnosisCodeLCodeAlerts = function () {
		if (_this.prescriptionDiagnosisCodes.length < 1 || _this.prescriptionLCodes.length < 1) return;
		var diagnosisCodeIds = _this.prescriptionDiagnosisCodes.map(pdc => pdc.diagnosisCodeId);
		var lCodeIds = _this.prescriptionLCodes.map(plc => plc.lCodeId);
		DiagnosisCodeLCodeAlertFactory.findByDiagnosisCodeIdsAndLCodeIds({
			diagnosisCodeIds: diagnosisCodeIds,
			lCodeIds: lCodeIds
		}).$promise.then(function (response) {
			_this.diagnosisCodeLCodeAlerts = response;
		});
	};

	this.loadPrescriptionPurchaseOrders = function(prescriptionId) {
		// I don't want to re-calculate these totals every time the user switches between prescriptions
		if (_this.prescriptionTotalMap != null && _this.prescriptionTotalMap.has(prescriptionId)) {
			_this.prescriptionCogByLCode = _this.cogByRxIdAndLCode.get(prescriptionId);
			_this.prescriptionTotalCost = _this.prescriptionTotalMap.get(prescriptionId);
			_this.cogTotal = _this.cogFromPoiMap.get(prescriptionId);
		} else {
			if (_this.prescriptionTotalMap == null) _this.prescriptionTotalMap = new Map();
			if (_this.prescriptionCogByLCode == null) _this.prescriptionCogByLCode = new Map();
			if (_this.cogFromPoiMap == null) _this.cogFromPoiMap = new Map();
			var prescriptionCog = 0;
			var prescriptionTotal = 0;
			// Get all purchase orders with items linked to current prescription
			EmpirePurchaseOrderFactory.getPurchaseOrderDTOs({prescriptionId: prescriptionId}).$promise.then(function (response) {
				_this.prescriptionPurchaseOrders = response;
				_this.prescription.purchaseOrderItems = [];
				// Loop through all purchase orders to total cost of all items linked to current prescription
				//    -> This is necessary because purchase orders can contain items for multiple prescriptions
				for (var i = 0; i < _this.prescriptionPurchaseOrders.length; i++) {
					var purchaseOrderTotal = 0;
					var purchaseOrderCog = 0;
					for (const poi of _this.prescriptionPurchaseOrders[i].purchaseOrderItems) {
						if (_this.prescriptionPurchaseOrders[i].isThirdPartyVendor) {
							poi.showOnDelivery = true;
						}
						// Populate all purchase order items into an array
						if (poi.prescriptionId == prescriptionId) {
							_this.prescription.purchaseOrderItems.push(poi);
							var itemCost = (poi.itemCost != null && poi.itemCost > 0) ? poi.itemCost : 0;
							var itemQuantity = (poi.quantity != null && poi.quantity > 0) ? poi.quantity : 1;
							// for backwards compatibility (moving forward, poi.totalCost should not be null)
							var itemTotalCost = (poi.totalCost != null && poi.totalCost > 0) ? poi.totalCost : itemCost * itemQuantity;
							if (itemTotalCost > 0){
								purchaseOrderTotal += itemTotalCost;
								// Not all items' totalCost is considered COG
								if (!_this.voidStatus.includes(_this.prescriptionPurchaseOrders[i].status)
									&& !_this.voidStatus.includes(poi.status)) {
									purchaseOrderCog += itemTotalCost;
									// Summarize cogs by lCodeId (if any) and save them in a map
									var lCodeId = poi.prescriptionLCode != null ? Number(poi.prescriptionLCode.lCodeId) : -1;
									if (lCodeId > 0) {
										if (_this.prescriptionCogByLCode.has(lCodeId)) {
											var oldCog = _this.prescriptionCogByLCode.get(lCodeId);
											if (oldCog && oldCog > 0) itemTotalCost += oldCog;
										}
										_this.prescriptionCogByLCode.set(lCodeId, itemTotalCost);
										if (_this.cogByRxIdAndLCode == null) _this.cogByRxIdAndLCode = new Map();
										_this.cogByRxIdAndLCode.set(prescriptionId, _this.prescriptionCogByLCode);
									}
								}
							}
						}
					}
					prescriptionTotal += purchaseOrderTotal;
					prescriptionCog += purchaseOrderCog;
					_this.prescriptionPurchaseOrders[i].prescriptionItemsTotalCost = purchaseOrderTotal;
					_this.prescriptionPurchaseOrders[i].prescriptionPurchaseOrderTotalCost = prescriptionTotal;
				}

				if (_this.prescription.manualDwoSignedDate) {
					_this.detailedWrittenOrderFaceToFaceDateWarning = (_this.faceToFaceAgeMonths(_this.prescription.manualDwoSignedDate) >= 6);
				} else if (_this.detailedWrittenOrder) {
					_this.detailedWrittenOrderFaceToFaceDateWarning = (_this.faceToFaceAgeMonths(_this.detailedWrittenOrder.signedDate) >= 6);
				}

				_this.prescriptionTotalCost = prescriptionTotal;
				_this.prescriptionTotalMap.set(prescriptionId, prescriptionTotal);
				_this.cogTotal = prescriptionCog;
				_this.cogFromPoiMap.set(prescriptionId, prescriptionCog);
			}, function (err) {
				console.log(err);
			});
		}
	};

	this.getSectionField = function (name, field) {
		var value;
		angular.forEach(_this.prescriptionSections, function (section, index) {
			if (section.section === name)
				value = section[field];
		});
		return value;
	};

	this.completeSection = function (name) {
		var sectionId = _this.getSectionField(name, 'id');
		PrescriptionSectionFactory.completeSection({id: sectionId}).$promise.then(function (response) {
			_this.prescriptionSections = response;
			// _this.setActivePrescription(_this.prescription.id);
			_this.loadSectionInfo(_this.prescription.id);
			TabService.wipSection[name + '_panel'].open = false;
		});
	};

	this.unlockSection = function (name) {
		var sectionId = _this.getSectionField(name, 'id');
		PrescriptionSectionFactory.unlockSection({id: sectionId}).$promise.then(function (response) {
			_this.prescriptionSections = response;
			_this.setActivePrescription(_this.prescription.id);
			_this.loadSectionInfo(_this.prescription.id);
		});
		// }
	};

	this.toggleShowOnDeliveryAll = function () {
		angular.forEach(_this.prescriptionLCodes, function (entry) {
			entry.showOnDelivery = _this.toggleShowOnDelivery;
		});
	};

	this.saveDeliveryReceipt = function () {
		if (!_this.preprocessSignature()) {
			return;
		}
		var signatureAdded = _this.prescription.signatureAdded;
		// SCRUM-4418: moved to setTreatingPractitionerIfNecessaryAndAddClaim, when the claim is actually created.
		//_this.prescription.deliveredOn = $moment().format("YYYY-MM-DD hh:mm:ss");
		PrescriptionFactory.save(_this.prescription).$promise.then(function (response) {
			if (!_this.prescription.signatureAdded && !_this.prescription.signatureRemoved) {
				angular.forEach(_this.prescriptionLCodes, function (entry, index) {
					PrescriptionLCodeFactory.save(entry).$promise.then(function (plc) {
						_this.prescriptionLCodes[index] = plc;
					});
				});
				PurchasingService.savePurchaseOrderItemsForPrescription(_this.prescription.id);
			}

			_this.prescription = response;

			if (signatureAdded) {
				UtilService.openPrintScreen('proof_of_delivery?prescriptionId=' + _this.prescription.id + '&showPodModifiers=' +
					_this.showPodModifiers + '&esod=' + PurchasingService.empireShowOnDelivery + '&patientId=' + _this.prescription.patientId + '&scsod=' + ShoppingCartService.showOnDeliveryItems +
					'?printOnly=false' + '&showDetJust=' + _this.showDetailedJustifications);
			} else {
				UtilService.displayAlert("success", "Delivery Receipt successfully saved.", "#delivery-receipt-alert-container");
			}
		}, function (error) {
			console.log(error.data.message);
			var errorMessage = "Changes were not saved!";
			if (error && error.data && error.data.message) {
				errorMessage += error.data.message;
				DiffModalService.popModal(error.data.diffs, error.data.message, _this.prescription.id, "Prescription");
			}
			UtilService.displayAlert("danger", errorMessage, "#header-alert-container");
		});
	};

	this.preprocessSignature = function () {
		var ok = true;
		if (_this.prescription.signatureAdded) {
			_this.prescription.signatureRemoved = false;
			if (_this.prescription && _this.prescription.signedBy === 'patient' && PatientService.isPatientAMinor()) {
				if (confirm('Warning: You are about to have a child patient sign this form. Please click "Cancel" and update the contact signing this form if this is in error.')) {
					// Can proceed
				} else {
					_this.prescription.signatureAdded = false;
					ok = false;
				}
			}
		} else if (_this.prescription.signatureRemoved) {
			_this.prescription.signatureAdded = false;
			if (confirm("Are you sure you want to reset signature?")) {
				delete _this.prescription.signedDate;
				delete _this.prescription.signature;
				delete _this.prescription.signedUserId;
				delete _this.prescription.userSignedDate;
				delete _this.prescription.signedBy;
			} else {
				_this.prescription.signatureRemoved = false;
				ok = false;
			}
		}
		return ok;
	};

	this.resetRecall = function () {
		$(".recall-fields input").attr("disabled", true).removeAttr("required").val("");
		$(".recall-fields").addClass("hidden");
		_this.prescription.patientRecall = false;
		_this.prescription.recallDays = 0;
		_this.prescription.recallReason = "";
		return false;
	};

	_this.currentPrescriptionId = undefined;

	this.setActivePrescription = function (prescriptionId) {
		if (!prescriptionId) {
			_this.prescription = undefined;
			_this.prescriptionSections = [];
			_this.prescriptionDiagnosisCodes = [];
			_this.prescriptionLCodes = [];
			return;
		}
		$stateParams.prescriptionId = prescriptionId;
		var expanded = [];
		var active_tab = [];
		_this.loadPrescriptionDetails(prescriptionId);

		$("#work-in-progress .panel-group .panel .panel-collapse.in").each(function (index, el) {
			expanded.push($(this).attr("id"));
		});
		$("#insurance-verification-panel .panel-body .nav.nav-tabs li.active").each(function (index, el) {
			active_tab.push($(this).find("a").data("tab"));
		});
		$("#pre-authorization-panel .panel-body .nav.nav-tabs li.active").each(function (index, el) {
			active_tab.push($(this).find("a").data("tab"));
		});
		$("#covered-codes-panel .panel-body .nav.nav-tabs li.active").each(function (index, el) {
			active_tab.push($(this).find("a").data("tab"));
		});
		$("#patient-tabs a[data-target='#work-in-progress']").tab("show");

		if (TabService.activeTab.length === 0 && prescriptionId !== 0) {
			TabService.changeActiveTab(0);
		}
		angular.forEach(expanded, function (entry, index) {
			if (TabService.wipSection[entry.replace(/-/g, '_')] !== undefined)
				TabService.wipSection[entry.replace(/-/g, '_')].open = true;
		});

		TabService.expanded = expanded;
		if (active_tab.length > 0)
			TabService.activeTab = active_tab;

		if(prescriptionId && ClaimService.prescriptionClaim[prescriptionId] && ClaimService.prescriptionClaim[prescriptionId].prescriptionClaimSubmissions) {
			_this.hasSubmissions = ClaimService.prescriptionClaim[prescriptionId].prescriptionClaimSubmissions.length;
		} else {
			_this.hasSubmissions = false;
		}
		_this.showLCodeSelectionAuditText = false;
		_this.initialHCPCSelectionAudit = undefined;
		_this.latestHCPCSelectionAudit = undefined;
		_this.addAuthorizationAudit = undefined;
		_this.authorizationAudit = undefined;
		_this.currentPrescriptionId = prescriptionId;
		PrescriptionLCodeFactory.findHCPCSSelectionByPrescriptionId({prescriptionId : prescriptionId}).$promise.then(function (response) {
			if(!angular.equals(response, {}) && undefined !== response["CHANGE_AUDIT"] && response["CHANGE_AUDIT"].type !== 'ADD') {
				_this.showLCodeSelectionAuditText = true;
			}
			if(response["ADD_AUTH_AUDIT"] !== undefined && response["ADD_AUTH_AUDIT"].addAuthorized === true) {
				_this.shouldReviewHcpcAddition = false;
			}
			//INIT_AUDIT, AUTH_AUDIT, and CHANGE_AUDIT are map keys being returned from the api.
			_this.initialHCPCSelectionAudit = response["INIT_AUDIT"];
			_this.authorizationAudit = response["AUTH_AUDIT"];
			_this.latestHCPCSelectionAudit = response["CHANGE_AUDIT"];
			_this.addAuthorizationAudit = response["ADD_AUTH_AUDIT"];
			_this.populateUserInfo(_this.authorizationAudit);
			_this.populateUserInfo(_this.latestHCPCSelectionAudit);
			_this.populateUserInfo(_this.initialHCPCSelectionAudit);
			_this.populateUserInfo(_this.addAuthorizationAudit);
		});
	};

	this.populateUserInfo = function(authorization) {
		if(authorization !== undefined && authorization.createdById !== undefined) {
			UserFactory.getById({id: authorization.createdById}).$promise.then(function(response) {
				authorization["user"] = response;
			});
		}
	}

	this.markHcpcSelectionAsReviewed = function(isChange, isAddition) {
		this.authorization = {
			prescriptionId: _this.currentPrescriptionId,
			createdBy: UserService.getCurrentUser().username,
			initialEdit: false,
			completeSection: false,
			createdById: UserService.getCurrentUser().id
		};
		if(isChange) {
			this.authorization.authorized = true;
		}
		if(isAddition) {
			this.authorization.addAuthorized = true;
		}
		PrescriptionLCodeFactory.authorizeHCPCSSelection(this.authorization).$promise.then(function () {
			PrescriptionLCodeFactory.findHCPCSSelectionByPrescriptionId({prescriptionId : _this.currentPrescriptionId}).$promise.then(function (response) {
				_this.initialHCPCSelectionAudit = response["INIT_AUDIT"];
				_this.authorizationAudit = response["AUTH_AUDIT"];
				_this.latestHCPCSelectionAudit = response["CHANGE_AUDIT"];
				_this.addAuthorizationAudit = response["ADD_AUTH_AUDIT"];
				_this.populateUserInfo(_this.authorizationAudit);
				_this.populateUserInfo(_this.latestHCPCSelectionAudit);
				_this.populateUserInfo(_this.initialHCPCSelectionAudit);
				_this.populateUserInfo(_this.addAuthorizationAudit);
				if(isChange && _this.authorizationAudit) {
					_this.reviewHcpcAdditionChange = false;
				}
				_this.setActivePrescription(_this.currentPrescriptionId);
			});
		})
	}

	this.openSelectLCodes = function () {
		var lCodesInstance = $uibModal.open({
			templateUrl: 'views/tmpl/patient/work_in_progress/_l_code_selection_modal_form.html',
			controller: 'LCodeModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: "lg modal-xlg",
			resolve: {
				branchId: function () {
					return $rootScope.branchId;
				},
				patientId: function () {
					return PatientService.patient.id;
				},
				prescriptionId: function () {
					return _this.prescription.id;
				},
				prescriptionLCodes: function () {
					return _this.prescriptionLCodes;
				},
				lCodes: function () {
					return LCodeFactory.search({active: true}).$promise.then(function (response) {
						return response;
					});
				},
				lCodeNodeTree: function () {
					return LCodeNodeFactory.loadTreeByParentId({parentId: 1}).$promise.then(function (response) {
						return response;
					});
				},
				dto: function () {
					return InsuranceService.patientInsurancesVerification[0];
				}
			}
		});

		lCodesInstance.result.then(function (response) {
			_this.setActivePrescription(_this.prescription.id);
			$rootScope.$emit('lCodeSelectionModalClosed', null);
			// $timeout(function () {
			if (_this.hcpcsItemLink && response.data && response.data.length) {
				_this.openHcpcsItemModal(response.data);
			}
			// }, 1000);
		});
	};

	this.openHcpcsItemModal = function (plcs) {
		ItemFactory.findByHCPCS(plcs).$promise.then(function (hcpcsItemDTOs) {
			var hasItems = false;
			angular.forEach(hcpcsItemDTOs, function (plcItem, index) {
				if (plcItem.items.length > 0) {
					hasItems = true;
				}
			});

			if (hasItems) {
				var hcpcsInstance = $uibModal.open({
					templateUrl: 'views/tmpl/_hcpcs_items_modal.html',
					controller: 'HcpcsItemsModalCtrl',
					backdrop: 'static',
					keyboard: false,
					size: "full-100",
					resolve: {
						hcpcsItemDTOs: function () {
							return hcpcsItemDTOs;
						}
					}
				});

				hcpcsInstance.result.then(function (results) {
					ShoppingCartService.saveAll(results);
					_this.setActivePrescription(_this.prescription.id);
				});
			}
		});

	};

	this.updateJustifications = function (isLocked) {
		if (isLocked)
			return;
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/patient/work_in_progress/_l_code_justification_modal_form.html',
			controller: 'LCodeJustificationCtrl',
			size: 'xlg',
			resolve: {
				prescriptionId: function () {
					return _this.prescription.id;
				},
				prescriptionLCodes: function () {
					return _this.prescriptionLCodes;
				},
				dto: function () {
					return InsuranceService.patientInsurancesVerification[0];
				}
			}
		});

		modalInstance.result.then(function () {
			_this.setActivePrescription(_this.prescription.id);
		});
	};

	this.saveDetailWrittenOrder = function (form, prescriptionId) {
		if (form.$valid) {
			$('#save-form').button('loading');
			// delete $scope.detailedWrittenOrder.patient;
			// delete $scope.detailedWrittenOrder.prescription;

			_this.detailedWrittenOrder.patientId = PatientService.patient.id;
			_this.detailedWrittenOrder.prescriptionId = prescriptionId;
			_this.detailedWrittenOrder.isReferringPhysician = _this.isReferringPhysician;
			_this.detailedWrittenOrder.startDate = _this.detailedWrittenOrder.$startDateForDisplay ? $moment(_this.detailedWrittenOrder.$startDateForDisplay).format("YYYY-MM-DD") : undefined;
			DetailedWrittenOrderFactory.save(_this.detailedWrittenOrder).$promise.then(function (response) {
				_this.detailedWrittenOrder = response;
				_this.detailedWrittenOrder.$startDateForDisplay = _this.detailedWrittenOrder.startDate ? $moment(_this.detailedWrittenOrder.startDate).toDate() : undefined;
				$('#save-form').button('reset');
			}, function (error) {
				UtilService.displayAlert("danger", "Error saving standard written order", "#detailed-written-order-alert");
				$('#save-form').button('reset');
			});
		}
	};

	this.saveAndSignSWO = function (form, prescriptionId) {
		_this.detailedWrittenOrder.isSignatureReady = false;
		_this.saveDetailWrittenOrder(form, prescriptionId);
		UtilService.openPrintScreen('standard_written_order?prescriptionId=' + prescriptionId +
			'&hideMods=' + _this.hideMods + '&isReferringPhysician=' + _this.isReferringPhysician + '?printOnly=false' +
			'&showDetJust=' + _this.showDetailedJustifications);
	};

	this.serviceEstimate = function (insuranceVerification) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/patient/work_in_progress/_service_estimate_modal_form.html',
			controller: 'ServiceEstimateCtrl',
			backdrop: 'static',
			keyboard: false,
			resolve: {
				patient: function () {
					return PatientService.patient;
				},
				prescription: function () {
					return _this.prescription;
				},
				insuranceVerification: function () {
					return insuranceVerification;
				}
			}
		});

		modalInstance.result.then(function (result) {
			if (result === 'cancel') return;
			var prescriptionId = _this.prescription.id;
			_this.setActivePrescription(undefined);
			setTimeout(function () {
				_this.setActivePrescription(prescriptionId);
			}, 1000);
		});
	};

	this.openFinancialResponsibility = function (insuranceVerification) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/patient/work_in_progress/_financial_responsibility_modal_form.html',
			controller: 'FinancialResponsibilityCtrl',
			backdrop: 'static',
			keyboard: false,
			resolve: {
				insuranceVerification: function () {
					return insuranceVerification;
				}
			}
		});

		modalInstance.result.then(function (prescriptionId) {
			_this.loadPrescriptionDetails(prescriptionId);
		});
	};

	this.changeTemplate = function (prescriptionId, patientId) {
		prescriptionId = prescriptionId || 0;
		var templateId = _this.noteTemplateId;

		if (templateId !== "") {
			var timezone = $moment.tz.guess(true);
			TemplateFactory.loadByIdPatientPrescription({
				templateId: templateId,
				patientId: patientId,
				prescriptionId: prescriptionId,
				userTimeZone: timezone
			}).$promise.then(function (response) {
				if (_this.prescription.additionalComponentNotes === undefined) _this.prescription.additionalComponentNotes = [];
				_this.prescription.additionalComponentNotes = response.data;
				_this.noteTemplateId = "";
			});
		}
	};

	this.summernoteOptions = {
		focus: false,
		airMode: false,
		toolbar: [
			['headline', ['style']],
			['style', ['bold', 'italic', 'underline', 'superscript', 'subscript', 'strikethrough', 'clear']],
			['fontface', ['fontname']],
			['textsize', ['fontsize']],
			['fontclr', ['color']],
			['alignment', ['ul', 'ol', 'paragraph', 'lineheight']],
			['height', ['height']],
			['table', ['table']],
			['insert', ['link', 'picture', 'video', 'hr']],
		],
		insertTableMaxSize: {
			col: 20,
			row: 20
		},

		dialogsInBody: true,
		callbacks: {
			onPaste: function (e) {
				var updatePastedText = function () {
					var replacedText = $scope.utilService.stripUnsupportedHTMLTagsSummerNote(e.currentTarget.innerHTML);
					if (replacedText) {
						e.currentTarget.innerHTML = replacedText;
					}
				};
				setTimeout(function () {
					updatePastedText();
				}, 100);
			}
		}
	};

	this.printPOD = function () {
		window.open('proof_of_delivery?prescriptionId=' + _this.prescription.id + '&showPodModifiers=' +
			_this.showPodModifiers + '&esod=' + PurchasingService.empireShowOnDelivery + '&patientId=' + patientId + '&scsod=' + ShoppingCartService.showOnDeliveryItems + 
			'?printOnly=true' + '&showDetJust=' + _this.showDetailedJustifications);
	};

	this.resetDWOSignature = function () {
		if (confirm("Are you sure you want to reset signature?")) {
			delete _this.detailedWrittenOrder.signedDate;
			delete _this.detailedWrittenOrder.signature;
			_this.detailedWrittenOrder.isSignatureReady = true;
			DetailedWrittenOrderFactory.save(_this.detailedWrittenOrder).$promise.then(function (response) {
				_this.detailedWrittenOrder = response;
			}, function (error) {
				UtilService.displayAlert("danger", "Error saving standard written order", "#detailed-written-order-alert");
			});
		}
	};

	this.showProducts = function (prescriptionId) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/available_products_modal.html',
			controller: 'AvailableProductsModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'lg',
			resolve: {
				prescriptionId: function () {
					return prescriptionId;
				}
			}
		});

		modalInstance.result.then(function () {

		});
	};

	this.viewEvaluationForm = function (evaluationForm) {
		$state.goNewTab(evaluationForm.form, {
			evaluationFormId: evaluationForm.id,
			prescriptionId: evaluationForm.prescriptionId,
			patientId: evaluationForm.patientId
		});
	};

	this.viewAbn = function (abn) {
		_this.currentAbnId = abn.id ? abn.id : null;
		UtilService.openPrintScreen('advance_beneficiary_notice' +
			'?prescriptionId=' + _this.prescription.id +
			'?patientId=' + _this.prescription.patientId +
			'?abnId=' + _this.currentAbnId);
	};

	this.saveEvaluationForm = function (evaluationForm) {
		EvaluationFormFactory.save(evaluationForm).$promise.then(function () {
		});
	};

	this.manualFormSave = function (type) {
		if ((!_this.prescription.manualDwoSignedDate && type === 'dwo') || (!_this.prescription.manualPodSignedDate && type === 'pod') || (!_this.prescription.manualFrSignedDate && type === 'fr')) {
			var modalInstance = $uibModal.open({
				templateUrl: '_manual_form_upload.html',
				controller: 'ManualFormUploadCtrl',
				backdrop: 'static',
				keyboard: false,
				size: 'sm',
				resolve: {
					prescription: function () {
						return _this.prescription;
					},
					type: function () {
						return type;
					}
				}
			});

			modalInstance.result.then(function (result) {
				PrescriptionFactory.save(result).$promise.then(function (response) {
					_this.prescription = response;
				});
			});
		} else {
			if (type === 'dwo') _this.prescription.manualDwoSignedDate = undefined;
			if (type === 'pod') _this.prescription.manualPodSignedDate = undefined;
			if (type === 'fr') _this.prescription.manualFrSignedDate = undefined;
			PrescriptionFactory.save(_this.prescription).$promise.then(function (response) {
				_this.prescription = response;
			});
		}
	};

	this.holdUntilDateModal = function (prescription) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/patient/_hold_until_date_modal.html',
			controller: 'HoldUntilDateModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'sm',
			resolve: {
				prescription: function () {
					// NOTE: the _this.prescriptions array is NOT updated when we edit prescriptions because we fetch a new copy
					// and update that.  We need to do an explicit fetch here or we'll blow away any changes made in the WIP, etc
					return PrescriptionFactory.get({id: prescription.id}).$promise.then(function (response) {
						return response;
					});
				}
			}
		});
		modalInstance.result.then(function () {
			PrescriptionFactory.findByPatientId({patientId: $stateParams.patientId}).$promise.then(function (response) {
				_this.prescriptions = response;
			});
		});

	};

	this.sendClaimToBilling = function () {
		var claims = ClaimService.prescriptionClaim[_this.prescription.id];

		if (FinancialResponsibilityService.financialResponsibility.id) {
			ClaimService.profileLoad(_this.prescription.patientId);
			if (ClaimService.prescriptionClaim !== undefined) {
				var modalInstance = $uibModal.open({
					templateUrl: 'assignUserModal.html',
					controller: 'AssignUserCtrl',
					backdrop: 'static',
					keyboard: false,
					size: 'lg',
					resolve: {
						disableCalendar: false,
						claims: function () {
							return claims;
						}
					}
				});

				modalInstance.result.then(function (result) {
					if (result.rentalInfo) {
						_this.prescription.rentalBillingPeriods = result.rentalInfo.rentalBillingPeriods;
						_this.prescription.rentalStartPeriod = result.rentalInfo.rentalStartPeriod;
						_this.prescription.nextPrescriptionDate = result.rentalInfo.nextPrescriptionDate;
					}
					setTreatingPractitionerIfNecessaryAndAddClaim(result, claims);
				});

			} else {
				setTreatingPractitionerIfNecessaryAndAddClaim({userId: null, dos: null, billingBranchId: null}, claims);
			}
		} else {
			ClaimService.missingSection = true;
		}
	};

	this.checkForMissingInfoPriorToSendToBilling = function () {
		var response = null;
		var ivlcTriggerPrimary = false;
		var ivlcTrigger23Other = false;
		var diagnosisCodeTrigger = false;
		var plcTrigger = true;
		var POS_Trigger = false;
		var PECOSnotVerified = false;
		var PECOSoldVerification = false;
		var ivlcsMissingAuthNumbers = false;
		var hasInactiveInsurance = false;
		var hasUnverifedInsurance = false;
		var hasPrimaryInsurance = false;
		var ivlcTriggerBillingUncoveredCode = false;
		var ivlcTriggerNotBillingCoveredCode = false;

		angular.forEach(InsuranceService.patientInsurancesVerification, function (pIV, index) {
			if (pIV) {
				if (pIV.insuranceVerificationLCodes) {
					angular.forEach(pIV.insuranceVerificationLCodes, function (ivlc) {
						if (!ivlc.covered) {
							if (ivlc.bill)
								ivlcTriggerBillingUncoveredCode = true;
							if (index === 0) {
								ivlcTriggerPrimary = true;
							} else if (ivlc.insuranceVerification.carrierType !== "inactive") {
								ivlcTrigger23Other = true;
							}
						} else if (ivlc.covered && !ivlc.bill) {
							ivlcTriggerNotBillingCoveredCode = true;
						}

						if (_this.CRT_hideDeviceType && !ivlc.authNumber) {
							ivlcsMissingAuthNumbers = true;
						}
						if (ivlc.insuranceVerification.carrierType === "primary") {
							hasPrimaryInsurance = true;
						}
					});
				}
				if (pIV.patientInsurance && !pIV.patientInsurance.active) {
					hasInactiveInsurance = true;
				}
				if (!pIV.insuranceVerification) {
					hasUnverifedInsurance = true;
				}
			}
		});

		if (_this.prescriptionLCodes) {
			angular.forEach(_this.prescriptionLCodes, function (plc) {
				if (plc.modifier1 !== "" || plc.modifier2 !== "" || plc.modifier3 !== "" || plc.modifier4 !== "") {
					plcTrigger = false;
				}

				// SCRUM--5846 take this out for now.  Should be in claim details (send claim file)
				// if (_this.prescription.patientInsurance.insuranceCompany.payerTypeId === 1 && (plc.placeOfService === 31 || plc.placeOfService === 32)) {
				// 	POS_Trigger = true;
				// }
			});
		}

		if (_this.prescription.primaryCarePhysician) {
			if (!_this.prescription.primaryCarePhysician.pecosVerified) {
				PECOSnotVerified = true;
			} else if (DateService.daysOld(_this.prescription.primaryCarePhysician.verifiedOn) >= 365) {
				PECOSoldVerification = true;
			}
		}
		if (_this.prescription.referringPhysician) {
			if (!_this.prescription.referringPhysician.pecosVerified) {
				PECOSnotVerified = true;
			} else if (DateService.daysOld(_this.prescription.referringPhysician.verifiedOn) >= 365) {
				PECOSoldVerification = true;
			}
		}

		if (_this.prescriptionDiagnosisCodes.length < 1) {
			diagnosisCodeTrigger = true;
		}

		if (!hasPrimaryInsurance || ivlcTriggerPrimary || ivlcTrigger23Other || plcTrigger || diagnosisCodeTrigger ||
			hasInactiveInsurance || hasUnverifedInsurance || ivlcTriggerBillingUncoveredCode || ivlcTriggerNotBillingCoveredCode ||
			POS_Trigger || PECOSnotVerified || PECOSoldVerification || ivlcsMissingAuthNumbers) {
			response = 'Are you sure you want to send to billing?' +
				(hasPrimaryInsurance ? '' : '<br>--No primary insurance is selected. ') +
				(ivlcTriggerPrimary ? '<br>--Not all covered codes on primary insurance marked covered. ' : '') +
				(ivlcTrigger23Other ? '<br>--Not all covered codes on non-primary insurance (i.e. secondary, tertiary, etc.) marked covered. ' : '') +
				(diagnosisCodeTrigger ? '<br>--There are no diagnosis codes attached to this prescription. ' : '') +
				(POS_Trigger ? '<br>--This is a medicare claim with the Place of Service set to 31/32 (Nursing Facility). ' : '') +
				(PECOSnotVerified ? '<br>--The primary or referring physician on this prescription is not PECOS verified. ' : '') +
				(PECOSoldVerification ? '<br>--The PECOS verification on the primary or referring physician is more than a year old. ' : '') +
				(ivlcsMissingAuthNumbers ? '<br>--At least one HCPCS is missing an authorization number. ' : '') +
				(plcTrigger ? '<br>--There are no modifiers on any HCPCS. ' : '') +
				(hasInactiveInsurance ? '<br>--At least one insurance is marked inactive. ' : '') +
				(hasUnverifedInsurance ? '<br>--At least one insurance is unverified.' : '') +
				(ivlcTriggerBillingUncoveredCode ? '<br>--At least one code will currently appear on a claim but is not marked as covered by that payer.' : '') +
				(ivlcTriggerNotBillingCoveredCode ? '<br>--At least one code is marked as covered by a payer and currently will not appear on that claim.' : '');
		}
		return response;
	};

	// this.checkForClaimFileWIP = function (patientId, prescriptionId) {
	// 	ClaimFactory.findByPatientId({patientId: patientId, claimFor: prescriptionId}).$promise.then(function (claims) {
	// 		angular.forEach(claims, function (claim) {
	// 			ClaimFileFactory.findByClaimId({claimId: claim.id}).$promise.then(function (response) {
	// 				_this.claimSubmissions = response;
	// 				if (_this.claimSubmissions.length > 0) {
	// 					_this.hasClaimFileWIP = true;
	// 				}
	// 			});
	//     });
	//   });
	// };

	this.openPrescriptionStatusHistoryModal = function (prescription) {
		var modalInstance = $uibModal.open({
			templateUrl: "prescriptionStatusHistoryModal.html",
			controller: 'PrescriptionStatusHistoryModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'lg',
			resolve: {
				prescriptionStatuses: function () {
					return _this.prescriptionStatuses;
				},
				prescription: function () {
					// NOTE: the _this.prescriptions array is NOT updated when we edit prescriptions because we fetch a new copy
					// and update that.  We need to do an explicit fetch here or we'll blow away any changes made in the WIP, etc
					return PrescriptionFactory.get({id: prescription.id}).$promise.then(function (response) {
						return response;
					});
				}
			}
		});

		modalInstance.result.then(function () {
			PrescriptionFactory.get({id: prescription.id}).$promise.then(function (response) {
				var index = _.findIndex(_this.prescriptions, ['id', prescription.id]);
				_this.prescription = response;
				_this.prescriptions[index].nymblStatus = response.nymblStatus;
				_this.prescriptions[index].nymblStatusId = response.nymblStatus.id;
			});
		});
	};

	this.openPhysicianDocumentationStatusHistoryModal = function (prescription) {
		var modalInstance = $uibModal.open({
			templateUrl: "physicianDocumentationStatusHistoryModal.html",
			controller: 'PhysicianDocumentationStatusHistoryModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'lg',
			resolve: {
				physicianDocumentationStatuses: function () {
					return _this.physicianDocumentationStatuses;
				},
				prescription: function () {
					// NOTE: the _this.prescriptions array is NOT updated when we edit prescriptions because we fetch a new copy
					// and update that.  We need to do an explicit fetch here or we'll blow away any changes made in the WIP, etc
					return PrescriptionFactory.get({id: prescription.id}).$promise.then(function (response) {
						return response;
					});
				}
			}
		});
		modalInstance.result.then(function () {
			PrescriptionFactory.findByPatientId({patientId: $stateParams.patientId}).$promise.then(function (response) {
				_this.prescriptions = response;
				_this.loadPrescriptionDetails(prescription.id);
			});
		});
	};

	var setTreatingPractitionerIfNecessaryAndAddClaim = function (data, claims) {

		var saveRx = false;
		if (data.treatingPractitionerId && (_this.prescription.treatingPractitionerId !== data.treatingPractitionerId)) {
			_this.prescription.treatingPractitionerId = data.treatingPractitionerId;
			saveRx = true;
		}


		//SCUM-4418: always set delivered on when the claim is created...
		if (data.dos !== null) { // SCRUM-5322: not when null
			_this.prescription.deliveredOn = $moment(data.dos).format("YYYY-MM-DD hh:mm:ss");
			saveRx = true;
		}

		if (saveRx) {
			PrescriptionFactory.save(_this.prescription).$promise.then(function (response) {
				_this.prescription = response;
				_addClaim(data, claims);
			}, function (error) {
				console.log(error.data.message);
				var errorMessage = "Changes were not saved!";
				if (error && error.data && error.data.message) {
					errorMessage += error.data.message;
					DiffModalService.popModal(error.data.diffs, error.data.message, _this.prescription.id, "Prescription");
				}
				UtilService.displayAlert("danger", errorMessage, "#header-alert-container");
			});
		} else {
			_addClaim(data, claims);
		}
	};

	var _addClaim = function (data, claims) {
		var dateOfService = $moment(data.dos).format("YYYY-MM-DD");
		ClaimFactory.addClaim({
			prescriptionId: _this.prescription.id,
			userId: data.userId,
			dateOfService: dateOfService,
			resend: claims ? claims.length : 0,
			billingBranchId: data.billingBranchId
		}).$promise.then(function (response) {
			ClaimService.profileLoad(PatientService.patient.id);
			_this.profileLoad(PatientService.patient.id);
			_this.sendSuccessful = true;
			_this.sendFail = false;
			UtilService.displayAlert("success", "Claim successfully added/updated.", "#l-code-selection-alert-container");
			NotificationService.buildClaimNotification(response, "A claim has been sent to billing and assigned to you");
		}, function (error) {
			_this.sendSuccessful = false;
			_this.sendFail = true;
			UtilService.displayAlert("danger", error, "#l-code-selection-alert-container");
		});
	};

	this.openLCodeAuthorizationModal = function (insuranceVerificationLCodes, prescriptionId, isLocked) {
		if (isLocked)
			return;
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/patient/work_in_progress/_l_code_authorization_modal.html',
			controller: 'LCodeAuthorizationCtrl',
			backdrop: 'static',
			keyboard: false,
			resolve: {
				insuranceVerificationLCodes: function () {
					return insuranceVerificationLCodes;
				}
			}
		});
		modalInstance.result.then(function () {
			_this.setActivePrescription(prescriptionId);
		});
	};

	this.openAbnCommentsModal = function (abnDTO) {
		var modalInstance = $uibModal.open({
			templateUrl: 'abn_comments_modal.html',
			controller: 'AbnCommentsCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				abnDTO: function () {
					return abnDTO;
				}
			}
		});
		modalInstance.result.then(function (abnDTO) {
			UserService.reloadAbnSectionInWIP = {action: abnDTO};
		});
		modalInstance.result.then(function (eFormDTO) {
			UserService.reloadEFormSectionInNotes = {action: eFormDTO};
		});
	};

	this.getPrescriptionsSubList = function (list) {
		var prescriptions = [];
		var children = [];
		angular.forEach(list, function (p, index) {
			if (p.parentId) {
				children.push(p);
			} else {
				prescriptions.push(p);
			}
		});
		angular.forEach(prescriptions, function (p, index) {
			p.$subPrescriptions = $filter('filter')(children, {parentId: p.id}, true);
		});
		return prescriptions;
	};

	this.faceToFaceAgeMonths = function (date) {
		if (_this.prescription && _this.prescription.faceToFaceExamDate && date) {
			return $moment(_this.prescription.faceToFaceExamDate).diff(date, 'months');
		} else {
			return 0;
		}
	};

	this.reloadPrescriptionAfterRentalChanges = function (prescriptionId, message) {
		_this.disableRentalButtons = true;
		PrescriptionFactory.findByPatientId({patientId: PatientService.patient.id}).$promise.then(function (response) {
			_this.prescriptions = response;
			_this.loadPrescriptionDetails(prescriptionId);
			ClaimService.profileLoad(PatientService.patient.id);
			_this.skipClaimSubmissionForRental = false;
			angular.forEach(_this.prescriptions, function (prescription) {
				_this.loadSectionInfo(prescription.id);
			});
			_this.disableRentalButtons = false;
			UtilService.displayAlert("success", message, "#rx-rental-alert-container");
		});
	};

	this.billDmeRental = function (prescription, skipClaimSubmissionForRental) {
		if (prescription.isRentalPaused) {
			alert("You cannot bill a paused rental.");
			return;
		}
		_this.disableRentalButtons = true;
		PrescriptionFactory.billDmeRental({
			id: prescription.id,
			submitRental: !skipClaimSubmissionForRental
		}).$promise.then(function () {
			// _this.profileLoad(PatientService.patient.id);
			_this.reloadPrescriptionAfterRentalChanges(prescription.id, "Next Rental Claim Generated");
		});
	};

	this.editRentalPeriod = function (prescription) {
		var modalInstance = $uibModal.open({
			templateUrl: 'editRentalModal.html',
			controller: 'EditRentalModalCtrl',
			backdrop: 'static',
			keyboard: false,
			resolve: {
				prescription: function () {
					return prescription;
				}
			}
		});

		modalInstance.result.then(function (result) {
			_this.prescription.rentalBillingPeriods = result.rentalBillingPeriods;
			_this.prescription.rentalStartPeriod = result.rentalStartPeriod;
			_this.prescription.nextPrescriptionDate = result.nextPrescriptionDate;
			PrescriptionFactory.save(_this.prescription).$promise.then(function (response) {
				_this.profileLoad(PatientService.patient.id);
				_this.reloadPrescriptionAfterRentalChanges(prescription.id, "Rental Updated");
			});
		});
	};

	this.cancelBillDmeRentals = function (prescription) {
		prescription.nextPrescriptionDate = null;
		prescription.isRentalPaused = false;
		PrescriptionFactory.save(prescription).$promise.then(function () {
			_this.profileLoad(PatientService.patient.id);
			_this.reloadPrescriptionAfterRentalChanges(prescription.id, "Rental canceled");
		});
	};

	this.toggleIsRentalPaused = function (prescription) {
		prescription.isRentalPaused = !prescription.isRentalPaused;
		PrescriptionFactory.save(prescription).$promise.then(function () {
			_this.profileLoad(PatientService.patient.id);
			_this.reloadPrescriptionAfterRentalChanges(prescription.id, "Rental has been " + (prescription.isRentalPaused ? "paused" : "unpaused"));
		});
	};

	this.verifyInsurance = function (dto) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/patient/work_in_progress/_insurance_verification_modal_form.html',
			controller: 'InsuranceVerificationCtrl',
			backdrop: 'static',
			keyboard: false,
			resolve: {
				dto: function () {
					return dto;
				}
			}
		});

		modalInstance.result.then(function (result) {
			dto = result;
			_this.loadPrescriptionDetails(dto.insuranceVerification.prescriptionId);
		});
	};

	this.importXMLQuote = function () {

		if (!InsuranceService.patientInsurancesVerification || !InsuranceService.patientInsurancesVerification.length) {
			UtilService.displayAlert('danger', '<p>No insurance found.</p>', '#header-alert-container');
			return;
		}
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/patient/_import_xml_quote_modal.html',
			controller: 'ImportXMLQuoteModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'xlg',
			resolve: {
				dto: function () {
					return InsuranceService.patientInsurancesVerification[0];
				},
				prescriptionId: function () {
					return _this.prescription.id;
				}
			}
		});

		modalInstance.result.then(function (quote) {
			if (!quote || !quote.lines || !quote.lines.length) {
				toastr.clear();
				toastr.warning('There are no quote items to import', 'Quote Not Imported', {
					timeOut: 5000,
					closeButton: true,
					closeHtml: '<i class="fa fa-times-circle"></i>'});
				return;
			}
			PrescriptionLCodeFactory.importFromQuote(quote).$promise.then(function (response) {
				var message = '';
				if (response.itemIds && response.itemIds.length > 0) {
					message += ', ' + response.itemIds.length + ' Vendor Item' + (response.itemIds.length > 1 ? 's' : '');
				}
				if (response.plcIds && response.plcIds.length > 0) {
					message += ', ' + response.plcIds.length + ' PLC' + (response.plcIds.length > 1 ? 's' : '');
				}
				if (quote.createPurchaseOrder && response.purchaseOrderId) {
					message += ', Purchase Order #' + response.purchaseOrderId;
					if (response.purchaseOrderItemIds && response.purchaseOrderItemIds.length > 0) {
						message += ' with ' + response.purchaseOrderItemIds.length
							+ ' Purchase Order Item' + (response.purchaseOrderItemIds.length > 1 ? 's' : '');
					}
				} else if (quote.createShoppingCart && response.shoppingCartIds && response.shoppingCartIds.length > 0) {
					message += ', ' + response.shoppingCartIds.length + ' Shopping Cart item' + (response.shoppingCartIds.length > 1 ? 's' : '');
				}
				if (message.length > 2) {
					// trim the leading ', ' and prepend 'Created or updated '
					message = 'Created or updated ' + message.substring(2);
				}
				toastr.clear();
				toastr.success(message, 'Quote imported successfully', {
					timeOut: 5000,
					closeButton: true,
					closeHtml: '<i class="fa fa-times-circle"></i>'});
				_this.setActivePrescription(_this.prescription.id);
			}, function (err) {
				console.log(err);
				UtilService.displayAlert('danger', '<p>There has been an error importing the quote.</p>', '#header-alert-container');
			});
		});
	};

	this.processLCodes = function () {
		angular.forEach(_this.prescriptionLCodes, function (plCode) {
			if (plCode.orderNum === 1 && _this.surveyLinkType === 'l_code') {
				var spanishText = _this.prescription.patient.spanishText;
				_this.surveyWrapper = {
					surveyName: spanishText ? plCode.lCode.spanishSurveyLinkName : plCode.lCode.surveyLinkName,
					surveyLink: spanishText ? plCode.lCode.spanishSurveyLink : plCode.lCode.surveyLink
				};
			}
			var insuranceCompanyId = _this.prescription.patientInsurance.insuranceCompany.id;
			var lCodeId = plCode.lCode.id;
			_this.lCodeAlerts = [];
			_this.lCodeAlertsTrackingArray = [];
			if (plCode.lCode.alert && !_.includes(_this.prescriptionLCodeAlertsTrackingArray, plCode.lCode.id)) {
				_this.prescriptionLCodeAlertsTrackingArray.push(plCode.lCode.id);
				_this.prescriptionLCodeAlerts.push(plCode.id);
			}
			InsuranceLCodeAlertFactory.findByInsuranceCompanyAndLCode({
				insuranceCompanyId: insuranceCompanyId,
				lCodeId: lCodeId
			}).$promise.then(function (alerts) {
				angular.forEach(alerts, function (alert) {
					if (alert.id && !_.includes(_this.lCodeAlertsTrackingArray, alert.id)) {
						_this.lCodeAlertsTrackingArray.push(alert.id);
						_this.lCodeAlerts.push(alert);
					}
				});
			}, function (err) {
				console.log(err);
			});
			// SCRUM-5005: see if any secondary and beyonds have LCode alerts:
			angular.forEach(InsuranceService.patientInsurancesVerification, function (pIV, index) {
				if (pIV) {
					if (pIV.insuranceVerification && pIV.patientInsurance && pIV.insuranceVerification.carrierType !== "inactive" && pIV.insuranceVerification.carrierType !== "primary") {
						InsuranceLCodeAlertFactory.findByInsuranceCompanyAndLCode({
							insuranceCompanyId: pIV.patientInsurance.insuranceCompany.id,
							lCodeId: lCodeId
						}).$promise.then(function (alerts) {
							angular.forEach(alerts, function (alert) {
								if (alert.id && !_.includes(_this.lCodeAlertsTrackingArray, alert.id)) {
									_this.lCodeAlertsTrackingArray.push(alert.id);
									_this.lCodeAlerts.push(alert);
								}
							});
						}, function (err) {
							console.log(err);
						});
					}
				}
			});
		});
	};

	this.signAsChanged = function () {
		_this.isReferringPhysician = !_this.isPrimaryCarePhysician;
	};

	$rootScope.$on('insuranceVerificationsLoaded', function () {
		_this.processLCodes();
	});

	this.updateInsuranceTypeModal = function (prescription) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/patient/work_in_progress/insurance_type/_insurance_type_modal.html',
			controller: 'InsuranceTypeCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'lg',
			resolve: {
				prescription: function () {
					return prescription;
				}
			}
		});
		modalInstance.result.then(function (result) {
			if (result.action === 'save') {
				const params = {
					primaryChanging: result.primaryChanging,
					willCreateClaim: result.willCreateClaim,
					newPrimaryPatientInsuranceId: result.newPrimaryPatientInsuranceId
				};
				InsuranceVerificationFactory.updateVerificationType(params, _this.insuranceService.patientInsurancesVerification).$promise.then(function (response) {
					_this.insuranceService.loadPatientInsurancesVerification(prescription.id);
					if (response && result.primaryChanging) {
						let url;
						if (response.claimId) {
							url = '#/app/billing/view/' + response.claimId;
						} else {
							url = '#/app/patient/profile/' + prescription.patientId + '?tab=work-in-progress&prescriptionId=' + response.prescriptionId + '&section=prescription_details';
						}
						if (result.newPrimaryPatientInsuranceId !== prescription.patientInsuranceId) {
							_this.profileLoad(PatientService.patient.id);
							alert("Primary insurance carrier updated.");
						}
						window.open(url, '_self');
					} else {
						_this.insuranceService.loadPatientInsurancesVerification(prescription.id);
						$rootScope.$broadcast('localStorageObjectLoadComplete');
					}
				});
			} else {
				_this.insuranceService.loadPatientInsurancesVerification(prescription.id);
				$rootScope.$broadcast('localStorageObjectLoadComplete');
			}
		});
	};

	this.getPrescription = function (prescriptionId) {
		var prescription = null;
		angular.forEach(_this.prescriptions, function (rx) {
			if (rx.id == prescriptionId) {
				prescription = rx;
			}
		});
		return prescription;
	};

	this.updatePlc = function (plc) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve(
					PrescriptionLCodeFactory.save(plc).$promise.then(function (response) {
						return response;
					})
				);
			}, 500);
		});
	};
}
