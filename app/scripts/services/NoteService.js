app.service('NoteService', NoteService);
NoteService.$inject = ['$uibModal', '$filter', '$moment', '$q', 'uiUploader', 'CacheFactory', 'NoteTypesFactory', 'NoteFactory',
	'TemplateFactory', 'UserService', 'PrescriptionService', 'PatientService', 'AppointmentService', 'UtilService',
	'EvaluationFormFactory', 'SystemSettingFactory', 'NoteChildTypesFactory', 'DiffModalService', 'SessionService'];

function NoteService($uibModal, $filter, $moment, $q, uiUploader, CacheFactory, NoteTypesFactory, NoteFactory, TemplateFactory,
					 UserService, PrescriptionService, PatientService, AppointmentService, UtilService,
					 EvaluationFormFactory, SystemSettingFactory, NoteChildTypesFactory, DiffModalService, SessionService) {

	var _this = this;

	this.noteTypes = NoteTypesFactory.get();
	this.childTypes = NoteChildTypesFactory.get();

	// PERF: just drives the dropdown until the user selects one and calls changeTemplate...but that guy fetches the template again...
	// PERF: just DTO this.
	this.templates = TemplateFactory.searchTemplates();
	this.currentUserId = UserService.getCurrentUserId();
	this.currentParentId = null;

	this.init = function () {
		_this.noteLoading = false;
		_this.prescriptionNotes = [];
		_this.mostRecentCorrections = [];
		_this.prescriptionList = [];
		_this.checked = [];
		_this.checkAllNotes = [];
		_this.disableCheckAllNotes = [];
		_this.noNoteType = [];
		_this.noSubject = [];
		_this.noAppointment = [];
		_this.noBody = [];
		_this.noteTemplateId = [];
		_this.appointment = [];
		_this.treatingPractitionerId = [];
		_this.validNoteTreatingPractitioners = [];
		_this.note = [];
		_this.subject = [];
		_this.noteType = [];
		_this.parentId = [];
		_this.evaluationFormsByPrescriptionId = [];
		_this.subjectRequired = true;
		_this.appointmentRequiredForClinicalNote = false;
		_this.prescriptionList.push({
			id: 0,
			deviceTypeId: 0,
			deviceType: {
				name: "GENERAL NOTES"
			}
		});
		_this.enableAllNotes = [];
		_this.enableAllNotes[0] = true;
		_this.evaluationFormsByPrescriptionId[0] = [];
		_this.prescriptionNotes[0] = []; //init General Notes array
		_this.validNoteTreatingPractitioners[0] = [];
		_this.mostRecentCorrections[0] = null;
		_this.checked[0] = [];
		_this.checkAllNotes[0] = false;
		_this.disableCheckAllNotes[0] = true;
		angular.forEach(_this.noteTypes, function (type) {
			if (type.lowerCaseName != null) {
				_this.checked[0].push(type.lowerCaseName);
				_this.checked[0][type.lowerCaseName] = true;
			}
		});
		_this.checkedTotal = _this.checked[0].length;
		_this.file = undefined;
		_this.size = undefined;
		_this.isAiNote = false;
		_this.mediaRecorder = undefined;
		_this.audioChunks = [];
		_this.aiNotesEnabled = false;
		_this.disableStartButton = false;
		_this.disableStopButton = true;
		_this.disablePauseButton = true;
		_this.disableAudioSaveButton = true;
		_this.pauseContext = "Pause";
		_this.mediaStream = undefined;
		_this.uploading = false;
		_this.isAudioRecording = false;
		_this.audioDuration = Infinity;
		_this.audioDisplay = "0:00";
		_this.stopped = false;
		_this.isStopping = false;
	};

	this.profileLoad = function (patientId) {
		_this.init();
		_this.noteLoading = true;
		var list = PrescriptionService.getPrescriptionsSubList(PrescriptionService.prescriptions);
		angular.forEach(list, function (prescription, index) {
			_this.prescriptionList.push(prescription);
			if (_this.evaluationFormsByPrescriptionId[prescription.id] === undefined) {
				_this.evaluationFormsByPrescriptionId[prescription.id] = [];
			}
			EvaluationFormFactory.findByPrescriptionId({prescriptionId: prescription.id}).$promise.then(function (response) {
				if (response.length > 0) {
					_this.evaluationFormsByPrescriptionId[prescription.id] = response;
				}
			});
			if (_this.prescriptionNotes[prescription.id] === undefined) {
				_this.prescriptionNotes[prescription.id] = [];
			}
			if (_this.validNoteTreatingPractitioners[prescription.id] === undefined) {
				_this.validNoteTreatingPractitioners[prescription.id] = [];
				if (prescription.treatingPractitioner != null) _this.validNoteTreatingPractitioners[prescription.id].push(prescription.treatingPractitioner);
			}
			angular.forEach(prescription.$subPrescriptions, function (sub, index) {
				if (_this.prescriptionNotes[sub.id] === undefined) {
					_this.prescriptionNotes[sub.id] = [];
				}
			})

			angular.forEach(_this.noteTypes, function (type) {
				if (_this.checked[prescription.id] === undefined) _this.checked[prescription.id] = [];
				if (type.lowerCaseName != null) {
					_this.checked[prescription.id].push(type.lowerCaseName);
					_this.checked[prescription.id][type.lowerCaseName] = true;
					_this.enableAllNotes[prescription.id] = true;
				}
				angular.forEach(prescription.$subPrescriptions, function (sub, index) {
					_this.checked[sub.id] = _this.checked[prescription.id];
				})
			});
			_this.noNoteType[prescription.id] = false;
		});


		SystemSettingFactory.findBySectionAndField({
			section: "general",
			field: "require_appt_on_clinical_note_enabled"
		}).$promise.then(function (response) {
			_this.appointmentRequiredForClinicalNote = response.value === 'Y';
		});

		SystemSettingFactory.findBySectionAndField({
			section: "general",
			field: "ai_notes_on"
		}).$promise.then(function (response) {
			_this.aiNotesEnabled = response.value === 'Y';
		});

		SystemSettingFactory.findBySectionAndField({
			section: "general",
			field: "use_note_subject"
		}).$promise.then(function (setting) {
			_this.subjectRequired = setting.value === 'Y';
			NoteFactory.findByPatientIdForPatientProfile({patientId: patientId}).$promise.then(function (notes) {
				// notes = orderBy(notes, 'createdAt', true);
				_this.prescriptionNotes = [];
				_this.checkedNotes = [];
				_this.mostRecentCorrections = [];
				angular.forEach(notes, function (note, index) {
					// note.$checked = true;
					var i = $.inArray(note, _this.checkedNotes);
					if (i === -1) {
						_this.checkedNotes.push(note.id);
					}
					if (note.prescriptionId === undefined) note.prescriptionId = 0;
					if (_this.prescriptionNotes[note.prescriptionId] === undefined) _this.prescriptionNotes[note.prescriptionId] = [];
					_this.prescriptionNotes[note.prescriptionId].push(note);
					if (note.parentId != null && note.childType === 'correction' &&
						(_this.mostRecentCorrections[note.parentId] == null || note.id > _this.mostRecentCorrections[note.parentId].id)) {
						_this.mostRecentCorrections[note.parentId] = note;
					}
					_this.disableCheckAllNotes[note.prescriptionId] = false;
					if (note.noteType === 'patient_summary') {
						angular.forEach(_this.prescriptionList, function (prescription, index) {
							if (prescription.id === note.prescriptionId) {
								_this.prescriptionList[index].$patientSummary = true;
							}
						});
					}
				});

				_this.noteLoading = false;

				// now populate the appointments with the same notes
				//AppointmentService.loadAppointmentNotes(_this.prescriptionNotes);
			});
		});
	};


	this.toggleCheckAllNotes = function (prescriptionId) {
		angular.forEach(_this.prescriptionNotes[prescriptionId], function (note) {
			if (_this.checked[prescriptionId].includes(note.noteType)) {
				note.$checked = _this.checkAllNotes[prescriptionId];
			}
		});
	};

	this.toggleCheckAllNoteTypes = function (prescriptionId) {
		if(_this.enableAllNotes[prescriptionId]===true){
			_this.checked[prescriptionId] = [];
			angular.forEach(_this.noteTypes, function (type) {
				_this.filterType(prescriptionId,type.lowerCaseName, true);
			});

			angular.forEach(_this.checked[prescriptionId], function (type) {
				_this.checked[prescriptionId][type] = true;
			});

		}else{
			angular.forEach(_this.noteTypes, function (type) {
				if(!_this.checked[prescriptionId].includes(type.lowerCaseName)){
					_this.checked[prescriptionId].push(type.lowerCaseName);
				}
				_this.checked[prescriptionId][type.lowerCaseName] = false;
			});
			angular.forEach(_this.noteTypes, function (type) {
				_this.filterType(prescriptionId,type.lowerCaseName, true);
			});
		}
	};

	this.hasNotes = function () {
		var available = false;
		angular.forEach(_this.prescriptionNotes, function (note, index) {
			if (note.length > 0) {
				available = true;
			}
		});
		return available;
	};

	this.isValidTreatingOrSupervisingPractitioner = function (note, parmPrescription) {
		var userId = UserService.getCurrentUserId();

		var appointmentSupervisingPractitioner = undefined;
		var prescriptionTreatingPractitioner = undefined;
		var appointmentAttendingId = undefined;
		if(note){
			if(note.appointment){
				appointmentSupervisingPractitioner = note.appointment.userFourId;
				appointmentAttendingId = note.appointment.userId;
			}
			if(note.prescription) {
				prescriptionTreatingPractitioner = note.prescription.treatingPractitionerId;
			}
		} else if (parmPrescription){
			var appointment = _this.appointment[parmPrescription.id];
			if (appointment) {
				appointmentSupervisingPractitioner = appointment.userFourId;
				appointmentAttendingId = appointment.userId;
			}
			prescriptionTreatingPractitioner = parmPrescription.treatingPractitionerId;
		} else {
			console.log("-error determining Treating/Supervising Practitioner-")
			return false;
		}

		if (appointmentSupervisingPractitioner && appointmentSupervisingPractitioner === userId) {
			return true;
		} else if (!appointmentSupervisingPractitioner && appointmentAttendingId && appointmentAttendingId === userId) { // for old appointments and ones where the attending is a valid practitioner
			return true;
		} else if (prescriptionTreatingPractitioner === userId) {
			return true;
		}
		return false;
	};

	this.openAiNotes = function(link) {
		UtilService.openV2Link(link);
	}


	this.isValidCareExtenderOrResident = function (note, parmPrescription) {
		var userId = UserService.getCurrentUserId();

		var appointmentSupervisingPractitioner = undefined;
		var appointmentAttendingId = undefined;
		var prescriptionTreatingPractitioner = undefined;
		var prescriptionResidentId = undefined;
		if(note){
			if(note.appointment){
				appointmentSupervisingPractitioner = note.appointment.userFourId;
				appointmentAttendingId = note.appointment.userId;
			}
			if(note.prescription) {
				prescriptionTreatingPractitioner = note.prescription.treatingPractitionerId;
				prescriptionResidentId = note.prescription.residentId;
			}
		} else if (parmPrescription){
			var appointment = _this.appointment[parmPrescription.id];
			if(appointment){
				appointmentSupervisingPractitioner = appointment.userFourId;
				appointmentAttendingId = appointment.userId;
			}
			prescriptionTreatingPractitioner = parmPrescription.treatingPractitionerId;
			prescriptionResidentId = parmPrescription.residentId;
		} else{
			console.log ("-error determining Treating/Supervising Practitioner-")
			return false;
		}
		if(appointmentSupervisingPractitioner) {
			if (appointmentSupervisingPractitioner !== userId && appointmentAttendingId === userId) {
				return true;
			}
		} else if (appointmentAttendingId === userId) {
			return false; // can save and publish (old appointment or an actual practitioner)
		} else if (prescriptionTreatingPractitioner !== userId && prescriptionResidentId === userId) {
			return true;
		}
		return false;
	};

	this.addAppointmentPractitionersToValidNoteTreatingPractitioners = function (prescription, appointment) {
		if (prescription == null || appointment == null) return;
		_this.validNoteTreatingPractitioners[prescription.id] = [];
		if (prescription.treatingPractitioner != null) {
			_this.validNoteTreatingPractitioners[prescription.id].push(prescription.treatingPractitioner);
		}
		if (appointment.userId != null && appointment.userId !== prescription.treatingPractitionerId && appointment.userFourId == null) { // Appointment Attending Practitioner
			_this.validNoteTreatingPractitioners[prescription.id].push({
				id: appointment.userId,
				firstAndLastName: appointment.practitioner
			});
		}
		if (appointment.userFourId != null && appointment.userFourId !== appointment.userId
			&& appointment.userFourId !== prescription.treatingPractitionerId) { // Appointment Supervising Practitioner
			_this.validNoteTreatingPractitioners[prescription.id].push({
				id: appointment.userFourId,
				firstAndLastName: appointment.practitionerFour
			});
		}
	};

	this.setDefaultNoteTreatingPractitioner = function (prescription, appointment) {
		if (prescription == null) return;

		var defaultTreatingPractitionerId = null;
		if (appointment != null && appointment.userFourId != null) {
			defaultTreatingPractitionerId = appointment.userFourId;
		} else if (appointment != null && appointment.userId != null) {
			defaultTreatingPractitionerId = appointment.userId;
		} else if (prescription.treatingPractitionerId != null) {
			defaultTreatingPractitionerId = prescription.treatingPractitionerId;
		}
		_this.treatingPractitionerId[prescription.id] = defaultTreatingPractitionerId;
	};

	this.changeTemplate = function (prescriptionId, patientId, modalNote) {
		prescriptionId = prescriptionId || 0;
		var templateId = _this.noteTemplateId[prescriptionId];

		if (templateId !== "") {
			var timezone = $moment.tz.guess(true);
			TemplateFactory.loadByIdPatientPrescription({
				templateId: templateId,
				patientId: patientId,
				prescriptionId: prescriptionId,
				userTimeZone: timezone
			}).$promise.then(function (response) {
				if (_this.note[prescriptionId] === undefined) _this.note[prescriptionId] = [];
				_this.note[prescriptionId] = _this.note[prescriptionId] + response.data;
				if(modalNote){
					modalNote.note = modalNote.note + response.data;
				}
			});
		}
	};

	this.filterType = function (prescriptionId, type, bulkUpdate) {
		var i = $.inArray(type, _this.checked[prescriptionId]);

		if (i > -1) {
			// Off
			_this.checked[prescriptionId].splice(i, 1);

			if(_this.enableAllNotes[prescriptionId]===true && !bulkUpdate){
				_this.enableAllNotes[prescriptionId]=false
			}
			angular.forEach(_this.prescriptionNotes[prescriptionId], function (note) {
				if (_this.checkedNotes.includes(note.id) && note.noteType === type) {
					var j = _this.checkedNotes.indexOf(note.id);
					_this.checkedNotes.splice(j, 1);
					note.$checked = false;
				}
			});
		} else {
			// On
			_this.checked[prescriptionId].push(type);

			if(_this.enableAllNotes[prescriptionId]===false && !bulkUpdate && this.checked[prescriptionId].length === _this.checkedTotal){
				_this.enableAllNotes[prescriptionId]=true
			}
			angular.forEach(_this.prescriptionNotes[prescriptionId], function (note) {
				if (!_this.checkedNotes.includes(note.id) && note.noteType === type) {
					_this.checkedNotes.push(note.id);
				}
				if (_this.checkAllNotes[prescriptionId]) note.$checked = true;
			});
		}
		_this.disableCheckAllNotes[prescriptionId] = _this.checkIfDisableAll(prescriptionId);
		if (_this.disableCheckAllNotes[prescriptionId]) _this.checkAllNotes[prescriptionId] = false;
		// note.$checked
	};

	this.checkIfDisableAll = function (prescriptionId) {
		var noFiltersChecked = _this.checked[prescriptionId].length === 0;
		var noNotes = true;
		angular.forEach(_this.prescriptionNotes[prescriptionId], function (note) {
			if (note['noteType'] !== undefined && _this.checked[prescriptionId] !== undefined && _this.checked[prescriptionId].length) {
				if ($.inArray(note['noteType'], _this.checked[prescriptionId]) > -1) {
					noNotes = false;
				}
			}
		});
		var result = noFiltersChecked || noNotes;
		return result;
	};

	this.printPatientProfileNotes = function (prescriptionId, patientId) {
		var notesToPrint = [];
		angular.forEach(_this.prescriptionNotes[prescriptionId], function (note) {
			if (note.$checked) {
				notesToPrint.push(note.id);
			}
		});
		UtilService.openPrintScreen('prescription_notes?prescriptionId=' + prescriptionId + '?patientId=' + patientId + '?noteIds=' + notesToPrint.join(','));
	};

	this.printClaimDetailsNotes = function (prescriptionId, patientId) {
		var notesToPrint = [];
		angular.forEach(_this.prescriptionNotes[prescriptionId], function (note) {
			if (note.$checked) {
				notesToPrint.push(note.id);
			}
		});
		UtilService.openPrintScreen('prescription_notes?prescriptionId=' + prescriptionId + '?patientId=' + patientId + '?noteIds=' + notesToPrint.join(','));
	};

	this.typeFilter = function (note) {
		if (note === undefined) return;
		var prescriptionId = note.prescriptionId;
		if (note['noteType'] !== undefined) {
			if (_this.checked[prescriptionId] !== undefined && _this.checked[prescriptionId].length) {
				if ($.inArray(note['noteType'], _this.checked[prescriptionId]) < 0) {
					return;
				}
				return note;
			}
		}
	};

	this.viewNote = function (note, isCorrection) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/notes/modal_note.html',
			controller: 'PatientNoteCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'xlg',
			resolve: {
				noteId: function () {
					var mostRecentCorrection = _this.mostRecentCorrections[note.id];
					return isCorrection && mostRecentCorrection != null ? mostRecentCorrection.id : note.id;
				},
				isCorrection: function () {
					return isCorrection;
				},
				noteTypes: function () {
					return _this.noteTypes;
				},
				appointments: function () {
					return AppointmentService.nonCancelledAppointments;
				},
				prescriptionSummaryNotes: function () {
					var rxSummaryNotes = [];
					angular.forEach(_this.prescriptionNotes[note.prescriptionId], function (rxNote) {
						if (rxNote.noteType === 'patient_summary') rxSummaryNotes.push(rxNote);
					});
					return rxSummaryNotes;
				}
			}
		});

		modalInstance.result.then(function (result) {
			var action = result.action;
			var modalNote = result.note;
			modalNote.action = action;
			if (action === 'delete') {
				if (confirm('Are you sure you want to delete the draft note?')) {
					if (modalNote.appointmentId) {
						delete modalNote.appointmentId;
					}
					NoteFactory.delete({id: modalNote.id}).$promise.then(function () {
						_this.profileLoad(PatientService.patient.id);
					});
				}
			} else if (action === 'unpublish') {
				modalNote.published = 0;
				// if (/\s/g.test(note.createdAt))
				// 	note.createdAt = $moment(note.createdAt).local('L');
				// if (/\s/g.test(note.publishedAt))
				// 	note.publishedAt = $moment(note.publishedAt).local('L');
				NoteFactory.saveDTO(modalNote).$promise.then(function (response) {
					_this.profileLoad(PatientService.patient.id);
				}, function (err) {
					var errorMessage = "Changes were not saved!  ";
					if (err && err.data && err.data.message) {
						errorMessage = '<p>' + err.data.message + '</p>';
						UtilService.displayAlert("danger", errorMessage, "#header-alert-container");
						DiffModalService.popModal(err.data.diffs, err.data.message, modalNote.id, "Note");
					}
				});
			} else {
				if (modalNote.noteType !== 'clinical') modalNote.treatingPractitionerId = null;

				if (modalNote.noteType === 'clinical' && !modalNote.treatingPractitionerId) {
					alert(
						'This clinical note does not have a treating practitioner set from the prescription or the appointment. ' +
						'The note will not be able to be published until a Treating Practitioner has been selected. If you do not ' +
						'see any Treating Practitioners in the dropdown menu, add a Treating Practitioner to the note\'s prescription, ' +
						'or link the note to an appointment with an Attending or Supervising Practitioner.',
					);
				}

				modalNote.published = action === 'publish';
				if (modalNote.prescriptionId === 0) delete modalNote.prescriptionId;
				NoteFactory.saveDTO(modalNote).$promise.then(function (response) {
					_this.profileLoad(PatientService.patient.id);
				}, function (err) {
					var errorMessage = "Changes were not saved!  "
					if (err && err.data && err.data.message) {
						errorMessage = '<p>' + err.data.message + '</p>';
						UtilService.displayAlert("danger", errorMessage, "#header-alert-container");
						DiffModalService.popModal(err.data.diffs, err.data.message, modalNote.id, "Note");
					}
				});
			}
		});
	};

	this.checkForPrint = function (note) {
		if (note.$checked) {
			_this.checkedNotes.push(note.id);
		} else {
			_this.checkedNotes.splice(_this.checkedNotes.indexOf(note.id), 1);
		}
	};

	this.syncSaveNote = function (prescription, action) {
		return $q(function (resolve, reject) {
			_this.saveNote(prescription, action).then(function (note) {
				resolve(note);
			}).catch(function (error) {
				reject(error);
			});
		});
	}

	this.initializeNoteCache = function () {
		if (!CacheFactory.get('noteCache')) {
			CacheFactory('noteCache', {
				maxAge: 60 * 60 * 1000 * 24, // Items added to this cache expire after 1 day
				deleteOnExpire: 'aggressive', // Items will be deleted from this cache right when they expire.
				storageMode: 'localStorage' // This cache will use `localStorage`.
			});
		}
	};

	this.saveNote = function (prescription, action, claimId) {
		var prescriptionId = prescription.id;
		var noteCache;
		var noteFromCache;
		if (CacheFactory.get('noteCache')) {
			CacheFactory.get('noteCache').get('/patient/' + PatientService.patient.id + '/prescription/note/' + prescriptionId);
			if (CacheFactory.get('noteCache').get('/patient/' + PatientService.patient.id + '/prescription/note/' + prescriptionId)) {
				//alert("saveNoteByPrescriptionId content = " + noteFromCache.content + " for patient id = " + PatientService.patient.id + " for prescriptionId = " + prescriptionId);
				_this.note[prescriptionId] = CacheFactory.get('noteCache').get('/patient/' + PatientService.patient.id + '/prescription/note/' + prescriptionId).content;
			}
		}
		_this.noNoteType[prescriptionId] = _this.noteType[prescriptionId] === undefined;
		if (action === 'draft' && !_this.subject[prescriptionId] && _this.subjectRequired) {
			_this.subject[prescriptionId] = 'Save As Draft Generated Subject Line';
		}

		_this.noSubject[prescriptionId] = !_this.subject[prescriptionId];
		_this.noBody[prescriptionId] = !_this.note[prescriptionId];
		var showNoAppointmentError = (action === 'publish'
			&& _this.noteType[prescriptionId] === 'clinical'
			&& _this.appointmentRequiredForClinicalNote && !_this.appointment[prescriptionId]);
		_this.noAppointment[prescriptionId] = showNoAppointmentError;
		if (!_this.noNoteType[prescriptionId] && (!_this.noSubject[prescriptionId] || !_this.subjectRequired)
			&& !_this.noBody[prescriptionId] && !showNoAppointmentError) {
			_this.noNoteType[prescriptionId] = false;
			_this.noSubject[prescriptionId] = false;
			_this.noAppointment[prescriptionId] = false;
			_this.noBody[prescriptionId] = false;
			var note = {
				id: 0,
				patientId: parseInt(PatientService.patient.id),
				note: _this.note[prescriptionId],
				subject: _this.subject[prescriptionId],
				userId: UserService.getCurrentUserId(),
				noteType: _this.noteType[prescriptionId],
				appointmentId: _this.appointment[prescriptionId] ? _this.appointment[prescriptionId].id : null,
				prescriptionId: prescriptionId,
				published: false,
				parentId: _this.currentParentId,
				// treatingPractitionerId: (_this.appointment[prescriptionId] && _this.appointment[prescriptionId].userFourId)
				//   ? _this.appointment[prescriptionId].userFourId : prescription.treatingPractitionerId,
				treatingPractitionerId: _this.treatingPractitionerId[prescriptionId] ? _this.treatingPractitionerId[prescriptionId] : null,
				action: action
			};

			// if(!note.treatingPractitionerId && note.appointmentId && _this.appointment[prescriptionId] && _this.appointment[prescriptionId].userId) {
			// 	note.treatingPractitionerId = _this.appointment[prescriptionId].userId;
			// }

			if (note.prescriptionId === 0) {
				delete note.prescriptionId;
			}

			if (claimId)
				note.claimId = claimId;

			if (note.noteType !== 'clinical') note.treatingPractitionerId = null;

			if (note.noteType === 'clinical' && !note.treatingPractitionerId && !confirm(
				'This clinical note does not have a treating practitioner set from the prescription or the appointment. ' +
				'The note will not be able to be published until a Treating Practitioner has been selected. If you do not ' +
				'see any Treating Practitioners in the dropdown menu, add a Treating Practitioner to the note\'s prescription, ' +
				'or link the note to an appointment with an Attending or Supervising Practitioner. Are you sure you want to save?')) {
				UtilService.displayAlert('danger', '<p>Note save canceled.</p>', '#note-alert-container');
				return;
			}

			note.published = action === 'publish';
			_this.saving = true;
			return NoteFactory.saveDTO(note).$promise.then(function (note) {
				_this.saving = false;
				if (CacheFactory.get('noteCache')) {
					CacheFactory.get('noteCache').put('/patient/' + PatientService.patient.id + '/prescription/note/' + prescriptionId, {
						note: note
					});
				}
				UtilService.displayAlert('success', '<p>Note saved successfully.</p>', '#note-alert-container');
				if (_this.prescriptionNotes[prescriptionId] === undefined)
					_this.prescriptionNotes[prescriptionId] = [];
				note.prescriptionId = prescriptionId;
				if (note.parentId) {
					var i = 0;
					angular.forEach(_this.prescriptionNotes[prescriptionId], function (n, index) {
						if (n.id === note.parentId) {
							i = index + 1;

						}
					});
					_this.prescriptionNotes[prescriptionId].splice(i, 0, note);
				} else {
					_this.prescriptionNotes[prescriptionId].unshift(note);
				}
				if (note.noteType === 'patient_summary') {
					angular.forEach(_this.prescriptionList, function (prescription, index) {
						if (prescription.id === note.prescriptionId) {
							_this.prescriptionList[index].$patientSummary = true;
						}
					});
				}
				_this.note[prescriptionId] = "";
				_this.subject[prescriptionId] = "";
				_this.noteType[prescriptionId] = undefined;
				_this.appointment[prescriptionId] = undefined;
				_this.treatingPractitionerId[prescriptionId] = undefined;
				_this.noteTemplateId[prescriptionId] = undefined;
				_this.cancelNote(prescriptionId);
				return note;
			}, function (err) {
				_this.saving = false;
				console.log(err);

				if (CacheFactory.get('noteCache') && CacheFactory.get('noteCache').info('/patient/' + PatientService.patient.id + '/prescription/note/' + prescriptionId) != undefined) {
					CacheFactory.get('noteCache').remove('/patient/' + PatientService.patient.id + '/prescription/note/' + prescriptionId);
				}

				if (err && err.data && err.data.message) {
					var errorMessage = '<p>' + err.data.message + '</p>';
					UtilService.displayAlert("danger", errorMessage, "#header-alert-container");
					DiffModalService.popModal(err.data.diffs, err.data.message, note.id, "Note");
				}
				UtilService.displayAlert('danger', '<p>Note saved failed. If this display disappears, you can find the error in the browser console.</p>{{err}}', '#note-alert-container');
				_this.cancelNote(prescriptionId);
			});
		}
	};


	this.cancelNote = function (prescriptionId) {
		$("#collapse-new-note-" + prescriptionId).collapse("hide");
		$("#show-new-note-" + prescriptionId).removeClass("hide");
		$(".addendum").removeClass("hide");
		_this.note[prescriptionId] = "";
		_this.subject[prescriptionId] = "";
		_this.noteType[prescriptionId] = undefined;
		_this.appointment[prescriptionId] = undefined;
		_this.treatingPractitionerId[prescriptionId] = undefined;
		_this.noteTemplateId[prescriptionId] = undefined;
		_this.currentParentId = null;
		if (CacheFactory.get('noteCache') && CacheFactory.get('noteCache').info('/patient/' + PatientService.patient.id + '/prescription/note/' + prescriptionId) != undefined) {
			CacheFactory.get('noteCache').remove('/patient/' + PatientService.patient.id + '/prescription/note/' + prescriptionId);
		}
	};


	this.openClinicalDocs = function (prescriptionId) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/notes/modal_clinical_doc_type.html',
			controller: 'NymblFormSelectCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				prescriptionId: function () {
					return prescriptionId;
				}
			}
		});
	};

	this.changeParentId = function (id) {
		this.currentParentId = id;
	};

	this.getNoteCount = function (noteCount, formNoteCount, subPrescriptionNotesCount) {
		var totalCount = 0;
		if (noteCount) totalCount += noteCount;
		if (formNoteCount) totalCount += formNoteCount;
		if (subPrescriptionNotesCount) totalCount += subPrescriptionNotesCount;
		return totalCount;
	};

	this.showCorrectionButton = function (note) {
		var mostRecentCorrection = _this.mostRecentCorrections[note.id];
		return !note.parentId
			&& note.published
			&& note.noteType !== 'patient_summary'
			&& (note.noteType !== 'clinical' || (UserService.isClinical() || UserService.isCareExtender()))
			&& (mostRecentCorrection == null || mostRecentCorrection.published);
	};

	this.showAppointmentForNote = function (note, prescription) {
		return function (appointment) {
			var subPrescriptionIds = (prescription != null && prescription.$subPrescriptions != null) ? prescription.$subPrescriptions.map(sub => sub.id) : [];
			return (appointment.prescriptionId == null && appointment.prescriptionTwoId == null)
				|| (note != null && note.prescriptionId === appointment.prescriptionId)
				|| (note != null && note.prescriptionId === appointment.prescriptionTwoId)
				|| (prescription != null && prescription.id === appointment.prescriptionId)
				|| (prescription != null && prescription.id === appointment.prescriptionTwoId)
				|| (prescription != null && subPrescriptionIds.includes(appointment.prescriptionId))
				|| (prescription != null && subPrescriptionIds.includes(appointment.prescriptionTwoId));
		}
	};

	this.openV2AINote = (patientId, prescriptionId) => {
		UtilService.openV2Link("patients/", "ai-notes", "patientId=" + patientId + "&prescriptionId=" + prescriptionId);
	}
}
