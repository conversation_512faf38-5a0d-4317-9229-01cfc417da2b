app.service('ManualCronService', ManualCronService);
ManualCronService.$inject = ['$http', 'toastr'];

function ManualCronService($http, toastr) {

	this.dailyGeneralLedger = function () {
		processCron($('#daily-general-ledger'), 'api/manual-cron/daily/generalLedger');
	};

	this.pecosVerification = function () {
		processCron($('#pecos-verification'), 'api/manual-cron/pecos');
	};

	this.dailyClearingHouse = function () {
		processCron($('#daily-clearing-house'), 'api/manual-cron/daily/clearingHouse');
	};

	this.userPermissions = function () {
		processCron($('#user-info'), 'api/manual-cron/daily/user-permissions');
	};

	this.assetUserPermissions = function () {
		processCron($('#asset-user-permissions'), 'api/manual-cron/daily/asset-user-permissions');
	};

	this.submitClaims = function () {
		processCron($('#submit-claims'), 'api/manual-cron/daily/submit-claims');
	};

	this.nymblDataDictionary = function () {
		processCron($('#nymbl-data-dictionary'), 'api/download/data-dictionary', 'data-dictionary.html');
	};

	this.qsClinicalOperations = function () {
		processCron($('#qs-clinical-operations'), 'api/manual-cron/quicksight/clinical-operations');
	};

	this.qsGeneralLedger = function () {
		processCron($('#qs-general-ledger'), 'api/manual-cron/quicksight/general-ledger');
	};

	this.qsPrescriptionSummary = function () {
		processCron($('#qs-prescription-summary'), 'api/manual-cron/quicksight/prescription-summary');

	};

	this.qsPurchasingHistory = function () {
		processCron($('#qs-purchasing-history'), 'api/manual-cron/quicksight/purchasing-history');
	};

	this.qsNymblStatusHistory = function () {
		processCron($('#qs-nymbl-status-history'), 'api/manual-cron/quicksight/nymbl-status-history');
	};

	this.qsAiNotesUsage = function () {
		processCron($('#qs-ai-notes-usage'), 'api/manual-cron/quicksight/ai-notes-usage');
	};

	this.customerSftpGeneralLedger = function () {
		processCron($('#customer-sftp-rx-summary'), '/api/manual-cron/customer/sftp/general-ledger').then(() => {
			toastr.success("General Ledger upload to customer sftp successful", "General Ledger");
		}).catch((error) => {
			toastr.error(error.data.message, "General Ledger Failed");
		});
	};

	this.customerSftpRxSummary = function () {
		processCron($('#customer-sftp-rx-summary'), '/api/manual-cron/customer/sftp/prescription-summary').then(() => {
			toastr.success("Rx Summary upload to customer sftp successful", "Rx Summary");
		}).catch((error) => {
			toastr.error(error.data.message, "Rx Summary Failed");
		});
	};

	this.customerSftpClinicalOperations = function () {
		processCron($('#customer-sftp-clinical-operations-summary'), '/api/manual-cron/customer/sftp/clinical-operations').then(() => {
			toastr.success("Clinical Operations upload to customer sftp successful", "Clinical Operations");
		}).catch((error) => {
			toastr.error(error.data.message, "Clinical Operations Failed");
		});
	};

	this.createS3Bucket = function () {
		processCron($('#create-s3-ai'), '/api/healthscribe/create-bucket');
	};

	this.createLmnTemplate = function () {
		processCron($('#create-lmn-template'), '/api/evaluation/add-lmn-template').then(() => {
			toastr.success("SOAP and Summary template files uploaded for LMN.", "LMN Templates Configured");
		}).catch((error)=> {
			toastr.error("Error creating templates, possibly duplicate records found.", "LMN Template Configuration Failed");
		});
	};

	/**
	 * Manual Cron job trigger via http request.
	 * @param btn - Button clicked
	 * @param apiUrl - URL to action get request
	 * @param viewUrl - URL to route user in new tab
	 * @returns Promise from HTTP call so messaging can be sent to the user via toastr.
	 */
	function processCron(btn, apiUrl, viewUrl) {
		btn.button('loading');
		return $http.get(apiUrl).then(function (response) {
			btn.button('reset');
			if (!!viewUrl && !!response.data) {
				window.open(viewUrl, "_blank").document.write(response.data);
			}
		}).catch(function (err) {
			btn.button('reset');
			console.log(err);
			throw err;
		});
	}
}
