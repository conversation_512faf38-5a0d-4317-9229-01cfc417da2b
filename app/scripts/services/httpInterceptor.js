'use strict';

app.factory('httpInterceptor', httpInterceptor);
httpInterceptor.$inject = ['$q', '$log'];

function httpInterceptor($q, $log) {
    return {
        // Intercept all requests
        request: function(config) {
            return config;
        },

        requestError: function(config) {
            return $q.reject(config);
        },

        // Intercept all responses
        response: function(response) {
            return response;
        },

        // Intercept all response errors
        responseError: function(rejection) {
            // Log to console
            $log.error('HTTP Error:', {
                url: rejection.config.url,
                method: rejection.config.method,
                status: rejection.status,
                statusText: rejection.statusText,
                data: rejection.data
            });

            // Capture error in Sentry
            if (window.Sentry) {
                Sentry.captureException(new Error(`HTTP Error: ${rejection.config.url}`), {
                    extra: {
                        url: rejection.config.url,
                        method: rejection.config.method,
                        status: rejection.status,
                        statusText: rejection.statusText,
                        data: rejection.data,
                        headers: rejection.config.headers,
                        params: rejection.config.params
                    }
                });
            }

            return $q.reject(rejection);
        }
    };
} 