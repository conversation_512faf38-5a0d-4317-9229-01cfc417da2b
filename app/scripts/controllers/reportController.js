'use strict';
app.controller('ReportsCtrl', ReportsCtrl);
ReportsCtrl.$inject = ['$scope', '$rootScope', '$uibModal', '$moment', '$window', '$cookies', 'ReportService', 'UtilService', 'UserService', 'ClaimFactory', 'BranchService', 'AdjustmentFactory', 'PaymentFactory', 'AppliedPaymentFactory', 'PatientService', 'InsuranceService', 'DateService'];

function ReportsCtrl($scope, $rootScope, $uibModal, $moment, $window, $cookies, ReportService, UtilService, UserService, ClaimFactory, BranchService, AdjustmentFactory, PaymentFactory, AppliedPaymentFactory, PatientService, InsuranceService, DateService) {
	$rootScope.page = {
		title: 'Reports',
		subtitle: 'All',
		view: 'report'
	};

	$scope.patientService = PatientService;
	$scope.reportService = ReportService;
	$scope.insuranceService = InsuranceService;
	$scope.utilService = UtilService;
  $scope.hasPermission = UserService.hasPermission;
	$scope.branchService = BranchService;
	$scope.dateService = DateService;
	$scope.checked = [];
  $scope.filter = [];
  $scope.canPrintInvoices = false;
  $scope.moment = $moment;
  $scope.commissionPercentage = {
    value: 0
  };
  $scope.hideRows = true;
  $scope.showCoBreakdown = false;
  $scope.hideText = "Details";

  $scope.calendar = {
    opened: {},
    dateOptions: {
      formatYear: 'yy',
      startingDay: 1
    },
    open: function ($event, which) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.calendar.opened[which] = true;
		}
	};
	$scope.$on("readyToLoadLeftNav", function () {
		$scope.hasV2CashAccess = UserService.features.hasV2CashAccess;
		$scope.hasV2RxSummaryAccess = UserService.features.hasV2RxSummaryAccess;
		$scope.hasV2ClaimsActivityAccess = UserService.features.hasV2ClaimsActivityAccess;
	});
	$scope.selectedAdjustments = [];
	$scope.getAdjustmentTypes = function () {
		AdjustmentFactory.search({q: null}).$promise.then(function (response) {
			$scope.adjustments = response;
		});
	};
	$scope.getAdjustmentTypes();

	$scope.printInvoices = function () {
		angular.forEach($scope.checked, function (checkBox, index) {
			if (checkBox === true) {
				// PaymentFactory.get({id: index}).$promise.then(function (payment) {
				//     UtilService.openPrintScreen('payment_invoice?prescriptionId=' + payment.claim.prescription.id + '?amountPaid=' + payment.amount + '?claimId=' + payment.claim.id);
				// })
				ClaimFactory.get({id: index}).$promise.then(function (claim) {
					$scope.utilService.openPrintScreen('patient_invoice?p=' + claim.prescriptionId);
				});
			}
		});
	};

	$scope.hasAnyFinancialReportPermissions = function () {
		return !!(this.hasPermission("financial_reports_view") || this.hasPermission("general_ledger_reports_view") ||
			this.hasPermission("balance_reports_view") || this.hasPermission("sales_reports_view") ||
			this.hasPermission("payment_reports_view"));
	};

	$scope.showGLReports = function () {
		return (this.hasPermission('financial_reports_view') || this.hasPermission('general_ledger_reports_view'));
	};

	$scope.showBalanceReports = function () {
		return (this.hasPermission('financial_reports_view') || this.hasPermission('balance_reports_view'));
	};

	$scope.showSalesReports = function () {
		return (this.hasPermission('financial_reports_view') || this.hasPermission('sales_reports_view'));
	};

	$scope.showPaymentReports = function () {
		return (this.hasPermission('financial_reports_view') || this.hasPermission('payment_reports_view'));
	};


	$scope.hasAnyProductivityReportPermissions = function () {
		return !!(this.hasPermission("productivity_reports_view") || this.hasPermission("purchasing_inventory_reports_view") ||
			this.hasPermission("actionable_reports_view") || this.hasPermission("efficiency_reports_view") ||
			this.hasPermission("feedback_reports_view") || this.hasPermission("scheduling_reports_view"));
	};

	$scope.showPIReports = function () {
		return (this.hasPermission('productivity_reports_view') || this.hasPermission('purchasing_inventory_reports_view'));
	};

	$scope.showActionableReports = function () {
		return (this.hasPermission('productivity_reports_view') || this.hasPermission('actionable_reports_view'));
	};

	$scope.showEfficiencyReports = function () {
		return (this.hasPermission('productivity_reports_view') || this.hasPermission('efficiency_reports_view'));
	};

	$scope.showFeedbackReports = function () {
		return (this.hasPermission('productivity_reports_view') || this.hasPermission('feedback_reports_view'));
	};

	$scope.showSchedulingReports = function () {
		return (this.hasPermission('productivity_reports_view') || this.hasPermission('scheduling_reports_view'));
	};

	$scope.showhide = function (text) {
		if (text === "Details") {
			$scope.hideRows = false;
			$scope.hideText = "Less";
		} else {
			$scope.hideRows = true;
			$scope.hideText = "Details";
		}
	};

	$scope.toggleCoBreakdown = function (){
		if($scope.showCoBreakdown === true){
			$scope.showCoBreakdown = false;
		} else {
			$scope.showCoBreakdown = true;
		}
	};

	$scope.uncollectedExport = function(){
        $scope.reportService.uncollectedExport();
	}

	function openV2Link(path, page) {
		UtilService.openV2Link(path, page);
	}

	$scope.openV2ReportLink = function (module, report) {
		openV2Link("reports/" + module + "/", report);
	}

	$scope.openGLLink = function (report) {
		openV2Link("reports/financial/general-ledger/", report);
	}

	$scope.openBalancesLink = function (report) {
		openV2Link("reports/financial/balances/", report);
	}

	$scope.openPaymentsLink = function (report) {
		openV2Link("reports/financial/payments/", report);
	}

	$scope.openPurchasingLink = function (report) {
		openV2Link("reports/purchasing/", report);
	}

	$scope.openSchedulingLink = function (report) {
		openV2Link("reports/scheduling/", report);
	}

	$scope.openEfficiencyLink = function (report) {
		openV2Link("reports/efficiency/", report);
	}

	$scope.openActionableLink = function (report) {
		openV2Link("reports/productivity/actionable/", report);
	}

	$scope.search = function () {
		$scope.loading = true;
		$scope.filter = {
			size: $scope.pageSize
		};
		PaymentFactory.search({
			id: $scope.paymentId,
			page: $scope.filter.page - 1,
			size: $scope.filter.size,
		  columnName: $scope.filter.columnName,
		  sortDirection: $scope.filter.sortDirection,
		  patientId: $scope.filter.patientId,
		  insuranceCompany: $scope.filter.insuranceCompany,
		  checkDate: $scope.filter.checkDate,
		  checkNumber: $scope.filter.checkNumber,
      amount: $scope.filter.amount,
      payerType: $scope.filter.payerType,
      unappliedType: $scope.filter.unappliedType,
      branchId: $scope.filter.branchId,
      patientDeposits: $scope.filter.patientDeposits,
      bulkPayments: $scope.filter.bulkPayments
    }).$promise.then(function (response) {
      $scope.payments = response.content;
      $scope.filter.numberOfElements = response.numberOfElements;
      $scope.filter.totalPages = response.totalPages;
      $scope.filter.totalElements = response.totalElements;
      $scope.loading = false;
    });
  };

  $scope.$watchGroup(['reportService.categoryId', 'reportService.report'], function () {
		if ($scope.reportService.report) {
			if ($scope.reportService.report.id === 'outstandingPatientResponsibilityBalances') {
				$scope.canPrintInvoices = true;
			} else {
				$scope.canPrintInvoices = false;
			}
			ReportService.resetResults();
			if (!$scope.$$phase)
				$scope.$apply();
		}
	});

	$scope.$watchGroup(['reportService.classname'], function () {
		if ($scope.reportService.classname !== null) {
			if ($scope.reportService.report && $scope.reportService.report.id === 'exportAll') {
				ReportService.classMetadata();
			}
		}
	});

	$scope.$watch('reportService.report', function (newValue, oldValue) {
		$rootScope.page.subtitle = newValue ? newValue.label : 'All';
	});
}
