'use strict';
app.controller('LoginCtrl', LoginCtrl);
LoginCtrl.$inject = ['$rootScope', '$scope', '$cookies', '$http', '$state', '$location', '$moment', 'toastr', 'UserService', 'SessionService', 'UtilService', 'UserFactory'];

function LoginCtrl($rootScope, $scope, $cookies, $http, $state, $location, $moment, toastr, UserService, SessionService, UtilService, UserFactory) {

    $rootScope.page = {
        title: 'Secured Login',
        subtitle: undefined
    };

    $scope.showAuthInput = false;
    $scope.codeAuthenticated = false;
    $scope.authCode = "";
    $scope.data = "";

    $scope.rememberme = $cookies.get("rememberme");
    $scope.toggleRememberMe = function () {
        var temp = $cookies.get("rememberme");
        $scope.rememberme = temp === "false" || temp === undefined ? "true" : "false";
        $cookies.put("rememberme", $scope.rememberme);
    };

    if ($scope.rememberme === "true") {
        $scope.credentials.username = $cookies.get("username");
    }

    UserService.authenticateFromLocalStorage(function () {
        if (SessionService.isAuthenticated()) {
            $state.transitionTo("app.dashboard");
        }
    });
    $scope.credentials = {};

    $scope.ssoLogin = function() {
        window.open('/oauth2/authorization/azure', '_self');
    }

    $scope.ssoError = function() {
        setTimeout(function() {
            window.location.href = "/";
        }, 5000);
    }

    $scope.ssoOauth = function() {
        $http.get('sso-oauth', {
            headers: {
                "content-type": "application/json"
            }
        })
            .success(function (data) {
                $scope.processAuthSuccess(data, true);
            }).error(function (data) {
            $scope.processAuthFailure(data);
        });
    }

    $scope.login = function () {
        $scope.inactiveUser = false;
        if ($scope.rememberme === "true")
            $cookies.put("username", $scope.credentials.username);
        else
            $cookies.remove("username");
        $http.post('auth', $scope.credentials, {
            headers: {
                "content-type": "application/json"
            }
        }).success(function (data) {
            $scope.processAuthSuccess(data, false);
        }).error(function (data) {
            $scope.processAuthFailure(data);
        });
    };

    $scope.processAuthFailure = function(data) {
        if (data.status === 403)
            // $scope.inactiveUser = true;
            UtilService.displayAlert('danger', '<p>Invalid username and password combination.</p>', '#loginFailure');
        else {
            UtilService.displayAlert('danger', '<p>Invalid username and password combination.</p>', '#inactiveUserFailure');
            SessionService.clearSession();
        }
    }

    $scope.processAuthSuccess = function (data, isOauthFlow) {
        $scope.data = data;
        if (data.user && data.user.company.twoFactorAuth === true && $scope.codeAuthenticated === false && !isOauthFlow) {
            if (data.user.authCode && data.user.company.twoFactorExpiration != null && data.user.authCodeExpirationTime && $moment().isBefore($moment.utc(data.user.authCodeExpirationTime).local())) {
                $scope.authCode = data.user.authCode;
                $scope.showAuthInput = true;
            } else {
                UserFactory.sendAuthCode(data.user).$promise.then(function (authUser) {
                    $scope.authCode = authUser.authCode;
                    $scope.showAuthInput = true;
                });
            }
        } else {
            UserService.authenticate(data, function () {
                if (SessionService.isAuthenticated()) {
                    // Set Sentry user context on successful login
                    window.Sentry &&
                    Sentry.onLoad(function () {
                        const user = SessionService.getCurrentUser();
                        const company = SessionService.getCurrentCompany();
                        
                        if (user) {
                            Sentry.setUser({
                                id: user.id,
                                username: user.username,
                                email: user.email
                            });
                        }

                        if (company) {
                            Sentry.setTags({
                                company_id: company.id,
                                company_name: company.name
                            });
                        }
                    });

                    if(isOauthFlow) {
                        window.location.href = "/";
                    }
                    $state.go("app.dashboard", {"releaseNote": data.releaseNote});
                    //Start of nymblsystems Pendo.io
                    // see https://app.pendo.io/s/4779389661282304/admin/app/-323232?autoOpenAddAppDrawer=true&tab=install
                    pendo.initialize({
                        visitor: {
                            id:              'VISITOR-UNIQUE-ID'   // Required if user is logged in, default creates anonymous ID
                            // email:        // Recommended if using Pendo Feedback, or NPS Email
                            // full_name:    // Recommended if using Pendo Feedback
                            // role:         // Optional

                            // You can add any additional visitor level key-values here,
                            // as long as it's not one of the above reserved names.
                        },

                        account: {
                            id:           'ACCOUNT-UNIQUE-ID' // Required if using Pendo Feedback, default uses the value 'ACCOUNT-UNIQUE-ID'
                            // name:         // Optional
                            // is_paying:    // Recommended if using Pendo Feedback
                            // monthly_value:// Recommended if using Pendo Feedback
                            // planLevel:    // Optional
                            // planPrice:    // Optional
                            // creationDate: // Optional

                            // You can add any additional account level key-values here,
                            // as long as it's not one of the above reserved names.
                        }
                    });
                    //End of nymblsystems Pendo.io

                } else {
                    UtilService.displayAlert('danger', '<p>Invalid username and password combination.</p>', '#inactiveUserFailure');
                }
            });
        }
    }

    $scope.checkAuth = function (inputAuthCode) {
        if (inputAuthCode === parseInt($scope.authCode)) {
            $scope.codeAuthenticated = true;
            $scope.login();
        } else {
            UtilService.displayAlert('danger', '<p>The authentication code you have entered is incorrect.</p>', '#incorrect-auth-code-alert-container');
        }
    };

    $scope.cancelAuth = function () {
        $scope.showAuthInput = false;
        $scope.codeAuthenticated = false;
        $scope.authCode = "";
        SessionService.clearSession();
    };

    $scope.resendCode = function () {
        UserFactory.sendAuthCode($scope.data.user).$promise.then(function (authUser) {
            UtilService.displayAlert('success', '<p>A new code has been sent to your device.</p>', '#resend-code-alert-container');
            $scope.authCode = authUser.authCode;
            $scope.showAuthInput = true;
        });
        $scope.codeAuthenticated = false;
    };

    $scope.sendAuthCodeToEmail = function () {
        UserFactory.sendAuthCodeToEmail($scope.data.user).$promise.then(function (authUser) {
            UtilService.displayAlert('success', '<p>An Authentication Code was sent to your email.</p>', '#resend-code-alert-container');
            $scope.authCode = authUser.authCode;
            $scope.showAuthInput = true;
        });
        $scope.codeAuthenticated = false;
    };

    $scope.checkForBrowserCompatibility = function () {
        var sUsrAg = navigator.userAgent;
        var sFeatureSupport = false;
        var minChromeVersion = 92; // Chromome version check...must be 88 or greater (7/2021 or newer)
        var detectedChromeVersion;
        var sBrowserOutOfDate = false;
        // var sBrowser;
        // The order matters here, and this may report false positives for unlisted browsers.
        if (window.location.href.indexOf('browser-com') < 0) {
            if (sUsrAg.indexOf("Firefox") > -1) {
                // sBrowser = "Mozilla Firefox";
                // "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:61.0) Gecko/20100101 Firefox/61.0"
            } else if (sUsrAg.indexOf("SamsungBrowser") > -1) {
                // sBrowser = "Samsung Internet";
                // "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G955F Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36"
            } else if (sUsrAg.indexOf("Opera") > -1 || sUsrAg.indexOf("OPR") > -1) {
                // sBrowser = "Opera";
                // "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 OPR/57.0.3098.106"
            } else if (sUsrAg.indexOf("Trident") > -1) {
                // sBrowser = "Microsoft Internet Explorer";
                // "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E; Zoom 3.6.0; wbx 1.0.0; rv:11.0) like Gecko"
            } else if (sUsrAg.indexOf("Edge") > -1) {
                // sBrowser = "Microsoft Edge";
                // "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36 Edge/16.16299"
            } else if (sUsrAg.indexOf("Chrome") > -1) {
                // sBrowser = "Google Chrome or Chromium";
                // "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/66.0.3359.181 Chrome/66.0.3359.181 Safari/537.36"
                sFeatureSupport = true;

                var rawAgentString = sUsrAg.match(/Chrom(e|ium)\/([0-9]+)\./);
                if(rawAgentString){
                    var iAgentVersion = parseInt(rawAgentString[2], 10);
                    if(iAgentVersion && iAgentVersion > 0 && iAgentVersion < minChromeVersion) {
                        sBrowserOutOfDate = true;
                    }
                }
            } else if (sUsrAg.match('CriOS')) {
                sFeatureSupport = true; // iPad, etc using Chrome

                var rawAgentString = sUsrAg.match(/Chrom(e|ium)\/([0-9]+)\./);
                if(rawAgentString){
                    var iAgentVersion = parseInt(rawAgentString[2], 10);
                    if(iAgentVersion && iAgentVersion > 0 && iAgentVersion < minChromeVersion) {
                        sBrowserOutOfDate = true;
                    }
                }
            } else if (sUsrAg.indexOf("Safari") > -1) {
                // sBrowser = "Apple Safari";
                // "Mozilla/5.0 (iPhone; CPU iPhone OS 11_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1 980x1306"
            } else {
                // sBrowser = "unknown";
            }
            // if (sFeatureSupport === "no") {
            // 	window.location.href = '/browser-compatibility';
            // }
        }
        if (!sFeatureSupport) {
            $state.transitionTo('core.browser_compatibility');
        } else if (sBrowserOutOfDate){
            toastr.error("Your version of Chrome (" + iAgentVersion + ") may not work correctly with Nymbl.  Please update the Chrome browser on your device.", 'Chrome is out of date', {
                timeOut: 10000,
                closeButton: true,
                closeHtml: '<i class="fa fa-times-circle"></i>',
                iconClasses: {
                    error: 'toast-error',
                    info: 'toast-info',
                    success: 'toast-success',
                    warning: 'toast-warning'
                },
            });
        }
    };

    $scope.checkForBrowserCompatibility();

}
