app.controller('ItemPhysicalModalCtrl', ItemPhysicalModalCtrl);
ItemPhysicalModalCtrl.$inject = ['$filter', '$moment', '$scope', '$timeout', '$uibModalInstance', 'BranchFactory', 'branchId',
	'BranchService', 'caller', 'DiffModalService', 'itemPhysical', 'item', 'ItemService', 'PatientService',
	'PrescriptionFactory', 'PrescriptionLCodeFactory', 'SystemSettingFactory', 'UtilService'];

function ItemPhysicalModalCtrl($filter, $moment, $scope, $timeout, $uibModalInstance, BranchFactory, branchId,
                               BranchService, caller, DiffModalService, itemPhysical, item, ItemService, PatientService,
                               PrescriptionFactory, PrescriptionLCodeFactory, SystemSettingFactory, UtilService) {

	$scope.branchService = BranchService;
	$scope.calendar = {
		opened: {},
		dateOptions: {
			formatYear: 'yy',
			minDate: new Date().setFullYear(new Date().getFullYear() - 130),
			startingDay: 1
		},
		open: function ($event, which) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.calendar.opened[which] = true;
		}
	};
	$scope.currentStatus = null;
	$scope.itemPhysical = null;
	$scope.itemPhysicalOriginal = null;
	$scope.itemService = ItemService;
	$scope.patientService = PatientService;
	$scope.prescriptions = [];
	$scope.prescriptionLCodes = [];
	$scope.depreciationPanelOpen = caller == 'item';
	$scope.rentalPanelOpen = caller == 'loaner';
	$scope.utilService = UtilService;
	$scope.codedReasons = $('#codedReasons').attr('value').split(',');
	$scope.rentalStatuses = ['cleaning', 'loaned', 'rented', 'repair', 'reserved'];

	$scope.calculateDepreciation = function (forceRefresh) {
		if (forceRefresh || !$scope.itemPhysical.depreciationAmount || !$scope.itemPhysical.depreciationPercentage) {
			if ($scope.itemPhysical.initialValue && $scope.itemPhysical.depreciationUnitsToZero) {
				if (!$scope.itemPhysical.depreciationUnitsCount) $scope.itemPhysical.depreciationUnitsCount = 0;
				if ($scope.itemPhysical.depreciationUnit === 'month') {
					$scope.itemPhysical.depreciationUnitsCount = $scope.itemPhysical.currentMonths;
					if ($scope.itemPhysical.depreciationUnitsCount > $scope.itemPhysical.depreciationUnitsToZero) {
						$scope.itemPhysical.depreciationUnitsCount = $scope.itemPhysical.depreciationUnitsToZero;
					}
				}
				var depreciationFraction = $scope.itemPhysical.depreciationUnitsCount / $scope.itemPhysical.depreciationUnitsToZero;
				$scope.itemPhysical.depreciationPercentage = $scope.roundMoney(depreciationFraction * 100);
				$scope.itemPhysical.depreciationAmount = $scope.roundMoney($scope.itemPhysical.initialValue * depreciationFraction);
				$scope.itemPhysical.currentValue = $scope.roundMoney($scope.itemPhysical.initialValue - $scope.itemPhysical.depreciationAmount);
			} else {
				$scope.itemPhysical.currentValue = null;
				$scope.itemPhysical.depreciationAmount = null;
				$scope.itemPhysical.depreciationPercentage = null;
			}
		}
	};

	var createItemPhysical = function (item) {
		$scope.itemPhysical = {
			id: null,
			createdAt: null,
			createdBy: null,
			createdById: null,
			branch: null,
			branchId: branchId,
			currentMonths: null,
			currentValue: null,
			depreciationAmount: null,
			depreciationPercentage: null,
			depreciationType: item.itemByManufacturer.depreciationType,
			depreciationUnit: item.itemByManufacturer.depreciationUnit,
			depreciationUnitsCount: null,
			depreciationUnitsToZero: item.itemByManufacturer.depreciationUnitsToZero,
			endTime: null,
			initialValue: item.price,
			item: item,
			itemId: item.id,
			reason: 'item_physical_modal',
			serialNumber: null,
			startDate: new Date(),
			status: 'inventory'
		};
	};

	$scope.depreciationTypeChanged = function () {
		var depreciationUnit = $scope.itemPhysical.depreciationUnit;
		switch ($scope.itemPhysical.depreciationType) {
			case 'ongoing_upon_receipt':
				$scope.itemPhysical.depreciationUnit = 'month';
				break;
			case 'sale_distributed':
				$scope.itemPhysical.depreciationUnit = 'rental_cycle';
				break;
			default:
				$scope.itemPhysical.depreciationUnit = null;
				break;
		}
		if ((depreciationUnit == null && $scope.itemPhysical.depreciationUnit != null) ||
			(depreciationUnit != null && $scope.itemPhysical.depreciationUnit == null) ||
			(depreciationUnit != null && depreciationUnit != $scope.itemPhysical.depreciationUnit)) {
			$scope.depreciationUnitChanged();
		}
	};

	$scope.depreciationUnitChanged = function () {
		if ($scope.itemPhysical.depreciationUnit == 'month') {
			$scope.itemPhysical.depreciationUnitsToZero = 60;
			$scope.receivedDateChanged();
		} else {
			$scope.itemPhysical.currentValue = null;
			$scope.itemPhysical.depreciationAmount = null;
			$scope.itemPhysical.depreciationPercentage = null;
			$scope.itemPhysical.depreciationUnitsCount = null;
			$scope.itemPhysical.depreciationUnitsToZero = 13;
			$scope.itemPhysical.startDate = '';
		}
	};

	/**
	 * Date input fields have a problem displaying existing dates because they are not dates but strings.
	 * Converting the strings to dates using "new Date()" produces the result which is off by one day,
	 * e.g '2000-01-01' turns into '1999-12-31', therefore, I use moment.
	 */
	var fixDates = function () {
		if ($scope.itemPhysical.startDate) $scope.itemPhysical.startDate = moment($scope.itemPhysical.startDate).toDate();
		if ($scope.itemPhysical.dateOfServiceEnd) $scope.itemPhysical.dateOfServiceEnd = moment($scope.itemPhysical.dateOfServiceEnd).toDate();
		if ($scope.itemPhysical.dateOfServiceStart) $scope.itemPhysical.dateOfServiceStart = moment($scope.itemPhysical.dateOfServiceStart).toDate();
	};

	$scope.getItems = function (keyword) {
		if (keyword) {
			var params = {
				columnName: null,
				keywords: keyword,
				lcodeCategoryId: null,
				oneTimePurchase: null,
				page: 0,
				showInactive: false,
				size: 100,
				sortDirection: null,
				vendorId: null
			};
			return ItemService.getItems(params).then(function (response) {
				return response.content;
			});
		} else {
			return null;
		}
	};

	var loadPrescriptions = function () {
		if ($scope.itemPhysical && $scope.itemPhysical.patientId) {
			PrescriptionFactory.findByPatientIdActiveIsTrue({patientId: $scope.itemPhysical.patientId}).$promise.then(function (response) {
				$scope.prescriptions = response;
			});
		}
	};

	$scope.patientChanged = function (updateStatus) {
		// clear previously selected patent attributes
		if (!$scope.itemPhysical.patient || $scope.itemPhysical.patientId != $scope.itemPhysical.patient.id) {
			$scope.itemPhysical.dateOfServiceEnd = null;
			$scope.itemPhysical.dateOfServiceStart = null;
			$scope.itemPhysical.insuranceVerificationLCodeId = null;
			$scope.itemPhysical.inventoryPurchaseOrderItemId = null;
			$scope.itemPhysical.notes = null;
			$scope.itemPhysical.patientId = null;
			$scope.itemPhysical.prescription = null;
			$scope.itemPhysical.prescriptionId = null;
			$scope.itemPhysical.prescriptionLCode = null;
			$scope.itemPhysical.prescriptionLCodeId = null;
			$scope.itemPhysical.reason = null;
			$scope.prescriptions = [];
		}
		// select the new patient
		if ($scope.itemPhysical.patient) {
			$scope.itemPhysical.patientId = $scope.itemPhysical.patient.id;
		}
		if (updateStatus) {
			if ($scope.itemPhysical.patient) {
				$scope.itemPhysical.status = caller == 'loaner' ? 'loaned' : 'prescription';
			} else {
				$scope.itemPhysical.status = 'cleaning';
			}
		}
	};

	$scope.prescriptionChanged = function (fromUI) {
		// load prescriptionLCodes
		if ($scope.itemPhysical.prescriptionId) {
			PrescriptionLCodeFactory.findByPrescriptionId({prescriptionId: $scope.itemPhysical.prescriptionId}).$promise.then(function (response) {
				$scope.prescriptionLCodes = response;
			});
		} else {
			$scope.prescriptionLCodes = [];
		}

		if (fromUI) {
			$scope.itemPhysical.prescriptionLCodeId = null;
			if ($scope.itemPhysical.prescriptionId) {
				$scope.itemPhysical.status = caller == 'loaner' ? 'rented' : 'prescription';
				var rx = $scope.prescriptions.find(rx => rx.id == $scope.itemPhysical.prescriptionId);
				if (rx) {
					$scope.itemPhysical.branchId = rx.branchId;
				}
			} else {
				$scope.itemPhysical.status = $scope.itemPhysical.patientId ? 'loaned' : 'cleaning';
			}
		}
	};

	// calculate depreciationUnitsCount
	$scope.receivedDateChanged = function () {
		if ($scope.itemPhysical.startDate && $scope.itemPhysical.startDate != '') {
			var today = new Date();
			var startDate = new Date($scope.itemPhysical.startDate);
			var years = today.getFullYear() - startDate.getFullYear();
			var months = (years * 12) + today.getMonth() - startDate.getMonth();
			if (today.getDate() < startDate.getDate()) months--;
			$scope.itemPhysical.currentMonths = months;
			$scope.itemPhysical.depreciationUnitsCount = months;
			$scope.calculateDepreciation(true);
		} else {
			$scope.itemPhysical.currentValue = null;
			$scope.itemPhysical.depreciationUnitsCount = null;
			$scope.itemPhysical.depreciationPercentage = null;
			$scope.itemPhysical.depreciationAmount = null;
		}
	};

	$scope.roundMoney = function (moneyFloat) {
		return Math.round(moneyFloat * 100) / 100;
	};

	$scope.save = function (form) {
		$scope.submitted = true;
		if (!form.$valid) {
			confirm('Please correct the errors in red');
			return;
		}
		if (!$scope.itemPhysical.reason || $scope.codedReasons.includes($scope.itemPhysical.reason)) {
			$scope.itemPhysical.reason = 'item_physical_modal';
		}
		// trim the fat (only the corresponding IDs will be stored in the database)
		$scope.itemPhysical.branch = $scope.itemPhysical.branchId ? {id:$scope.itemPhysical.branchId} : null;
		$scope.itemPhysical.inventoryPurchaseOrderItem = $scope.itemPhysical.inventoryPurchaseOrderItemId ? {id:$scope.itemPhysical.inventoryPurchaseOrderItemId} : null;
		$scope.itemPhysical.item = $scope.itemPhysical.itemId ? {id:$scope.itemPhysical.itemId} : null;
		$scope.itemPhysical.patient = $scope.itemPhysical.patientId ? {id:$scope.itemPhysical.patientId} : null;
		$scope.itemPhysical.prescription = $scope.itemPhysical.prescriptionId ? {id:$scope.itemPhysical.prescriptionId} : null;
		$scope.itemPhysical.purchaseOrderItem = $scope.itemPhysical.purchaseOrderItemId ? {id:$scope.itemPhysical.purchaseOrderItemId} : null;

		ItemService.saveItemPhysical($scope.itemPhysical).then(function (response) {
			if (response && response.length) {
				$scope.itemPhysicalOriginal = response;
			}
			$scope.submitted = false;
			$uibModalInstance.close(response);
		}, function (error) {
			// this never happens because all error catching is done in ItemService.saveItemPhysical()
		});
	};

	$scope.selectItem = function (item) {
		if ($scope.itemPhysical && $scope.itemPhysical.patientId) {
			$scope.itemPhysical.item = item;
			$scope.itemPhysical.itemId = item ? item.id : null;
			$scope.itemPhysical.depreciationType = item.itemByManufacturer.depreciationType;
			$scope.itemPhysical.depreciationUnit = item.itemByManufacturer.depreciationUnit;
			$scope.itemPhysical.depreciationUnitsToZero = item.itemByManufacturer.depreciationUnitsToZero;
		} else {
			createItemPhysical(item);
		}
		$scope.currentStatus = $scope.itemPhysical.status;
	};

	$scope.selectPatient = function ($patient, $model, $label) {
		$scope.itemPhysical.patient = $patient;
		$scope.itemPhysical.patientId = $patient.id;
		loadPrescriptions();
		$scope.prescriptionChanged(false);
	};

	$scope.serialNumberChanged = function () {
		if ($scope.itemPhysical == null) createItemPhysical();
		if ($scope.itemPhysical.serialNumber == null || $scope.itemPhysical.serialNumber == '') {
			$scope.itemPhysical.currentValue = null;
			$scope.itemPhysical.depreciationAmount = null;
			$scope.itemPhysical.depreciationType = null;
			$scope.itemPhysical.depreciationUnit = null;
			$scope.itemPhysical.depreciationUnitsCount = null;
			$scope.itemPhysical.depreciationUnitsToZero = null;
			$scope.itemPhysical.initialValue = null;
			$scope.itemPhysical.itemCondition = null;
			$scope.itemPhysical.status = null;
		} else {
			if ($scope.itemPhysical.item) {
				$scope.itemPhysical.initialValue = $scope.itemPhysical.item.price;
			} else {
				$scope.itemPhysical.initialValue = null;
			}
		}
	};

	$scope.statusChanged = function () {
		if (!$scope.rentalStatuses.includes($scope.itemPhysical.status)) {
			$scope.itemPhysical.dateOfServiceEnd = null;
			$scope.itemPhysical.dateOfServiceStart = null;
		}
		if ($scope.itemPhysical.status == 'inventory') {
			if ($scope.rentalStatuses.includes($scope.currentStatus) && $scope.itemPhysical.depreciationUnit == 'rental_cycle') {
				$scope.itemPhysical.depreciationUnitsCount++;
			}
			$scope.patientChanged(false);
		}
		$scope.currentStatus = $scope.itemPhysical.status;
	};

	// initialize the model
	if (itemPhysical) {
		$scope.itemPhysicalOriginal = itemPhysical;
		$scope.itemPhysical = angular.copy(itemPhysical);
		fixDates(itemPhysical);
		loadPrescriptions();
		$scope.prescriptionChanged(false);
	} else {
		if (item && item.id && item.itemByManufacturer) {
			$scope.selectItem(item);
		}
		if ($scope.branchService.branches.length == 1) {
			$scope.itemPhysical.branch = $scope.branchService.branches[0];
			$scope.itemPhysical.branchId = $scope.branchService.branches[0].id;
		}
	}

}
