"use strict";

app.controller("CalendarCtrl", [
	'$scope', '$rootScope', '$state', '$timeout', '$compile', '$uibModal', '$moment', "$q", 'AppointmentStatusFactory', 'CalendarTemplatesFactory',
	'AppointmentFactory', 'NylasFactory', 'SystemSettingFactory', 'BranchService', 'UserFactory', 'UserService', 'UserBranchFactory', 'UtilService', 'PatientService', 'DateService',
	function ($scope, $rootScope, $state, $timeout, $compile, $uibModal, $moment, $q, AppointmentStatusFactory, CalendarTemplatesFactory, AppointmentFactory, NylasFactory,
	          SystemSettingFactory, BranchService, UserFactory, UserService, UserBranchFactory, UtilService, PatientService, DateService) {

		if (!$rootScope.branchId) {
			$state.go('app.dashboard');
			alert('Please select a branch');
		}
		$rootScope.page = {
			title: "Calendar",
			subtitle: ""
		};

		$('#filter-toggle').hide();

		var NymblCal;
		var currentUser;
		var defaultUsers = [];
		var isInitialized = false;

		const resolveAfter = (value, delay) =>
			new Promise(resolve => {
				setTimeout(() => resolve(value, delay));
			});

		$scope.$on('localStorageObjectLoadComplete', function () {
			// Calendar is duplicating by hitting init() multiple times.
			if (isInitialized) {
				return;
			}

			$scope.timeOfLastReload = (new Date()).getTime();
			$scope.refreshTimer = null;
			SystemSettingFactory.findBySectionAndField({
				section: "general",
				field: "calendar_auto_refresh_enabled"
			}).$promise.then(function (response) {
				if(response.value && response.value === "Y") {
					$scope.refreshCalendar();
				}
			});

			SystemSettingFactory.findBySectionAndField({
				section: "general",
				field: "calendar_day_scroll_enabled"
			}).$promise.then(function (response) {
				$scope.calendar_day_scroll_enabled = response.value;
			});
			isInitialized = true;
			currentUser = UserService.getCurrentUser();
			//SCRUM-5266: BranchService.loadUserBranches();

			$scope.showCanceled = false;
			$scope.utilService = UtilService;
			$scope.branchService = BranchService;
			$scope.userService = UserService;
			$scope.dateService = DateService;
			$scope.resources = [];
			$scope.appointments = [];
			$scope.miniCalendarDate = null;
			$scope.appointmentStatus = AppointmentStatusFactory.get();
			$scope.nylasEventColor = "#000000";
			$scope.calendarTemplateBranches = $scope.branchService.userBranches;

			if (currentUser.calendarDefaultUsers && !currentUser.viewAllUsers) {
				defaultUsers = currentUser.calendarDefaultUsers.split(',').map(function (x) {
					return parseInt(x, 10);
				});
			}

			$scope.filter = {
				startDate: null,
				endDate: null,
				branchId: currentUser.calendarAllBranches ? [] : [$rootScope.branchId],
				roleId: "",
				userIds: [],
				status: "",
				cancelled: false,
				loading: false,
				template: null
			};


			$scope.miniCalendar = {
				opened: false,
				dateOptions: {
					formatYear: 'yy',
					minDate: new Date().setYear(new Date().getYear() - 130),
					startingDay: 1
				},
				open: function ($event) {
					$event.preventDefault();
					$event.stopPropagation();
					$scope.miniCalendar.opened = true;
				}
			};

			CalendarTemplatesFactory.findByUserId({userId: currentUser.id}).$promise.then(function(response) {
				if(response.length > 0) {
					angular.forEach(response, function (template) {
						$scope.userCalendarTemplates = response;
						if(template.name === "Default") {
							$scope.filter.template = template.id;
							UserFactory.getActiveUsersForCompanyByCompanyIdNoSuperAdmin({companyId: UserService.getCompanyId()}).$promise.then(function (users) {
								$scope.users = [];
								angular.forEach(users, function (user) {
									if (user.canHaveAppointments) {
										$scope.users.push(user);
									}
								});
								$scope.processCalendarTemplate($scope.filter.template, false)
									.then(function () {
										$scope.init($scope.filter.template)
									});
							});
						}
					})
				} else {
					UserFactory.getActiveUsersForCompanyByCompanyIdNoSuperAdmin({companyId: UserService.getCompanyId()}).$promise.then(function (users) {
						$scope.users = [];
						angular.forEach(users, function (user) {
							if (user.canHaveAppointments) {
								$scope.users.push(user);
							}
						});
					});
					$scope.filter.userIds = currentUser.viewAllUsers ? [] : (defaultUsers.length ? defaultUsers : (currentUser.canHaveAppointments ? [currentUser.id] : []));
					$scope.init();
				}
			})

			SystemSettingFactory.findBySectionAndField({
				section: "general",
				field: "nylas_enabled"
			}).$promise.then(function (response) {
				$scope.nylasActive = response.value === 'Y';
			});

			SystemSettingFactory.findBySectionAndField({
				section: "general",
				field: "nylas_event_color"
			}).$promise.then(function (response) {
				if (response.value && response.value.length === 7) {
					$scope.nylasEventColor = response.value;
				}
			});
		});

		$scope.selectedDay = function (date, allDay, jsEvent, view) {
			if (allDay) {
				if ($scope.miniCalendarDate !== null)
					date = $moment(date).format("YYYY-MM-DD");
				NymblCal.gotoDate(date);
				$scope.miniCalendarDate = null;
			}
		};

    /* Helps us get values from nested objects safely */
    var _getSafely = function (path) {
      return function (obj) {
        return path.reduce(function (parent, child) {
          return parent && parent[child] ? parent[child] : "";
        }, obj);
      };
    };

		$scope.applyFilter = function () {
			var view = NymblCal.view;
			if (view.type === 'resourceTimeGridDay') {
				NymblCal.refetchResources();
			}
			$scope.search();
		};

		$scope.datesRender = function (view, element) {
			$scope.filter.startDate = $moment(view.currentStart).format("YYYY-MM-DD");
			$scope.filter.endDate = $moment(view.currentEnd).subtract(1, 'day').format("YYYY-MM-DD");
			$scope.search();
		};

		$scope.refreshCalendar = function() {
			let nymblCalendarElement =  document.getElementById("nymblCal")
			if(nymblCalendarElement) {
				if((new Date()).getTime() - $scope.timeOfLastReload > 300000) {
					$scope.applyFilter();
					$scope.timeOfLastReload = (new Date()).getTime();
					if($scope.refreshTimer){
						clearTimeout($scope.refreshTimer);
						$scope.refreshTimer = null;
					}
				}
			}

			if(!$scope.refreshTimer){
				$scope.refreshTimer = setTimeout($scope.refreshCalendar, 300000);
			}
			if(!nymblCalendarElement && $scope.refreshTimer) {
				clearTimeout($scope.refreshTimer);
			}
		}

		$scope.loadCalendarValues = function(templateId) {
			$scope.processCalendarTemplate(templateId, true);
		}

		$scope.selectCalendarTemplateUser = function(user, template) {
			$scope.filter.userIds.push(user.id);
		}

		$scope.deselectCalendarTemplateUser = function(user) {
			const filterIndex = $scope.filter.userIds.indexOf(user.id);
			$scope.filter.userIds.splice(filterIndex, 1);
		}

		$scope.selectCalendarTemplateBranch = function(branch) {
			$scope.filter.branchId.push(branch.id);
		}

		$scope.deselectCalendarTemplateBranch = function(branch) {
			const filterIndex = $scope.filter.branchId.indexOf(branch.id);
			$scope.filter.branchId.splice(filterIndex, 1);
		}

		$scope.processCalendarTemplate = function(templateId, clearUserIdArray) {
			let delay = $q.defer();
			if(clearUserIdArray) {
				$scope.filter.userIds = [];
			}
			if(templateId) {
				const currUserIdx = $scope.filter.userIds.indexOf(currentUser.id);
				$scope.filter.userIds.splice(currUserIdx, 1);
			}
			$scope.filter.branchId = [];
			$scope.users.selected = [];
			$scope.calendarTemplateBranches.selected = [];
			angular.forEach($scope.userCalendarTemplates, function (template) {
				if(template.id === templateId) {
					if(template.name === "Default" && currentUser.viewAllUsers) {
						angular.forEach($scope.users, function (user) {
							$scope.filter.userIds.push(user.id);
							$scope.users.selected.push(user);
						});
					}
					else if(template.calendarDefaultUsers && template.calendarDefaultUsers.length > 0) {
						angular.forEach(template.calendarDefaultUsers.split(","), function (id) {
							angular.forEach($scope.users, function (user) {
								if(parseInt(id, 10) === user.id) {
									$scope.filter.userIds.push(user.id);
									$scope.users.selected.push(user);
								}
							});
						})
					}
					if(template.name === "Default" && currentUser.calendarAllBranches) {
						angular.forEach($scope.calendarTemplateBranches, function (branch) {
							$scope.calendarTemplateBranches.selected.push(branch);
							$scope.filter.branchId.push(branch.id)
						})
					}
					else if(template.calendarDefaultBranches && template.calendarDefaultBranches.length > 0) {
						angular.forEach(template.calendarDefaultBranches.split(","), function (id) {
							angular.forEach($scope.calendarTemplateBranches, function (branch) {
								if(parseInt(id, 10) === branch.id) {
									$scope.calendarTemplateBranches.selected.push(branch);
									$scope.filter.branchId.push(branch.id)
								}
							})
						});
					}
					delay.resolve();
				}
			})
			return delay.promise;
		}

		$scope.search = function () {
			$scope.filter.loading = true;
			NymblCal.batchRendering(function () { // Removing current events from calendar.
				angular.forEach(NymblCal.getEvents(), function (event) {
					event.remove();
				});
			});

			var list = $('#nymblCal');
			if("Y" === $scope.calendar_day_scroll_enabled) {
				if ($scope.filter.userIds.length >= 8 && $scope.filter.startDate === $scope.filter.endDate) {
					var minWidth = $scope.filter.userIds.length * 250;
					var widthString = minWidth.toString() + "px";
					$(list[0]).css('min-width', widthString);
				} else {
					$(list[0]).css('min-width', 0);
				}
			}

			$scope.filter.startDateTime = $moment($scope.filter.startDate).startOf('day').toISOString();
			$scope.filter.endDateTime = $moment($scope.filter.endDate).endOf('day').toISOString();
			AppointmentFactory.getAppointmentAndOutstandingBalance($scope.filter).$promise.then(function (appointmentDTOs) {
				$scope.appointments = [];
				angular.forEach(appointmentDTOs, function (appointmentDTO) {
					var user1, user2, user3, user4;
					if ($scope.filter.userIds.includes(appointmentDTO.appointment.userId)) {
						user1 = _formatAsEvent(appointmentDTO, appointmentDTO.appointment.userId);
						if (NymblCal.getEventById(user1.id)) {
							NymblCal.getEventById(user1.id).remove();
						}
						$scope.appointments.push(user1);
					}
					if (appointmentDTO.appointment.userTwoId && $scope.filter.userIds.includes(appointmentDTO.appointment.userTwoId)) {
						user2 = _formatAsEvent(appointmentDTO, appointmentDTO.appointment.userTwoId);
						if (NymblCal.getEventById(user2.id)) {
							NymblCal.getEventById(user2.id).remove();
						}
						$scope.appointments.push(user2);
					}
					if (appointmentDTO.appointment.userThreeId && $scope.filter.userIds.includes(appointmentDTO.appointment.userThreeId)) {
						user3 = _formatAsEvent(appointmentDTO, appointmentDTO.appointment.userThreeId);
						if (NymblCal.getEventById(user3.id)) {
							NymblCal.getEventById(user3.id).remove();
						}
						$scope.appointments.push(user3);
					}

					// if (appointmentDTO.appointment.userFourId && $scope.filter.userIds.includes(appointmentDTO.appointment.userFourId)) {
					// 	user4 = _formatAsEvent(appointmentDTO, appointmentDTO.appointment.userFourId);
					// 	if (NymblCal.getEventById(user4.id)) {
					// 		NymblCal.getEventById(user4.id).remove();
					// 	}
					// 	$scope.appointments.push(user4);
					// }
				});

				// Need to decide whether batching appointments together or separately has better performance.
				NymblCal.batchRendering(function () {
					angular.forEach($scope.appointments, function (appointment) {
						NymblCal.addEvent(appointment);
					});
				});

				NylasFactory.getNylasEvents({userIds: $scope.filter.userIds, startDate: $scope.filter.startDate, endDate: $scope.filter.endDate }).$promise.then(function(response) {
					$scope.formatNylasEventForFC(response);
				});
				$scope.filter.loading = false;
			});
		};

		$scope.getResources = function (templateId) {
			$scope.resources = [];
			if(templateId) {
				angular.forEach($scope.users.selected, function (user) {
					$scope.resources.push({
						id: user.id,
						title: UtilService.formatName(user, 'FiL'),
						lastName: user.lastName,
						extendedProps: user
					});
				})
			} else {
				if ($scope.filter.userIds.length === 0) {
					angular.forEach($scope.users, function (user) {
						$scope.filter.userIds.push(user.id);
						$scope.resources.push({
							id: user.id,
							title: UtilService.formatName(user, 'FiL'),
							lastName: user.lastName,
							extendedProps: user
						});
					});
				} else {
					angular.forEach($scope.users, function (user) {
						if ($.inArray(user.id, $scope.filter.userIds) < 0) {

						} else {
							$scope.resources.push({
								id: user.id,
								title: UtilService.formatName(user, 'FiL'),
								lastName: user.lastName,
								extendedProps: user
							});
						}
					});
				}
			}
			return $scope.resources;
		};

		$scope.init = function (templateId) {
			var nymblCalElement = document.getElementById('nymblCal');
			NymblCal = new FullCalendar.Calendar(nymblCalElement, {
				plugins: ['dayGrid', 'timeGrid', 'interaction', 'list', 'timeline', 'resourceDayGrid', 'resourceTimeGrid', 'resourceTimeline'],
				schedulerLicenseKey: 'CC-Attribution-NonCommercial-NoDerivatives',
				// allDaySlot: true,

				header: {
					left: 'prev,next, filterToggle, addAppointment',
					center: 'title',
					right: 'datePicker, | , resourceTimeGridDay,timeGridWeek,dayGridMonth,listWeek'
				},
				height: 1300,
				navLinks: true,
				minTime: "7:00:00",
				maxTime: "20:00:00",
				firstDay: 1,
				slotDuration: "00:15:00",
				defaultView: 'resourceTimeGridDay', // Default day view with user columns. Use timeGridWeek to change to default week view.
				eventLimit: true, // Limits number of events per day. In month view, extra events will be listed in a popover in order to keep size dimensions the same.
				displayEventEnd: true,
				eventLongPressDelay: 5000, // do not begin to drag an event until they hold for 5 seconds
				eventStartEditable: true,
				eventDurationEditable: true,
				eventResourceEditable: true,
				resourceOrder: 'lastName', // sort columns by last name.
				views: {
					resourceTimeGridDay: {
						titleFormat: {
							weekday: 'long',
							day: 'numeric',
							month: 'long',
						}
					}
				},
				customButtons: {
					addAppointment: {
						text: 'New Appointment',
						click: function () {
							$scope.addAppointmentModal();
						}
					},
					datePicker: {
						text: 'Go To Day',
						click: function () {
							$('#calendar-date-picker').click();
						}
					},
					filterToggle: {
						text: 'Show Filters',
						click: function () {
							$('#filter-toggle').toggle();
						}
					}
				},
				resources: function (fetchInfo, successCallback, failureCallback) {
					successCallback($scope.getResources(templateId));
				},
				resourceRender: function(resourceObj) {
					var user = resourceObj.resource.extendedProps;
					resourceObj.el.title = user.firstName + ' ' + (user.middleName ? user.middleName : '') + ' ' + user.lastName + ' | ' + user.username;
				},
				datesRender: function (info) { // Grabs start date and end date of calendar.
					$scope.datesRender(info.view, info.el);
				},
				eventRender: function (info) {
					$scope.attachTooltip(info.event, info.el, info.view);
				},
				eventClick: function (info) {
					if (info.event.extendedProps.appointmentId) {
						$scope.updateAppointmentModal(info.event, info.el, info.view);
					}
				},
				dateClick: function (info) {
					if (!info.date._ambigTime) {
						if (info.resource) {
							$scope.addAppointmentModal(info.date, info.resource.id);
						} else {
							$scope.addAppointmentModal(info.date, false);
						}
					}
				},
				eventDrop: function (eventDropInfo) {
																																											// SCRUM-3714: Nylas imported events not draggable
					if(!$rootScope.isMobile && eventDropInfo.event.extendedProps.appointmentId  /*  nylas? && eventDropInfo.event.extendedProps.editable*/) {
						if (UserService.hasPermission('appointment_edit')) {
							$scope.updateAppointment(eventDropInfo.event, eventDropInfo.newResource);
						} else {
							alert('User does not have permission to edit appointment times.');
						}
					}
				},
				eventResize: function (eventResizeInfo) {
					$scope.updateAppointment(eventResizeInfo.event);
				},
			});
			NymblCal.render();
		};

		var _formatAsEvent = function (apptDTO, providerId) {
			var name = apptDTO.appointment.patient ? UtilService.formatName(apptDTO.appointment.patient, "FiL") : apptDTO.appointment.appointmentType.name;
			var start = $moment(apptDTO.appointment.startDateTime)._d;
			var end = $moment(apptDTO.appointment.endDateTime)._d;
			var color = _getSafely(["appointmentType", "color"]);
			var apptType = apptDTO.appointment.appointmentType.name;
			var nickName = apptDTO.appointment.patient ? apptDTO.appointment.patient.nickname : '';
			var formattedKeys = {};
			formattedKeys.appointmentId = apptDTO.appointment.id;
			formattedKeys.id = apptDTO.appointment.id + '-' + providerId;
			formattedKeys.resourceId = providerId;

			if (["no_show", "miscellaneous", "unconfirmed", "rejected"].includes(apptDTO.appointment.status)) {
				var hex = color(apptDTO.appointment);
				var opacity = 0.5;
				hex = hex.replace('#', '');
				var r = parseInt(hex.substring(0, 2), 16);
				var g = parseInt(hex.substring(2, 4), 16);
				var b = parseInt(hex.substring(4, 6), 16);

				formattedKeys.backgroundColor = 'rgba(' + r + ',' + g + ',' + b + ',' + opacity + ')';
			} else {
				formattedKeys.backgroundColor = color(apptDTO.appointment) || "rgb(81, 68, 94)";
			}

			if (apptDTO.claim) {
				var ptBalance = 0.00;
				angular.forEach(apptDTO.claim, function (claim) {
					ptBalance += claim.totalPtResponsibilityBalance;
				});
				formattedKeys.title = ptBalance > 0.00
					? ('$ ' + name + (nickName ? ' (' + nickName + ')' : '') + ' | ' + apptType)
					: name + ' | ' + apptType;

			} else {
				formattedKeys.title = name + (nickName ? ' (' + nickName + ')' : '') + ' | ' + apptType;
			}
			formattedKeys.start = start;
			formattedKeys.end = end;
			formattedKeys.startStr = start;
			formattedKeys.endStr = end;
			formattedKeys.allDay = false;
			formattedKeys.borderColor = 'lightgrey';
			formattedKeys.textColor = "white";
			formattedKeys.className = 'calEventBorder';
			formattedKeys.editable = true;
			formattedKeys.extendedProps = apptDTO;

			return formattedKeys;
		};

		var generateTooltipContent = function (event) {
      var eventAppointment = event.extendedProps.appointment;
      var apptType = _getSafely(["appointmentType", "name"]);
      var status = _getSafely(["status"]);
      var practitioner = _getSafely(["practitioner"]);
      var pracName = practitioner(eventAppointment) ? UtilService.formatName(practitioner(eventAppointment), "FL") : " ";
			pracName = pracName.replaceAll("\'", "&quot;");
      var patient = _getSafely(["patient"]);
      var notes = _getSafely(["notes"])(eventAppointment);
      var notesContent = "";
      var nickName = _getSafely(["patient", "nickname"])(eventAppointment);
      var branchName = eventAppointment && eventAppointment.branch ? eventAppointment.branch.name : " ";
			branchName = branchName.replaceAll("\'", "&quot;");
      var patientInfo = eventAppointment && eventAppointment.patient ? "(#" + eventAppointment.patient.id + ") " + " " + $moment(eventAppointment.patient.dob).format("MM/DD/YYYY") + " " + UtilService.formatName(eventAppointment.patient, "FL") : " ";
      patientInfo = patientInfo.replaceAll("\'", "&quot;");
      var prescriptionInfo = "";
			if(eventAppointment && eventAppointment.prescription){
				prescriptionInfo = eventAppointment.prescription.deviceType.name + ' #' + eventAppointment.prescription.id;
				if(eventAppointment.prescriptionTwo){
					prescriptionInfo = prescriptionInfo + ', ' + eventAppointment.prescriptionTwo.deviceType.name + ' #' + eventAppointment.prescriptionTwo.id;
				}
				prescriptionInfo = prescriptionInfo.replaceAll("\'", "&quot;");
			}
			if (notes && notes.length > 0) {
        notes = notes.replace(/'/g, "&#39;");
        notes = notes.replace("{{", "[[");
        notes = notes.replace("}}", "]]");
        notesContent = "<div>" + notes + "</div>";
      } else if (!eventAppointment && event.extendedProps.body) {
				notes = event.extendedProps.body;
	      notes = notes.replace(/'/g, "&#39;");
	      notes = notes.replace("{{", "[[");
	      notes = notes.replace("}}", "]]");
	      notesContent = "<div>" + notes + "</div>";
      }
      // AppointmentStatusFactory.get().$promise.then(function (statuss) {
      //   $scope.appointmentStatusFullList = statuss;
      // });

      if (eventAppointment && status(eventAppointment)) {
        var text = status(eventAppointment);
        text.toUpperCase();
        text.charAt(0).toUpperCase();
        text.slice(1);
        text.replace(/_/g, " ");
        var statuss = status(eventAppointment).charAt(0).toUpperCase() + status(eventAppointment).replace(/_/g, " ").slice(1);

      }
      var content = "";
			if (eventAppointment) {
        content = "'<div>" + nickName + "</div>" +
	        "<div>" + branchName.replace(/'/, "") + "</div>" +
	        "<div>" + apptType(eventAppointment).replace(/'/, " ") + " | " + statuss + "</div>" +
	        "<div>" + $moment(eventAppointment.startDateTime).format("h:mm A") + " - " + $moment(eventAppointment.endDateTime).format("h:mm A") + "</div>" +
	        "<div>" + "Practitioner: " + pracName + "</div>" +
	        "<div>" + patientInfo + "</div>" +
        "<div>" + prescriptionInfo + "</div>" +
	      "<div>" + (patient(eventAppointment) ?
	      (PatientService.getPreferredPhoneNumber(patient(eventAppointment)) ? "Preferred: " + UtilService.formatPhone(PatientService.getPreferredPhoneNumber(patient(eventAppointment))) :
		      patient(eventAppointment).cellPhone ? "C: " + UtilService.formatPhone(patient(eventAppointment).cellPhone) :
			      patient(eventAppointment).homePhone ? "Home: " + UtilService.formatPhone(patient(eventAppointment).homePhone) : "")
	      : " ") + "</div>" +
	      notesContent + "'";
      return content;
			} else {
				content = "'<div>" + event.title + "</div>" +
					"<div>" + "Practitioner: " + event.extendedProps.userName + "</div>" +
					"<div>" + $moment(event.start).format("h:mm A") + " - " + $moment(event.end).format("h:mm A") + "</div>" +
					notesContent + "'";
				return content;
			}
    };
		/* Attach tooltip to event */
		$scope.attachTooltip = function (event, element, view) {
			var currentView = view.constructor.name;
			var placement;
			switch (currentView) {
				case "MonthView":
					placement = "top";
					break;
				case "resourceDayGridWeekView":
					placement = "top";
					break;
				default:
					placement = "top";
			}
			element.setAttribute("uib-tooltip-html", generateTooltipContent(event));
			element.setAttribute("tooltip-append-to-body", true);
			element.setAttribute("tooltip-placement", placement);
			$compile(element)($scope);
		};

		var addNewAppointmentEvent = function (appointment) {
			var tempDTO = {
				appointment: appointment,
				claim: []
			};
			var user1 = _formatAsEvent(tempDTO, appointment.userId);
			NymblCal.addEvent(user1);
			var user2, user3, user4;

			if (appointment.userTwoId) {
				user2 = _formatAsEvent(tempDTO, appointment.userTwoId);
				NymblCal.addEvent(user2);
			}
			if (appointment.userThreeId) {
				user3 = _formatAsEvent(tempDTO, appointment.userThreeId);
				NymblCal.addEvent(user3);
			}
			// if (appointment.userFourId) {
			// 	user4 = _formatAsEvent(tempDTO, appointment.userFourId);
			// 	NymblCal.addEvent(user4);
			// }
		};

		var removePreviousAppointmentEvents = function (appointment) { // Removing all events attached to this appointment. Checks for events with old and new user, user2, and user3 ids.
			if (NymblCal.getEventById(appointment.id)) {
				NymblCal.getEventById(appointment.id).remove();
			}
			if (appointment.prevUserTwoId && NymblCal.getEventById(appointment.id + '-' + appointment.prevUserTwoId)) {
				NymblCal.getEventById(appointment.id + '-' + appointment.prevUserTwoId).remove();
			}
			if (appointment.prevUserThreeId && NymblCal.getEventById(appointment.id + '-' + appointment.prevUserThreeId)) {
				NymblCal.getEventById(appointment.id + '-' + appointment.prevUserThreeId).remove();
			}
			if (appointment.prevUserFourId && NymblCal.getEventById(appointment.id + '-' + appointment.prevUserFourId)) {
				NymblCal.getEventById(appointment.id + '-' + appointment.prevUserFourId).remove();
			}
			if (NymblCal.getEventById(appointment.id + '-' + appointment.userId)) {
				NymblCal.getEventById(appointment.id + '-' + appointment.userId).remove();
			}
			if (appointment.userTwoId && NymblCal.getEventById(appointment.id + '-' + appointment.userTwoId)) {
				NymblCal.getEventById(appointment.id + '-' + appointment.userTwoId).remove();
			}
			if (appointment.userThreeId && NymblCal.getEventById(appointment.id + '-' + appointment.userThreeId)) {
				NymblCal.getEventById(appointment.id + '-' + appointment.userThreeId).remove();
			}
			if (appointment.userFourId && NymblCal.getEventById(appointment.id + '-' + appointment.userFourId)) {
				NymblCal.getEventById(appointment.id + '-' + appointment.userFourId).remove();
			}
		};

		$scope.addAppointmentModal = function (startTime, userId) {
			var modalInstance = $uibModal.open({
				templateUrl: "addAppointmentModal.html",
				backdrop: "static",
				keyboard: false,
				size: "lg",
				// controller: "AppointmentModalCalendarCtrl",
				controller: "AppointmentCtrl",
				resolve: {
					isModal: function () {
						return true;
					},
					apptDetails: function () {
						var apptDetails = {
							apptId: false,
							profilePatientId: false,
							startTime: startTime ? startTime : false,
							userId: userId ? parseInt(userId) : false
						};
						return apptDetails;
					}
				}
			});
			modalInstance.result.then(function (result) {
				if (result === 'cancel') return;
				if (result.id) {
					AppointmentFactory.get({id: result.id}, function (appointment) {
						addNewAppointmentEvent(appointment);
					});
				} else {
					$scope.search();
				}
			});
		};

		$scope.updateAppointmentModal = function (event, element, view) {
			var modalInstance = $uibModal.open({
				templateUrl: "updateAppointmentModal.html",
				backdrop: "static",
				keyboard: false,
				size: "lg",
				controller: "AppointmentCtrl",
				resolve: {
					isModal: function () {
						return true;
					},
					apptDetails: function () {
						var apptDetails = {
							apptId: event.extendedProps.appointmentId,
							profilePatientId: false,
							startTime: false,
							userId: false
						};
						return apptDetails;
					}
				}
			});
			modalInstance.result.then(function (result) {
				if (result === 'cancel') return;
				if (result === 'Deleted series') $scope.search();
				if (result === 'delete') {
					removePreviousAppointmentEvents(event.extendedProps.appointment);
					return;
				}
				if (result.id && result !== 'delete') { // When existing appointment is updated.
					removePreviousAppointmentEvents(result);
					AppointmentFactory.get({id: event.extendedProps.appointmentId}).$promise.then(function (appointment) {
						if (appointment && appointment.status !== 'cancelled') {
							addNewAppointmentEvent(appointment);
						}
					});
				} else {
					// New appointment(s).
					angular.forEach(result, function (appointment) {
						var tempDTO = {
							appointment: appointment,
							claim: []
						};
						var formattedAppt = _formatAsEvent(tempDTO);
						NymblCal.addEvent(formattedAppt);
					});
				}
			});
		};

		$scope.updateAppointment = function (event, newResource) {
			AppointmentFactory.get({id: event.extendedProps.appointmentId}, function (appt) {
				if (!appt.rescheduledTo) {
					var appointment = appt;
					console.log("Calendar updateAppointment start: " + event.start);
					console.log("Calendar updateAppointment end: " + event.end);
					appointment.startDateTime = $moment(event.start).toISOString();
					appointment.endDateTime = $moment(event.end).toISOString();
					appointment.updatedById = UserService.getCurrentUserId();
					var eventUserId = event.id.split('-').pop();
					if (eventUserId == appt.userId && NymblCal.getEventById(appt.id + '-' + appt.userId)) {
						NymblCal.getEventById(appt.id + '-' + appt.userId).remove();
						if (newResource) appointment.userId = newResource.id;
					}
					if (eventUserId == appt.userTwoId) {
						NymblCal.getEventById(appt.id + '-' + appt.userTwoId).remove();
						if (newResource) appointment.userTwoId = newResource.id;
					}
					if (eventUserId == appt.userThreeId) {
						NymblCal.getEventById(appt.id + '-' + appt.userThreeId).remove();
						if (newResource) appointment.userThreeId = newResource.id;
					}
					// if (eventUserId == appt.userFourId) {
					// 	NymblCal.getEventById(appt.id + '-' + appt.userFourId).remove();
					// 	if (newResource) appointment.userFourId = newResource.id;
					// }

					if ($scope.nylasActive) { //NYM-392: this runs if the COMPANY has nylas on, not if the USER has it on (could be editing another user's appointment)
						AppointmentFactory.updateNylas({timeZoneOffset: new Date().getTimezoneOffset() / -60}, appointment).$promise.then(function (nylasResponse) {
						});
					}

					AppointmentFactory.save(appointment).$promise.then(function (response) {
						removePreviousAppointmentEvents(response);
						AppointmentFactory.get({id: event.extendedProps.appointmentId}, function (newAppointment) {
							if (response) {
								addNewAppointmentEvent(newAppointment);
							}
						});
					});
				}
			});
		};

		$scope.formatNylasEventForFC = function (events) { // TODO--NOT SURE HOW TO TEST
			$scope.formattedEvents = [];

			if(events.length <= 0){
				return;
			}

			//SCRUM-5307: ensure we remove existing Nylas events.  Multiple searches in rapid succession can lag and cause double/triple of the remote appointments
			angular.forEach(NymblCal.getEvents(), function (event) {
				if(!event.extendedProps.appointmentId){
					event.remove();
				}
			});

			angular.forEach(events, function (event) {
				var formattedKeys = {};
				formattedKeys.appointmentId = null;
				formattedKeys.id = null;
				formattedKeys.resourceId = event.metadata && event.metadata.nymblUserId ? parseInt(event.metadata.nymblUserId) : "";
				formattedKeys.backgroundColor = $scope.nylasEventColor;

				formattedKeys.title = event.title;
				formattedKeys.allDay = false;
				if (event.when.object === "DATE") {
					formattedKeys.allDay = true;
					formattedKeys.start = event.when.date;
				} else if (event.when.object === "TIMESPAN") {
					formattedKeys.start =  $moment.unix(event.when.startTime).format();
					formattedKeys.end =  $moment.unix(event.when.endTime).format();
				} else if (event.when.object === "DATESPAN") {
					formattedKeys.allDay = true;
					formattedKeys.start = event.when.startDate;
					formattedKeys.end = event.when.endDate;
				} else {
						formattedKeys.start = $moment(event.when.startTime)._d;
						formattedKeys.end = $moment(event.when.endTime)._d;
				}
				formattedKeys.borderColor = 'lightgrey';
				formattedKeys.textColor = "white";
				formattedKeys.className = 'calEventBorder';
				formattedKeys.editable = false; // SCRUM-3714: Nylas imported events not draggable
				formattedKeys.extendedProps = {userName: event.metadata && event.metadata.userName ? event.metadata.userName : ""};
				NymblCal.addEvent(formattedKeys);
			});
		};

	}]);
