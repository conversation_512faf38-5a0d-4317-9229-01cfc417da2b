app.controller('BranchCtrl', BranchCtrl);
BranchCtrl.$inject = ['$rootScope', '$scope', '$timeout', 'uiUploader', 'BranchFactory', 'BranchService', 'ClearingHouseFactory', 'StatesFactory', 'UserService', 'FileFactory'];

function BranchCtrl($rootScope, $scope, $timeout, uiUploader, BranchFactory, BranchService, ClearingHouseFactory, StatesFactory, UserService, FileFactory) {

	$rootScope.page = {
		title: 'Maintenance',
		subtitle: 'Branches',
		view: 'branch'
	};

	$scope.errorMessage = "";
	$scope.hasPermission = UserService.hasPermission;
	$scope.userService = UserService;

	$scope.submitted = false;
	$scope.activeRecord = undefined;
	$scope.editingRecord = undefined;
	$scope.editing = false;
	$scope.currentBanchLogo = null;
	$scope.currentBranchLogoURL = null;

	$scope.filter = {
		searchTerm: "",
		active: ""
	};

	$scope.dto = {
		branch: {},
		branchLogo: ""
	};

	$scope.fileRecord = {
		description: "",
		fileName: "",
		form: "logo",
		providedById: UserService.getCurrentUserId(),
		url: ""
	};

	$scope.states = StatesFactory.get();

	$scope.clearingHouses = ClearingHouseFactory.query();

	$scope.options = {
		focus: false,
		airMode: false,
		toolbar: [
			['headline', ['style']],
			['style', ['bold', 'italic', 'underline', 'superscript', 'subscript', 'strikethrough', 'clear']],
			['fontface', ['fontname']],
			['textsize', ['fontsize']],
			['fontclr', ['color']],
			['alignment', ['ul', 'ol', 'paragraph', 'lineheight']],
			['height', ['height']],
			['insert', ['picture']]
		],
		dialogsInBody: true
	};

	$scope.$watch('editing', function (value) {
		$(".note-editable").attr('contenteditable', value);
	});

	$scope.setActive = function (record) {
		$scope.activeRecord = angular.copy(record);
		$scope.saveParentId = record.parentId; // parent ID gets unset in the chosen because the array is cleared below.  Save it and reset
		if (record) $scope.getBranchLogo(record.id);
		$scope.editing = false;
		$scope.activeBranches = [];
		BranchFactory.active().$promise.then(function (response) {
			angular.forEach(response, function (entry, index) {
				if ($scope.activeRecord.id !== entry.id) {
					$scope.activeBranches.push(entry);
				}
			});
			$timeout(function () {
				$('#parentId').prop('disabled', !$scope.editing).trigger("chosen:updated");
				$scope.activeRecord.parentId = $scope.saveParentId;
			}, 50);
		});
	};

	$scope.getBranchLogo = function (branchId) {
		FileFactory.getBranchLogo({branchId: branchId}).$promise.then(function (result) {
			$scope.dto.branchLogo = result.branchLogo;
		});
	};

	$scope.edit = function () {
		$scope.editing = true;
		$scope.editingRecord = angular.copy($scope.activeRecord);
	};

	$scope.newRecord = function () {
		$scope.editingRecord = undefined;
		$scope.activeRecord = {
			id: 0,
			useBranchName: false,
			useBranchLogo: false,
			hideCompanyName: false,
			hideCompanyLogo: false,
			hideServiceFacilityLocation: false,
			useSalesTax: false,
			salesTaxCalculationValue: "allowable",
			active: true,
			taxIdType: "leave_blank",
			parentId: null,
			useRealTimeRulesEngine: false
		};
		$scope.editing = true;
		if ($scope.branchForm) $scope.branchForm.$setPristine();
		$scope.activeBranches = BranchFactory.active();
	};

	$scope.cancel = function () {
		$scope.editing = false;
		$scope.submitted = false;
		$scope.setActive($scope.editingRecord);
		if ($scope.branchForm) $scope.branchForm.$setPristine();
	};

	$scope.save = function (form) {
		$('#save-form').button('loading');
		$scope.submitted = true;
		$scope.errorMessage = "";
		if (form.$valid) {
			$scope.editing = false;
			delete $scope.activeRecord.parent;
			$scope.dto.branch = $scope.activeRecord;
			BranchFactory.saveDTO($scope.dto).$promise.then(function (response) {
				$scope.activeRecord = response.branch;
				$scope.search();
				if ($scope.branchForm) $scope.branchForm.$setPristine();
				$scope.editing = false;
				$scope.submitted = false;
				$('#save-form').button('reset');
			}, function (error) {
				$scope.editing = true;
				if (error.data && error.data.errors && error.data.errors.name) {
					$scope.errorMessage = error.data.errors.name;
				} else if (error.data && typeof error.data === 'string') {
					$scope.errorMessage = error.data;
				} else {
					$scope.errorMessage = "There was an error saving the branch. Please check your input and try again.";
				}
				$('#save-form').button('reset');
			});
		} else {
			$('#save-form').button('reset');
		}
	};

	$scope.delete = function () {
		if (confirm('Are you sure you want to delete the current Branch?')) {
			$scope.editing = false;
			var id = $scope.activeRecord.id;
			BranchFactory.deleteBranch({id: id}, function () {
				$scope.activeRecord = undefined;
				$scope.filter = {
					searchTerm: "",
					active: ""
				};
				$scope.search();
				if ($scope.branchForm) {
					$scope.branchForm.$setPristine();
				}
			}, function (error) {
				if (error.data.message === "foreign key constraint fails") {
					$scope.errorMessage = "This branch is attached to one or more appointments or inventory items and cannot be deleted.";
				} else {
					$scope.errorMessage = "There was an error with this delete attempt. Please contact Nymbl support";
				}
				$scope.loading = false;
			});
		}
	};

	$scope.search = function () {
		$scope.errorMessage = "";
		$scope.branches = [];
		BranchFactory.search({
			q: $scope.filter.searchTerm,
			active: $scope.filter.active
		}).$promise.then(function (response) {
			angular.forEach(response, function (entry, index) {
				entry.$name = BranchService.getName(entry, "");
				$scope.branches.push(entry);
			});
		});
	};

	$scope.search();

	$timeout(function () {
		$(".note-editable").attr('contenteditable', $scope.editing);
		$('#inClearingHouseId').prop('disabled', !$scope.editing).trigger("chosen:updated");
		$('#outClearingHouseId').prop('disabled', !$scope.editing).trigger("chosen:updated");
	}, 500);

}
