@charset "UTF-8";
/*@import url(fonts.googleapis.css);*/

#content > .page {
    overflow-y: scroll;

    height: calc(100vh - 45px);
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: scroll;
}

.tab-content {
    background: #FFFFFF;
}

.page {
    padding: 0 20px;
}

.page-full {
    height: 100%;
    padding: 0;
}

.page-core {
    position: fixed;

    overflow: auto;

    width: 100%;
    height: 100%;
    padding: 60px 10px;

    background-color: #3F4E62;
}

.page-tree {
    height: 100%;
    padding: 0;
}

.page-chat #inbox {
    margin: 20px -15px -15px;
}

.page-chat #inbox > li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.page-chat #inbox > li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;

    border-bottom: 0;
}

.page-chat #inbox > li > a {
    position: relative;

    display: block;

    padding: 15px;

    color: #616F77;
}

.page-chat #inbox > li > a:hover {
    text-decoration: none;

    background-color: rgba(255, 255, 255, 0.5);
}

.page-chat #inbox > li > a:hover .chat-actions {
    display: block;
}

.page-chat #inbox > li > a:focus {
    text-decoration: none;
}

.page-chat #inbox > li > a .media {
    position: relative;

    height: 45px;
}

.page-chat #inbox > li > a .media .media-body {
    position: absolute;
    top: 0;

    display: inline-block;

    width: 100%;
    padding-right: 40px;
}

.page-chat #inbox > li > a .message {
    display: block;
    overflow: hidden;

    width: 85%;

    white-space: nowrap;
    text-overflow: ellipsis;
}

.page-chat #inbox > li > a .chat-actions {
    font-size: 11px;

    display: none;
}

.page-chat #inbox > li > a .chat-actions > span {
    margin-left: 4px;

    cursor: pointer;
}

.page-chat #inbox > li > a .chat-actions > span.archive:hover {
    color: #FF4A43;
}

.page-chat #inbox > li > a .chat-actions > span.mark-unread:hover {
    color: #16A085;
}

.page-chat #inbox > li > a .chat-actions > span.mark-unread:hover i:before {
    content: "\f111";
}

.page-chat #inbox > li > a .chat-actions > span.mark-readed {
    color: #16A085;
}

.page-chat #inbox > li > a .chat-actions > span.mark-readed:hover {
    color: #616F77;
}

.page-chat #inbox > li > a .chat-actions > span.mark-readed:hover i:before {
    content: "\f10c";
}

.page-chat #inbox > li.active > a {
    background-color: white;
}

.page-chat ul.chats > li .media-body {
    padding: 10px;
}

.page-chat ul.chats > li.in .media-body {
    padding-left: 20px;

    background-color: white;
}

.page-chat ul.chats > li.out .media-body {
    padding-right: 20px;

    background-color: rgba(255, 255, 255, 0.5);
}

ul.chats {
    list-style: none;
}

ul.chats > li {
    font-size: 12px;

    margin: 20px auto;
}

ul.chats > li:last-child {
    margin-bottom: 0;
}

ul.chats > li .media {
    position: relative;
    z-index: 1;
}

ul.chats > li .name,
ul.chats > li .datetime {
    font-size: 14px;
    font-weight: 400;
}

ul.chats > li .datetime {
    font-size: 12px;

    color: #95A2A9;
}

ul.chats > li.conversation-divider {
    font-size: 10px;

    text-align: center;

    color: white;
}

ul.chats > li.conversation-divider span {
    position: relative;

    display: inline-block;

    padding: 4px 10px;

    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);

    -ms-border-radius: 2px;
    -o-border-radius: 2px;
}

ul.chats > li.in .media-body {
    margin-left: 65px;
    padding-left: 5px;

    text-align: left;

    border-left: 3px solid #16A085;
}

ul.chats > li.in .name {
    color: #16A085;
}

ul.chats > li.in .media:after {
    position: absolute;
    z-index: 1;
    top: 18px;
    left: 36px;

    width: 0;
    height: 0;

    content: "";

    border-top: 4px solid transparent;
    border-right: 4px solid #16A085;
    border-bottom: 4px solid transparent;
}

ul.chats > li.out .media-body {
    margin-right: 65px;
    padding-right: 5px;

    text-align: right;

    border-right: 3px solid #E05D6F;
}

ul.chats > li.out .name {
    color: #E05D6F;
}

ul.chats > li.out .name,
ul.chats > li.out .datetime {
    text-align: right;
}

ul.chats > li.out .media:after {
    position: absolute;
    z-index: 1;
    top: 18px;
    right: 36px;

    width: 0;
    height: 0;

    content: "";

    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 4px solid #E05D6F;
}

.chat-form {
    clear: both;
    overflow: hidden;

    padding: 10px;

    background-color: #DBE0E2;
}

.page.page-mail #mail-nav #mail-folders > li > a,
.page.page-mail #mail-nav #mail-labels > li > a {
    color: #616F77;
}

.page.page-mail #mail-nav #mail-folders > li > a .badge,
.page.page-mail #mail-nav #mail-labels > li > a .badge {
    margin-top: 2px;
}

.page.page-mail #mail-nav #mail-folders > li > a:hover,
.page.page-mail #mail-nav #mail-labels > li > a:hover {
    background-color: white;
}

.page.page-mail #mail-nav #mail-folders > li.active > a,
.page.page-mail #mail-nav #mail-labels > li.active > a {
    color: white;
}

.page.page-mail #mail-nav #mail-folders > li.active > a:hover,
.page.page-mail #mail-nav #mail-labels > li.active > a:hover {
    background-color: #10ACDD;
}

.page.page-mail .note-editable {
    background-color: white;
}

.page.page-mail #mails-list {
    margin-bottom: 0;
}

.page.page-mail #mails-list > li {
    margin-bottom: 1px;
    padding: 20px;

    border: 0;
    border-left: 3px solid;
}

.page.page-mail #mails-list > li .thumb > div {
    font-family: "Dosis", "Arial", sans-serif;
    font-size: 26px;

    height: 40px;
    padding: 1px 0;

    text-align: center;
    text-transform: uppercase;

    color: white;
    background-color: #616F77;
}

.page.page-mail #mails-list > li .media-heading > a {
    font-weight: 700;
}

.page.page-mail #mails-list > li .media-heading > a:hover {
    text-decoration: none;
}

.page.page-mail #mails-list > li .controls {
    display: inline-block;

    margin-top: -2px;
    margin-right: 10px;

    text-align: center;
    vertical-align: top;
}

.page.page-mail #mails-list > li .controls .favourite {
    font-size: 16px;
}

.page.page-mail #mails-list > li .controls .favourite:hover,
.page.page-mail #mails-list > li .controls .favourite:focus:hover {
    color: #798992;
}

.page.page-mail #mails-list > li .controls .favourite:focus {
    color: #FFC100;
}

.page.page-mail #mails-list > li .controls .favourite.active > i:before {
    content: "\f005";
}

.page.page-mail #mails-list > li .controls .mail-select {
    width: 20px;
}

.page-search-results .search-bar {
    margin: 10px 0;
}

.page-search-results .search-results {
    margin: 0;
    padding: 10px 0;

    list-style: none;
}

.page-search-results .search-results > .search-result {
    padding: 15px 0;

    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.page-search-results .search-results > .search-result:last-child {
    padding-bottom: 0;

    border-bottom: 0;
}

.page-search-results .search-results > .search-result .cover {
    padding: 20px;

    -webkit-transition: all 0.25s linear;
    -moz-transition: all 0.25s linear;
    transition: all 0.25s linear;

    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    background-color: white;

    -ms-border-radius: 2px;
    -o-border-radius: 2px;
}

.page-search-results .search-results > .search-result .cover h4 {
    font-family: "Dosis", "Arial", sans-serif;
    font-weight: 700;

    margin: 0 0 10px 0;
    padding: 0;
}

.page-search-results .search-results > .search-result .cover h4 a {
    color: #3F484D;
}

.page-search-results .search-results > .search-result .cover h4 a:hover {
    text-decoration: none;

    color: #418BCA;
}

.page-search-results .search-results > .search-result .cover > p {
    color: #95A2A9;
}

.page-search-results .search-results > .search-result .cover > a {
    font-size: 12px;

    color: #418BCA;
}

.page-search-results .search-results > .search-result .cover > a:hover {
    color: #69A3D5;
}

.page-search-results .search-results > .search-result:hover .cover {
    background-color: #FAFAFA;
}

.page-search-results .search-results > .search-result .media img {
    width: 130px;
    max-height: 85px;
    margin-right: 10px;
}

.page-search-results .search-results > .search-result .media img.img-circle {
    width: 85px;
}

.page-search-results .search-results > .search-result .media > a > i {
    font-size: 3.5em;
    line-height: 85px;

    width: 130px;
    margin-right: 10px;

    text-align: center;

    color: #616F77;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.05);

    -ms-border-radius: 2px;
    -o-border-radius: 2px;
}

.page-search-results .search-results > .search-result .media > a:hover i {
    color: #418BCA;
}

.page-search-results .search-results > .search-result .media .media-body small {
    font-size: 10px;

    color: #95A2A9;
}

.page-search-results .refine-results .slider {
    width: 100%;
}

.search-filters > a {
    font-size: 12px;
    line-height: 36px;

    margin: 0 2px;
    padding: 5px 15px;

    color: #616F77;
    border: 1px solid #616F77;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;

    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}

.search-filters > a:hover {
    text-decoration: none;

    color: #418BCA;
    border: 1px solid #418BCA;
}

.search-filters > a.active {
    color: white;
    border-color: transparent;
    background-color: #418BCA;
}

html.boxed-layout {
    height: 100%;
}

html.boxed-layout body {
    height: 100%;

    background-color: #CBD1D4;
}

html.boxed-layout #wrap {
    position: relative;

    min-height: 100%;
    margin: 0 auto;

    background-color: #E7EAEB;
    -webkit-box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
}

html.boxed-layout #header {
    position: relative !important;
}

html.boxed-layout .appWrapper.header-static.aside-fixed #header .branding {
    left: auto;

    margin-left: -250px;
}

html.boxed-layout .appWrapper.header-static.aside-fixed.sidebar-sm #header .branding {
    margin-left: -80px;
}

html.boxed-layout .appWrapper.header-static.aside-fixed.sidebar-xs #header .branding {
    margin-left: -40px;
}

html.boxed-layout .appWrapper.header-fixed.aside-static #header {
    position: fixed !important;
    right: auto;
    left: auto;

    width: 100%;
}

@media only screen and (min-width: 768px) {
    html.boxed-layout #wrap {
        width: 750px;
    }

    html.boxed-layout .appWrapper.header-fixed.aside-static #header {
        width: 750px;
    }
}

@media only screen and (min-width: 992px) {
    html.boxed-layout #wrap {
        width: 970px;
    }

    html.boxed-layout .appWrapper.header-fixed.aside-static #header {
        width: 970px;
    }
}

@media only screen and (min-width: 1200px) {
    html.boxed-layout #wrap {
        width: 1170px;
    }

    html.boxed-layout .appWrapper.header-fixed.aside-static #header {
        width: 1170px;
    }
}

@media only screen and (min-width: 1100px) {
    .hz-menu #sidebar {
        bottom: auto !important;

        width: 100%;
    }

    .hz-menu #sidebar .slimScrollDiv {
        overflow: visible !important;
    }

    .hz-menu #sidebar .panel-group {
        overflow: visible !important;

        height: auto !important;
    }

    .hz-menu #sidebar .panel-group .panel-heading,
    .hz-menu #sidebar .panel-group .charts,
    .hz-menu #sidebar .panel-group .settings {
        display: none;
    }

    .hz-menu #sidebar .panel-group .panel-body {
        padding: 0 10px;
    }

    .hz-menu #sidebar .panel-group .panel-collapse.collapse {
        display: block !important;
    }

    .hz-menu #sidebar .panel-group .panel-group {
        height: auto;
    }

    .hz-menu #sidebar .panel-group .panel-group .panel,
    .hz-menu #sidebar .panel-group .panel-group .panel-collapse,
    .hz-menu #sidebar .panel-group .panel-group .panel-body {
        height: auto !important;
    }

    .hz-menu #header {
        z-index: 999;

        -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    }

    .hz-menu #header .sidebar-collapse {
        display: none;
    }

    .hz-menu #navigation > li {
        display: inline-block;
        float: left;
        overflow: visible !important;
    }

    .hz-menu #navigation > li > a {
        font-size: 12px;
        font-weight: 300;

        padding: 15px;

        text-align: center;
    }

    .hz-menu #navigation > li > a .badge {
        left: 5px;
    }

    .hz-menu #navigation > li > a .label {
        top: 5px;
        right: auto;
        left: 5px;
    }

    .hz-menu #navigation > li > a > i {
        display: block;

        margin: 0 auto 2px;
    }

    .hz-menu #navigation > li.dropdown > a > i:last-of-type {
        top: auto;
        bottom: 3px;
        left: 50%;

        margin-left: -10px;
    }

    .hz-menu #navigation > li.dropdown > a > i:last-of-type:before {
        content: "\f107";
    }

    .hz-menu #navigation > li.dropdown > ul {
        position: absolute;
        top: 100%;
        left: 0;

        width: auto;
        min-width: 220px;

        background-color: #3C3246;
    }

    .hz-menu #navigation > li.dropdown.open > ul {
        display: none !important;
    }

    .hz-menu #navigation > li.dropdown:hover > ul {
        display: block !important;
    }

    .hz-menu #navigation > li li.submenu > ul {
        position: absolute;
        top: 0;
        left: 100%;

        width: auto;
        min-width: 220px;

        background-color: #3C3246;
    }

    .hz-menu #navigation > li li.submenu.open > ul {
        display: none !important;
    }

    .hz-menu #navigation > li li.submenu:hover > ul {
        display: block !important;
    }

    .hz-menu #content {
        top: 110px !important;
        left: 0 !important;
    }

    .appWrapper.hz-menu.aside-static #controls {
        width: 100%;
    }

    .appWrapper.hz-menu.aside-static #controls #sidebar {
        min-height: 0;
    }

    .appWrapper.hz-menu.aside-static #content {
        padding-top: 110px !important;
        padding-left: 0 !important;
    }

    .appWrapper.hz-menu.aside-static.header-static #content {
        padding-top: 60px !important;
    }

    .appWrapper.hz-menu.header-static.aside-fixed #header {
        position: fixed;
        right: 0;
        left: 0;
    }

    .appWrapper.hz-menu.header-static.aside-fixed #content {
        padding-top: 110px !important;
        padding-left: 0 !important;
    }
}

.appWrapper.rtl {
    /*******************************************************************************
 *              bootstrap-rtl (Version 3.2.0-rc7)
 *      Author: Morteza Ansarinia <<EMAIL>> (http://github.com/morteza)
 *  Created on: September 11,2014
 *     Project: bootstrap-rtl
 *   Copyright: See the file "LICENSE.md" for the full license governing this code.
 *******************************************************************************/
    direction: rtl;
    unicode-bidi: embed;
}

.appWrapper.rtl .list-unstyled {
    padding-right: 0;
    padding-left: initial;
}

.appWrapper.rtl .list-inline {
    margin-right: -5px;
    margin-left: 0;
    padding-right: 0;
    padding-left: initial;
}

.appWrapper.rtl dd {
    margin-right: 0;
    margin-left: initial;
}

@media (min-width: 768px) {
    .appWrapper.rtl .dl-horizontal dt {
        float: right;
        clear: right;

        text-align: left;
    }

    .appWrapper.rtl .dl-horizontal dd {
        margin-right: 180px;
        margin-left: 0;
    }
}

.appWrapper.rtl blockquote {
    border-right: 5px solid #EEEEEE;
    border-left: 0;
}

.appWrapper.rtl .blockquote-reverse,
.appWrapper.rtl blockquote.pull-left {
    padding-right: 0;
    padding-left: 15px;

    text-align: left;

    border-right: 0;
    border-left: 5px solid #EEEEEE;
}

.appWrapper.rtl .col-xs-1,
.appWrapper.rtl .col-sm-1,
.appWrapper.rtl .col-md-1,
.appWrapper.rtl .col-lg-1,
.appWrapper.rtl .col-xs-2,
.appWrapper.rtl .col-sm-2,
.appWrapper.rtl .col-md-2,
.appWrapper.rtl .col-lg-2,
.appWrapper.rtl .col-xs-3,
.appWrapper.rtl .col-sm-3,
.appWrapper.rtl .col-md-3,
.appWrapper.rtl .col-lg-3,
.appWrapper.rtl .col-xs-4,
.appWrapper.rtl .col-sm-4,
.appWrapper.rtl .col-md-4,
.appWrapper.rtl .col-lg-4,
.appWrapper.rtl .col-xs-5,
.appWrapper.rtl .col-sm-5,
.appWrapper.rtl .col-md-5,
.appWrapper.rtl .col-lg-5,
.appWrapper.rtl .col-xs-6,
.appWrapper.rtl .col-sm-6,
.appWrapper.rtl .col-md-6,
.appWrapper.rtl .col-lg-6,
.appWrapper.rtl .col-xs-7,
.appWrapper.rtl .col-sm-7,
.appWrapper.rtl .col-md-7,
.appWrapper.rtl .col-lg-7,
.appWrapper.rtl .col-xs-8,
.appWrapper.rtl .col-sm-8,
.appWrapper.rtl .col-md-8,
.appWrapper.rtl .col-lg-8,
.appWrapper.rtl .col-xs-9,
.appWrapper.rtl .col-sm-9,
.appWrapper.rtl .col-md-9,
.appWrapper.rtl .col-lg-9,
.appWrapper.rtl .col-xs-10,
.appWrapper.rtl .col-sm-10,
.appWrapper.rtl .col-md-10,
.appWrapper.rtl .col-lg-10,
.appWrapper.rtl .col-xs-11,
.appWrapper.rtl .col-sm-11,
.appWrapper.rtl .col-md-11,
.appWrapper.rtl .col-lg-11,
.appWrapper.rtl .col-xs-12,
.appWrapper.rtl .col-sm-12,
.appWrapper.rtl .col-md-12,
.appWrapper.rtl .col-lg-12 {
    position: relative;

    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
}

.appWrapper.rtl .col-xs-1,
.appWrapper.rtl .col-xs-2,
.appWrapper.rtl .col-xs-3,
.appWrapper.rtl .col-xs-4,
.appWrapper.rtl .col-xs-5,
.appWrapper.rtl .col-xs-6,
.appWrapper.rtl .col-xs-7,
.appWrapper.rtl .col-xs-8,
.appWrapper.rtl .col-xs-9,
.appWrapper.rtl .col-xs-10,
.appWrapper.rtl .col-xs-11,
.appWrapper.rtl .col-xs-12 {
    float: right;
}

.appWrapper.rtl .col-xs-12 {
    width: 100%;
}

.appWrapper.rtl .col-xs-11 {
    width: 91.66666667%;
}

.appWrapper.rtl .col-xs-10 {
    width: 83.33333333%;
}

.appWrapper.rtl .col-xs-9 {
    width: 75%;
}

.appWrapper.rtl .col-xs-8 {
    width: 66.66666667%;
}

.appWrapper.rtl .col-xs-7 {
    width: 58.33333333%;
}

.appWrapper.rtl .col-xs-6 {
    width: 50%;
}

.appWrapper.rtl .col-xs-5 {
    width: 41.66666667%;
}

.appWrapper.rtl .col-xs-4 {
    width: 33.33333333%;
}

.appWrapper.rtl .col-xs-3 {
    width: 25%;
}

.appWrapper.rtl .col-xs-2 {
    width: 16.66666667%;
}

.appWrapper.rtl .col-xs-1 {
    width: 8.33333333%;
}

.appWrapper.rtl .col-xs-pull-12 {
    right: auto;
    left: 100%;
}

.appWrapper.rtl .col-xs-pull-11 {
    right: auto;
    left: 91.66666667%;
}

.appWrapper.rtl .col-xs-pull-10 {
    right: auto;
    left: 83.33333333%;
}

.appWrapper.rtl .col-xs-pull-9 {
    right: auto;
    left: 75%;
}

.appWrapper.rtl .col-xs-pull-8 {
    right: auto;
    left: 66.66666667%;
}

.appWrapper.rtl .col-xs-pull-7 {
    right: auto;
    left: 58.33333333%;
}

.appWrapper.rtl .col-xs-pull-6 {
    right: auto;
    left: 50%;
}

.appWrapper.rtl .col-xs-pull-5 {
    right: auto;
    left: 41.66666667%;
}

.appWrapper.rtl .col-xs-pull-4 {
    right: auto;
    left: 33.33333333%;
}

.appWrapper.rtl .col-xs-pull-3 {
    right: auto;
    left: 25%;
}

.appWrapper.rtl .col-xs-pull-2 {
    right: auto;
    left: 16.66666667%;
}

.appWrapper.rtl .col-xs-pull-1 {
    right: auto;
    left: 8.33333333%;
}

.appWrapper.rtl .col-xs-pull-0 {
    right: auto;
    left: auto;
}

.appWrapper.rtl .col-xs-push-12 {
    right: 100%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-11 {
    right: 91.66666667%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-10 {
    right: 83.33333333%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-9 {
    right: 75%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-8 {
    right: 66.66666667%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-7 {
    right: 58.33333333%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-6 {
    right: 50%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-5 {
    right: 41.66666667%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-4 {
    right: 33.33333333%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-3 {
    right: 25%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-2 {
    right: 16.66666667%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-1 {
    right: 8.33333333%;
    left: 0;
}

.appWrapper.rtl .col-xs-push-0 {
    right: auto;
    left: 0;
}

.appWrapper.rtl .col-xs-offset-12 {
    margin-right: 100%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-11 {
    margin-right: 91.66666667%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-10 {
    margin-right: 83.33333333%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-9 {
    margin-right: 75%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-8 {
    margin-right: 66.66666667%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-7 {
    margin-right: 58.33333333%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-6 {
    margin-right: 50%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-5 {
    margin-right: 41.66666667%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-4 {
    margin-right: 33.33333333%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-3 {
    margin-right: 25%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-2 {
    margin-right: 16.66666667%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-1 {
    margin-right: 8.33333333%;
    margin-left: 0;
}

.appWrapper.rtl .col-xs-offset-0 {
    margin-right: 0;
    margin-left: 0;
}

@media (min-width: 768px) {
    .appWrapper.rtl .col-sm-1,
    .appWrapper.rtl .col-sm-2,
    .appWrapper.rtl .col-sm-3,
    .appWrapper.rtl .col-sm-4,
    .appWrapper.rtl .col-sm-5,
    .appWrapper.rtl .col-sm-6,
    .appWrapper.rtl .col-sm-7,
    .appWrapper.rtl .col-sm-8,
    .appWrapper.rtl .col-sm-9,
    .appWrapper.rtl .col-sm-10,
    .appWrapper.rtl .col-sm-11,
    .appWrapper.rtl .col-sm-12 {
        float: right;
    }

    .appWrapper.rtl .col-sm-12 {
        width: 100%;
    }

    .appWrapper.rtl .col-sm-11 {
        width: 91.66666667%;
    }

    .appWrapper.rtl .col-sm-10 {
        width: 83.33333333%;
    }

    .appWrapper.rtl .col-sm-9 {
        width: 75%;
    }

    .appWrapper.rtl .col-sm-8 {
        width: 66.66666667%;
    }

    .appWrapper.rtl .col-sm-7 {
        width: 58.33333333%;
    }

    .appWrapper.rtl .col-sm-6 {
        width: 50%;
    }

    .appWrapper.rtl .col-sm-5 {
        width: 41.66666667%;
    }

    .appWrapper.rtl .col-sm-4 {
        width: 33.33333333%;
    }

    .appWrapper.rtl .col-sm-3 {
        width: 25%;
    }

    .appWrapper.rtl .col-sm-2 {
        width: 16.66666667%;
    }

    .appWrapper.rtl .col-sm-1 {
        width: 8.33333333%;
    }

    .appWrapper.rtl .col-sm-pull-12 {
        right: auto;
        left: 100%;
    }

    .appWrapper.rtl .col-sm-pull-11 {
        right: auto;
        left: 91.66666667%;
    }

    .appWrapper.rtl .col-sm-pull-10 {
        right: auto;
        left: 83.33333333%;
    }

    .appWrapper.rtl .col-sm-pull-9 {
        right: auto;
        left: 75%;
    }

    .appWrapper.rtl .col-sm-pull-8 {
        right: auto;
        left: 66.66666667%;
    }

    .appWrapper.rtl .col-sm-pull-7 {
        right: auto;
        left: 58.33333333%;
    }

    .appWrapper.rtl .col-sm-pull-6 {
        right: auto;
        left: 50%;
    }

    .appWrapper.rtl .col-sm-pull-5 {
        right: auto;
        left: 41.66666667%;
    }

    .appWrapper.rtl .col-sm-pull-4 {
        right: auto;
        left: 33.33333333%;
    }

    .appWrapper.rtl .col-sm-pull-3 {
        right: auto;
        left: 25%;
    }

    .appWrapper.rtl .col-sm-pull-2 {
        right: auto;
        left: 16.66666667%;
    }

    .appWrapper.rtl .col-sm-pull-1 {
        right: auto;
        left: 8.33333333%;
    }

    .appWrapper.rtl .col-sm-pull-0 {
        right: auto;
        left: auto;
    }

    .appWrapper.rtl .col-sm-push-12 {
        right: 100%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-11 {
        right: 91.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-10 {
        right: 83.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-9 {
        right: 75%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-8 {
        right: 66.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-7 {
        right: 58.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-6 {
        right: 50%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-5 {
        right: 41.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-4 {
        right: 33.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-3 {
        right: 25%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-2 {
        right: 16.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-1 {
        right: 8.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-sm-push-0 {
        right: auto;
        left: 0;
    }

    .appWrapper.rtl .col-sm-offset-12 {
        margin-right: 100%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-11 {
        margin-right: 91.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-10 {
        margin-right: 83.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-9 {
        margin-right: 75%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-8 {
        margin-right: 66.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-7 {
        margin-right: 58.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-6 {
        margin-right: 50%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-5 {
        margin-right: 41.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-4 {
        margin-right: 33.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-3 {
        margin-right: 25%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-2 {
        margin-right: 16.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-1 {
        margin-right: 8.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-sm-offset-0 {
        margin-right: 0;
        margin-left: 0;
    }
}

@media (min-width: 992px) {
    .appWrapper.rtl .col-md-1,
    .appWrapper.rtl .col-md-2,
    .appWrapper.rtl .col-md-3,
    .appWrapper.rtl .col-md-4,
    .appWrapper.rtl .col-md-5,
    .appWrapper.rtl .col-md-6,
    .appWrapper.rtl .col-md-7,
    .appWrapper.rtl .col-md-8,
    .appWrapper.rtl .col-md-9,
    .appWrapper.rtl .col-md-10,
    .appWrapper.rtl .col-md-11,
    .appWrapper.rtl .col-md-12 {
        float: right;
    }

    .appWrapper.rtl .col-md-12 {
        width: 100%;
    }

    .appWrapper.rtl .col-md-11 {
        width: 91.66666667%;
    }

    .appWrapper.rtl .col-md-10 {
        width: 83.33333333%;
    }

    .appWrapper.rtl .col-md-9 {
        width: 75%;
    }

    .appWrapper.rtl .col-md-8 {
        width: 66.66666667%;
    }

    .appWrapper.rtl .col-md-7 {
        width: 58.33333333%;
    }

    .appWrapper.rtl .col-md-6 {
        width: 50%;
    }

    .appWrapper.rtl .col-md-5 {
        width: 41.66666667%;
    }

    .appWrapper.rtl .col-md-4 {
        width: 33.33333333%;
    }

    .appWrapper.rtl .col-md-3 {
        width: 25%;
    }

    .appWrapper.rtl .col-md-2 {
        width: 16.66666667%;
    }

    .appWrapper.rtl .col-md-1 {
        width: 8.33333333%;
    }

    .appWrapper.rtl .col-md-pull-12 {
        right: auto;
        left: 100%;
    }

    .appWrapper.rtl .col-md-pull-11 {
        right: auto;
        left: 91.66666667%;
    }

    .appWrapper.rtl .col-md-pull-10 {
        right: auto;
        left: 83.33333333%;
    }

    .appWrapper.rtl .col-md-pull-9 {
        right: auto;
        left: 75%;
    }

    .appWrapper.rtl .col-md-pull-8 {
        right: auto;
        left: 66.66666667%;
    }

    .appWrapper.rtl .col-md-pull-7 {
        right: auto;
        left: 58.33333333%;
    }

    .appWrapper.rtl .col-md-pull-6 {
        right: auto;
        left: 50%;
    }

    .appWrapper.rtl .col-md-pull-5 {
        right: auto;
        left: 41.66666667%;
    }

    .appWrapper.rtl .col-md-pull-4 {
        right: auto;
        left: 33.33333333%;
    }

    .appWrapper.rtl .col-md-pull-3 {
        right: auto;
        left: 25%;
    }

    .appWrapper.rtl .col-md-pull-2 {
        right: auto;
        left: 16.66666667%;
    }

    .appWrapper.rtl .col-md-pull-1 {
        right: auto;
        left: 8.33333333%;
    }

    .appWrapper.rtl .col-md-pull-0 {
        right: auto;
        left: auto;
    }

    .appWrapper.rtl .col-md-push-12 {
        right: 100%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-11 {
        right: 91.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-10 {
        right: 83.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-9 {
        right: 75%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-8 {
        right: 66.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-7 {
        right: 58.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-6 {
        right: 50%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-5 {
        right: 41.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-4 {
        right: 33.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-3 {
        right: 25%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-2 {
        right: 16.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-1 {
        right: 8.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-md-push-0 {
        right: auto;
        left: 0;
    }

    .appWrapper.rtl .col-md-offset-12 {
        margin-right: 100%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-11 {
        margin-right: 91.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-10 {
        margin-right: 83.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-9 {
        margin-right: 75%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-8 {
        margin-right: 66.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-7 {
        margin-right: 58.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-6 {
        margin-right: 50%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-5 {
        margin-right: 41.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-4 {
        margin-right: 33.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-3 {
        margin-right: 25%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-2 {
        margin-right: 16.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-1 {
        margin-right: 8.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-md-offset-0 {
        margin-right: 0;
        margin-left: 0;
    }
}

@media (min-width: 1200px) {
    .appWrapper.rtl .col-lg-1,
    .appWrapper.rtl .col-lg-2,
    .appWrapper.rtl .col-lg-3,
    .appWrapper.rtl .col-lg-4,
    .appWrapper.rtl .col-lg-5,
    .appWrapper.rtl .col-lg-6,
    .appWrapper.rtl .col-lg-7,
    .appWrapper.rtl .col-lg-8,
    .appWrapper.rtl .col-lg-9,
    .appWrapper.rtl .col-lg-10,
    .appWrapper.rtl .col-lg-11,
    .appWrapper.rtl .col-lg-12 {
        float: right;
    }

    .appWrapper.rtl .col-lg-12 {
        width: 100%;
    }

    .appWrapper.rtl .col-lg-11 {
        width: 91.66666667%;
    }

    .appWrapper.rtl .col-lg-10 {
        width: 83.33333333%;
    }

    .appWrapper.rtl .col-lg-9 {
        width: 75%;
    }

    .appWrapper.rtl .col-lg-8 {
        width: 66.66666667%;
    }

    .appWrapper.rtl .col-lg-7 {
        width: 58.33333333%;
    }

    .appWrapper.rtl .col-lg-6 {
        width: 50%;
    }

    .appWrapper.rtl .col-lg-5 {
        width: 41.66666667%;
    }

    .appWrapper.rtl .col-lg-4 {
        width: 33.33333333%;
    }

    .appWrapper.rtl .col-lg-3 {
        width: 25%;
    }

    .appWrapper.rtl .col-lg-2 {
        width: 16.66666667%;
    }

    .appWrapper.rtl .col-lg-1 {
        width: 8.33333333%;
    }

    .appWrapper.rtl .col-lg-pull-12 {
        right: auto;
        left: 100%;
    }

    .appWrapper.rtl .col-lg-pull-11 {
        right: auto;
        left: 91.66666667%;
    }

    .appWrapper.rtl .col-lg-pull-10 {
        right: auto;
        left: 83.33333333%;
    }

    .appWrapper.rtl .col-lg-pull-9 {
        right: auto;
        left: 75%;
    }

    .appWrapper.rtl .col-lg-pull-8 {
        right: auto;
        left: 66.66666667%;
    }

    .appWrapper.rtl .col-lg-pull-7 {
        right: auto;
        left: 58.33333333%;
    }

    .appWrapper.rtl .col-lg-pull-6 {
        right: auto;
        left: 50%;
    }

    .appWrapper.rtl .col-lg-pull-5 {
        right: auto;
        left: 41.66666667%;
    }

    .appWrapper.rtl .col-lg-pull-4 {
        right: auto;
        left: 33.33333333%;
    }

    .appWrapper.rtl .col-lg-pull-3 {
        right: auto;
        left: 25%;
    }

    .appWrapper.rtl .col-lg-pull-2 {
        right: auto;
        left: 16.66666667%;
    }

    .appWrapper.rtl .col-lg-pull-1 {
        right: auto;
        left: 8.33333333%;
    }

    .appWrapper.rtl .col-lg-pull-0 {
        right: auto;
        left: auto;
    }

    .appWrapper.rtl .col-lg-push-12 {
        right: 100%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-11 {
        right: 91.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-10 {
        right: 83.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-9 {
        right: 75%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-8 {
        right: 66.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-7 {
        right: 58.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-6 {
        right: 50%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-5 {
        right: 41.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-4 {
        right: 33.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-3 {
        right: 25%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-2 {
        right: 16.66666667%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-1 {
        right: 8.33333333%;
        left: 0;
    }

    .appWrapper.rtl .col-lg-push-0 {
        right: auto;
        left: 0;
    }

    .appWrapper.rtl .col-lg-offset-12 {
        margin-right: 100%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-11 {
        margin-right: 91.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-10 {
        margin-right: 83.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-9 {
        margin-right: 75%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-8 {
        margin-right: 66.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-7 {
        margin-right: 58.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-6 {
        margin-right: 50%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-5 {
        margin-right: 41.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-4 {
        margin-right: 33.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-3 {
        margin-right: 25%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-2 {
        margin-right: 16.66666667%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-1 {
        margin-right: 8.33333333%;
        margin-left: 0;
    }

    .appWrapper.rtl .col-lg-offset-0 {
        margin-right: 0;
        margin-left: 0;
    }
}

.appWrapper.rtl th {
    text-align: right;
}

@media screen and (max-width: 767px) {
    .appWrapper.rtl .table-responsive > .table-bordered {
        border: 0;
    }

    .appWrapper.rtl .table-responsive > .table-bordered > thead > tr > th:first-child,
    .appWrapper.rtl .table-responsive > .table-bordered > tbody > tr > th:first-child,
    .appWrapper.rtl .table-responsive > .table-bordered > tfoot > tr > th:first-child,
    .appWrapper.rtl .table-responsive > .table-bordered > thead > tr > td:first-child,
    .appWrapper.rtl .table-responsive > .table-bordered > tbody > tr > td:first-child,
    .appWrapper.rtl .table-responsive > .table-bordered > tfoot > tr > td:first-child {
        border-right: 0;
        border-left: initial;
    }

    .appWrapper.rtl .table-responsive > .table-bordered > thead > tr > th:last-child,
    .appWrapper.rtl .table-responsive > .table-bordered > tbody > tr > th:last-child,
    .appWrapper.rtl .table-responsive > .table-bordered > tfoot > tr > th:last-child,
    .appWrapper.rtl .table-responsive > .table-bordered > thead > tr > td:last-child,
    .appWrapper.rtl .table-responsive > .table-bordered > tbody > tr > td:last-child,
    .appWrapper.rtl .table-responsive > .table-bordered > tfoot > tr > td:last-child {
        border-right: initial;
        border-left: 0;
    }
}

.appWrapper.rtl .radio label,
.appWrapper.rtl .checkbox label {
    padding-right: 20px;
    padding-left: initial;
}

.appWrapper.rtl .radio input[type=radio],
.appWrapper.rtl .radio-inline input[type=radio],
.appWrapper.rtl .checkbox input[type=checkbox],
.appWrapper.rtl .checkbox-inline input[type=checkbox] {
    margin-right: -20px;
    margin-left: auto;
}

.appWrapper.rtl .radio-inline,
.appWrapper.rtl .checkbox-inline {
    padding-right: 20px;
    padding-left: 0;
}

.appWrapper.rtl .radio-inline + .radio-inline,
.appWrapper.rtl .checkbox-inline + .checkbox-inline {
    margin-right: 10px;
    margin-left: 0;
}

.appWrapper.rtl .has-feedback .form-control {
    padding-right: 12px;
    padding-left: 42.5px;
}

.appWrapper.rtl .form-control-feedback {
    right: auto;
    left: 0;
}

@media (min-width: 768px) {
    .appWrapper.rtl .form-inline label {
        padding-right: 0;
        padding-left: initial;
    }

    .appWrapper.rtl .form-inline .radio input[type=radio],
    .appWrapper.rtl .form-inline .checkbox input[type=checkbox] {
        margin-right: 0;
        margin-left: auto;
    }
}

@media (min-width: 768px) {
    .appWrapper.rtl .form-horizontal .control-label {
        text-align: left;
    }
}

.appWrapper.rtl .form-horizontal .has-feedback .form-control-feedback {
    right: auto;
    left: 15px;
}

.appWrapper.rtl .caret {
    margin-right: 2px;
    margin-left: 0;
}

.appWrapper.rtl .dropdown-menu {
    right: 0;
    left: auto;

    float: left;

    text-align: right;
}

.appWrapper.rtl .dropdown-menu.pull-right {
    right: auto;
    left: 0;

    float: right;
}

.appWrapper.rtl .dropdown-menu-right {
    right: 0;
    left: auto;
}

.appWrapper.rtl .dropdown-menu-left {
    right: auto;
    left: 0;
}

@media (min-width: 768px) {
    .appWrapper.rtl .navbar-right .dropdown-menu {
        right: 0;
        left: auto;
    }

    .appWrapper.rtl .navbar-right .dropdown-menu-left {
        right: auto;
        left: 0;
    }
}

.appWrapper.rtl .btn-group > .btn,
.appWrapper.rtl .btn-group-vertical > .btn {
    float: right;
}

.appWrapper.rtl .btn-group .btn + .btn,
.appWrapper.rtl .btn-group .btn + .btn-group,
.appWrapper.rtl .btn-group .btn-group + .btn,
.appWrapper.rtl .btn-group .btn-group + .btn-group {
    margin-right: -1px;
    margin-left: 0;
}

.appWrapper.rtl .btn-toolbar {
    margin-right: -5px;
    margin-left: 0;
}

.appWrapper.rtl .btn-toolbar .btn-group,
.appWrapper.rtl .btn-toolbar .input-group {
    float: right;
}

.appWrapper.rtl .btn-toolbar > .btn,
.appWrapper.rtl .btn-toolbar > .btn-group,
.appWrapper.rtl .btn-toolbar > .input-group {
    margin-right: 5px;
    margin-left: 0;
}

.appWrapper.rtl .btn-group > .btn:first-child {
    margin-right: 0;
}

.appWrapper.rtl .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-top-left-radius: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 0;
}

.appWrapper.rtl .btn-group > .btn:last-child:not(:first-child),
.appWrapper.rtl .btn-group > .dropdown-toggle:not(:first-child) {
    border-top-left-radius: 4px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 4px;
}

.appWrapper.rtl .btn-group > .btn-group {
    float: right;
}

.appWrapper.rtl .btn-group.btn-group-justified > .btn,
.appWrapper.rtl .btn-group.btn-group-justified > .btn-group {
    float: none;
}

.appWrapper.rtl .btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
    border-radius: 0;
}

.appWrapper.rtl .btn-group > .btn-group:first-child > .btn:last-child,
.appWrapper.rtl .btn-group > .btn-group:first-child > .dropdown-toggle {
    border-top-left-radius: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 0;
}

.appWrapper.rtl .btn-group > .btn-group:last-child > .btn:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 4px;
}

.appWrapper.rtl .btn .caret {
    margin-right: 0;
}

.appWrapper.rtl .btn-group-vertical > .btn + .btn,
.appWrapper.rtl .btn-group-vertical > .btn + .btn-group,
.appWrapper.rtl .btn-group-vertical > .btn-group + .btn,
.appWrapper.rtl .btn-group-vertical > .btn-group + .btn-group {
    margin-top: -1px;
    margin-right: 0;
}

.appWrapper.rtl .input-group .form-control {
    float: right;
}

.appWrapper.rtl .input-group .form-control:first-child,
.appWrapper.rtl .input-group-addon:first-child,
.appWrapper.rtl .input-group-btn:first-child > .btn,
.appWrapper.rtl .input-group-btn:first-child > .btn-group > .btn,
.appWrapper.rtl .input-group-btn:first-child > .dropdown-toggle,
.appWrapper.rtl .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.appWrapper.rtl .input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
    border-top-left-radius: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 0;
}

.appWrapper.rtl .input-group-addon:first-child {
    border-right: 1px solid #CCCCCC;
    border-left: 0;
}

.appWrapper.rtl .input-group .form-control:last-child,
.appWrapper.rtl .input-group-addon:last-child,
.appWrapper.rtl .input-group-btn:last-child > .btn,
.appWrapper.rtl .input-group-btn:last-child > .btn-group > .btn,
.appWrapper.rtl .input-group-btn:last-child > .dropdown-toggle,
.appWrapper.rtl .input-group-btn:first-child > .btn:not(:first-child),
.appWrapper.rtl .input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
    border-top-left-radius: 4px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 4px;
}

.appWrapper.rtl .input-group-addon:last-child {
    border-right: 0;
    border-left: 1px solid #CCCCCC;
}

.appWrapper.rtl .input-group-btn > .btn + .btn {
    margin-right: -1px;
    margin-left: auto;
}

.appWrapper.rtl .input-group-btn:first-child > .btn,
.appWrapper.rtl .input-group-btn:first-child > .btn-group {
    margin-right: auto;
    margin-left: -1px;
}

.appWrapper.rtl .input-group-btn:last-child > .btn,
.appWrapper.rtl .input-group-btn:last-child > .btn-group {
    margin-right: -1px;
    margin-left: auto;
}

.appWrapper.rtl .nav {
    padding-right: 0;
    padding-left: initial;
}

.appWrapper.rtl .nav-tabs > li {
    float: right;
}

.appWrapper.rtl .nav-tabs > li > a {
    margin-right: -2px;
    margin-left: auto;

    border-radius: 4px 4px 0 0;
}

.appWrapper.rtl .nav-pills > li {
    float: none;
}

.appWrapper.rtl .nav-pills > li > a {
    border-radius: 4px;
}

.appWrapper.rtl .nav-pills > li + li {
    margin-right: 2px;
    margin-left: auto;
}

.appWrapper.rtl .nav-stacked > li {
    float: none;
}

.appWrapper.rtl .nav-stacked > li + li {
    margin-right: 0;
    margin-left: auto;
}

.appWrapper.rtl .nav-justified > .dropdown .dropdown-menu {
    right: auto;
}

.appWrapper.rtl .nav-tabs-justified > li > a {
    margin-right: auto;
    margin-left: 0;
}

@media (min-width: 768px) {
    .appWrapper.rtl .nav-tabs-justified > li > a {
        border-radius: 4px 4px 0 0;
    }
}

@media (min-width: 768px) {
    .appWrapper.rtl .navbar-header {
        float: right;
    }
}

.appWrapper.rtl .navbar-collapse {
    padding-right: 15px;
    padding-left: 15px;
}

.appWrapper.rtl .navbar-brand {
    float: right;
}

@media (min-width: 768px) {
    .appWrapper.rtl .navbar > .container .navbar-brand,
    .appWrapper.rtl .navbar > .container-fluid .navbar-brand {
        margin-right: -15px;
        margin-left: auto;
    }
}

.appWrapper.rtl .navbar-toggle {
    float: left;

    margin-right: auto;
    margin-left: 15px;
}

@media (max-width: 767px) {
    .appWrapper.rtl .navbar-nav .open .dropdown-menu > li > a,
    .appWrapper.rtl .navbar-nav .open .dropdown-menu .dropdown-header {
        padding: 5px 25px 5px 15px;
    }
}

@media (min-width: 768px) {
    .appWrapper.rtl .navbar-nav {
        float: right;
    }

    .appWrapper.rtl .navbar-nav > li {
        float: right;
    }

    .appWrapper.rtl .navbar-nav.navbar-right:last-child {
        margin-right: auto;
        margin-left: -15px;
    }

    .appWrapper.rtl .navbar-nav.navbar-right.flip {
        float: left !important;
    }

    .appWrapper.rtl .navbar-nav.navbar-right .dropdown-menu {
        right: auto;
        left: 0;
    }
}

@media (min-width: 768px) {
    .appWrapper.rtl .navbar-text {
        float: right;
    }

    .appWrapper.rtl .navbar-text.navbar-right:last-child {
        margin-right: auto;
        margin-left: 0;
    }
}

.appWrapper.rtl .pagination {
    padding-right: 0;
}

.appWrapper.rtl .pagination > li > a,
.appWrapper.rtl .pagination > li > span {
    float: right;

    margin-right: -1px;
    margin-left: 0;
}

.appWrapper.rtl .pagination > li:first-child > a,
.appWrapper.rtl .pagination > li:first-child > span {
    margin-left: 0;

    border-top-left-radius: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 0;
}

.appWrapper.rtl .pagination > li:last-child > a,
.appWrapper.rtl .pagination > li:last-child > span {
    margin-right: -1px;

    border-top-left-radius: 4px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 4px;
}

.appWrapper.rtl .pager {
    padding-right: 0;
    padding-left: initial;
}

.appWrapper.rtl .pager .next > a,
.appWrapper.rtl .pager .next > span {
    float: left;
}

.appWrapper.rtl .pager .previous > a,
.appWrapper.rtl .pager .previous > span {
    float: right;
}

.appWrapper.rtl .nav-pills > li > a > .badge {
    margin-right: 3px;
    margin-left: 0;
}

.appWrapper.rtl .alert-dismissable,
.appWrapper.rtl .alert-dismissible {
    padding-right: 15px;
    padding-left: 35px;
}

.appWrapper.rtl .alert-dismissable .close,
.appWrapper.rtl .alert-dismissible .close {
    right: 0;
    left: 21px;
}

.appWrapper.rtl .progress-bar {
    float: right;
}

.appWrapper.rtl .media > .pull-left {
    margin-right: 10px;
}

.appWrapper.rtl .media > .pull-left.flip {
    margin-right: 0;
    margin-left: 10px;
}

.appWrapper.rtl .media > .pull-right {
    margin-left: 10px;
}

.appWrapper.rtl .media > .pull-right.flip {
    margin-right: 10px;
    margin-left: 0;
}

.appWrapper.rtl .media-list {
    padding-right: 0;
    padding-left: initial;

    list-style: none;
}

.appWrapper.rtl .list-group {
    padding-right: 0;
    padding-left: initial;
}

.appWrapper.rtl .list-group-item > .badge {
    float: left;
}

.appWrapper.rtl .list-group-item > .badge + .badge {
    margin-right: 5px;
    margin-left: auto;
}

.appWrapper.rtl .panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.appWrapper.rtl .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
.appWrapper.rtl .panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.appWrapper.rtl .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.appWrapper.rtl .panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
.appWrapper.rtl .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
.appWrapper.rtl .panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.appWrapper.rtl .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 3px;
}

.appWrapper.rtl .panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.appWrapper.rtl .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
.appWrapper.rtl .panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.appWrapper.rtl .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.appWrapper.rtl .panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
.appWrapper.rtl .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
.appWrapper.rtl .panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.appWrapper.rtl .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
    border-top-left-radius: 3px;
    border-top-right-radius: 0;
}

.appWrapper.rtl .panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.appWrapper.rtl .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.appWrapper.rtl .panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.appWrapper.rtl .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.appWrapper.rtl .panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.appWrapper.rtl .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.appWrapper.rtl .panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
.appWrapper.rtl .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
    border-top-right-radius: 0;
    border-bottom-left-radius: 3px;
}

.appWrapper.rtl .panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.appWrapper.rtl .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.appWrapper.rtl .panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.appWrapper.rtl .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.appWrapper.rtl .panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.appWrapper.rtl .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.appWrapper.rtl .panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
.appWrapper.rtl .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
    border-top-left-radius: 0;
    border-bottom-right-radius: 3px;
}

.appWrapper.rtl .panel > .table-bordered > thead > tr > th:first-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.appWrapper.rtl .panel > .table-bordered > tbody > tr > th:first-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.appWrapper.rtl .panel > .table-bordered > tfoot > tr > th:first-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.appWrapper.rtl .panel > .table-bordered > thead > tr > td:first-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.appWrapper.rtl .panel > .table-bordered > tbody > tr > td:first-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.appWrapper.rtl .panel > .table-bordered > tfoot > tr > td:first-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-right: 0;
    border-left: none;
}

.appWrapper.rtl .panel > .table-bordered > thead > tr > th:last-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.appWrapper.rtl .panel > .table-bordered > tbody > tr > th:last-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.appWrapper.rtl .panel > .table-bordered > tfoot > tr > th:last-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.appWrapper.rtl .panel > .table-bordered > thead > tr > td:last-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.appWrapper.rtl .panel > .table-bordered > tbody > tr > td:last-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.appWrapper.rtl .panel > .table-bordered > tfoot > tr > td:last-child,
.appWrapper.rtl .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: none;
    border-left: 0;
}

.appWrapper.rtl .embed-responsive .embed-responsive-item,
.appWrapper.rtl .embed-responsive iframe,
.appWrapper.rtl .embed-responsive embed,
.appWrapper.rtl .embed-responsive object {
    right: 0;
    left: auto;
}

.appWrapper.rtl .close {
    float: left;
}

.appWrapper.rtl .modal-footer {
    text-align: left;
}

.appWrapper.rtl .modal-footer .btn + .btn {
    margin-right: 5px;
    margin-left: auto;
}

.appWrapper.rtl .modal-footer .btn-group .btn + .btn {
    margin-right: -1px;
    margin-left: auto;
}

.appWrapper.rtl .modal-footer .btn-block + .btn-block {
    margin-right: 0;
    margin-left: auto;
}

.appWrapper.rtl .popover {
    left: auto;

    text-align: right;
}

.appWrapper.rtl .popover.top > .arrow {
    right: 50%;
    left: auto;

    margin-right: -11px;
    margin-left: auto;
}

.appWrapper.rtl .popover.top > .arrow:after {
    margin-right: -10px;
    margin-left: auto;
}

.appWrapper.rtl .popover.bottom > .arrow {
    right: 50%;
    left: auto;

    margin-right: -11px;
    margin-left: auto;
}

.appWrapper.rtl .popover.bottom > .arrow:after {
    margin-right: -10px;
    margin-left: auto;
}

.appWrapper.rtl .carousel-control {
    right: 0;
    bottom: 0;
}

.appWrapper.rtl .carousel-control.left {
    right: auto;
    left: 0;

    background-image: -webkit-linear-gradient(left, color-stop(rgba(0, 0, 0, 0.5) 0), color-stop(rgba(0, 0, 0, 0.0001) 100%));
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5) 0, rgba(0, 0, 0, 0.0001) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0, rgba(0, 0, 0, 0.0001) 100%);
    background-repeat: repeat-x;

    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
}

.appWrapper.rtl .carousel-control.right {
    right: 0;
    left: auto;

    background-image: -webkit-linear-gradient(left, color-stop(rgba(0, 0, 0, 0.0001) 0), color-stop(rgba(0, 0, 0, 0.5) 100%));
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0, rgba(0, 0, 0, 0.5) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0, rgba(0, 0, 0, 0.5) 100%);
    background-repeat: repeat-x;

    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
}

.appWrapper.rtl .carousel-control .icon-prev,
.appWrapper.rtl .carousel-control .glyphicon-chevron-left {
    right: auto;
    left: 50%;

    margin-right: -10px;
}

.appWrapper.rtl .carousel-control .icon-next,
.appWrapper.rtl .carousel-control .glyphicon-chevron-right {
    right: 50%;
    left: auto;

    margin-left: -10px;
}

.appWrapper.rtl .carousel-indicators {
    right: 50%;
    left: 0;

    margin-right: -30%;
    margin-left: 0;
    padding-left: 0;
}

@media screen and (min-width: 768px) {
    .appWrapper.rtl .carousel-control .glyphicon-chevron-left,
    .appWrapper.rtl .carousel-control .icon-prev {
        margin-right: -15px;
        margin-left: 0;
    }

    .appWrapper.rtl .carousel-control .glyphicon-chevron-right,
    .appWrapper.rtl .carousel-control .icon-next {
        margin-right: -15px;
        margin-left: 0;
    }

    .appWrapper.rtl .carousel-caption {
        right: 20%;
        left: 20%;

        padding-bottom: 30px;
    }
}

.appWrapper.rtl .pull-right.flip {
    float: left !important;
}

.appWrapper.rtl .pull-left.flip {
    float: right !important;
}

.appWrapper.rtl #header .branding {
    float: right;
}

.appWrapper.rtl #header .branding a.brand {
    float: right;
}

.appWrapper.rtl #header .nav-left {
    float: right !important;

    padding: 0;
    padding-left: 20px;
}

.appWrapper.rtl #header .nav-left .divided-right {
    border-right: 0;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.appWrapper.rtl #header .search:after {
    right: auto;
    left: 0;
}

.appWrapper.rtl #header .nav-right {
    float: left !important;
}

.appWrapper.rtl #header .nav-left > li.nav-profile > a span > i,
.appWrapper.rtl #header .nav-right > li.nav-profile > a span > i {
    margin-right: 5px;
    margin-left: 0;
}

.appWrapper.rtl .pageheader .page-bar .page-breadcrumb > li:first-child {
    margin-right: 0;
    margin-left: 8px;
}

.appWrapper.rtl .pageheader .page-bar .page-toolbar {
    float: left;
}

.appWrapper.rtl #sidebar accordion .panel-group .panel > .panel-heading .panel-title > a > i {
    right: auto;
    left: 0;
}

.appWrapper.rtl #sidebar .summary .media .pull-right {
    float: left !important;
}

.appWrapper.rtl #sidebar .settings .onoffswitch {
    right: auto;
    left: -10px;
}

.appWrapper.rtl .onoffswitch.labeled .onoffswitch-inner:after {
    padding-right: 0;
    padding-left: 7px;
}

.appWrapper.rtl .onoffswitch.labeled .onoffswitch-switch {
    right: auto;
    left: 21px;
}

.appWrapper.rtl .onoffswitch.small {
    right: auto;
    left: -5px;
}

.appWrapper.rtl .onoffswitch.small .onoffswitch-switch {
    right: auto;
    left: 6px;
}

.appWrapper.rtl .onoffswitch-inner {
    display: block;

    width: 200%;
    margin-right: -100%;
    margin-left: 0;

    -webkit-transition: margin 0.2s ease-in 0s;
    -moz-transition: margin 0.2s ease-in 0s;
    transition: margin 0.2s ease-in 0s;
}

.appWrapper.rtl .onoffswitch-switch {
    right: auto;
    left: 15px;
}

.appWrapper.rtl .onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-inner {
    margin-right: 0;
}

.appWrapper.rtl .onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-switch {
    right: auto;
    left: 0;
}

.appWrapper.rtl .onoffswitch-inner:before,
.appWrapper.rtl .onoffswitch-inner:after {
    float: right;
}

.appWrapper.rtl #navigation > li > a > i {
    margin-right: 0;
    margin-left: 10px;
}

.appWrapper.rtl #navigation > li > a > i:last-of-type {
    right: auto;
    left: 0;
}

.appWrapper.rtl #navigation > li > a .badge {
    right: 25px;
    left: auto;
}

.appWrapper.rtl #navigation > li > a .label {
    right: auto;
    left: 10px;
}

.appWrapper.rtl #navigation .dropdown > ul li > a {
    padding: 8px 30px 8px 0;
}

.appWrapper.rtl #navigation .dropdown > ul li > a > i {
    margin-right: 0;
    margin-left: 15px;
}

.appWrapper.rtl #navigation .dropdown > ul li > a > i:first-of-type:before {
    content: "\f0d9";
}

.appWrapper.rtl #navigation .dropdown > ul li > a > i:last-of-type {
    right: auto;
    left: 0;
}

.appWrapper.rtl #navigation .dropdown.submenu > ul {
    padding-right: 15px;
    padding-left: 0;
}

.appWrapper.rtl .form-control.underline-input {
    padding-right: 0;
    padding-left: 12px;
}

.appWrapper.rtl .dropdown-menu {
    right: 0;
    left: auto;

    float: right;

    text-align: right;
}

.appWrapper.rtl .dropdown-menu.with-arrow:before {
    right: 12px;
    left: auto;
}

.appWrapper.rtl .dropdown-menu.with-arrow:after {
    right: 13px;
    left: auto;
}

.appWrapper.rtl .dropdown-menu.pull-right.with-arrow:before {
    right: auto;
    left: 11px;
}

.appWrapper.rtl .dropdown-menu.pull-right.with-arrow:after {
    right: auto;
    left: 12px;
}

.appWrapper.rtl .dropdown-menu.panel .panel-footer > a > i {
    float: left !important;
}

.appWrapper.rtl .dropdown-menu.panel .panel-footer > a > i:before {
    content: "\f104";
}

.appWrapper.rtl .dropdown-menu > li > a > i {
    margin-right: 0;
    margin-left: 5px;
}

.appWrapper.rtl .dropdown-menu > li > a > .label,
.appWrapper.rtl .dropdown-menu > li > a > .badge {
    float: left !important;
}

.appWrapper.rtl .dropdown.settings .color-schemes {
    margin-right: 0;
    padding: 5px 10px;
}

.appWrapper.rtl.header-fixed.aside-fixed #content {
    right: 250px;
    left: 0;
}

.appWrapper.rtl #rightbar .tab-content .tab-pane ul > li .media .media-body {
    text-align: left;
}

.appWrapper.rtl #rightbar .tab-content .tab-pane ul.settings > li .form-group .control-label .onoffswitch {
    right: auto;
    left: -35px;
}

.appWrapper.rtl.rightbar-hidden #rightbar {
    right: auto;
    left: -250px;
}

.appWrapper.rtl.header-fixed.rightbar-show #rightbar,
.appWrapper.rtl.aside-fixed.rightbar-show #rightbar {
    right: auto;
    left: 0;
}

.appWrapper.rtl.header-fixed.rightbar-show #header,
.appWrapper.rtl.aside-fixed.rightbar-show #header {
    right: 0;
    left: 250px;
}

.appWrapper.rtl.header-fixed.rightbar-show #content,
.appWrapper.rtl.aside-fixed.rightbar-show #content {
    left: 250px;
}

.appWrapper.rtl .nav-tabs.nav-justified > li {
    float: none;
}

.appWrapper.rtl.sidebar-sm #sidebar #navigation > li.dropdown > ul,
.appWrapper.rtl.sidebar-xs #sidebar #navigation > li.dropdown > ul {
    right: 100%;
    left: auto;
}

.appWrapper.rtl.sidebar-sm #sidebar #navigation > li li.submenu > ul,
.appWrapper.rtl.sidebar-xs #sidebar #navigation > li li.submenu > ul {
    right: 100%;
    left: auto;
}

.appWrapper.rtl.sidebar-xs #sidebar #navigation > li > a .badge {
    right: 5px;
    left: auto;
}

.appWrapper.rtl.header-fixed.sidebar-sm #content {
    right: 80px;
    left: 0;

    padding-right: 0;
    padding-left: 0;
}

.appWrapper.rtl.header-fixed.sidebar-xs #content {
    right: 40px;
    left: 0;

    padding-right: 0;
    padding-left: 0;
}

.appWrapper.rtl.header-static #header {
    padding-right: 250px;
    padding-left: 0;
}

.appWrapper.rtl.header-static #header .branding {
    position: fixed;
    right: 0;
    left: auto;
}

.appWrapper.rtl.header-static #content {
    padding-top: 0;
    padding-right: 250px;
    padding-left: 0;
}

.appWrapper.rtl.header-static.sidebar-sm #header {
    padding-right: 80px;
    padding-left: 0;
}

.appWrapper.rtl.header-static.sidebar-sm #header .branding .brand {
    margin-right: 8px;
    margin-left: 0;
}

.appWrapper.rtl.header-static.sidebar-sm #content {
    padding-right: 80px;
    padding-left: 0;
}

.appWrapper.rtl.header-static.sidebar-xs #header {
    padding-right: 40px;
    padding-left: 0;
}

.appWrapper.rtl.header-static.sidebar-xs #content {
    padding-right: 40px;
    padding-left: 0;
}

.appWrapper.rtl.header-static.rightbar-show #rightbar {
    right: auto;
    left: 0;
}

.appWrapper.rtl.header-static.rightbar-show #header {
    padding-left: 250px;
}

.appWrapper.rtl.header-static.rightbar-show #content {
    padding-left: 250px;
}

.appWrapper.rtl.aside-static #wrap #controls {
    right: 0;
    left: auto;
}

.appWrapper.rtl.aside-static #sidebar {
    float: right;
}

.appWrapper.rtl.aside-static #content {
    padding-right: 250px;
    padding-left: 0;
}

.appWrapper.rtl.aside-static.sidebar-sm #content {
    padding-right: 80px;
    padding-left: 0;
}

.appWrapper.rtl.aside-static.sidebar-xs #content {
    padding-right: 40px;
    padding-left: 0;
}

.appWrapper.rtl.aside-static.header-static #header {
    padding-right: 0;
}

@media only screen and (max-width: 1200px) {
    .appWrapper.rtl.rightbar-show #rightbar {
        left: 0;
    }

    .appWrapper.rtl.rightbar-show #header {
        left: 0 !important;

        padding-left: 0 !important;
    }

    .appWrapper.rtl.rightbar-show #content {
        left: 0 !important;

        padding-left: 0 !important;
    }
}

@media only screen and (max-width: 992px) {
    .appWrapper.rtl.sidebar-sm #header .branding .brand,
    .appWrapper.rtl:not(.sidebar-sm):not(.sidebar-xs) #header .branding .brand {
        margin-right: 8px;
        margin-left: 0;
    }

    .appWrapper.rtl:not(.sidebar-sm):not(.sidebar-xs) #header .branding .brand {
        margin-right: -12px;
        margin-left: 0;
    }

    .appWrapper.rtl.header-fixed.aside-fixed:not(.sidebar-sm):not(.sidebar-xs) #content {
        right: 80px;
        left: 0;
    }
}

@media only screen and (max-width: 767px) {
    .appWrapper.rtl.sidebar-xs #header .branding {
        position: relative;

        float: none;
    }

    .appWrapper.rtl.sidebar-xs #header .branding > a {
        float: none;

        background-position: 8px -1px;
    }

    .appWrapper.rtl.sidebar-xs #header .branding .brand > span {
        display: inline-block;
    }

    .appWrapper.rtl.sidebar-xs #header .search {
        right: 15px;
        left: auto;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-left {
        right: auto;
        left: 0;

        padding-left: 0;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-left .settings .dropdown-menu {
        right: auto;
        left: 0;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-left .settings .dropdown-menu.with-arrow:before {
        right: auto;
        left: 11px;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-left .settings .dropdown-menu.with-arrow:after {
        right: auto;
        left: 12px;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-right {
        float: right !important;

        padding-right: 3px;
        padding-left: 0;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-right .nav-profile {
        right: auto;
        left: 40px;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-right .nav-profile .dropdown-menu {
        right: auto;
        left: 0;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-right .nav-profile .dropdown-menu.with-arrow:before {
        right: auto;
        left: 11px;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-right .nav-profile .dropdown-menu.with-arrow:after {
        right: auto;
        left: 12px;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-right .toggle-right-sidebar {
        right: auto;
        left: 0;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-right .users .dropdown-menu,
    .appWrapper.rtl.sidebar-xs #header .nav-right .messages .dropdown-menu,
    .appWrapper.rtl.sidebar-xs #header .nav-right .notifications .dropdown-menu {
        right: 0;
        left: auto;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-right .users .dropdown-menu.with-arrow:before,
    .appWrapper.rtl.sidebar-xs #header .nav-right .messages .dropdown-menu.with-arrow:before,
    .appWrapper.rtl.sidebar-xs #header .nav-right .notifications .dropdown-menu.with-arrow:before {
        right: 11px;
        left: auto;
    }

    .appWrapper.rtl.sidebar-xs #header .nav-right .users .dropdown-menu.with-arrow:after,
    .appWrapper.rtl.sidebar-xs #header .nav-right .messages .dropdown-menu.with-arrow:after,
    .appWrapper.rtl.sidebar-xs #header .nav-right .notifications .dropdown-menu.with-arrow:after {
        right: 12px;
        left: auto;
    }

    .appWrapper.rtl.sidebar-xs.header-static.aside-fixed #header .search {
        right: 55px;
        left: auto;
    }
}

@media only screen and (max-width: 480px) {
    .appWrapper.rtl.sidebar-xs #header .branding {
        padding-right: 10px;
        padding-left: 0;

        text-align: right;
    }

    .appWrapper.rtl.sidebar-xs #header .branding > a {
        background-position: 5px -1px;
    }

    .appWrapper.rtl.sidebar-xs #header .branding .brand > span {
        display: none;
    }

    .appWrapper.rtl.sidebar-xs #header .search {
        right: 55px;
        left: auto;
    }

    .appWrapper.rtl.sidebar-xs.header-static.aside-fixed #header .search {
        right: 95px;
        left: auto;
    }
}

@media only screen and (max-width: 360px) {
    .appWrapper.rtl.rightbar-hidden #rightbar {
        right: auto;
        left: -100%;
    }
}

.no-rtl {
    direction: ltr;
    unicode-bidi: embed;
}

#content {
    width: auto;
    padding-top: 45px;

    -webkit-transition: right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335), padding-right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335);
    -moz-transition: right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335), padding-right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335);
    transition: right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335), padding-right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335);

    -webkit-overflow-scrolling: touch;
    -webkit-overflow-scrolling: -blackberry-touch;
}

#content > div[ui-view] {
    height: 100%;
}

#content.ng-enter .page {
    -webkit-animation: littleFadeInUp 0.5s ease;
    -moz-animation: littleFadeInUp 0.5s ease;
    animation: littleFadeInUp 0.5s ease;

    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

#content.ng-leave .page {
    opacity: 0;

    filter: alpha(opacity=0);
}

#content > [ui-view].ng-enter,
#content > [ui-view].ng-leave {
    position: absolute;
    right: 0;
    left: 0;

    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
}

#content > [ui-view].ng-enter {
    -webkit-transform: scale3d(0.5, 0.5, 0.5);
    -moz-transform: scale3d(0.5, 0.5, 0.5);
    -ms-transform: scale3d(0.5, 0.5, 0.5);
    -o-transform: scale3d(0.5, 0.5, 0.5);
    transform: scale3d(0.5, 0.5, 0.5);

    opacity: 0;

    filter: alpha(opacity=0);
}

#content > [ui-view].ng-enter-active {
    -webkit-transform: scale3d(1, 1, 1);
    -moz-transform: scale3d(1, 1, 1);
    -ms-transform: scale3d(1, 1, 1);
    -o-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);

    opacity: 1;

    filter: alpha(opacity=100);
}

#content > [ui-view].ng-leave {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);

    opacity: 1;

    filter: alpha(opacity=100);
}

#content > [ui-view].ng-leave-active {
    -webkit-transform: translate3d(100px, 100px, 0);
    -moz-transform: translate3d(100px, 100px, 0);
    -ms-transform: translate3d(100px, 100px, 0);
    -o-transform: translate3d(100px, 100px, 0);
    transform: translate3d(100px, 100px, 0);

    opacity: 0;

    filter: alpha(opacity=0);
}

.appWrapper.header-fixed.aside-fixed #content {
    position: absolute;
    z-index: 1;
    top: 45px;
    right: 0;
    bottom: 0;
    left: 250px;

    width: auto;
    padding-top: 0;
    padding-left: 0;
}

.appWrapper.header-static #content {
    padding-top: 0;
    padding-left: 250px;
}

.appWrapper.aside-static #content {
    padding-left: 250px;
}

@media only screen and (max-width: 992px) {
    .appWrapper.header-fixed.aside-fixed:not(.sidebar-sm):not(.sidebar-xs) #content {
        left: 80px;
    }
}

@media only screen and (max-width: 767px) {
    .appWrapper.header-fixed.aside-fixed:not(.sidebar-sm):not(.sidebar-xs) #content {
        left: 40px;
    }

    .appWrapper.aside-static #content {
        padding-top: 90px;
    }

    .appWrapper.header-static.aside-static #content {
        padding-top: 0;
    }
}

.appWrapper.header-fixed #header {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
}

.appWrapper:not(.header-fixed) #header {
    padding-left: 250px;
}

.appWrapper:not(.header-fixed) #header .branding {
    position: fixed;
    left: 0;
}

.appWrapper:not(.header-fixed):not(.aside-fixed) #header {
    position: relative;

    padding-left: 0;
}

.appWrapper:not(.header-fixed):not(.aside-fixed) #header .branding {
    position: static;
}

.appWrapper:not(.sidebar-offcanvas) #header .branding .offcanvas-toggle {
    display: none !important;
}

#header {
    z-index: 1001;

    -webkit-transition: right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335), padding-right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335);
    -moz-transition: right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335), padding-right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335);
    transition: right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335), padding-right 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335);

    background-color: #51445F;
}

#header .branding {
    float: left;

    width: 250px;
    height: 45px;
    padding: 0 15px;

    background-color: #493D55;
}

#header .branding a.brand {
    font-family: "Dosis", "Arial", sans-serif;
    font-size: 18px;
    font-weight: 300;
    line-height: 45px;

    float: left;

    height: 45px;
    padding-left: 45px;

    -webkit-transition: none;
    -moz-transition: none;
    transition: none;

    color: white;
    background: url(../images/nymbl_logo.png) no-repeat 0;
    background-size: contain;
}

#header .branding a.brand:hover {
    text-decoration: none;
}

#header .branding .offcanvas-toggle {
    font-size: 18px;

    margin-left: 5px;
    padding: 1px 4px;

    opacity: 0.5;
    color: white;
}

#header .branding .offcanvas-toggle:hover {
    opacity: 1;
}

#header .search {
    position: relative;

    display: inline-block;

    width: 400px;
    margin-left: 15px;
}

#header .search .form-control {
    margin-top: 3px;
}

#header .search:after {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    position: absolute;
    top: 0;
    right: 0;
    content: "\f002";
    color: rgba(255, 255, 255, 0.25);
    -webkit-font-smoothing: antialiased;
}

#header .search .underline-input {
    font-family: "Ubuntu Mono", sans-serif;
    font-size: 16px;

    letter-spacing: -1px;

    color: rgba(255, 255, 255, 0.7);
    border-color: rgba(255, 255, 255, 0.25);
}

#header .search .underline-input::-webkit-input-placeholder {
    font-size: 16px;
    font-style: normal;
    line-height: 24px;

    color: rgba(255, 255, 255, 0.25);
}

#header .search .underline-input::-moz-placeholder {
    font-size: 16px;
    font-style: normal;
    line-height: 24px;

    color: rgba(255, 255, 255, 0.25);
}

#header .search .underline-input:-moz-placeholder {
    font-size: 16px;
    font-style: normal;
    line-height: 24px;

    color: rgba(255, 255, 255, 0.25);
}

#header .search .underline-input:-ms-input-placeholder {
    font-size: 16px;
    font-style: normal;
    line-height: 24px;

    color: rgba(255, 255, 255, 0.25);
}

#header .search .underline-input:focus {
    border-color: rgba(255, 255, 255, 0.7);
}

#header .search .underline-input:focus::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

#header .search .underline-input:focus::-moz-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

#header .search .underline-input:focus:-moz-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

#header .search .underline-input:focus:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

#header .nav-right,
#header .nav-left {
    margin-bottom: 0;
}

#header .nav-right > li,
#header .nav-left > li {
    margin-top: 3px;

    vertical-align: top;
}

#header .nav-right > li > a,
#header .nav-left > li > a {
    line-height: 42px;

    position: relative;

    display: inline-block;

    padding: 0 10px;

    color: rgba(255, 255, 255, 0.7);
}

#header .nav-right > li > a:hover,
#header .nav-left > li > a:hover {
    color: white;
}

#header .nav-right > li > a > .badge,
#header .nav-left > li > a > .badge {
    position: absolute;
    top: 7px;
    left: 1px;
}

#header .nav-right > li.nav-profile,
#header .nav-left > li.nav-profile {
    margin-top: 0;
}

#header .nav-right > li.nav-profile > a,
#header .nav-left > li.nav-profile > a {
    font-size: 12px;
}

#header .nav-right > li.nav-profile > a img,
#header .nav-left > li.nav-profile > a img {
    margin-top: -2px;
}

#header .nav-right > li.nav-profile > a span,
#header .nav-left > li.nav-profile > a span {
    line-height: 45px;

    margin: 0 5px;
}

#header .nav-right > li.nav-profile > a span > i,
#header .nav-left > li.nav-profile > a span > i {
    margin-left: 5px;
}

#header .nav-right > li.toggle-right-sidebar,
#header .nav-left > li.toggle-right-sidebar {
    margin-top: 0;
    padding: 0;
}

#header .nav-right > li.toggle-right-sidebar > a,
#header .nav-left > li.toggle-right-sidebar > a {
    line-height: 45px;

    padding: 0 15px;

    background-color: #493D55;
}

#header .nav-left {
    margin-left: 0;
}

#header .nav-left > li.divided-right {
    margin-top: 0;

    border-color: rgba(255, 255, 255, 0.1);
}

#header .nav-left > li.divided-right > a {
    line-height: 45px;
}

@media only screen and (max-width: 992px) {
    .appWrapper.sidebar-sm #header .branding,
    .appWrapper:not(.sidebar-sm):not(.sidebar-xs) #header .branding {
        width: 80px;
    }

    .appWrapper.sidebar-sm #header .branding .brand > span,
    .appWrapper:not(.sidebar-sm):not(.sidebar-xs) #header .branding .brand > span {
        display: none;
    }

    .appWrapper.sidebar-sm #header .branding .brand,
    .appWrapper:not(.sidebar-sm):not(.sidebar-xs) #header .branding .brand {
        margin-left: 12px;
    }

    .appWrapper:not(.sidebar-sm):not(.sidebar-xs) #header .branding {
        width: 40px;
    }

    .appWrapper:not(.sidebar-sm):not(.sidebar-xs) #header .branding .brand {
        margin-left: -9px;
    }
}

@media only screen and (max-width: 767px) {
    .appWrapper.sidebar-xs #header {
        position: relative;
        z-index: 22;
    }

    .appWrapper.sidebar-xs #header .branding {
        position: relative;

        float: none;

        width: 100% !important;

        text-align: center;
    }

    .appWrapper.sidebar-xs #header .branding > a {
        float: none;

        width: auto;

        background-position: 8px 1px;
    }

    .appWrapper.sidebar-xs #header .branding .brand > span {
        display: inline-block;

        margin-left: 10px;
    }

    .appWrapper.sidebar-xs #header .search {
        position: absolute;
        top: 0;
        left: 0;

        width: 120px;
    }

    .appWrapper.sidebar-xs #header .nav-left {
        position: absolute;
        top: 0;
        right: 0;
    }

    .appWrapper.sidebar-xs #header .nav-left .sidebar-collapse {
        display: none;
    }

    .appWrapper.sidebar-xs #header .nav-left .settings .dropdown-menu {
        right: 0;
        left: auto;
    }

    .appWrapper.sidebar-xs #header .nav-left .settings .dropdown-menu.with-arrow:before {
        right: 12px;
        left: auto;
    }

    .appWrapper.sidebar-xs #header .nav-left .settings .dropdown-menu.with-arrow:after {
        right: 13px;
        left: auto;
    }

    .appWrapper.sidebar-xs #header .nav-right {
        float: left !important;

        padding-left: 10px;
    }

    .appWrapper.sidebar-xs #header .nav-right .nav-profile {
        position: absolute;
        right: 40px;
        bottom: 0;
    }

    .appWrapper.sidebar-xs #header .nav-right .nav-profile > a > span {
        display: none;
    }

    .appWrapper.sidebar-xs #header .nav-right .nav-profile .dropdown-menu {
        right: 0;
        left: auto;
    }

    .appWrapper.sidebar-xs #header .nav-right .nav-profile .dropdown-menu.with-arrow:before {
        right: 12px;
        left: auto;
    }

    .appWrapper.sidebar-xs #header .nav-right .nav-profile .dropdown-menu.with-arrow:after {
        right: 13px;
        left: auto;
    }

    .appWrapper.sidebar-xs #header .nav-right .toggle-right-sidebar {
        position: absolute;
        right: 0;
        bottom: 0;
    }

    .appWrapper.sidebar-xs #header .nav-right .users .dropdown-menu,
    .appWrapper.sidebar-xs #header .nav-right .messages .dropdown-menu,
    .appWrapper.sidebar-xs #header .nav-right .notifications .dropdown-menu {
        right: auto;
        left: 0;
    }

    .appWrapper.sidebar-xs #header .nav-right .users .dropdown-menu.with-arrow:before,
    .appWrapper.sidebar-xs #header .nav-right .messages .dropdown-menu.with-arrow:before,
    .appWrapper.sidebar-xs #header .nav-right .notifications .dropdown-menu.with-arrow:before {
        right: auto;
        left: 12px;
    }

    .appWrapper.sidebar-xs #header .nav-right .users .dropdown-menu.with-arrow:after,
    .appWrapper.sidebar-xs #header .nav-right .messages .dropdown-menu.with-arrow:after,
    .appWrapper.sidebar-xs #header .nav-right .notifications .dropdown-menu.with-arrow:after {
        right: auto;
        left: 13px;
    }

    .appWrapper.sidebar-xs.header-static.aside-fixed #header .search {
        left: 40px;
    }

    .appWrapper.sidebar-xs.header-fixed #header {
        position: fixed;
    }
}

@media only screen and (max-width: 480px) {
    .appWrapper.sidebar-xs #header .branding {
        padding-left: 10px;

        text-align: left;
    }

    .appWrapper.sidebar-xs #header .branding > a {
        background-position: 0 1px;
    }

    .appWrapper.sidebar-xs #header .branding .brand > span {
        display: none;
    }

    .appWrapper.sidebar-xs #header .search {
        left: 40px;
    }

    .appWrapper.sidebar-xs.header-static.aside-fixed #header .search {
        left: 80px;
    }

    .appWrapper.sidebar-xs.sidebar-offcanvas #header .search {
        left: 70px;
    }
}

@media only screen and (max-width: 420px) {
    .appWrapper.sidebar-xs #header .nav-right > li {
        position: static !important;
    }

    .appWrapper.sidebar-xs #header .nav-right > li.toggle-right-sidebar {
        position: absolute !important;
    }

    .appWrapper.sidebar-xs #header .nav-right > li .dropdown-menu {
        right: 0 !important;
        left: 0 !important;
    }

    .appWrapper.sidebar-xs #header .nav-right > li .dropdown-menu.with-arrow:before,
    .appWrapper.sidebar-xs #header .nav-right > li .dropdown-menu.with-arrow:after {
        display: none !important;
    }
}

.appWrapper.sidebar-xs .sidebar-collapse i:before {
    content: "\f03c";
}

.appWrapper.aside-fixed #sidebar {
    position: fixed;
    top: 45px;
    bottom: 0;
}

.appWrapper.aside-static #wrap {
    position: relative;
}

.appWrapper.aside-static #wrap #controls {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
}

.appWrapper.aside-static #wrap #controls.dropdown-open {
    right: 0;
}

.appWrapper.aside-static #sidebar {
    position: static;

    float: left;

    min-height: 100%;
    padding-top: 45px;
}

.appWrapper.aside-static.header-static #sidebar {
    padding-top: 45px;
}

#sidebar {
    z-index: 21;

    width: 250px;
    padding: 0;

    background-color: #493D55;

    -webkit-overflow-scrolling: touch;
    -webkit-overflow-scrolling: -blackberry-touch;
}

#sidebar .panel-group {
    margin-bottom: 0;
}

#sidebar .panel-group .panel + .panel {
    margin-top: 0;
}

#sidebar .panel-group .panel {
    border: 0;
    border-radius: 0;
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
}

#sidebar .panel-group .panel > .panel-heading {
    padding: 0;

    text-transform: uppercase;

    border: 0;
    background-color: transparent;
}

#sidebar .panel-group .panel > .panel-heading .panel-title {
    margin: 0 15px;
}

#sidebar .panel-group .panel > .panel-heading .panel-title > a {
    font-size: 10px;

    position: relative;

    display: block;

    margin: 0 -15px;
    padding: 15px 15px 0;

    cursor: pointer;
    text-decoration: none;

    color: rgba(255, 255, 255, 0.2);
}

#sidebar .panel-group .panel > .panel-heading .panel-title > a:after {
    display: block;

    margin-top: 15px;

    content: "";

    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

#sidebar .panel-group .panel > .panel-heading .panel-title > a:hover {
    text-decoration: none;

    color: rgba(255, 255, 255, 0.5);
}

#sidebar .panel-group .panel > .panel-heading .panel-title > a > i {
    position: absolute;
    right: 15px;
}

#sidebar .panel-group .panel > .panel-heading + .panel-collapse > .panel-body {
    border-top: 0;
}

#sidebar .panel-group .panel .panel-title > a.collapsed {
    padding: 15px;

    color: rgba(255, 255, 255, 0.5);
    background-color: rgba(0, 0, 0, 0.05);
}

#sidebar .panel-group .panel .panel-title > a.collapsed:after {
    display: none;
}

#sidebar .panel-group .panel .panel-title > a.collapsed:hover {
    color: white;
}

#sidebar .panel-group .panel .panel-title > a.collapsed > i:before {
    content: "\f107";
}

#sidebar .panel-group .panel .panel-body {
    color: rgba(255, 255, 255, 0.5);
}

#sidebar .summary .media .media-body {
    font-size: 12px;

    text-transform: uppercase;

    color: rgba(255, 255, 255, 0.3);
}

#sidebar .summary .media .media-body .media-heading {
    font-weight: 700;

    color: rgba(255, 255, 255, 0.5);
}

#sidebar .settings label {
    font-weight: 400;
}

#sidebar .settings .onoffswitch {
    right: -10px;
}

#sidebar .settings .onoffswitch .onoffswitch-label {
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;

    opacity: 0.4;

    filter: alpha(opacity=40);
}

#sidebar .settings .onoffswitch-checkbox:checked + .onoffswitch-label {
    opacity: 1;

    filter: alpha(opacity=100);
}

#sidebar-wrap {
    width: 100%;
    height: 100%;

    -webkit-overflow-scrolling: touch;
    -webkit-overflow-scrolling: -blackberry-touch;
}

#navigation {
    margin: 0 -15px;
    padding: 0;

    list-style: none;

    background-color: #493D55;

    -webkit-overflow-scrolling: touch;
    -webkit-overflow-scrolling: -blackberry-touch;
}

#navigation > li > a > i {
    font-size: 14px;
    line-height: 1;

    display: inline-block;

    width: 20px;
    margin-right: 10px;

    -webkit-transition: all 0.1s linear;
    -moz-transition: all 0.1s linear;
    transition: all 0.1s linear;
    text-align: center;
}

#navigation > li {
    position: relative;
    z-index: 10;

    overflow: hidden;

    margin: 0;
}

#navigation > li.open > a,
#navigation > li:hover > a {
    color: white;
    background-color: rgba(0, 0, 0, 0.2);
}

#navigation > li.open > i,
#navigation > li:hover > i {
    color: white;
}

#navigation > li.active > a {
    color: white;
    background-color: #16A085;
}

#navigation > li.active > i {
    color: white;
}

#navigation > li.active .ink {
    z-index: 9;
}

#navigation > li a {
    font-size: 13px;

    position: relative;

    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    transition: all 0.2s linear;

    color: rgba(255, 255, 255, 0.5);
}

#navigation > li a:hover {
    text-decoration: none;
}

#navigation > li .ink {
    position: absolute;

    display: block;

    width: 250px !important;
    height: 250px !important;

    transform: scale(0);

    border-radius: 100%;
    background: rgba(255, 255, 255, 0.1);
}

#navigation > li .ink.animate {
    -webkit-animation: ripple 0.65s linear;
    -moz-animation: ripple 0.65s linear;
    animation: ripple 0.65s linear;
}

#navigation > li > a {
    font-weight: 700;

    position: relative;

    display: block;

    padding: 12px 15px;
}

#navigation > li > a > .badge {
    position: absolute;
    top: 7px;
    left: 25px;
}

#navigation > li > a > .label {
    position: absolute;
    top: 13px;
    right: 10px;
}

#navigation .dropdown > a > i:last-of-type {
    font-size: 10px;

    position: absolute;
    top: 17px;
    right: 0;
}

#navigation .dropdown.close {
    font-size: 21px;
    font-weight: normal;
    line-height: 1;

    float: none;

    opacity: 1;
    color: #000000;
    text-shadow: none;

    filter: alpha(opacity=20);
}

#navigation .dropdown.close > a > i:last-of-type:before {
    font-family: "FontAwesome";
    font-weight: 900;
    content: "\f067";
}

#navigation .dropdown.open > a > i:last-of-type:before {
    font-family: "FontAwesome";
    font-weight: 900;
    content: "\f068";
}

#navigation .dropdown > a > .label {
    right: 35px;
}

#navigation .dropdown > ul {
    display: none;

    padding: 5px 0;

    list-style: none;

    background-color: rgba(0, 0, 0, 0.15);
}

#navigation .dropdown > ul li {
    position: relative;

    padding: 0;
}

#navigation .dropdown > ul li:hover > a,
#navigation .dropdown > ul li.active > a {
    color: #16A085;
}

#navigation .dropdown > ul li:last-child > a {
    border-bottom: 0;
}

#navigation .dropdown > ul li > a {
    font-size: 12px;

    position: relative;

    display: block;

    padding: 4px 0 4px 30px;

    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    transition: all 0.2s linear;
}

#navigation .dropdown > ul li > a > i {
    width: 12.85px;
    margin-right: 8px;
}

#navigation .dropdown > ul li > a > .badge,
#navigation .dropdown > ul li > a > .label {
    position: absolute;
    top: 8px;
    right: 10px;
}

#navigation .dropdown.submenu > a > i:last-of-type {
    top: 7px;
}

#navigation .dropdown.submenu.open > a {
    color: white !important;
}

#navigation .dropdown.submenu > ul {
    padding-left: 15px;

    background-color: transparent;
}

.sidebar-sm #sidebar,
.sidebar-xs #sidebar {
    background-color: transparent;
}

.sidebar-sm #sidebar #sidebar-wrap:before,
.sidebar-xs #sidebar #sidebar-wrap:before {
    position: absolute;

    display: block;

    width: 80px;
    height: 100%;

    content: "";

    background-color: #493D55;
}

.sidebar-sm #sidebar.dropdown-open,
.sidebar-xs #sidebar.dropdown-open {
    width: 100% !important;
}

.sidebar-sm #sidebar .slimScrollDiv .slimScrollBar,
.sidebar-sm #sidebar .slimScrollDiv .slimScrollRail,
.sidebar-xs #sidebar .slimScrollDiv .slimScrollBar,
.sidebar-xs #sidebar .slimScrollDiv .slimScrollRail {
    right: auto !important;
    left: 1px !important;

    width: 4px !important;
}

.sidebar-sm #sidebar .panel-group .panel-heading,
.sidebar-sm #sidebar .panel-group .charts,
.sidebar-sm #sidebar .panel-group .settings,
.sidebar-xs #sidebar .panel-group .panel-heading,
.sidebar-xs #sidebar .panel-group .charts,
.sidebar-xs #sidebar .panel-group .settings {
    display: none;
}

.sidebar-sm #sidebar .panel-group .panel-body,
.sidebar-xs #sidebar .panel-group .panel-body {
    padding: 0 10px;
}

.sidebar-sm #sidebar .panel-group .panel-collapse.collapse,
.sidebar-xs #sidebar .panel-group .panel-collapse.collapse {
    display: block !important;
}

.sidebar-sm #sidebar .panel-group .panel-group,
.sidebar-xs #sidebar .panel-group .panel-group {
    height: 100%;
}

.sidebar-sm #sidebar .panel-group .panel-group .panel,
.sidebar-sm #sidebar .panel-group .panel-group .panel-collapse,
.sidebar-sm #sidebar .panel-group .panel-group .panel-body,
.sidebar-xs #sidebar .panel-group .panel-group .panel,
.sidebar-xs #sidebar .panel-group .panel-group .panel-collapse,
.sidebar-xs #sidebar .panel-group .panel-group .panel-body {
    height: 100% !important;
}

.sidebar-sm #sidebar #navigation,
.sidebar-xs #sidebar #navigation {
    height: 100%;
    margin: 0 -10px;
}

.sidebar-sm #sidebar #navigation > li,
.sidebar-xs #sidebar #navigation > li {
    overflow: visible;
}

.sidebar-sm #sidebar #navigation > li > a,
.sidebar-xs #sidebar #navigation > li > a {
    font-size: 10px;

    padding: 15px 5px;

    text-align: center;
}

.sidebar-sm #sidebar #navigation > li > a > i,
.sidebar-xs #sidebar #navigation > li > a > i {
    font-size: 22px;

    display: block;

    margin: 0 auto 3px;
}

.sidebar-sm #sidebar #navigation > li > a > .label,
.sidebar-xs #sidebar #navigation > li > a > .label {
    display: none;
}

.sidebar-sm #sidebar #navigation > li .ink,
.sidebar-xs #sidebar #navigation > li .ink {
    display: none;
}

.sidebar-sm #sidebar #navigation > li.dropdown > a > i:last-of-type,
.sidebar-xs #sidebar #navigation > li.dropdown > a > i:last-of-type {
    display: none;
}

.sidebar-sm #sidebar #navigation > li.dropdown > ul,
.sidebar-xs #sidebar #navigation > li.dropdown > ul {
    position: absolute;
    top: 0;
    left: 100%;

    width: 220px;

    background-color: #3C3246;
}

.sidebar-sm #sidebar #navigation > li.dropdown.open > ul,
.sidebar-xs #sidebar #navigation > li.dropdown.open > ul {
    display: none !important;
}

.sidebar-sm #sidebar #navigation > li.dropdown:hover > ul,
.sidebar-xs #sidebar #navigation > li.dropdown:hover > ul {
    display: block !important;
}

.sidebar-sm #sidebar #navigation > li li.submenu > ul,
.sidebar-xs #sidebar #navigation > li li.submenu > ul {
    position: absolute;
    top: 0;
    left: 100%;

    width: 220px;
    padding-left: 0;

    background-color: #3C3246;
}

.sidebar-sm #sidebar #navigation > li li.submenu.open > ul,
.sidebar-xs #sidebar #navigation > li li.submenu.open > ul {
    display: none !important;
}

.sidebar-sm #sidebar #navigation > li li.submenu:hover > ul,
.sidebar-xs #sidebar #navigation > li li.submenu:hover > ul {
    display: block !important;
}

.appWrapper.header-fixed.sidebar-sm #content {
    left: 80px;

    padding-left: 0;
}

.appWrapper:not(.header-fixed).sidebar-sm #header {
    padding-left: 80px;
}

.appWrapper:not(.header-fixed).sidebar-sm #header .branding {
    width: 80px;
}

.appWrapper:not(.header-fixed).sidebar-sm #header .branding .brand > span {
    display: none;
}

.appWrapper:not(.header-fixed).sidebar-sm #header .branding .brand {
    margin-left: 12px;
}

.appWrapper:not(.header-fixed).sidebar-sm #content {
    padding-left: 80px;
}

.appWrapper:not(.aside-fixed).sidebar-sm #content {
    padding-left: 80px;
}

.appWrapper:not(.header-fixed):not(.aside-fixed).sidebar-sm #header {
    padding-left: 0;
}

.sidebar-sm #sidebar,
.sidebar-sm #navigation {
    width: 80px;
}

.sidebar-sm #content {
    padding-left: 80px;
}

.sidebar-xs #header .branding {
    width: 40px;
    padding: 0;
}

.sidebar-xs #header .branding > a {
    width: 40px;

    background-position: 6px 10px;
}

.sidebar-xs #header .branding > a > span {
    display: none;
}

.sidebar-xs #sidebar,
.sidebar-xs #navigation {
    width: 40px;
}

.sidebar-xs #sidebar #navigation > li > a {
    padding: 10px 5px;
}

.sidebar-xs #sidebar #navigation > li > a > i {
    font-size: 14px;

    display: inline-block;

    margin: 0;
}

.sidebar-xs #sidebar #navigation > li > a span {
    display: none;
}

.sidebar-xs #sidebar #navigation > li > a .badge {
    top: 3px;
    left: 5px;

    display: block;
}

.sidebar-xs #content {
    padding-left: 40px;
}

.sidebar-xs #sidebar #sidebar-wrap:before {
    width: 40px;
}

.appWrapper.header-fixed.sidebar-xs #content {
    left: 40px;

    padding-left: 0;
}

.appWrapper:not(.header-fixed).sidebar-xs #header {
    padding-left: 40px;
}

.appWrapper:not(.header-fixed).sidebar-xs #header .branding {
    width: 40px;
}

.appWrapper:not(.header-fixed).sidebar-xs #content {
    padding-left: 40px;
}

.appWrapper:not(.aside-fixed).sidebar-xs #content {
    padding-left: 40px;
}

.appWrapper:not(.header-fixed):not(.aside-fixed).sidebar-xs #header {
    padding-left: 0;
}

.appWrapper.sidebar-xs.sidebar-offcanvas #sidebar {
    left: -40px;

    -webkit-transition: left 0.2s;
    -moz-transition: left 0.2s;
    transition: left 0.2s;
}

.appWrapper.sidebar-xs.sidebar-offcanvas #content {
    left: 0;
}

.appWrapper.sidebar-xs.sidebar-offcanvas.offcanvas-opened #sidebar {
    left: 0;
}

.appWrapper.sidebar-xs.sidebar-offcanvas.offcanvas-opened #content {
    left: 40px;
}

@media only screen and (max-width: 767px) {
    .appWrapper.sidebar-xs.aside-fixed.header-static #sidebar {
        z-index: 99;
        top: 0;
    }

    .appWrapper.sidebar-xs.aside-fixed.header-static #sidebar #navigation > li > a {
        line-height: 26px;

        height: 45px;
    }

    .appWrapper.sidebar-xs.aside-static #sidebar {
        padding-top: 90px;
    }
}

.pageheader {
    margin-bottom: 30px;
}

.pageheader h2 {
    font-size: 32px;
    font-weight: 300;

    display: inline-block;

    margin-top: 10px;

    color: #4A555B;
}

.pageheader h2 span {
    font-size: 14px;
    font-style: italic;

    color: #95A2A9;
}

.pageheader .page-bar {
    background-color: white;
}

.pageheader .page-bar .page-breadcrumb {
    display: inline-block;

    margin: 0;
    padding: 0;

    list-style: none;
}

.pageheader .page-bar .page-breadcrumb > li {
    display: inline-block;
}

.pageheader .page-bar .page-breadcrumb > li > a {
    display: inline-block;

    padding: 8px 10px;

    color: #95A2A9;
}

.pageheader .page-bar .page-breadcrumb > li > a:hover {
    text-decoration: none;

    color: #16A085;
}

.pageheader .page-bar .page-breadcrumb > li:first-child {
    margin-right: 8px;

    background-color: #493D55;
}

.pageheader .page-bar .page-breadcrumb > li:first-child > a {
    color: rgba(255, 255, 255, 0.5);
}

.pageheader .page-bar .page-breadcrumb > li:first-child > a:hover {
    color: white;
}

.pageheader .page-bar .page-breadcrumb > li:first-child:before {
    content: "";
}

.pageheader .page-bar .page-toolbar {
    display: inline-block;
    float: right;
}

.pageheader .page-bar .page-toolbar .btn {
    padding: 8px 12px;
}

@media only screen and (max-width: 992px) {
    .page-bar .page-toolbar .btn span {
        display: none;
    }
}

@media only screen and (max-width: 767px) {
    .page-bar .page-toolbar .btn {
        width: 37px;
    }

    .page-bar .page-toolbar .btn i:last-of-type {
        display: none;
    }
}

@media only screen and (max-width: 480px) {
    .pageheader h2 span {
        display: block;
    }

    .pageheader .page-bar .page-breadcrumb {
        width: 100%;
    }

    .pageheader .page-bar .page-breadcrumb > li {
        font-size: 12px;

        display: block;

        padding-left: 10px;
    }

    .pageheader .page-bar .page-breadcrumb > li:first-child {
        margin-right: 0;
        padding-left: 0;
    }

    .pageheader .page-bar .page-breadcrumb > li > a {
        padding: 8px 5px;
    }

    .pageheader .page-bar .page-toolbar {
        display: none;
    }
}

.appWrapper.rightbar-hidden #rightbar {
    right: -250px;
}

.appWrapper.header-fixed.rightbar-show #rightbar,
.appWrapper.aside-fixed.rightbar-show #rightbar {
    right: 0;
}

.appWrapper.header-fixed.rightbar-show #header,
.appWrapper.aside-fixed.rightbar-show #header {
    right: 250px;
}

.appWrapper.header-fixed.rightbar-show #content,
.appWrapper.aside-fixed.rightbar-show #content {
    right: 250px;
}

.appWrapper.header-static.rightbar-show #rightbar {
    right: 0;
}

.appWrapper.header-static.rightbar-show #header {
    padding-right: 250px;
}

.appWrapper.header-static.rightbar-show #content {
    padding-right: 250px;
}

.appWrapper.aside-static.rightbar-show #content {
    padding-right: 250px;
}

#rightbar {
    position: fixed;
    z-index: 9999;
    top: 0;
    bottom: 0;

    overflow: auto;

    width: 250px;

    -webkit-transition: all 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335);
    -moz-transition: all 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335);
    transition: all 0.25s cubic-bezier(0.6, 0.04, 0.98, 0.335);

    background-color: #1D2833;
}

#rightbar .nav.nav-tabs {
    z-index: 2;

    max-height: 45px;
    padding: 0;
}

#rightbar .nav.nav-tabs > li {
    display: table-cell;
    float: none;

    width: 1%;

    text-align: center;
}

#rightbar .nav.nav-tabs > li > a {
    min-height: 45px;
    padding: 12px 15px;

    cursor: pointer;

    color: #95A2A9;
    border: 0;
    border-radius: 0;
    background-color: #141B23;
}

#rightbar .nav.nav-tabs > li > a:hover {
    color: #BFC7CB;
    background-color: #172029;
}

#rightbar .nav.nav-tabs > li.active > a {
    color: white;
    background-color: #1D2833;
}

#rightbar .nav.nav-tabs > li.active > a:hover
#rightbar .nav.nav-tabs > li.active > a:focus {
    color: white;
    background-color: #1D2833;
}

#rightbar .tab-content {
    left: 0;

    padding: 5px 15px;

    background: #ffffff;
}

#rightbar .tab-content .tab-pane {
    padding: 0;
}

#rightbar .tab-content .tab-pane > h6 {
    font-family: "Dosis", "Arial", sans-serif;
    font-weight: 300;

    padding-bottom: 10px;

    text-transform: uppercase;

    color: #95A2A9;
    border-bottom: 1px dotted rgba(255, 255, 255, 0.15);
}

#rightbar .tab-content .tab-pane ul {
    margin: 0;
    padding: 0;

    list-style-type: none;
}

#rightbar .tab-content .tab-pane ul > li {
    margin-bottom: 15px;
}

#rightbar .tab-content .tab-pane ul > li:last-child {
    margin-bottom: 30px;
}

#rightbar .tab-content .tab-pane ul > li .media {
    position: relative;
}

#rightbar .tab-content .tab-pane ul > li .media .unread {
    position: absolute;
    left: 22px;
}

#rightbar .tab-content .tab-pane ul > li .media .media-body {
    position: relative;

    padding-top: 2px;
    padding-bottom: 6px;
}

#rightbar .tab-content .tab-pane ul > li .media .media-body .media-heading {
    font-family: "Lato", "Arial", sans-serif;
    font-weight: 300;

    display: block;

    margin: 0 0 3px;

    color: #BFC7CB;
}

#rightbar .tab-content .tab-pane ul > li .media .media-body small {
    font-size: 11px;

    display: inline-block;
    overflow: hidden;

    width: 130px;

    white-space: nowrap;
    text-overflow: ellipsis;

    color: rgba(0, 0, 0, 0.3);
}

#rightbar .tab-content .tab-pane ul > li .media .media-body small i {
    font-size: 14px;
}

#rightbar .tab-content .tab-pane ul > li .media .media-body .status {
    position: absolute;
    top: 18px;
    right: 0;
}

#rightbar .tab-content .tab-pane ul > li.online .status {
    border-color: #16A085;
}

#rightbar .tab-content .tab-pane ul > li.busy .status {
    border-color: #FFC100;
}

#rightbar .tab-content .tab-pane ul > li.offline .media .thumb {
    opacity: 0.4;

    filter: alpha(opacity=40);
}

#rightbar .tab-content .tab-pane ul > li.offline .status {
    border-color: #616F77;
}

#rightbar .tab-content .tab-pane ul.settings > li .form-group .control-label {
    font-weight: 300;
    line-height: 22px;

    margin: 0;
    margin-bottom: 15px;
    padding: 0;

    color: #95A2A9;
}

#rightbar .tab-content .tab-pane ul.settings > li .form-group .control-label .onoffswitch {
    right: -35px;
}

@media only screen and (max-width: 1200px) {
    #rightbar {
        top: 45px;
    }

    .appWrapper:not(.rtl).rightbar-show #rightbar {
        right: 0;
    }

    .appWrapper:not(.rtl).rightbar-show #header {
        right: 0 !important;

        padding-right: 0 !important;
    }

    .appWrapper:not(.rtl).rightbar-show #content {
        right: 0 !important;

        padding-right: 0 !important;
    }
}

@media only screen and (max-width: 767px) {
    #rightbar {
        top: 90px;
    }
}

@media only screen and (max-width: 360px) {
    #rightbar {
        width: 100%;
    }

    .appWrapper.rightbar-hidden #rightbar {
        right: -100%;
    }
}

#pageloader.hide {
    display: none;
}

#pageloader.animate {
    position: fixed;
    top: 50%;
    left: 50%;

    width: 40px;
    height: 40px;
    margin: -30px 0 0 -30px;

    -webkit-animation: loaderrotate 2s infinite linear;
    -moz-animation: loaderrotate 2s infinite linear;
    animation: loaderrotate 2s infinite linear;
    text-align: center;
}

#pageloader.animate .dot1,
#pageloader.animate .dot2 {
    position: absolute;
    top: 0;

    display: inline-block;

    width: 60%;
    height: 60%;

    -webkit-animation: loaderbounce 2s infinite ease-in-out;
    -moz-animation: loaderbounce 2s infinite ease-in-out;
    animation: loaderbounce 2s infinite ease-in-out;

    border-radius: 100%;
    background-color: #16A085;
}

#pageloader.animate .dot2 {
    top: auto;
    bottom: 0;

    -webkit-animation-delay: -1s;
    -moz-animation-delay: -1s;
    animation-delay: -1s;

    background-color: #E05D6F;
}

@-webkit-keyframes loaderrotate {
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes loaderrotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-webkit-keyframes loaderbounce {
    0%,
    100% {
        -webkit-transform: scale(0);
    }
    50% {
        -webkit-transform: scale(1);
    }
}

@keyframes loaderbounce {
    0%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    50% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

.add-nav {
    margin-top: -29px;
}

.add-nav .nav-heading {
    padding: 30px 20px;

    background-color: white;
}

.add-nav .nav-heading h1,
.add-nav .nav-heading h2,
.add-nav .nav-heading h3,
.add-nav .nav-heading h4,
.add-nav .nav-heading h5,
.add-nav .nav-heading h6 {
    display: inline-block;

    margin: 0;
}

.add-nav .nav-tabs {
    background-color: white;
}

.add-nav .nav-tabs > li:first-child {
    margin-left: 10px;
}

.add-nav .nav-tabs > li > a {
    margin: 0 10px;
    padding: 0 8px 10px;

    color: #616F77;
}

.add-nav .nav-tabs > li > a:hover {
    color: #428BCA;
    background-color: transparent;
}

.add-nav .nav-tabs > li.active > a,
.add-nav .nav-tabs > li.active > a:hover,
.add-nav .nav-tabs > li.active > a:focus {
    font-weight: 700;

    color: #428BCA;
    background-color: white;
}

.add-nav .nav-tabs > li.active:after {
    display: none;
}

.add-nav .tab-content .tab-pane {
    padding: 40px 0 15px;
}

.tile {
    position: relative;

    margin-bottom: 20px;

    -webkit-transition: opacity 0.25s ease-out;
    -moz-transition: opacity 0.25s ease-out;
    transition: opacity 0.25s ease-out;

    opacity: 1;
    color: #616F77;
    background-color: white;

    filter: alpha(opacity=100);
}

.tile.collapsed .controls .minimize {
    display: none;
}

.tile.collapsed .controls .expand {
    display: inline !important;
}

.tile.refreshing {
    cursor: not-allowed;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    opacity: 0.3;

    filter: alpha(opacity=30);
}

.tile.refreshing a {
    cursor: default;
    pointer-events: none;
}

.tile.refreshing .controls .settings i:first-of-type {
    display: none;
}

.tile.refreshing .controls .settings i:last-of-type {
    display: inline-block !important;
}

.tile.isInFullScreen {
    width: 90%;
}

.tile.isInFullScreen .controls .remove {
    display: none;
}

.tile.isInFullScreen .controls > li:first-child {
    margin-right: 0 !important;
}

.tile .tile-header,
.tile .tile-widget,
.tile .tile-body,
.tile .tile-footer {
    position: relative;

    padding: 15px;
}

.tile .dvd {
    border-width: 0;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.1);
}

.tile .dvd.dvd-white {
    border-color: rgba(255, 255, 255, 0.1);
}

.tile .dvd.dvd-btm {
    border-bottom-width: 1px;
}

.tile .dvd.dvd-top {
    border-top-width: 1px;
}

.tile.tile-simple .tile-header .controls > li {
    background-color: transparent !important;
}

.tile.tile-simple .tile-header .controls > li > a {
    font-size: 12px;
    line-height: 28px;

    min-width: 28px;
    padding: 0 10px;
}

.tile .tile-header {
    padding: 8px 15px;
}

.tile .tile-header h1,
.tile .tile-header h2,
.tile .tile-header h3,
.tile .tile-header h4 {
    font-size: 20px;
    font-weight: 400;
    line-height: 26px;

    display: inline-block;

    margin: 0;
    padding: 0;
}

.tile .tile-header h2 {
    font-size: 18px;
}

.tile .tile-header h3 {
    font-size: 16px;
}

.tile .tile-header h4 {
    font-size: 14px;
}

.tile .tile-header .controls {
    position: absolute;
    z-index: 9;
    top: 0;
    right: 0;

    height: 100%;
    margin: 0;
    padding: 0;

    list-style: none;
}

.tile .tile-header .controls > li {
    display: inline-block;
    float: left;

    height: 100%;
}

.tile .tile-header .controls > li > a {
    line-height: 43px;

    display: block;

    min-width: 41px;
    height: 100%;
    padding: 0 10px;

    text-align: center;

    color: #95A2A9;
}

.tile .tile-header .controls > li > a:hover {
    text-decoration: none;

    color: #616F77;
}

.tile .tile-header .controls .expand {
    display: none;
}

.tile .tile-header .controls .settings > i:last-of-type {
    display: none;
}

.tile .tile-header.dvd .controls {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.tile .tile-header.dvd .controls > li {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

.tile .tile-header.dvd.dvd-white .controls > li {
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.tile .tile-header .note {
    font-family: "Dosis", "Arial", sans-serif;
    font-size: 12px;

    position: relative;
    top: -2px;

    display: inline-block;

    margin-top: 5px;
    margin-left: 5px;
    padding: 2px 5px;

    color: #95A2A9;
    background-color: rgba(0, 0, 0, 0.1);
}

.tile .tile-header .btn {
    margin-right: -24px;
    padding: 2px 5px;
}

.tile .tile-nav {
    margin-bottom: 15px;
}

.tile .tile-nav > li {
    display: inline-block;
}

.tile .tile-nav > li > a {
    padding: 6px 12px;
}

.tile[class*="bg-"]:not(.bg-default) .dvd,
.tile[class*="bg-"]:not(.bg-default).dvd,
.tile-header[class*="bg-"]:not(.bg-default) .dvd,
.tile-header[class*="bg-"]:not(.bg-default).dvd,
.tile-widget[class*="bg-"]:not(.bg-default) .dvd,
.tile-widget[class*="bg-"]:not(.bg-default).dvd,
.tile-body[class*="bg-"]:not(.bg-default) .dvd,
.tile-body[class*="bg-"]:not(.bg-default).dvd,
.tile-footer[class*="bg-"]:not(.bg-default) .dvd,
.tile-footer[class*="bg-"]:not(.bg-default).dvd {
    border-color: rgba(255, 255, 255, 0.2);
}

.tile[class*="bg-"]:not(.bg-default) .controls > li,
.tile-header[class*="bg-"]:not(.bg-default) .controls > li,
.tile-widget[class*="bg-"]:not(.bg-default) .controls > li,
.tile-body[class*="bg-"]:not(.bg-default) .controls > li,
.tile-footer[class*="bg-"]:not(.bg-default) .controls > li {
    border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.tile[class*="bg-"]:not(.bg-default) .controls > li:last-child,
.tile-header[class*="bg-"]:not(.bg-default) .controls > li:last-child,
.tile-widget[class*="bg-"]:not(.bg-default) .controls > li:last-child,
.tile-body[class*="bg-"]:not(.bg-default) .controls > li:last-child,
.tile-footer[class*="bg-"]:not(.bg-default) .controls > li:last-child {
    border: 0;
    background-color: rgba(0, 0, 0, 0.1);
}

.tile[class*="bg-"]:not(.bg-default) .controls > li:nth-last-child(2),
.tile-header[class*="bg-"]:not(.bg-default) .controls > li:nth-last-child(2),
.tile-widget[class*="bg-"]:not(.bg-default) .controls > li:nth-last-child(2),
.tile-body[class*="bg-"]:not(.bg-default) .controls > li:nth-last-child(2),
.tile-footer[class*="bg-"]:not(.bg-default) .controls > li:nth-last-child(2) {
    border: 0;
    background-color: rgba(0, 0, 0, 0.05);
}

.tile[class*="bg-"]:not(.bg-default) .legend table,
.tile-header[class*="bg-"]:not(.bg-default) .legend table,
.tile-widget[class*="bg-"]:not(.bg-default) .legend table,
.tile-body[class*="bg-"]:not(.bg-default) .legend table,
.tile-footer[class*="bg-"]:not(.bg-default) .legend table {
    color: white !important;
}

.tile[class*="bg-"]:not(.bg-default) h1.underline,
.tile[class*="bg-"]:not(.bg-default) h2.underline,
.tile[class*="bg-"]:not(.bg-default) h3.underline,
.tile[class*="bg-"]:not(.bg-default) h4.underline,
.tile[class*="bg-"]:not(.bg-default) h5.underline,
.tile[class*="bg-"]:not(.bg-default) h6.underline,
.tile[class*="bg-"]:not(.bg-default) .h1.underline,
.tile[class*="bg-"]:not(.bg-default) .h2.underline,
.tile[class*="bg-"]:not(.bg-default) .h3.underline,
.tile[class*="bg-"]:not(.bg-default) .h4.underline,
.tile[class*="bg-"]:not(.bg-default) .h5.underline,
.tile[class*="bg-"]:not(.bg-default) .h6.underline,
.tile-header[class*="bg-"]:not(.bg-default) h1.underline,
.tile-header[class*="bg-"]:not(.bg-default) h2.underline,
.tile-header[class*="bg-"]:not(.bg-default) h3.underline,
.tile-header[class*="bg-"]:not(.bg-default) h4.underline,
.tile-header[class*="bg-"]:not(.bg-default) h5.underline,
.tile-header[class*="bg-"]:not(.bg-default) h6.underline,
.tile-header[class*="bg-"]:not(.bg-default) .h1.underline,
.tile-header[class*="bg-"]:not(.bg-default) .h2.underline,
.tile-header[class*="bg-"]:not(.bg-default) .h3.underline,
.tile-header[class*="bg-"]:not(.bg-default) .h4.underline,
.tile-header[class*="bg-"]:not(.bg-default) .h5.underline,
.tile-header[class*="bg-"]:not(.bg-default) .h6.underline,
.tile-widget[class*="bg-"]:not(.bg-default) h1.underline,
.tile-widget[class*="bg-"]:not(.bg-default) h2.underline,
.tile-widget[class*="bg-"]:not(.bg-default) h3.underline,
.tile-widget[class*="bg-"]:not(.bg-default) h4.underline,
.tile-widget[class*="bg-"]:not(.bg-default) h5.underline,
.tile-widget[class*="bg-"]:not(.bg-default) h6.underline,
.tile-widget[class*="bg-"]:not(.bg-default) .h1.underline,
.tile-widget[class*="bg-"]:not(.bg-default) .h2.underline,
.tile-widget[class*="bg-"]:not(.bg-default) .h3.underline,
.tile-widget[class*="bg-"]:not(.bg-default) .h4.underline,
.tile-widget[class*="bg-"]:not(.bg-default) .h5.underline,
.tile-widget[class*="bg-"]:not(.bg-default) .h6.underline,
.tile-body[class*="bg-"]:not(.bg-default) h1.underline,
.tile-body[class*="bg-"]:not(.bg-default) h2.underline,
.tile-body[class*="bg-"]:not(.bg-default) h3.underline,
.tile-body[class*="bg-"]:not(.bg-default) h4.underline,
.tile-body[class*="bg-"]:not(.bg-default) h5.underline,
.tile-body[class*="bg-"]:not(.bg-default) h6.underline,
.tile-body[class*="bg-"]:not(.bg-default) .h1.underline,
.tile-body[class*="bg-"]:not(.bg-default) .h2.underline,
.tile-body[class*="bg-"]:not(.bg-default) .h3.underline,
.tile-body[class*="bg-"]:not(.bg-default) .h4.underline,
.tile-body[class*="bg-"]:not(.bg-default) .h5.underline,
.tile-body[class*="bg-"]:not(.bg-default) .h6.underline,
.tile-footer[class*="bg-"]:not(.bg-default) h1.underline,
.tile-footer[class*="bg-"]:not(.bg-default) h2.underline,
.tile-footer[class*="bg-"]:not(.bg-default) h3.underline,
.tile-footer[class*="bg-"]:not(.bg-default) h4.underline,
.tile-footer[class*="bg-"]:not(.bg-default) h5.underline,
.tile-footer[class*="bg-"]:not(.bg-default) h6.underline,
.tile-footer[class*="bg-"]:not(.bg-default) .h1.underline,
.tile-footer[class*="bg-"]:not(.bg-default) .h2.underline,
.tile-footer[class*="bg-"]:not(.bg-default) .h3.underline,
.tile-footer[class*="bg-"]:not(.bg-default) .h4.underline,
.tile-footer[class*="bg-"]:not(.bg-default) .h5.underline,
.tile-footer[class*="bg-"]:not(.bg-default) .h6.underline {
    border-color: rgba(255, 255, 255, 0.2);
}

.tile[class*="bg-"]:not(.bg-default) .owl-theme .owl-controls .owl-page span,
.tile-header[class*="bg-"]:not(.bg-default) .owl-theme .owl-controls .owl-page span,
.tile-widget[class*="bg-"]:not(.bg-default) .owl-theme .owl-controls .owl-page span,
.tile-body[class*="bg-"]:not(.bg-default) .owl-theme .owl-controls .owl-page span,
.tile-footer[class*="bg-"]:not(.bg-default) .owl-theme .owl-controls .owl-page span {
    background: white;
}

.tile[class*="bg-"]:not(.bg-default) .chosen-container .chosen-choices .search-field input,
.tile-header[class*="bg-"]:not(.bg-default) .chosen-container .chosen-choices .search-field input,
.tile-widget[class*="bg-"]:not(.bg-default) .chosen-container .chosen-choices .search-field input,
.tile-body[class*="bg-"]:not(.bg-default) .chosen-container .chosen-choices .search-field input,
.tile-footer[class*="bg-"]:not(.bg-default) .chosen-container .chosen-choices .search-field input {
    color: rgba(255, 255, 255, 0.5);
}

.tile[class*="bg-"]:not(.bg-default) .chosen-container.chosen-container-active .chosen-choices .search-field input,
.tile-header[class*="bg-"]:not(.bg-default) .chosen-container.chosen-container-active .chosen-choices .search-field input,
.tile-widget[class*="bg-"]:not(.bg-default) .chosen-container.chosen-container-active .chosen-choices .search-field input,
.tile-body[class*="bg-"]:not(.bg-default) .chosen-container.chosen-container-active .chosen-choices .search-field input,
.tile-footer[class*="bg-"]:not(.bg-default) .chosen-container.chosen-container-active .chosen-choices .search-field input {
    color: white !important;
}

.tile[class*="bg-"]:not(.bg-default) .form-control,
.tile-header[class*="bg-"]:not(.bg-default) .form-control,
.tile-widget[class*="bg-"]:not(.bg-default) .form-control,
.tile-body[class*="bg-"]:not(.bg-default) .form-control,
.tile-footer[class*="bg-"]:not(.bg-default) .form-control {
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    background-color: transparent;
}

.tile[class*="bg-"]:not(.bg-default) .form-control::-webkit-input-placeholder,
.tile-header[class*="bg-"]:not(.bg-default) .form-control::-webkit-input-placeholder,
.tile-widget[class*="bg-"]:not(.bg-default) .form-control::-webkit-input-placeholder,
.tile-body[class*="bg-"]:not(.bg-default) .form-control::-webkit-input-placeholder,
.tile-footer[class*="bg-"]:not(.bg-default) .form-control::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.tile[class*="bg-"]:not(.bg-default) .form-control::-moz-placeholder,
.tile-header[class*="bg-"]:not(.bg-default) .form-control::-moz-placeholder,
.tile-widget[class*="bg-"]:not(.bg-default) .form-control::-moz-placeholder,
.tile-body[class*="bg-"]:not(.bg-default) .form-control::-moz-placeholder,
.tile-footer[class*="bg-"]:not(.bg-default) .form-control::-moz-placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.tile[class*="bg-"]:not(.bg-default) .form-control:-moz-placeholder,
.tile-header[class*="bg-"]:not(.bg-default) .form-control:-moz-placeholder,
.tile-widget[class*="bg-"]:not(.bg-default) .form-control:-moz-placeholder,
.tile-body[class*="bg-"]:not(.bg-default) .form-control:-moz-placeholder,
.tile-footer[class*="bg-"]:not(.bg-default) .form-control:-moz-placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.tile[class*="bg-"]:not(.bg-default) .form-control:-ms-input-placeholder,
.tile-header[class*="bg-"]:not(.bg-default) .form-control:-ms-input-placeholder,
.tile-widget[class*="bg-"]:not(.bg-default) .form-control:-ms-input-placeholder,
.tile-body[class*="bg-"]:not(.bg-default) .form-control:-ms-input-placeholder,
.tile-footer[class*="bg-"]:not(.bg-default) .form-control:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.tile[class*="bg-"]:not(.bg-default) .form-control:hover:not(:disabled),
.tile-header[class*="bg-"]:not(.bg-default) .form-control:hover:not(:disabled),
.tile-widget[class*="bg-"]:not(.bg-default) .form-control:hover:not(:disabled),
.tile-body[class*="bg-"]:not(.bg-default) .form-control:hover:not(:disabled),
.tile-footer[class*="bg-"]:not(.bg-default) .form-control:hover:not(:disabled) {
    border-color: rgba(255, 255, 255, 0.5);
}

.tile[class*="bg-"]:not(.bg-default) .form-control:focus,
.tile[class*="bg-"]:not(.bg-default) .form-control:focus:hover,
.tile-header[class*="bg-"]:not(.bg-default) .form-control:focus,
.tile-header[class*="bg-"]:not(.bg-default) .form-control:focus:hover,
.tile-widget[class*="bg-"]:not(.bg-default) .form-control:focus,
.tile-widget[class*="bg-"]:not(.bg-default) .form-control:focus:hover,
.tile-body[class*="bg-"]:not(.bg-default) .form-control:focus,
.tile-body[class*="bg-"]:not(.bg-default) .form-control:focus:hover,
.tile-footer[class*="bg-"]:not(.bg-default) .form-control:focus,
.tile-footer[class*="bg-"]:not(.bg-default) .form-control:focus:hover {
    border-color: #22BEEF;
}

.tile[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap.open,
.tile-header[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap.open,
.tile-widget[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap.open,
.tile-body[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap.open,
.tile-footer[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap.open {
    border-color: #22BEEF;
}

.tile[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-webkit-input-placeholder,
.tile-header[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-webkit-input-placeholder,
.tile-widget[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-webkit-input-placeholder,
.tile-body[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-webkit-input-placeholder,
.tile-footer[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.tile[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-moz-placeholder,
.tile-header[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-moz-placeholder,
.tile-widget[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-moz-placeholder,
.tile-body[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-moz-placeholder,
.tile-footer[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search::-moz-placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.tile[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-moz-placeholder,
.tile-header[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-moz-placeholder,
.tile-widget[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-moz-placeholder,
.tile-body[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-moz-placeholder,
.tile-footer[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-moz-placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.tile[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-ms-input-placeholder,
.tile-header[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-ms-input-placeholder,
.tile-widget[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-ms-input-placeholder,
.tile-body[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-ms-input-placeholder,
.tile-footer[class*="bg-"]:not(.bg-default) .ui-select-multiple.ui-select-bootstrap input.ui-select-search:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.5);
}

@media only screen and (max-width: 628px) {
    .tile .tile-header .controls {
        display: none;
    }
}

.form-control {
    font-weight: bold;

    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    transition: all 0.2s linear;
    vertical-align: top;

    color: #020DA7;
    border: 1px solid #DBE0E2;
    border-radius: 2px;
    outline: 0;
    background-color: #FFFFFF;
    -webkit-box-shadow: none;
    box-shadow: none;

    filter: none !important;
}

.form-control:not(select) {
    -webkit-appearance: none !important;
}

.form-control::-webkit-input-placeholder {
    font-size: 12px;
    font-style: italic;
    line-height: 20px;

    -webkit-transition: color 0.2s linear;
    -moz-transition: color 0.2s linear;
    transition: color 0.2s linear;

    color: #BFC7CB;
}

.form-control::-moz-placeholder {
    font-size: 12px;
    font-style: italic;
    line-height: 20px;

    -webkit-transition: color 0.2s linear;
    -moz-transition: color 0.2s linear;
    transition: color 0.2s linear;

    color: #BFC7CB;
}

.form-control:-moz-placeholder {
    font-size: 12px;
    font-style: italic;
    line-height: 20px;

    -webkit-transition: color 0.2s linear;
    -moz-transition: color 0.2s linear;
    transition: color 0.2s linear;

    color: #BFC7CB;
}

.form-control:-ms-input-placeholder {
    font-size: 12px;
    font-style: italic;
    line-height: 20px;

    -webkit-transition: color 0.2s linear;
    -moz-transition: color 0.2s linear;
    transition: color 0.2s linear;

    color: #BFC7CB;
}

.form-control.input-lg::-webkit-input-placeholder {
    font-size: 18px;
}

.form-control.input-lg::-moz-placeholder {
    font-size: 18px;
}

.form-control.input-lg:-moz-placeholder {
    font-size: 18px;
}

.form-control.input-lg:-ms-input-placeholder {
    font-size: 18px;
}

.form-control.input-sm::-webkit-input-placeholder {
    line-height: 18px;
}

.form-control.input-sm::-moz-placeholder {
    line-height: 18px;
}

.form-control.input-sm:-moz-placeholder {
    line-height: 18px;
}

.form-control.input-sm:-ms-input-placeholder {
    line-height: 18px;
}

.form-control:hover:not(:disabled) {
    border-color: #B1BAC0;
}

.form-control:focus,
.form-control:focus:hover {
    border: 1px solid;
    border-color: #22BEEF;
    outline: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.form-control.underline-input {
    padding-left: 0;

    vertical-align: middle;

    border: 0;
    border-color: #DBE0E2;
    border-bottom: 1px solid;
    border-radius: 0;
    background: none;

    -webkit-appearance: none !important;
}

.form-control.underline-input:focus:hover {
    border: 0;
    border-bottom: 1px solid #22BEEF;
}

.form-control.underline-input:focus {
    border-color: #22BEEF;
}

.form-control.rounded {
    border-radius: 600px;
}

.form-control.input-unstyled {
    padding: 0;

    border: 0;
    background-color: transparent;
}

.form-control.input-unstyled::-webkit-input-placeholder {
    font-size: 16px;
}

.form-control.input-unstyled::-moz-placeholder {
    font-size: 16px;
}

.form-control.input-unstyled:-moz-placeholder {
    font-size: 16px;
}

.form-control.input-unstyled:-ms-input-placeholder {
    font-size: 16px;
}

.form-control.input-unstyled:focus,
.form-control.input-unstyled:focus:hover {
    border: 0;
}

.form-group.legend h1,
.form-group.legend h2,
.form-group.legend h3,
.form-group.legend h4,
.form-group.legend h5,
.form-group.legend h6 {
    margin-bottom: 3px;

    color: #428BCA;
}

.form-group.legend p {
    font-size: 12px;

    padding-bottom: 5px;

    color: #95A2A9;
    border-bottom: 1px dotted #CACACA;
}

label {
    font-weight: 400;
}

.input-group-addon {
    border-radius: 2px;
}

.input-group-addon input[type="radio"],
.input-group-addon input[type="checkbox"] {
    margin-top: 3px;
}

.input-group .btn {
    border-radius: 2px;
}

.form-validation .form-control.has-focus + .help-block {
    display: none;
}

.form-validation .form-control.has-visited:not(.has-focus) + .help-block {
    display: block;
}

.form-validation .form-control.ng-dirty.ng-invalid,
.form-validation .form-control.ng-dirty.ng-invalid[type="checkbox"] + i {
    border-color: #FF7B76;
}

.form-validation .form-control.ng-dirty.ng-invalid + .help-block,
.form-validation .form-control.ng-dirty.ng-invalid[type="checkbox"] + i + .help-block {
    color: #FF635C;
}

.form-validation .form-control.ng-dirty.ng-valid {
    border-color: #B2E600;
}

.form-validation .form-control.ng-dirty.ng-valid + .help-block {
    color: #AADC00;
}

.has-error .form-control,
.has-error .form-control:focus {
    border-color: #FF7B76;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.has-error .form-control.has-focus,
.has-error .form-control:focus.has-focus {
    border-color: #22BEEF;
}

.has-error .form-control.has-visited:not(.has-focus),
.has-error .form-control:focus.has-visited:not(.has-focus) {
    border-color: #FF7B76;
}

.has-error .control-label,
.has-error .help-block {
    color: #FF635C;
}

.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline {
    color: #616F77;
}

.has-error .checkbox-custom > i,
.has-error .checkbox-custom:hover > i {
    border-color: #FF7B76;
}

.has-success .form-control,
.has-success .form-control:focus {
    border-color: #B2E600;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.has-success .form-control.has-focus,
.has-success .form-control:focus.has-focus {
    border-color: #22BEEF;
}

.has-success .form-control.has-visited:not(.has-focus),
.has-success .form-control:focus.has-visited:not(.has-focus) {
    border-color: #B2E600;
}

.has-success .control-label,
.has-success .help-block {
    color: #AADC00;
}

.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline {
    color: #616F77;
}

.has-success .checkbox-custom > i {
    border-color: #B2E600;
}

.has-warning .form-control,
.has-warning .form-control:focus {
    border-color: #FFCD33;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.has-warning .form-control.has-focus,
.has-warning .form-control:focus.has-focus {
    border-color: #22BEEF;
}

.has-warning .form-control.has-visited:not(.has-focus),
.has-warning .form-control:focus.has-visited:not(.has-focus) {
    border-color: #FFCD33;
}

.has-warning .control-label,
.has-warning .help-block {
    color: #FFC71A;
}

.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline {
    color: #616F77;
}

.has-warning .checkbox-custom > i {
    border-color: #FFCD33;
}

.checkbox-custom,
.checkbox-custom-alt,
.radio-custom-alt {
    padding-left: 20px;

    cursor: pointer;
}

.checkbox-custom input,
.checkbox-custom-alt input,
.radio-custom-alt input {
    position: absolute;

    opacity: 0;
}

.checkbox-custom input:checked + i,
.checkbox-custom-alt input:checked + i,
.radio-custom-alt input:checked + i {
    border-color: #428BCA;
    background-color: #428BCA;
}

.checkbox-custom input:checked + i:before,
.checkbox-custom-alt input:checked + i:before,
.radio-custom-alt input:checked + i:before {
    top: 3px;
    left: 3px;

    width: 12px;
    height: 12px;

    background-color: #FFFFFF;
}

.checkbox-custom input:checked + span .active,
.checkbox-custom-alt input:checked + span .active,
.radio-custom-alt input:checked + span .active {
    display: inherit;
}

.checkbox-custom input[type="radio"] + i,
.checkbox-custom input[type="radio"] + i:before,
.checkbox-custom-alt input[type="radio"] + i,
.checkbox-custom-alt input[type="radio"] + i:before,
.radio-custom-alt input[type="radio"] + i,
.radio-custom-alt input[type="radio"] + i:before {
    border-radius: 50%;
}

.checkbox-custom input[disabled] + i,
.checkbox-custom-alt input[disabled] + i,
.radio-custom-alt input[disabled] + i {
    border-color: #E2E2E2;
}

.checkbox-custom input[disabled] + i:before,
.checkbox-custom-alt input[disabled] + i:before,
.raido-custom-alt input[disabled] + i:before {
    background-color: #E2E2E2;
}

.checkbox-custom > i,
.checkbox-custom-alt > i,
.radio-custom-alt > i {
    line-height: 1;

    position: relative;

    display: inline-block;

    width: 20px;
    height: 20px;
    margin-top: -2px;
    margin-right: 4px;
    margin-left: -20px;

    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    transition: all 0.2s;
    vertical-align: middle;

    border: 1px solid rgba(0, 0, 0, 0.2);
    background-color: #FFFFFF;
}

.checkbox-custom > i:before,
.checkbox-custom-alt > i:before,
.radio-custom-alt > i:before {
    position: absolute;
    z-index: 1;
    top: 50%;
    left: -100%;

    width: 0;
    height: 0;

    content: "";
    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    transition: all 0.2s;

    background-color: transparent;
}

.checkbox-custom > span,
.checkbox-custom-alt > span,
.radio-custom-alt > span {
    margin-left: -20px;
}

.checkbox-custom > span .active,
.checkbox-custom-alt > span .active,
.radio-custom-alt > span .active {
    display: none;
}

.checkbox-custom:hover > i,
.checkbox-custom-alt:hover > i,
.radio-custom-alt:hover > i {
    border-color: #22BEEF;
}

.checkbox-custom.checkbox-custom-sm input:checked + i:before,
.checkbox-custom-alt.checkbox-custom-sm input:checked + i:before {
    top: 2px;
    left: 2px;

    width: 10px;
    height: 10px;
}

.checkbox-custom.checkbox-custom-sm > i,
.checkbox-custom-alt.checkbox-custom-sm > i {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    margin-left: -18px;
}

.checkbox-custom.checkbox-custom-lg input:checked + i:before,
.checkbox-custom-alt.checkbox-custom-lg input:checked + i:before {
    top: 3px;
    left: 3px;

    width: 22px;
    height: 22px;
}

.checkbox-custom.checkbox-custom-lg > i,
.checkbox-custom-alt.checkbox-custom-lg > i {
    width: 30px;
    height: 30px;
}

.checkbox-custom-alt input:checked + i {
    color: #666666;
    border-color: #666666;
    background-color: transparent;
}

.checkbox-custom-alt input:checked + i:before {
    top: 2px;
    left: 2px;

    width: auto;
    height: auto;

    opacity: 1;
    background-color: transparent;
}

.checkbox-custom-alt input[type="radio"]:checked + i:before {
    left: 1px;
}

.checkbox-custom-alt input[disabled] + i {
    border-color: #E2E2E2;
    background-color: #F2F2F2;
}

.checkbox-custom-alt input[disabled] + i:before {
    color: #CCCCCC;
    background-color: transparent;
}

.checkbox-custom-alt > i {
    width: 18px;
    height: 18px;

    border: 2px solid #DFDFDF;
    background-color: transparent;
}

.checkbox-custom-alt > i:before {
    font-family: "FontAwesome";
    font-weight: 900;
    font-size: 11px;
    font-style: normal;
    line-height: 1;

    top: 0;
    left: 0;

    display: inline-block;

    content: "\f00c";
    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    transition: all 0.2s;

    opacity: 0;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.checkbox-custom-alt.checkbox-custom-sm > i:before {
    font-size: 9px;
}

.checkbox-custom-alt.checkbox-custom-sm input:checked + i:before {
    font-size: 9px;

    top: 1px;
}

.checkbox-custom-alt.checkbox-custom-lg input:checked + i:before {
    font-size: 18px;

    top: 4px;
    left: 4px;
}

.onoffswitch {
    position: relative;

    width: 40px;
    height: 25px;

    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.onoffswitch.green .onoffswitch-inner:before {
    background-color: #A2D200;
}

.onoffswitch.red .onoffswitch-inner:before {
    background-color: #FF4A43;
}

.onoffswitch.cyan .onoffswitch-inner:before {
    background-color: #22BEEF;
}

.onoffswitch.orange .onoffswitch-inner:before {
    background-color: #FFC100;
}

.onoffswitch.amethyst .onoffswitch-inner:before {
    background-color: #CD97EB;
}

.onoffswitch.greensea .onoffswitch-inner:before {
    background-color: #16A085;
}

.onoffswitch.dutch .onoffswitch-inner:before {
    background-color: #1693A5;
}

.onoffswitch.hotpink .onoffswitch-inner:before {
    background-color: #FF0066;
}

.onoffswitch.drank .onoffswitch-inner:before {
    background-color: #A40778;
}

.onoffswitch.blue .onoffswitch-inner:before {
    background-color: #418BCA;
}

.onoffswitch.slategray .onoffswitch-inner:before {
    background-color: #536781;
}

.onoffswitch.darkgray .onoffswitch-inner:before {
    background-color: #4D4D4D;
}

.onoffswitch.lightred .onoffswitch-inner:before {
    background-color: #E05D6F;
}

.onoffswitch.primary .onoffswitch-inner:before {
    background-color: #428BCA;
}

.onoffswitch.success .onoffswitch-inner:before {
    background-color: #5CB85C;
}

.onoffswitch.warning .onoffswitch-inner:before {
    background-color: #F0AD4E;
}

.onoffswitch.danger .onoffswitch-inner:before {
    background-color: #D9534F;
}

.onoffswitch.info .onoffswitch-inner:before {
    background-color: #5BC0DE;
}

.onoffswitch.labeled {
    width: 46px;
}

.onoffswitch.labeled .onoffswitch-inner:before {
    padding-left: 7px;

    content: "On";
}

.onoffswitch.labeled .onoffswitch-inner:after {
    padding-right: 7px;

    content: "Off";
}

.onoffswitch.labeled .onoffswitch-switch {
    right: 21px;
}

.onoffswitch.small {
    right: -5px;

    width: 20px;
    height: 15px;
    margin-top: 3px;
}

.onoffswitch.small .onoffswitch-switch {
    right: 6px;
}

.onoffswitch.small .onoffswitch-inner:before,
.onoffswitch.small .onoffswitch-inner:after {
    line-height: 15px;

    height: 15px;
}

.onoffswitch.small .onoffswitch-switch {
    width: 10px;
}

.onoffswitch.medium {
    right: -5px;

    width: 30px;
    height: 20px;
    margin-top: 3px;
}

.onoffswitch.medium .onoffswitch-switch {
    right: 11px;
}

.onoffswitch.medium .onoffswitch-inner:before,
.onoffswitch.medium .onoffswitch-inner:after {
    line-height: 20px;

    height: 20px;
}

.onoffswitch.medium .onoffswitch-switch {
    width: 15px;
}

.onoffswitch-checkbox {
    display: none;
}

.onoffswitch-label {
    display: block;
    overflow: hidden;

    cursor: pointer;

    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    border-radius: 50px;

    -ms-border-radius: 50px;
    -o-border-radius: 50px;
}

.onoffswitch-inner {
    display: block;

    width: 200%;
    margin-left: -100%;

    -webkit-transition: margin 0.2s ease-in 0s;
    -moz-transition: margin 0.2s ease-in 0s;
    transition: margin 0.2s ease-in 0s;
}

.onoffswitch-inner:before,
.onoffswitch-inner:after {
    font-size: 9px;
    line-height: 25px;

    float: left;

    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 50%;
    height: 25px;
    padding: 0;

    -webkit-transform: none;

    color: white;
    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    border-radius: 50px;

    -ms-border-radius: 50px;
    -o-border-radius: 50px;
}

.onoffswitch-inner:before {
    padding-left: 9px;

    content: "";

    color: #FFFFFF;
    background-color: rgba(0, 0, 0, 0.2);
}

.onoffswitch-inner:after {
    padding-right: 9px;

    content: "";
    text-align: right;

    color: #999999;
    background-color: rgba(0, 0, 0, 0.8);
}

.onoffswitch-switch {
    position: absolute;
    top: 0;
    right: 15px;
    bottom: 0;

    display: block;

    width: 21px;
    margin: 2px;

    -webkit-transition: all 0.2s ease-in 0s;
    -moz-transition: all 0.2s ease-in 0s;
    transition: all 0.2s ease-in 0s;

    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    border-radius: 50px;
    background: #FFFFFF;

    -ms-border-radius: 50px;
    -o-border-radius: 50px;
}

.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-inner {
    margin-left: 0;
}

.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-switch {
    right: 0;
}

fieldset[disabled] .checkbox-custom input + i {
    border-color: rgba(0, 0, 0, 0.1);
}

fieldset[disabled] .checkbox-custom input + i:before {
    background-color: rgba(0, 0, 0, 0.1);
}

.help-block {
    font-size: 12px;

    color: #95A2A9;
}

.slider {
    position: relative;

    display: inline-block;

    vertical-align: middle;
}

.slider.slider-horizontal {
    width: 210px;
    height: 20px;
}

.slider.slider-horizontal .slider-track {
    top: 50%;
    left: 0;

    width: 100%;
    margin-top: -5px;
}

.slider.slider-horizontal .slider-selection {
    top: 0;
    bottom: 0;

    height: 100%;
}

.slider.slider-horizontal .slider-handle {
    margin-top: -8px;
    margin-left: -10px;
}

.slider.slider-horizontal .slider-handle.triangle {
    width: 0;
    height: 0;
    margin-top: 0;

    border-width: 0 10px 10px 10px;
    border-color: transparent;
    border-bottom-color: #E05D6F;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.slider.slider-vertical {
    width: 20px;
    height: 210px;
}

.slider.slider-vertical .slider-track {
    top: 0;
    left: 50%;

    width: 6px;
    height: 100%;
    margin-left: -2px;
}

.slider.slider-vertical .slider-selection {
    top: 0;
    bottom: 0;
    left: 0;

    width: 100%;
}

.slider.slider-vertical .slider-handle {
    margin-top: -10px;
    margin-left: -8px;
}

.slider.slider-vertical .slider-handle.triangle {
    width: 1px;
    height: 1px;
    margin-left: 0;

    border-width: 10px 0 10px 10px;
    border-left-color: #E05D6F;
}

.slider.disabled .slider-track {
    cursor: not-allowed;
}

.slider input {
    display: none;
}

.slider .tooltip {
    display: none;
}

.slider .tooltip.top {
    margin-top: -36px;
}

.slider:hover .tooltip {
    display: block;
}

.slider .tooltip-inner {
    white-space: nowrap;
}

.slider .hide {
    display: none;
}

.slider .slider-track {
    position: absolute;

    height: 6px;

    cursor: pointer;

    border: 1px solid #DBE0E2;
    border-radius: 3px;
    background-color: #FFFFFF;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.slider .slider-selection {
    position: absolute;

    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;

    border-radius: 3px;
    background-color: #418BCA;
    background-image: none;
}

.slider .slider-handle {
    position: absolute;

    width: 20px;
    height: 20px;

    border: 1px solid;
    border-color: rgba(0, 0, 0, 0.1);
    outline: none;
    background-color: #FFFFFF;
    background-image: none;
    -webkit-box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.05);
}

.slider .slider-handle.round {
    border-radius: 20px;
}

.slider .slider-handle.triangle {
    background: transparent none;
}

.ui-select-bootstrap > .ui-select-match {
    border-color: #DBE0E2 !important;
    border-radius: 2px;
}

.ui-select-bootstrap > .ui-select-match > span:first-of-type {
    font-size: 12px;
    font-style: italic;

    color: #BFC7CB;
}

.ui-select-bootstrap > .ui-select-match > span:nth-of-type(2) > a {
    font-size: 10px;

    position: absolute;
    top: 7px;
    right: 35px;

    padding: 2px;

    color: #BFC7CB;
}

.ui-select-bootstrap > .ui-select-match > span:nth-of-type(2) > a:hover {
    color: #428BCA;
}

.ui-select-bootstrap .ui-select-match-item.btn-primary {
    color: white;
}

.ui-select-choices .ui-select-choices-row {
    padding: 0;
}

.ui-select-choices .ui-select-choices-row > a {
    font-size: 12px;

    color: #616F77;
}

.ui-select-choices .ui-select-choices-row.active > a {
    background: #798992;
}

.ui-select-multiple.ui-select-bootstrap.open {
    border-color: #22BEEF;
}

.ui-select-multiple.ui-select-bootstrap .ui-select-match .close {
    line-height: 0.8;
}

.ui-select-multiple.ui-select-bootstrap input.ui-select-search::-webkit-input-placeholder {
    font-size: 12px;
    font-style: italic;
    line-height: 20px;

    padding: 0 7px;

    color: #BFC7CB;
}

.ui-select-multiple.ui-select-bootstrap input.ui-select-search::-moz-placeholder {
    font-size: 12px;
    font-style: italic;
    line-height: 20px;

    padding: 0 7px;

    color: #BFC7CB;
}

.ui-select-multiple.ui-select-bootstrap input.ui-select-search:-moz-placeholder {
    font-size: 12px;
    font-style: italic;
    line-height: 20px;

    padding: 0 7px;

    color: #BFC7CB;
}

.ui-select-multiple.ui-select-bootstrap input.ui-select-search:-ms-input-placeholder {
    font-size: 12px;
    font-style: italic;
    line-height: 20px;

    padding: 0 7px;

    color: #BFC7CB;
}

.ui-select-multiple.ui-select-bootstrap.underline-input input.ui-select-search::-webkit-input-placeholder {
    padding: 0;
}

.ui-select-multiple.ui-select-bootstrap.underline-input input.ui-select-search::-moz-placeholder {
    padding: 0;
}

.ui-select-multiple.ui-select-bootstrap.underline-input input.ui-select-search:-moz-placeholder {
    padding: 0;
}

.ui-select-multiple.ui-select-bootstrap.underline-input input.ui-select-search:-ms-input-placeholder {
    padding: 0;
}

.ta-toolbar {
    margin-bottom: 10px;
}

.ta-root.focussed .ta-scroll-window.form-control {
    border-color: #22BEEF;
    -webkit-box-shadow: none;
    box-shadow: none;
}

input.parsley-success,
select.parsley-success,
textarea.parsley-success {
    border-color: #B2E600 !important;
}

input.parsley-error,
select.parsley-error,
textarea.parsley-error,
.checkbox-custom.parsley-error > i {
    border-color: #FF7B76 !important;
}

.parsley-errors-list {
    font-size: 12px;
    line-height: 0.9em;

    margin: 0;
    padding: 0;

    list-style-type: none;

    -webkit-transition: all 0.3s ease-in;
    -moz-transition: all 0.3s ease-in;
    -o-transition: all 0.3s ease-in;
    transition: all 0.3s ease-in;

    opacity: 0;
    color: #FF7B76 !important;
}

.parsley-errors-list.filled {
    padding: 5px 0 0;

    opacity: 1;
}

table tbody.files > tr > td {
    vertical-align: middle;
}

.has-warning .twitter-typeahead .tt-input,
.has-warning .twitter-typeahead .tt-hint {
    border-color: #8A6D3B;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.has-warning .twitter-typeahead .tt-input:focus,
.has-warning .twitter-typeahead .tt-hint:focus {
    border-color: #66512C;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #C0A16B;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #C0A16B;
}

.has-error .twitter-typeahead .tt-input,
.has-error .twitter-typeahead .tt-hint {
    border-color: #A94442;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.has-error .twitter-typeahead .tt-input:focus,
.has-error .twitter-typeahead .tt-hint:focus {
    border-color: #843534;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #CE8483;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #CE8483;
}

.has-success .twitter-typeahead .tt-input,
.has-success .twitter-typeahead .tt-hint {
    border-color: #3C763D;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.has-success .twitter-typeahead .tt-input:focus,
.has-success .twitter-typeahead .tt-hint:focus {
    border-color: #2B542C;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67B168;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67B168;
}

.input-group .twitter-typeahead:first-child .tt-input,
.input-group .twitter-typeahead:first-child .tt-hint {
    width: 100%;

    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.input-group .twitter-typeahead:last-child .tt-input,
.input-group .twitter-typeahead:last-child .tt-hint {
    width: 100%;

    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.input-group.input-group-sm .twitter-typeahead .tt-input,
.input-group.input-group-sm .twitter-typeahead .tt-hint {
    font-size: 12px;
    line-height: 1.5;

    height: 30px;
    padding: 5px 10px;

    border-radius: 3px;
}

select.input-group.input-group-sm .twitter-typeahead .tt-input,
select.input-group.input-group-sm .twitter-typeahead .tt-hint {
    line-height: 30px;

    height: 30px;
}

textarea.input-group.input-group-sm .twitter-typeahead .tt-input,
textarea.input-group.input-group-sm .twitter-typeahead .tt-hint,
select[multiple].input-group.input-group-sm .twitter-typeahead .tt-input,
select[multiple].input-group.input-group-sm .twitter-typeahead .tt-hint {
    height: auto;
}

.input-group.input-group-sm .twitter-typeahead:not(:first-child):not(:last-child) .tt-input,
.input-group.input-group-sm .twitter-typeahead:not(:first-child):not(:last-child) .tt-hint {
    border-radius: 0;
}

.input-group.input-group-sm .twitter-typeahead:first-child .tt-input,
.input-group.input-group-sm .twitter-typeahead:first-child .tt-hint {
    border-top-left-radius: 3px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 3px;
}

.input-group.input-group-sm .twitter-typeahead:last-child .tt-input,
.input-group.input-group-sm .twitter-typeahead:last-child .tt-hint {
    border-top-left-radius: 0;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 0;
}

.input-group.input-group-lg .twitter-typeahead .tt-input,
.input-group.input-group-lg .twitter-typeahead .tt-hint {
    font-size: 18px;
    line-height: 1.33;

    height: 46px;
    padding: 10px 16px;

    border-radius: 6px;
}

select.input-group.input-group-lg .twitter-typeahead .tt-input,
select.input-group.input-group-lg .twitter-typeahead .tt-hint {
    line-height: 46px;

    height: 46px;
}

textarea.input-group.input-group-lg .twitter-typeahead .tt-input,
textarea.input-group.input-group-lg .twitter-typeahead .tt-hint,
select[multiple].input-group.input-group-lg .twitter-typeahead .tt-input,
select[multiple].input-group.input-group-lg .twitter-typeahead .tt-hint {
    height: auto;
}

.input-group.input-group-lg .twitter-typeahead:not(:first-child):not(:last-child) .tt-input,
.input-group.input-group-lg .twitter-typeahead:not(:first-child):not(:last-child) .tt-hint {
    border-radius: 0;
}

.input-group.input-group-lg .twitter-typeahead:first-child .tt-input,
.input-group.input-group-lg .twitter-typeahead:first-child .tt-hint {
    border-top-left-radius: 6px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 6px;
}

.input-group.input-group-lg .twitter-typeahead:last-child .tt-input,
.input-group.input-group-lg .twitter-typeahead:last-child .tt-hint {
    border-top-left-radius: 0;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 0;
}

.twitter-typeahead {
    width: 100%;
}

.input-group .twitter-typeahead {
    display: table-cell !important;
}

.twitter-typeahead .tt-hint {
    color: #999999;
}

.twitter-typeahead .tt-input {
    z-index: 2;
}

.twitter-typeahead .tt-input[disabled],
.twitter-typeahead .tt-input[readonly],
fieldset[disabled] .twitter-typeahead .tt-input {
    cursor: not-allowed;

    background-color: #EEEEEE !important;
}

.tt-dropdown-menu,
.tt-menu {
    font-size: 14px;

    position: absolute;
    z-index: 1000;
    top: 100%;
    left: 0;

    width: 100%;
    min-width: 160px;
    margin: 2px 0 0;
    padding: 5px 0;

    list-style: none;

    border: 1px solid #CCCCCC;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    background-color: #FFFFFF;
    background-clip: padding-box;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);

    *border-right-width: 2px;
    *border-bottom-width: 2px;
}

.tt-dropdown-menu .tt-suggestion,
.tt-menu .tt-suggestion {
    font-weight: normal;
    line-height: 1.42857143;

    display: block;
    clear: both;

    padding: 3px 20px;

    color: #333333;
}

.tt-dropdown-menu .tt-suggestion.tt-cursor,
.tt-menu .tt-suggestion.tt-cursor,
.tt-dropdown-menu .tt-suggestion:hover,
.tt-menu .tt-suggestion:hover {
    cursor: pointer;
    text-decoration: none;

    color: #262626;
    outline: 0;
    background-color: #F5F5F5;
}

.tt-dropdown-menu .tt-suggestion.tt-cursor a,
.tt-menu .tt-suggestion.tt-cursor a,
.tt-dropdown-menu .tt-suggestion:hover a,
.tt-menu .tt-suggestion:hover a {
    color: #262626;
}

.tt-dropdown-menu .tt-suggestion p,
.tt-menu .tt-suggestion p {
    margin: 0;
}

.badge,
.label {
    font-size: 10px;
    font-weight: 400;
}

.badge {
    padding: 3px 5px;
}

.badge.badge-success {
    background-color: #5CB85C;
}

.badge.badge-danger {
    background-color: #D9534F;
}

.badge.badge-warning {
    background-color: #F0AD4E;
}

.badge.badge-info {
    background-color: #5BC0DE;
}

.badge.badge-primary {
    background-color: #428BCA;
}

.badge.badge-default {
    background-color: #616F77;
}

.badge.badge-outline {
    display: inline-block;

    width: 10px;
    height: 10px;
    padding: 0;

    border: 1px solid;
    background: transparent;
}

.panel-group .panel .panel-heading {
    padding: 0;
}

.panel {
    border-radius: 2px;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.panel .panel-heading {
    border-radius: 0;
}

.panel .panel-heading .panel-title > a {
    font-size: 14px;

    display: block;

    padding: 10px 15px;

    cursor: pointer;
}

.panel .panel-heading .panel-title > a span {
    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    transition: all 0.2s;
}

.panel .panel-heading .panel-title > a.collapsed span {
    color: #798992;
}

.panel .panel-heading .panel-title > a.collapsed span i.fa-minus:before {
    content: "\f067";
}

.panel .panel-heading .panel-title > a:hover {
    text-decoration: none;
}

.panel .panel-heading .panel-title > a:hover span {
    padding-left: 5px;
}

.panel .panel-heading .panel-title > a:hover span.text-muted {
    padding-left: 0;
}

.panel .panel-heading .panel-title > a:focus {
    text-decoration: none;
}

.panel.panel-cyan {
    border-color: #22BEEF;
}

.panel.panel-cyan > .panel-heading {
    color: white;
    border-color: #22BEEF;
    background-color: #22BEEF;
}

.panel.panel-amethyst {
    border-color: #CD97EB;
}

.panel.panel-amethyst > .panel-heading {
    color: white;
    border-color: #CD97EB;
    background-color: #CD97EB;
}

.panel.panel-green {
    border-color: #A2D200;
}

.panel.panel-green > .panel-heading {
    color: white;
    border-color: #A2D200;
    background-color: #A2D200;
}

.panel.panel-orange {
    border-color: #FFC100;
}

.panel.panel-orange > .panel-heading {
    color: white;
    border-color: #FFC100;
    background-color: #FFC100;
}

.panel.panel-red {
    border-color: #FF4A43;
}

.panel.panel-red > .panel-heading {
    color: white;
    border-color: #FF4A43;
    background-color: #FF4A43;
}

.panel.panel-greensea {
    border-color: #16A085;
}

.panel.panel-greensea > .panel-heading {
    color: white;
    border-color: #16A085;
    background-color: #16A085;
}

.panel.panel-dutch {
    border-color: #1693A5;
}

.panel.panel-dutch > .panel-heading {
    color: white;
    border-color: #1693A5;
    background-color: #1693A5;
}

.panel.panel-hotpink {
    border-color: #FF0066;
}

.panel.panel-hotpink > .panel-heading {
    color: white;
    border-color: #FF0066;
    background-color: #FF0066;
}

.panel.panel-drank {
    border-color: #A40778;
}

.panel.panel-drank > .panel-heading {
    color: white;
    border-color: #A40778;
    background-color: #A40778;
}

.panel.panel-blue {
    border-color: #418BCA;
}

.panel.panel-blue > .panel-heading {
    color: white;
    border-color: #418BCA;
    background-color: #418BCA;
}

.panel.panel-warning {
    border-color: #F0AD4E;
}

.panel.panel-warning > .panel-heading {
    color: white;
    border-color: #F0AD4E;
    background-color: #F0AD4E;
}

.panel.panel-danger {
    border-color: #D9534F;
}

.panel.panel-danger > .panel-heading {
    color: white;
    border-color: #D9534F;
    background-color: #D9534F;
}

.panel.panel-lightred {
    border-color: #E05D6F;
}

.panel.panel-lightred > .panel-heading {
    color: white;
    border-color: #E05D6F;
    background-color: #E05D6F;
}

.panel.panel-slategray {
    border-color: #3F4E62;
}

.panel.panel-slategray > .panel-heading {
    color: white;
    border-color: #3F4E62;
    background-color: #3F4E62;
}

.panel.panel-darkgray {
    border-color: #333333;
}

.panel.panel-darkgray > .panel-heading {
    color: white;
    border-color: #333333;
    background-color: #333333;
}

.panel.panel-filled.panel-default .panel-body {
    background-color: white;
}

.panel.panel-filled.panel-primary .panel-body {
    color: white;
    background-color: #6AA3D5;
}

.panel.panel-filled.panel-success .panel-body {
    color: #357935;
    background-color: #EAF6EA;
}

.panel.panel-filled.panel-warning .panel-body {
    color: #C77C11;
    background-color: #FEF9F3;
}

.panel.panel-filled.panel-danger .panel-body {
    color: #A02622;
    background-color: #FDF7F7;
}

.panel.panel-filled.panel-info .panel-body {
    color: #2390B0;
    background-color: #F0F9FC;
}

.panel.panel-filled.panel-cyan .panel-body {
    color: white;
    background-color: #3AC5F1;
}

.panel.panel-filled.panel-amethyst .panel-body {
    color: white;
    background-color: #D1A0ED;
}

.panel.panel-filled.panel-green .panel-body {
    color: white;
    background-color: #AADC00;
}

.panel.panel-filled.panel-orange .panel-body {
    color: white;
    background-color: #FFC71A;
}

.panel.panel-filled.panel-red .panel-body {
    color: white;
    background-color: #FF635C;
}

.panel.panel-filled.panel-greensea .panel-body {
    color: white;
    background-color: #19B698;
}

.panel.panel-filled.panel-dutch .panel-body {
    color: white;
    background-color: #19A7BC;
}

.panel.panel-filled.panel-hotpink .panel-body {
    color: white;
    background-color: #FF1A75;
}

.panel.panel-filled.panel-drank .panel-body {
    color: white;
    background-color: #BC088A;
}

.panel.panel-filled.panel-blue .panel-body {
    color: white;
    background-color: #5597D0;
}

.panel.panel-filled.panel-lightred .panel-body {
    color: white;
    background-color: #E47282;
}

.panel.panel-filled.panel-slategray .panel-body {
    color: white;
    background-color: #495A72;
}

.panel.panel-filled.panel-darkgray .panel-body {
    color: white;
    background-color: #404040;
}

.panel.panel-transparent {
    border-right: 0;
    border-left: 0;
}

.panel.panel-transparent:last-of-type {
    border-bottom: 0;
}

.panel.panel-transparent .panel-heading {
    background: none;
}

.panel.panel-transparent .panel-heading .panel-title > a {
    padding: 15px;
}

.panel.panel-transparent .panel-body {
    padding: 0;

    background-color: #F7F7F7;
}

.panel-group .panel {
    border-radius: 0;
}

.panel-group .panel.panel-transparent + .panel.panel-transparent {
    margin-top: -1px;
}

.well {
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.breadcrumb {
    border-radius: 0;
}

.breadcrumb > li + li:before {
    font-family: "FontAwesome";
    font-weight: 900;
    font-style: normal;
    line-height: 1;

    display: inline-block;

    content: "\f105";

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.datepicker .btn-default {
    width: 100%;

    border-width: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.datepicker .btn-default .glyphicon {
    top: 2px;
}

.datepicker .btn[disabled] {
    opacity: 0.3;
}

.datepicker .btn-info {
    color: white !important;
}

.datepicker .btn-info .text-info {
    color: white;
}

.datepicker .btn-info.active {
    background-color: #5BC0DE !important;
}

.datepicker td > em {
    color: #95A2A9;
}

.datepicker > div > table > thead,
.timepicker > div > table > thead {
    color: #51445F;
}

.bootstrap-datetimepicker-widget table td span {
    color: #51445F;
}

.bootstrap-datetimepicker-widget .datepicker-decades table td span {
    line-height: 54px;
    line-height: 1.8em !important;

    float: left;

    width: 54px;
    height: 54px;
    margin: 2px 1.5px;

    cursor: pointer;

    border-radius: 4px;
}

.bootstrap-datetimepicker-widget .datepicker-decades table tbody td {
    padding: 5px 14px;
}

.bootstrap-datetimepicker-widget .datepicker-decades table td span.active {
    color: #FFFFFF;
}

.pagination {
    border-radius: 0;
}

.pagination > li > a,
.pagination > li > span {
    border-color: #E4E7E9;
}

.pagination > li > a:hover,
.pagination > li > a:focus,
.pagination > li > span:hover,
.pagination > li > span:focus {
    background-color: #F2F3F4;
}

.pagination > li:first-child > a,
.pagination > li:last-child > a {
    border-radius: 0;
}

.pager > li > a,
.pager > li > span {
    border-color: #E4E7E9;
}

.pager > li > a:hover,
.pager > li > a:focus,
.pager > li > span:hover,
.pager > li > span:focus {
    background-color: #F2F3F4;
}

.popover {
    padding: 0;

    border-color: #CDD3D7;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.popover .popover-title {
    font-weight: 700;

    border-radius: 0;
    background-color: #F8F8F9;
}

.popover .popover-content {
    font-size: 12px;

    color: #95A2A9;
}

check-toggler,
.check-toggler {
    padding: 2px;

    cursor: pointer;
}

check-toggler:before,
.check-toggler:before {
    font-family: "FontAwesome";
    font-weight: 900;
    font-style: normal;
    line-height: 1;

    display: inline-block;

    content: "\f00d";

    color: #D9534F;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

check-toggler.checked:before,
.check-toggler.checked:before {
    content: "\f00c";

    color: #5CB85C;
}

.dropdown > a:hover,
.dropdown > a:focus {
    text-decoration: none;
}

.dropdown.settings .dropdown-menu {
    padding: 0;
}

.dropdown.settings .dropdown-menu:after {
    border-bottom-color: #F2F2F2;
}

.dropdown.settings .color-schemes {
    margin-bottom: 10px;
    padding: 5px 10px;

    border-bottom: 1px solid #E2E2E2;
    background-color: #F2F2F2;
}

.dropdown.settings .color-schemes li:not(.title) {
    padding: 3px;
}

.dropdown.settings .color-schemes li:not(.title) > a {
    display: inline-block;

    width: 10px;
    height: 10px;

    -webkit-transition: all 0.25s ease;
    -moz-transition: all 0.25s ease;
    transition: all 0.25s ease;

    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;

    -ms-border-radius: 20px;
    -o-border-radius: 20px;
}

.dropdown.settings .color-schemes li:not(.title) > a:hover {
    -webkit-transform: scale(1.3, 1.3);
    -moz-transform: scale(1.3, 1.3);
    -ms-transform: scale(1.3, 1.3);
    -o-transform: scale(1.3, 1.3);
    transform: scale(1.3, 1.3);

    -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.dropdown.settings .form-group {
    margin-bottom: 0;
}

.dropdown.nav-profile .dropdown-menu {
    min-width: 180px;
}

#header .dropdown > .dropdown-menu {
    margin-top: -2px;
}

#header .dropdown.open > a {
    color: white;
}

.dropdown-menu {
    border-radius: 0;
    -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    overflow: scroll;
}

.dropdown-menu.with-arrow:before {
    position: absolute;
    top: -10px;
    left: 12px;

    width: 0;
    height: 0;

    content: "";

    border-right: 9px solid transparent;
    border-bottom: 9px solid rgba(0, 0, 0, 0.15);
    border-left: 9px solid transparent;
}

.dropdown-menu.with-arrow:after {
    position: absolute;
    top: -8px;
    left: 13px;

    width: 0;
    height: 0;

    content: "";

    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
    border-left: 8px solid transparent;
}

.dropdown-menu.pull-right.with-arrow:before {
    right: 11px;
    left: auto;
}

.dropdown-menu.pull-right.with-arrow:after {
    right: 12px;
    left: auto;
}

.dropdown-menu > li > a {
    padding: 5px 15px;

    color: #4A555B;
}

.dropdown-menu > li > a > i {
    margin-right: 5px;
}

.dropdown-menu > li > a > .label,
.dropdown-menu > li > a > .badge {
    margin-top: 2px;
}

.dropdown-menu > li > div {
    padding: 5px 10px;
}

.dropdown-menu > li > ul {
    margin-left: 0;
    padding: 0 10px;
}

.dropdown-menu > li > ul > li.title {
    font-size: 12px;

    display: block;

    padding: 5px 0 0;

    color: #616F77;
    border-bottom: 1px solid rgba(0, 0, 0, 0.3);
}

.dropdown-menu.panel {
    min-width: 280px;
    margin-bottom: 0;
    padding: 0;

    border-color: rgba(0, 0, 0, 0.15);
}

.dropdown-menu.panel .panel-heading {
    font-weight: 300;

    color: #95A2A9;
    border: 0;
    border-radius: 0;
    background-color: #333333;
}

.dropdown-menu.panel .panel-heading strong {
    color: white;
}

.dropdown-menu.panel .panel-footer {
    padding: 0;

    background-color: white;
}

.dropdown-menu.panel .panel-footer > a {
    font-size: 12px;

    display: block;

    padding: 10px 15px;

    color: #616F77;
}

.dropdown-menu.panel .panel-footer > a > i {
    margin-top: 2px;
}

.dropdown-menu.panel .panel-footer > a:hover {
    text-decoration: none;

    color: #428BCA;
}

.dropdown-menu.panel.with-arrow:before {
    border-color: transparent;
}

.dropdown-menu.panel.with-arrow:after {
    width: 0;
    height: 0;

    border-right: 9px solid transparent;
    border-bottom: 9px solid #333333;
    border-left: 9px solid transparent;
}

.dropdown-menu.panel .list-group > li {
    padding: 0;

    border-color: #EAEAEA;
}

.dropdown-menu.panel .list-group > li .media .media-object {
    margin: 10px 10px 10px 15px;
    padding: 0;
}

.dropdown-menu.panel .list-group > li .media .media-body {
    padding: 10px 15px 10px 0;
}

.dropdown-menu.panel .list-group > li:hover {
    background-color: #F8F8F8;
}

.dropdown-menu.panel .list-group > li > a:hover {
    text-decoration: none;
}

.jqstooltip {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}

.flotTip {
    font-size: 12px;

    z-index: 100;

    padding: 4px 10px;

    color: #FFFFFF;
    border: 0;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.8);
}

.legendColorBox > div {
    margin: 3px 5px;

    border: none !important;
}

.legendColorBox > div > div {
    border-radius: 10px;
}

.easypiechart {
    position: relative;

    display: inline-block;

    margin: 5px auto;

    text-align: center;
}

.easypiechart .pie-percent {
    font-size: 40px;
    font-weight: 300;
    line-height: 100%;

    display: inline-block;

    color: #95A2A9;
}

.easypiechart .pie-percent:after {
    font-size: 0.6em;

    margin-left: 0.1em;

    content: "%";
}

.easypiechart canvas {
    position: absolute;
    top: 0;
    left: 0;
}

/* General button style (reset) */
.btn-custom-width {
    width: 15em;
}
.btn-ef {
    position: relative;
    z-index: 1;

    display: inline-block;

    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;

    border: none;
    outline: none;
    /* Success and error */
}

.btn-ef:focus,
.btn-ef:active {
    outline: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.btn-ef:after {
    position: absolute;
    z-index: -1;

    content: "";
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.btn-ef.btn-ef-1 {
    color: #616F77;
    border: 2px solid #616F77;
    background: none;
}

.btn-ef.btn-ef-1.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1a.active {
    color: white;
    background-color: #616F77;
}

.btn-ef.btn-ef-1.btn-ef-1b:after {
    top: 0;
    left: 0;

    width: 100%;
    height: 0;

    background: #616F77;
}

.btn-ef.btn-ef-1.btn-ef-1b:hover,
.btn-ef.btn-ef-1.btn-ef-1b:active,
.btn-ef.btn-ef-1.btn-ef-1b.active {
    color: white;
}

.btn-ef.btn-ef-1.btn-ef-1b:hover:after,
.btn-ef.btn-ef-1.btn-ef-1b:active:after,
.btn-ef.btn-ef-1.btn-ef-1b.active:after {
    height: 100%;
}

.btn-ef.btn-ef-1.btn-ef-1c:after {
    top: 0;
    left: 0;

    width: 0;
    height: 100%;

    background: #616F77;
}

.btn-ef.btn-ef-1.btn-ef-1c:hover,
.btn-ef.btn-ef-1.btn-ef-1c:active,
.btn-ef.btn-ef-1.btn-ef-1c.active {
    color: white;
}

.btn-ef.btn-ef-1.btn-ef-1c:hover:after,
.btn-ef.btn-ef-1.btn-ef-1c:active:after,
.btn-ef.btn-ef-1.btn-ef-1c.active:after {
    width: 100%;
}

.btn-ef.btn-ef-1.btn-ef-1d {
    overflow: hidden;
}

.btn-ef.btn-ef-1.btn-ef-1d:after {
    top: 50%;
    left: 50%;

    width: 0;
    height: 103%;

    -webkit-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    -o-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);

    opacity: 0;
    background: #616F77;
}

.btn-ef.btn-ef-1.btn-ef-1d:hover,
.btn-ef.btn-ef-1.btn-ef-1d:active,
.btn-ef.btn-ef-1.btn-ef-1d.active {
    color: white;
}

.btn-ef.btn-ef-1.btn-ef-1d:hover:after,
.btn-ef.btn-ef-1.btn-ef-1d:active:after,
.btn-ef.btn-ef-1.btn-ef-1d.active:after {
    width: 80%;

    opacity: 1;
}

.btn-ef.btn-ef-1.btn-ef-1d:active:after,
.btn-ef.btn-ef-1.btn-ef-1d.active:after {
    width: 101%;

    opacity: 1;
}

.btn-ef.btn-ef-1.btn-ef-1e {
    overflow: hidden;
}

.btn-ef.btn-ef-1.btn-ef-1e:after {
    top: 50%;
    left: 50%;

    width: 110%;
    height: 0;

    -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    -moz-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    -ms-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    -o-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    transform: translateX(-50%) translateY(-50%) rotate(45deg);

    opacity: 0;
    background: #616F77;
}

.btn-ef.btn-ef-1.btn-ef-1e:hover,
.btn-ef.btn-ef-1.btn-ef-1e:active,
.btn-ef.btn-ef-1.btn-ef-1e.active {
    color: white;
}

.btn-ef.btn-ef-1.btn-ef-1e:hover:after,
.btn-ef.btn-ef-1.btn-ef-1e:active:after,
.btn-ef.btn-ef-1.btn-ef-1e.active:after {
    height: 160%;

    opacity: 1;
}

.btn-ef.btn-ef-1.btn-ef-1e:active:after,
.btn-ef.btn-ef-1.btn-ef-1e.active:after {
    height: 400%;

    opacity: 1;
}

.btn-ef.btn-ef-1.btn-ef-1f {
    overflow: hidden;
}

.btn-ef.btn-ef-1.btn-ef-1f:after {
    top: 50%;
    left: 50%;

    width: 101%;
    height: 0;

    -webkit-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    -o-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);

    opacity: 0;
    background: #616F77;
}

.btn-ef.btn-ef-1.btn-ef-1f:hover,
.btn-ef.btn-ef-1.btn-ef-1f:active,
.btn-ef.btn-ef-1.btn-ef-1f.active {
    color: white;
}

.btn-ef.btn-ef-1.btn-ef-1f:hover:after,
.btn-ef.btn-ef-1.btn-ef-1f:active:after,
.btn-ef.btn-ef-1.btn-ef-1f.active:after {
    height: 75%;

    opacity: 1;
}

.btn-ef.btn-ef-1.btn-ef-1f:active:after,
.btn-ef.btn-ef-1.btn-ef-1f.active:after {
    height: 130%;

    opacity: 1;
}

.btn-ef.btn-ef-1.btn-ef-1-primary {
    color: #428BCA;
    border-color: #428BCA;
}

.btn-ef.btn-ef-1.btn-ef-1-primary.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-primary.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-primary.btn-ef-1a.active {
    background-color: #428BCA;
}

.btn-ef.btn-ef-1.btn-ef-1-primary:after {
    background-color: #428BCA;
}

.btn-ef.btn-ef-1.btn-ef-1-success {
    color: #5CB85C;
    border-color: #5CB85C;
}

.btn-ef.btn-ef-1.btn-ef-1-success.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-success.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-success.btn-ef-1a.active {
    background-color: #5CB85C;
}

.btn-ef.btn-ef-1.btn-ef-1-success:after {
    background-color: #5CB85C;
}

.btn-ef.btn-ef-1.btn-ef-1-warning {
    color: #F0AD4E;
    border-color: #F0AD4E;
}

.btn-ef.btn-ef-1.btn-ef-1-warning.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-warning.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-warning.btn-ef-1a.active {
    background-color: #F0AD4E;
}

.btn-ef.btn-ef-1.btn-ef-1-warning:after {
    background-color: #F0AD4E;
}

.btn-ef.btn-ef-1.btn-ef-1-danger {
    color: #D9534F;
    border-color: #D9534F;
}

.btn-ef.btn-ef-1.btn-ef-1-danger.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-danger.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-danger.btn-ef-1a.active {
    background-color: #D9534F;
}

.btn-ef.btn-ef-1.btn-ef-1-danger:after {
    background-color: #D9534F;
}

.btn-ef.btn-ef-1.btn-ef-1-info {
    color: #5BC0DE;
    border-color: #5BC0DE;
}

.btn-ef.btn-ef-1.btn-ef-1-info.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-info.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-info.btn-ef-1a.active {
    background-color: #5BC0DE;
}

.btn-ef.btn-ef-1.btn-ef-1-info:after {
    background-color: #5BC0DE;
}

.btn-ef.btn-ef-1.btn-ef-1-cyan {
    color: #22BEEF;
    border-color: #22BEEF;
}

.btn-ef.btn-ef-1.btn-ef-1-cyan.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-cyan.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-cyan.btn-ef-1a.active {
    background-color: #22BEEF;
}

.btn-ef.btn-ef-1.btn-ef-1-cyan:after {
    background-color: #22BEEF;
}

.btn-ef.btn-ef-1.btn-ef-1-amethyst {
    color: #CD97EB;
    border-color: #CD97EB;
}

.btn-ef.btn-ef-1.btn-ef-1-amethyst.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-amethyst.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-amethyst.btn-ef-1a.active {
    background-color: #CD97EB;
}

.btn-ef.btn-ef-1.btn-ef-1-amethyst:after {
    background-color: #CD97EB;
}

.btn-ef.btn-ef-1.btn-ef-1-green {
    color: #A2D200;
    border-color: #A2D200;
}

.btn-ef.btn-ef-1.btn-ef-1-green.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-green.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-green.btn-ef-1a.active {
    background-color: #A2D200;
}

.btn-ef.btn-ef-1.btn-ef-1-green:after {
    background-color: #A2D200;
}

.btn-ef.btn-ef-1.btn-ef-1-orange {
    color: #FFC100;
    border-color: #FFC100;
}

.btn-ef.btn-ef-1.btn-ef-1-orange.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-orange.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-orange.btn-ef-1a.active {
    background-color: #FFC100;
}

.btn-ef.btn-ef-1.btn-ef-1-orange:after {
    background-color: #FFC100;
}

.btn-ef.btn-ef-1.btn-ef-1-red {
    color: #FF4A43;
    border-color: #FF4A43;
}

.btn-ef.btn-ef-1.btn-ef-1-red.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-red.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-red.btn-ef-1a.active {
    background-color: #FF4A43;
}

.btn-ef.btn-ef-1.btn-ef-1-red:after {
    background-color: #FF4A43;
}

.btn-ef.btn-ef-1.btn-ef-1-greensea {
    color: #16A085;
    border-color: #16A085;
}

.btn-ef.btn-ef-1.btn-ef-1-greensea.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-greensea.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-greensea.btn-ef-1a.active {
    background-color: #16A085;
}

.btn-ef.btn-ef-1.btn-ef-1-greensea:after {
    background-color: #16A085;
}

.btn-ef.btn-ef-1.btn-ef-1-dutch {
    color: #1693A5;
    border-color: #1693A5;
}

.btn-ef.btn-ef-1.btn-ef-1-dutch.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-dutch.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-dutch.btn-ef-1a.active {
    background-color: #1693A5;
}

.btn-ef.btn-ef-1.btn-ef-1-dutch:after {
    background-color: #1693A5;
}

.btn-ef.btn-ef-1.btn-ef-1-hotpink {
    color: #FF0066;
    border-color: #FF0066;
}

.btn-ef.btn-ef-1.btn-ef-1-hotpink.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-hotpink.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-hotpink.btn-ef-1a.active {
    background-color: #FF0066;
}

.btn-ef.btn-ef-1.btn-ef-1-hotpink:after {
    background-color: #FF0066;
}

.btn-ef.btn-ef-1.btn-ef-1-drank {
    color: #A40778;
    border-color: #A40778;
}

.btn-ef.btn-ef-1.btn-ef-1-drank.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-drank.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-drank.btn-ef-1a.active {
    background-color: #A40778;
}

.btn-ef.btn-ef-1.btn-ef-1-drank:after {
    background-color: #A40778;
}

.btn-ef.btn-ef-1.btn-ef-1-blue {
    color: #418BCA;
    border-color: #418BCA;
}

.btn-ef.btn-ef-1.btn-ef-1-blue.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-blue.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-blue.btn-ef-1a.active {
    background-color: #418BCA;
}

.btn-ef.btn-ef-1.btn-ef-1-blue:after {
    background-color: #418BCA;
}

.btn-ef.btn-ef-1.btn-ef-1-lightred {
    color: #E05D6F;
    border-color: #E05D6F;
}

.btn-ef.btn-ef-1.btn-ef-1-lightred.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-lightred.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-lightred.btn-ef-1a.active {
    background-color: #E05D6F;
}

.btn-ef.btn-ef-1.btn-ef-1-lightred:after {
    background-color: #E05D6F;
}

.btn-ef.btn-ef-1.btn-ef-1-slategray {
    color: #3F4E62;
    border-color: #3F4E62;
}

.btn-ef.btn-ef-1.btn-ef-1-slategray.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-slategray.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-slategray.btn-ef-1a.active {
    background-color: #3F4E62;
}

.btn-ef.btn-ef-1.btn-ef-1-slategray:after {
    background-color: #3F4E62;
}

.btn-ef.btn-ef-1.btn-ef-1-darkgray {
    color: #333333;
    border-color: #333333;
}

.btn-ef.btn-ef-1.btn-ef-1-darkgray.btn-ef-1a:hover,
.btn-ef.btn-ef-1.btn-ef-1-darkgray.btn-ef-1a:active,
.btn-ef.btn-ef-1.btn-ef-1-darkgray.btn-ef-1a.active {
    background-color: #333333;
}

.btn-ef.btn-ef-1.btn-ef-1-darkgray:after {
    background-color: #333333;
}

.btn-ef.btn-ef-2 {
    -webkit-transition: none;
    -moz-transition: none;
    transition: none;

    color: #616F77;
    border-radius: 0 0 4px 4px;
    background: #E6E6E6;
    -webkit-box-shadow: 0 3px;
    box-shadow: 0 3px;
}

.btn-ef.btn-ef-2.btn-rounded {
    border-radius: 4px;
}

.btn-ef.btn-ef-2.btn-ef-2a:hover {
    top: 2px;

    -webkit-box-shadow: 0 1px;
    box-shadow: 0 1px;
}

.btn-ef.btn-ef-2.btn-ef-2a:active,
.btn-ef.btn-ef-2.btn-ef-2a.active {
    top: 3px;

    -webkit-box-shadow: 0 0;
    box-shadow: 0 0;
}

.btn-ef.btn-ef-2.btn-ef-2b:hover {
    top: -2px;

    -webkit-box-shadow: 0 5px;
    box-shadow: 0 5px;
}

.btn-ef.btn-ef-2.btn-ef-2b:active,
.btn-ef.btn-ef-2.btn-ef-2b.active {
    top: 3px;

    -webkit-box-shadow: 0 0;
    box-shadow: 0 0;
}

.btn-ef.btn-ef-2.btn-ef-2c {
    -webkit-box-shadow: -3px 0;
    box-shadow: -3px 0;
}

.btn-ef.btn-ef-2.btn-ef-2c:hover {
    left: -2px;

    -webkit-box-shadow: -1px 0;
    box-shadow: -1px 0;
}

.btn-ef.btn-ef-2.btn-ef-2c:active,
.btn-ef.btn-ef-2.btn-ef-2c.active {
    left: -3px;

    -webkit-box-shadow: 0 0;
    box-shadow: 0 0;
}

.btn-ef.btn-ef-2.btn-ef-2d {
    -webkit-box-shadow: 3px 0;
    box-shadow: 3px 0;
}

.btn-ef.btn-ef-2.btn-ef-2d:hover {
    left: 2px;

    -webkit-box-shadow: 1px 0;
    box-shadow: 1px 0;
}

.btn-ef.btn-ef-2.btn-ef-2d:active,
.btn-ef.btn-ef-2.btn-ef-2d.active {
    left: 3px;

    -webkit-box-shadow: 0 0;
    box-shadow: 0 0;
}

.btn-ef.btn-ef-2.btn-ef-2-primary {
    color: #428BCA;
    background-color: #CDE1F1;
}

.btn-ef.btn-ef-2.btn-ef-2-success {
    color: #5CB85C;
    background-color: #D8EED8;
}

.btn-ef.btn-ef-2.btn-ef-2-warning {
    color: #F0AD4E;
    background-color: #FCEEDB;
}

.btn-ef.btn-ef-2.btn-ef-2-danger {
    color: #D9534F;
    background-color: #F9E2E2;
}

.btn-ef.btn-ef-2.btn-ef-2-info {
    color: #5BC0DE;
    background-color: #F0F9FC;
}

.btn-ef.btn-ef-2.btn-ef-2-cyan {
    color: #22BEEF;
    background-color: #E0F6FD;
}

.btn-ef.btn-ef-2.btn-ef-2-amethyst {
    color: #CD97EB;
    background-color: #F6EDFB;
}

.btn-ef.btn-ef-2.btn-ef-2-green {
    color: #A2D200;
    background-color: #F5FFD2;
}

.btn-ef.btn-ef-2.btn-ef-2-orange {
    color: orange;
    background-color: #FFF3CC;
}

.btn-ef.btn-ef-2.btn-ef-2-red {
    color: #FF4A43;
    background-color: #FFDDDC;
}

.btn-ef.btn-ef-2.btn-ef-2-greensea {
    color: #16A085;
    background-color: #D5F9F2;
}

.btn-ef.btn-ef-2.btn-ef-2-dutch {
    color: #1693A5;
    background-color: #DAF6FA;
}

.btn-ef.btn-ef-2.btn-ef-2-hotpink {
    color: #FF0066;
    background-color: #FFE5F0;
}

.btn-ef.btn-ef-2.btn-ef-2-drank {
    color: #A40778;
    background-color: #FDC7EE;
}

.btn-ef.btn-ef-2.btn-ef-2-blue {
    color: #418BCA;
    background-color: #E1ECF6;
}

.btn-ef.btn-ef-2.btn-ef-2-lightred {
    color: #E05D6F;
    background-color: #F9DDE1;
}

.btn-ef.btn-ef-2.btn-ef-2-slategray {
    color: #3F4E62;
    background-color: #B6C1D0;
}

.btn-ef.btn-ef-2.btn-ef-2-darkgray {
    color: #333333;
    background-color: #A6A6A6;
}

.btn-ef.btn-ef-3 {
    /* Button 3a */
    /* Button 3b */
    /* Button 3c */
}

.btn-ef.btn-ef-3.btn-default {
    color: #616F77;
    background: #E2E2E2;
}

.btn-ef.btn-ef-3.btn-default:hover {
    background: #C9C9C9;
}

.btn-ef.btn-ef-3.btn-default:active,
.btn-ef.btn-ef-3.btn-default.active {
    background: #BCBCBC;
}

.btn-ef.btn-ef-3.btn-default:focus {
    color: #616F77;
    background: #E2E2E2;
}

.btn-ef.btn-ef-3:active {
    top: 2px;
}

.btn-ef.btn-ef-3 > i {
    font-size: 130%;
    line-height: 32px;

    position: absolute;
    top: 0;
    left: 0;

    width: 40px;
    height: 100%;
}

.btn-ef.btn-ef-3.btn-ef-3a {
    padding-left: 50px;
}

.btn-ef.btn-ef-3.btn-ef-3a > i {
    background: rgba(0, 0, 0, 0.05);
}

.btn-ef.btn-ef-3.btn-ef-3b {
    padding-left: 45px;
}

.btn-ef.btn-ef-3.btn-ef-3b > i {
    width: 36px;

    border-right: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-ef.btn-ef-3.btn-ef-3c {
    overflow: hidden;

    padding-right: 45px;
}

.btn-ef.btn-ef-3.btn-ef-3c > i {
    font-size: 12px;

    z-index: 2;
    right: -6px;
    left: auto;
}

.btn-ef.btn-ef-3.btn-ef-3c:after {
    z-index: 1;
    top: 0;
    right: 0;

    width: 30%;
    height: 200%;
    margin: -5px 0 0 -5px;

    -webkit-transform: rotate(-20deg);
    -moz-transform: rotate(-20deg);
    -ms-transform: rotate(-20deg);
    transform: rotate(-20deg);
    -webkit-transform-origin: 0 0;
    -moz-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;

    background: rgba(255, 255, 255, 0.1);
}

.btn-ef.btn-ef-3.btn-ef-3c:hover:after {
    width: 40%;
}

.btn-ef.btn-ef-4 {
    overflow: hidden;

    padding: 6px 30px;
    /* Button 4a */
    /* Button 4b */
    /* Button 4c */
    /* Button 4d */
}

.btn-ef.btn-ef-4.btn-default {
    border: 2px solid #CCCCCC;
}

.btn-ef.btn-ef-4.btn-default:hover {
    border: 2px solid #B3B3B3;
}

.btn-ef.btn-ef-4:active,
.btn-ef.btn-ef-4.active {
    top: 2px;
}

.btn-ef.btn-ef-4 > i {
    line-height: 32px;

    position: absolute;

    height: 100%;

    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.btn-ef.btn-ef-4.btn-ef-4a > i {
    top: 0;
    left: 130%;
}

.btn-ef.btn-ef-4.btn-ef-4a:hover > i {
    left: 80%;
}

.btn-ef.btn-ef-4.btn-ef-4b > i {
    top: 0;
    left: 70%;

    opacity: 0;
}

.btn-ef.btn-ef-4.btn-ef-4b:hover > i {
    left: 80%;

    opacity: 1;
}

.btn-ef.btn-ef-4.btn-ef-4c > i {
    top: 0;
    left: -50%;
}

.btn-ef.btn-ef-4.btn-ef-4c:hover > i {
    left: 10%;
}

.btn-ef.btn-ef-4.btn-ef-4d > i {
    top: 0;
    left: 30%;

    opacity: 0;
}

.btn-ef.btn-ef-4.btn-ef-4d:hover > i {
    left: 10%;

    opacity: 1;
}

.btn-ef.btn-ef-5 {
    overflow: hidden;

    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* Button 5a */
    /* Button 5b */
}

.btn-ef.btn-ef-5.btn-default {
    border-width: 1px;
    border-style: solid;
    border-color: #CCCCCC;
}

.btn-ef.btn-ef-5.btn-default:hover {
    border-color: #B3B3B3;
}

.btn-ef.btn-ef-5:active,
.btn-ef.btn-ef-5.active {
    top: 2px;
}

.btn-ef.btn-ef-5:active i,
.btn-ef.btn-ef-5.active i {
    color: white;
}

.btn-ef.btn-ef-5 span {
    display: inline-block;

    width: 100%;
    height: 100%;

    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;

    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.btn-ef.btn-ef-5 i {
    font-size: 130%;
    line-height: 1.8;

    position: absolute;

    width: 100%;
    height: 100%;

    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.btn-ef.btn-ef-5.btn-ef-5a:hover span {
    -webkit-transform: translateY(300%);
    -moz-transform: translateY(300%);
    -ms-transform: translateY(300%);
    -o-transform: translateY(300%);
    transform: translateY(300%);
}

.btn-ef.btn-ef-5.btn-ef-5a:hover i {
    top: 0;
}

.btn-ef.btn-ef-5.btn-ef-5a i {
    top: -100%;
    left: 0;
}

.btn-ef.btn-ef-5.btn-ef-5b:hover span {
    -webkit-transform: translateX(200%);
    -moz-transform: translateX(200%);
    -ms-transform: translateX(200%);
    -o-transform: translateX(200%);
    transform: translateX(200%);
}

.btn-ef.btn-ef-5.btn-ef-5b:hover i {
    left: 0;
}

.btn-ef.btn-ef-5.btn-ef-5b i {
    top: 0;
    left: -100%;
}

.btn-ef.btn-ef-6 {
    padding: 8px 14px;

    -webkit-transition: none;
    -moz-transition: none;
    transition: none;

    border-width: 0;
    /* Button 6a */
    /* Button 6b */
    /* Button 6c */
    /* Button 6d */
}

.btn-ef.btn-ef-6.btn-default {
    color: #616F77;
    background: #E2E2E2;
}

.btn-ef.btn-ef-6.btn-default:hover {
    background: #C9C9C9;
}

.btn-ef.btn-ef-6.btn-default:active,
.btn-ef.btn-ef-6.btn-default.active {
    background: #BCBCBC;
}

.btn-ef.btn-ef-6.btn-default:focus {
    color: #616F77;
    background: #E2E2E2;
}

.btn-ef.btn-ef-6:active,
.btn-ef.btn-ef-6.active {
    top: 2px;
}

.btn-ef.btn-ef-6:hover {
    padding: 6px 12px;

    color: #616F77;
    border-width: 2px;
    background: transparent !important;
}

.btn-ef.btn-ef-6.btn-ef-6a {
    border-style: solid;
}

.btn-ef.btn-ef-6.btn-ef-6b {
    border-style: dashed;
}

.btn-ef.btn-ef-6.btn-ef-6c {
    border-style: dotted;
}

.btn-ef.btn-ef-6.btn-ef-6d {
    padding: 10px 16px;

    border-style: double;
}

.btn-ef.btn-ef-6.btn-ef-6d:hover {
    padding: 6px 12px;

    border-width: 4px;
}

.btn-ef.btn-ef-7 {
    padding: 6px 16px 6px 40px;

    border-width: 1px;
    border-style: solid;
    /* Button 7a */
    /* Button 7b */
    /* Button 7c */
    /* Button 7d */
    /* Button 7e */
    /* Button 7f */
    /* Button 7g */
    /* Button 7h */
}

.btn-ef.btn-ef-7:not(.btn-icon-only) i {
    color: rgba(255, 255, 255, 0.5);
}

.btn-ef.btn-ef-7.btn-default {
    border-color: #CCCCCC;
}

.btn-ef.btn-ef-7.btn-default:not(.btn-icon-only) i {
    color: rgba(0, 0, 0, 0.5);
}

.btn-ef.btn-ef-7.btn-default:hover {
    border-color: #B3B3B3;
}

.btn-ef.btn-ef-7.btn-ef-7a {
    overflow: hidden;
}

.btn-ef.btn-ef-7.btn-ef-7a i {
    font-size: 130%;
    line-height: 1.1;

    position: absolute;
    left: 0;

    width: 32%;
}

.btn-ef.btn-ef-7.btn-ef-7a.btn-activated {
    -webkit-animation: fadeOutText 0.5s;
    -moz-animation: fadeOutText 0.5s;
    animation: fadeOutText 0.5s;
}

.btn-ef.btn-ef-7.btn-ef-7a.btn-activated i {
    -webkit-animation: moveToRight 0.5s;
    -moz-animation: moveToRight 0.5s;
    animation: moveToRight 0.5s;
}

.btn-ef.btn-ef-7.btn-ef-7b {
    overflow: hidden;
}

.btn-ef.btn-ef-7.btn-ef-7b i {
    font-size: 130%;
    line-height: 1.1;

    position: absolute;
    left: 0;

    width: 28%;
}

.btn-ef.btn-ef-7.btn-ef-7b.btn-activated i {
    -webkit-animation: scaleUp 0.5s;
    -moz-animation: scaleUp 0.5s;
    animation: scaleUp 0.5s;
}

.btn-ef.btn-ef-7.btn-ef-7c {
    overflow: hidden;
}

.btn-ef.btn-ef-7.btn-ef-7c i {
    z-index: 1;
}

.btn-ef.btn-ef-7.btn-ef-7c:after {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;

    width: 0;
    height: 100%;

    -webkit-transition: none;
    -moz-transition: none;
    transition: none;

    background: rgba(0, 0, 0, 0.1);
}

.btn-ef.btn-ef-7.btn-ef-7c.btn-activated:after {
    -webkit-animation: fillToRight 0.7s forwards;
    -moz-animation: fillToRight 0.7s forwards;
    animation: fillToRight 0.7s forwards;
}

.btn-ef.btn-ef-7.btn-ef-7d {
    overflow: hidden;
}

.btn-ef.btn-ef-7.btn-ef-7d i {
    z-index: 1;
}

.btn-ef.btn-ef-7.btn-ef-7d:after {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;

    width: 100%;
    height: 0;

    -webkit-transition: none;
    -moz-transition: none;
    transition: none;

    background: rgba(0, 0, 0, 0.1);
}

.btn-ef.btn-ef-7.btn-ef-7d.btn-activated:after {
    -webkit-animation: emptyBottom 0.7s forwards;
    -moz-animation: emptyBottom 0.7s forwards;
    animation: emptyBottom 0.7s forwards;
}

.btn-ef.btn-ef-7.btn-ef-7e i.after {
    font-size: 22px;
    line-height: 42px;

    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;

    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);

    opacity: 0;
    color: #EA515E;

    filter: alpha(opacity=0);
}

.btn-ef.btn-ef-7.btn-ef-7e.btn-activated i.after {
    -webkit-animation: scaleFade 0.5s forwards;
    -moz-animation: scaleFade 0.5s forwards;
    animation: scaleFade 0.5s forwards;
}

.btn-ef.btn-ef-7.btn-ef-7f i.after {
    font-size: 22px;
    line-height: 42px;

    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;

    visibility: hidden;

    width: 100%;
    height: 100%;

    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
    -webkit-transform: scale(4);
    -moz-transform: scale(4);
    -ms-transform: scale(4);
    -o-transform: scale(4);
    transform: scale(4);

    opacity: 0;
    color: #FFE44D;

    filter: alpha(opacity=0);
}

.btn-ef.btn-ef-7.btn-ef-7f.btn-activated i.after {
    visibility: visible;

    -webkit-animation: dropDown 0.3s forwards;
    -moz-animation: dropDown 0.3s forwards;
    animation: dropDown 0.3s forwards;
}

.btn-ef.btn-ef-7.btn-ef-7g i.after {
    font-size: 22px;
    line-height: 42px;

    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;

    visibility: hidden;

    width: 100%;
    height: 100%;

    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
    -webkit-transform: scale(2);
    -moz-transform: scale(2);
    -ms-transform: scale(2);
    -o-transform: scale(2);
    transform: scale(2);

    opacity: 0;
    color: #616F77;

    filter: alpha(opacity=0);
}

.btn-ef.btn-ef-7.btn-ef-7g.btn-activated i.after {
    visibility: visible;

    -webkit-animation: dropDownFade 0.5s forwards;
    -moz-animation: dropDownFade 0.5s forwards;
    animation: dropDownFade 0.5s forwards;
}

.btn-ef.btn-ef-7.btn-ef-7h span {
    display: inline-block;

    width: 100%;
}

.btn-ef.btn-ef-7.btn-ef-7h i {
    font-size: 130%;
    line-height: 1.1;

    position: absolute;
    left: 0;

    width: 28%;
}

.btn-ef.btn-ef-7.btn-ef-7h.btn-activated i {
    -webkit-animation: scaleUp 0.5s;
    -moz-animation: scaleUp 0.5s;
    animation: scaleUp 0.5s;
}

.btn-ef.btn-activated-success,
.btn-ef.btn-activated-error {
    color: transparent !important;
}

.btn-ef.btn-activated-success:after,
.btn-ef.btn-activated-error:after {
    z-index: 1;
    left: 40%;

    color: #FFFFFF;
}

.btn-ef.btn-activated-success .fa:before {
    content: "\f118";
}

.btn-ef.btn-activated-success:after {
    content: "Success!";
    -webkit-animation: moveUp 0.5s;
    -moz-animation: moveUp 0.5s;
    animation: moveUp 0.5s;
}

.btn-ef.btn-activated-error {
    -webkit-animation: shake 0.5s;
    -moz-animation: shake 0.5s;
    animation: shake 0.5s;
}

.btn-ef.btn-activated-error .fa:before {
    content: "\f119";
}

.btn-ef.btn-activated-error:after {
    content: "Error!";
    -webkit-animation: scaleFromUp 0.5s;
    -moz-animation: scaleFromUp 0.5s;
    animation: scaleFromUp 0.5s;
}

button:focus {
    outline: 0 !important;
}

.btn {
    border-radius: 0;
    outline: 0 !important;
}

.btn.rounded-corners,
.btn.btn-rounded {
    border-radius: 4px;
}

.btn.rounded-corners.btn-lg,
.btn.btn-rounded.btn-lg {
    border-radius: 6px;
}

.btn.rounded-corners.btn-sm,
.btn.rounded-corners.btn-xs,
.btn.btn-rounded.btn-sm,
.btn.btn-rounded.btn-xs {
    border-radius: 3px;
}

.btn.btn-rounded-10 {
    border-radius: 10px;
}

.btn.btn-rounded-20 {
    border-radius: 20px;
}

.btn.btn-rounded-40 {
    border-radius: 40px;
}

.btn.btn-rounded-50p {
    border-radius: 50%;
}

.btn.no-border {
    border: 0;
}

.btn.btn-icon-only {
    font-size: 0;

    position: relative;

    padding: 20px 25px;
}

.btn.btn-icon-only i {
    font-size: 22px;
    line-height: 42px;

    position: absolute;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;

    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.btn:focus,
.btn:active {
    outline: 0 !important;
}

.btn.btn-cyan {
    color: white;
    border-color: #10ACDD;
    background-color: #22BEEF;
}

.btn.btn-cyan:hover,
.btn.btn-cyan:active,
.btn.btn-cyan.active {
    color: white;
    border-color: #0F9DCA;
    background-color: #10ACDD;
}

.btn.btn-cyan:active,
.btn.btn-cyan.active {
    border-color: #0E92BC;
    background-color: #0F9DCA;
}

.btn.btn-amethyst {
    color: white;
    border-color: #C382E7;
    background-color: #CD97EB;
}

.btn.btn-amethyst:hover,
.btn.btn-amethyst:active,
.btn.btn-amethyst.active {
    color: white;
    border-color: #BA71E4;
    background-color: #C382E7;
}

.btn.btn-amethyst:active,
.btn.btn-amethyst.active {
    border-color: #B464E1;
    background-color: #BA71E4;
}

.btn.btn-green {
    color: white;
    border-color: #96C300;
    background-color: #A2D200;
}

.btn.btn-green:hover,
.btn.btn-green:active,
.btn.btn-green.active {
    color: white;
    border-color: #86AE00;
    background-color: #96C300;
}

.btn.btn-green:active,
.btn.btn-green.active {
    border-color: #7B9F00;
    background-color: #86AE00;
}

.btn.btn-orange {
    color: white;
    border-color: #F0B500;
    background-color: #FFC100;
}

.btn.btn-orange:hover,
.btn.btn-orange:active,
.btn.btn-orange.active {
    color: white;
    border-color: #DBA600;
    background-color: #F0B500;
}

.btn.btn-orange:active,
.btn.btn-orange.active {
    border-color: #CC9A00;
    background-color: #DBA600;
}

.btn.btn-red {
    color: white;
    border-color: #FF1910;
    background-color: #FF4A43;
}

.btn.btn-red:hover,
.btn.btn-red:active,
.btn.btn-red.active {
    color: white;
    border-color: #E60900;
    background-color: #FF1910;
}

.btn.btn-red:active,
.btn.btn-red.active {
    border-color: #D70800;
    background-color: #E60900;
}

.btn.btn-greensea {
    color: white;
    border-color: #138A72;
    background-color: #16A085;
}

.btn.btn-greensea:hover,
.btn.btn-greensea:active,
.btn.btn-greensea.active {
    color: white;
    border-color: #107863;
    background-color: #138A72;
}

.btn.btn-greensea:active,
.btn.btn-greensea.active {
    border-color: #0F6A58;
    background-color: #107863;
}

.btn.btn-dutch {
    color: white;
    border-color: #137F8F;
    background-color: #1693A5;
}

.btn.btn-dutch:hover,
.btn.btn-dutch:active,
.btn.btn-dutch.active {
    color: white;
    border-color: #116F7D;
    background-color: #137F8F;
}

.btn.btn-dutch:active,
.btn.btn-dutch.active {
    border-color: #0F636F;
    background-color: #116F7D;
}

.btn.btn-hotpink {
    color: white;
    border-color: #E6005C;
    background-color: #FF0066;
}

.btn.btn-hotpink:hover,
.btn.btn-hotpink:active,
.btn.btn-hotpink.active {
    color: white;
    border-color: #D10054;
    background-color: #E6005C;
}

.btn.btn-hotpink:active,
.btn.btn-hotpink.active {
    border-color: #C2004E;
    background-color: #D10054;
}

.btn.btn-drank {
    color: white;
    border-color: #8C0666;
    background-color: #A40778;
}

.btn.btn-drank:hover,
.btn.btn-drank:active,
.btn.btn-drank.active {
    color: white;
    border-color: #780558;
    background-color: #8C0666;
}

.btn.btn-drank:active,
.btn.btn-drank.active {
    border-color: #69044D;
    background-color: #780558;
}

.btn.btn-blue {
    color: white;
    border-color: #357EBD;
    background-color: #418BCA;
}

.btn.btn-blue:hover,
.btn.btn-blue:active,
.btn.btn-blue.active {
    color: white;
    border-color: #3074AD;
    background-color: #357EBD;
}

.btn.btn-blue:active,
.btn.btn-blue.active {
    border-color: #2D6CA1;
    background-color: #3074AD;
}

.btn.btn-lightred {
    color: white;
    border-color: #DC485C;
    background-color: #E05D6F;
}

.btn.btn-lightred:hover,
.btn.btn-lightred:active,
.btn.btn-lightred.active {
    color: white;
    border-color: #D9364D;
    background-color: #DC485C;
}

.btn.btn-lightred:active,
.btn.btn-lightred.active {
    border-color: #D62A41;
    background-color: #D9364D;
}

.btn.btn-slategray {
    color: white;
    border-color: #354252;
    background-color: #3F4E62;
}

.btn.btn-slategray:hover,
.btn.btn-slategray:active,
.btn.btn-slategray.active {
    color: white;
    border-color: #2D3846;
    background-color: #354252;
}

.btn.btn-slategray:active,
.btn.btn-slategray.active {
    border-color: #27303D;
    background-color: #2D3846;
}

.btn.btn-darkgray {
    color: white;
    border-color: #262626;
    background-color: #333333;
}

.btn.btn-darkgray:hover,
.btn.btn-darkgray:active,
.btn.btn-darkgray.active {
    color: white;
    border-color: #1C1C1C;
    background-color: #262626;
}

.btn.btn-darkgray:active,
.btn.btn-darkgray.active {
    border-color: #141414;
    background-color: #1C1C1C;
}

.btn.btn-default {
    color: #616F77;
    border-color: #CDD3D7;
}

.btn.btn-default:focus {
    border-color: #B1BAC0;
    background-color: white;
}

.btn.btn-default:hover {
    background-color: #F2F3F4;
}

.btn.btn-default:active,
.btn.btn-default.active {
    background-color: #EAECED;
}

.btn.btn-primary:focus {
    border-color: #357EBD;
    background-color: #428BCA;
}

.btn.btn-primary:hover {
    background-color: #3071A9;
}

.btn.btn-primary:active,
.btn.btn-primary.active {
    background-color: #2A6496;
}

.btn.btn-success:focus {
    border-color: #4CAE4C;
    background-color: #5CB85C;
}

.btn.btn-success:hover {
    background-color: #449D44;
}

.btn.btn-success:active,
.btn.btn-success.active {
    background-color: #3D8B3D;
}

.btn.btn-warning:focus {
    border-color: #EEA236;
    background-color: #F0AD4E;
}

.btn.btn-warning:hover {
    background-color: #EC971F;
}

.btn.btn-warning:active,
.btn.btn-warning.active {
    background-color: #DF8A13;
}

.btn.btn-danger:focus {
    border-color: #D43F3A;
    background-color: #D9534F;
}

.btn.btn-danger:hover {
    background-color: #C9302C;
}

.btn.btn-danger:active,
.btn.btn-danger.active {
    background-color: #B52B27;
}

.btn.btn-info:focus {
    border-color: #46B8DA;
    background-color: #5BC0DE;
}

.btn.btn-info:hover {
    background-color: #31B0D5;
}

.btn.btn-info:active,
.btn.btn-info.active {
    background-color: #28A1C5;
}

.btn.btn-border {
    border-width: 2px;
    border-style: solid;
    background-color: transparent;
}

.btn.btn-border:hover,
.btn.btn-border:active,
.btn.btn-border.active,
.btn.btn-border:focus {
    background-color: transparent;
}

.btn.btn-border.btn-xs,
.btn.btn-border.btn-sm {
    border-width: 1px;
}

.btn.btn-border.btn-cyan {
    color: #22BEEF;
}

.btn.btn-border.btn-cyan:hover,
.btn.btn-border.btn-cyan:active,
.btn.btn-border.btn-cyan.active {
    color: #0F9DCA;
}

.btn.btn-border.btn-amethyst {
    color: #CD97EB;
}

.btn.btn-border.btn-amethyst:hover,
.btn.btn-border.btn-amethyst:active,
.btn.btn-border.btn-amethyst.active {
    color: #BA71E4;
}

.btn.btn-border.btn-green {
    color: #A2D200;
}

.btn.btn-border.btn-green:hover,
.btn.btn-border.btn-green:active,
.btn.btn-border.btn-green.active {
    color: #86AE00;
}

.btn.btn-border.btn-orange {
    color: #FFC100;
}

.btn.btn-border.btn-orange:hover,
.btn.btn-border.btn-orange:active,
.btn.btn-border.btn-orange.active {
    color: #DBA600;
}

.btn.btn-border.btn-red {
    color: #FF4A43;
}

.btn.btn-border.btn-red:hover,
.btn.btn-border.btn-red:active,
.btn.btn-border.btn-red.active {
    color: #E60900;
}

.btn.btn-border.btn-greensea {
    color: #16A085;
}

.btn.btn-border.btn-greensea:hover,
.btn.btn-border.btn-greensea:active,
.btn.btn-border.btn-greensea.active {
    color: #107863;
}

.btn.btn-border.btn-dutch {
    color: #1693A5;
}

.btn.btn-border.btn-dutch:hover,
.btn.btn-border.btn-dutch:active,
.btn.btn-border.btn-dutch.active {
    color: #116F7D;
}

.btn.btn-border.btn-hotpink {
    color: #FF0066;
}

.btn.btn-border.btn-hotpink:hover,
.btn.btn-border.btn-hotpink:active,
.btn.btn-border.btn-hotpink.active {
    color: #D10054;
}

.btn.btn-border.btn-drank {
    color: #A40778;
}

.btn.btn-border.btn-drank:hover,
.btn.btn-border.btn-drank:active,
.btn.btn-border.btn-drank.active {
    color: #780558;
}

.btn.btn-border.btn-blue {
    color: #418BCA;
}

.btn.btn-border.btn-blue:hover,
.btn.btn-border.btn-blue:active,
.btn.btn-border.btn-blue.active {
    color: #3074AD;
}

.btn.btn-border.btn-lightred {
    color: #E05D6F;
}

.btn.btn-border.btn-lightred:hover,
.btn.btn-border.btn-lightred:active,
.btn.btn-border.btn-lightred.active {
    color: #D9364D;
}

.btn.btn-border.btn-slategray {
    color: #3F4E62;
}

.btn.btn-border.btn-slategray:hover,
.btn.btn-border.btn-slategray:active,
.btn.btn-border.btn-slategray.active {
    color: #2D3846;
}

.btn.btn-border.btn-darkgray {
    color: #333333;
}

.btn.btn-border.btn-darkgray:hover,
.btn.btn-border.btn-darkgray:active,
.btn.btn-border.btn-darkgray.active {
    color: #1C1C1C;
}

.btn.btn-border.btn-primary {
    color: #428BCA;
}

.btn.btn-border.btn-primary:hover,
.btn.btn-border.btn-primary:active,
.btn.btn-border.btn-primary.active {
    color: #245682;
}

.btn.btn-border.btn-success {
    color: #5CB85C;
}

.btn.btn-border.btn-success:hover,
.btn.btn-border.btn-success:active,
.btn.btn-border.btn-success.active {
    color: #357935;
}

.btn.btn-border.btn-warning {
    color: #F0AD4E;
}

.btn.btn-border.btn-warning:hover,
.btn.btn-border.btn-warning:active,
.btn.btn-border.btn-warning.active {
    color: #C77C11;
}

.btn.btn-border.btn-danger {
    color: #D9534F;
}

.btn.btn-border.btn-danger:hover,
.btn.btn-border.btn-danger:active,
.btn.btn-border.btn-danger.active {
    color: #A02622;
}

.btn.btn-border.btn-info {
    color: #5BC0DE;
}

.btn.btn-border.btn-info:hover,
.btn.btn-border.btn-info:active,
.btn.btn-border.btn-info.active {
    color: #2390B0;
}

.btn.btn-border.btn-white {
    color: #FFFFFF;
    border-color: white;
}

.btn.btn-border.btn-white:hover,
.btn.btn-border.btn-white:active,
.btn.btn-border.btn-white.active {
    background-color: rgba(255, 255, 255, 0.1);
}

.open .dropdown-toggle.btn-cyan {
    color: white;
    background-color: #10ACDD;
}

.open .dropdown-toggle.btn-amethyst {
    color: white;
    background-color: #C382E7;
}

.open .dropdown-toggle.btn-green {
    color: white;
    background-color: #96C300;
}

.open .dropdown-toggle.btn-orange {
    color: white;
    background-color: #F0B500;
}

.open .dropdown-toggle.btn-red {
    color: white;
    background-color: #FF1910;
}

.open .dropdown-toggle.btn-greensea {
    color: white;
    background-color: #138A72;
}

.open .dropdown-toggle.btn-dutch {
    color: white;
    background-color: #137F8F;
}

.open .dropdown-toggle.btn-hotpink {
    color: white;
    background-color: #E6005C;
}

.open .dropdown-toggle.btn-drank {
    color: white;
    background-color: #DC485C;
}

.open .dropdown-toggle.btn-drank {
    color: white;
    background-color: #DC485C;
}

.open .dropdown-toggle.btn-blue {
    color: white;
    background-color: #357EBD;
}

.open .dropdown-toggle.btn-lightred {
    color: white;
    background-color: #DC485C;
}

.open .dropdown-toggle.btn-slategray {
    color: white;
    background-color: #354252;
}

.open .dropdown-toggle.btn-darkgray {
    color: white;
    background-color: #262626;
}

.btn-group > .btn {
    border-radius: 0;
}

.btn-group.btn-group-rounded > .btn {
    border-radius: 4px;
}

.btn-group.btn-group-rounded-20 > .btn {
    border-radius: 20px;
}

.btn-group-lg > .btn {
    border-radius: 0;
}

.btn-group-lg.btn-group-rounded > .btn {
    border-radius: 6px;
}

.btn-group-sm > .btn,
.btn-group-xs > .btn {
    border-radius: 0;
}

.btn-group-sm.btn-group-rounded > .btn,
.btn-group-xs.btn-group-rounded > .btn {
    border-radius: 3px;
}

.btn-group-vertical > .btn:first-child:not(:last-child) {
    border-radius: 0;
}

.btn-group > .btn:not(.btn-default) {
    /*&:not(.dropdown-toggle){
    &:hover,
    &:focus,
    &.active,
    &:active {
      border-right: 1px solid $transparent-black-2;
    }
  }*/
}

.btn-group > .btn:not(.btn-default) + .dropdown-toggle {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

.btn-group > .btn:not(.btn-default) + .dropdown-toggle:hover,
.btn-group > .btn:not(.btn-default) + .dropdown-toggle:focus,
.btn-group > .btn:not(.btn-default) + .dropdown-toggle.active,
.btn-group > .btn:not(.btn-default) + .dropdown-toggle:active {
    border-left: 1px solid rgba(0, 0, 0, 0.2);
}

.tile-button {
    font-size: 18px;

    display: block;

    padding: 30px 20px;

    text-align: center;

    opacity: 0.6;
    color: #616F77;
}

.tile-button:hover,
.tile-button:focus {
    text-decoration: none;

    opacity: 1;
    color: #616F77;
}

@font-face {
    font-family: "Simple-Line-Icons";
    font-weight: normal;
    font-style: normal;

    src: url("../fonts/Simple-Line-Icons.eot");
    src: url("../fonts/Simple-Line-Icons.eot?#iefix") format("embedded-opentype"), url("../fonts/Simple-Line-Icons.woff") format("woff"), url("../fonts/Simple-Line-Icons.ttf") format("truetype"), url("../fonts/Simple-Line-Icons.svg#Simple-Line-Icons") format("svg");
}

@font-face {
    font-family: "weathericons";
    font-weight: normal;
    font-style: normal;

    src: url("../fonts/weathericons-regular-webfont.eot");
    src: url("../fonts/weathericons-regular-webfont.eot?#iefix") format("embedded-opentype"), url("../fonts/weathericons-regular-webfont.woff") format("woff"), url("../fonts/weathericons-regular-webfont.ttf") format("truetype"), url("../fonts/weathericons-regular-webfont.svg#weathericons-regular-webfontRg") format("svg");
}

.myIcon {
    font-size: 0;

    position: relative;
    z-index: 1;

    display: inline-block;

    width: 50px;
    height: 50px;
    margin: 10px;

    cursor: pointer;
    text-align: center;

    color: #FFFFFF;
    border-radius: 50%;
    /* Effect 1 */
    /* Effect 2 */
    /* Effect 3 */
    /* Effect 4 */
    /* Effect 5 */
    /* Effect 6 */
    /* Effect 7 */
    /* Effect 8 */
    /* Effect 9 */
}

.myIcon:hover {
    text-decoration: none;
}

.myIcon > .fa {
    font-size: 26px;
    line-height: 50px;

    display: block;

    speak: none;
}

.myIcon:after,
.myIcon:before {
    position: absolute;

    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    width: 100%;
    height: 100%;

    content: "";
    pointer-events: none;

    border-radius: 50%;
}

.myIcon:before {
    display: none;
}

.myIcon.icon-ef-1 {
    -webkit-transition: background 0.2s, color 0.2s;
    -moz-transition: background 0.2s, color 0.2s;
    transition: background 0.2s, color 0.2s;
    /* Effect 1a */
    /* Effect 1b */
}

.myIcon.icon-ef-1:after {
    top: -5px;
    left: -5px;

    padding: 5px;

    -webkit-transition: -webkit-transform 0.2s, opacity 0.2s;
    -moz-transition: -moz-transform 0.2s, opacity 0.2s;
    transition: transform 0.2s, opacity 0.2s;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);

    opacity: 0;
    box-shadow: 0 0 0 3px;

    filter: alpha(opacity=0);
}

.myIcon.icon-ef-1:before {
    display: none;
}

.myIcon.icon-ef-1.icon-ef-1a:hover:after {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);

    opacity: 1;

    filter: alpha(opacity=100);
}

.myIcon.icon-ef-1.icon-ef-1b:after {
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2);
}

.myIcon.icon-ef-1.icon-ef-1b:hover:after {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);

    opacity: 1;

    filter: alpha(opacity=100);
}

.myIcon.icon-ef-2 {
    -webkit-transition: color 0.3s;
    -moz-transition: color 0.3s;
    transition: color 0.3s;

    background-color: transparent !important;
    /* Effect 2a */
    /* Effect 2b */
}

.myIcon.icon-ef-2:after {
    top: 0;
    left: 0;

    box-shadow: 0 0 0 3px;
}

.myIcon.icon-ef-2:before {
    z-index: -1;
    top: -2px;
    left: -2px;

    display: block;

    padding: 2px;

    -webkit-transition: -webkit-transform 0.2s, opacity 0.2s;
    -moz-transition: -moz-transform 0.2s, opacity 0.2s;
    transition: transform 0.2s, opacity 0.2s;
}

.myIcon.icon-ef-2.icon-ef-2a:hover:before {
    -webkit-transform: scale(0.85);
    -moz-transform: scale(0.85);
    -ms-transform: scale(0.85);
    -o-transform: scale(0.85);
    transform: scale(0.85);
}

.myIcon.icon-ef-2.icon-ef-2b:hover:before {
    -webkit-transition: -webkit-transform 0.4s, opacity 0.2s;
    -moz-transition: -moz-transform 0.4s, opacity 0.2s;
    transition: transform 0.4s, opacity 0.2s;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);

    opacity: 0;
}

.myIcon.icon-ef-3 {
    -webkit-transition: color 0.3s;
    -moz-transition: color 0.3s;
    transition: color 0.3s;

    background-color: transparent !important;
    /* Effect 3a */
    /* Effect 3b */
}

.myIcon.icon-ef-3:after {
    top: 0;
    left: 0;

    box-shadow: 0 0 0 3px;
}

.myIcon.icon-ef-3:before {
    z-index: -1;
    top: -2px;
    left: -2px;

    display: block;

    padding: 2px;

    -webkit-transition: -webkit-transform 0.2s, opacity 0.3s;
    -moz-transition: -moz-transform 0.2s, opacity 0.3s;
    transition: transform 0.2s, opacity 0.3s;
}

.myIcon.icon-ef-3.icon-ef-3a:hover:before {
    -webkit-transform: scale(1.3);
    -moz-transform: scale(1.3);
    -ms-transform: scale(1.3);
    -o-transform: scale(1.3);
    transform: scale(1.3);

    opacity: 0;

    filter: alpha(opacity=0);
}

.myIcon.icon-ef-3.icon-ef-3b:before {
    -webkit-transform: scale(1.3);
    -moz-transform: scale(1.3);
    -ms-transform: scale(1.3);
    -o-transform: scale(1.3);
    transform: scale(1.3);

    opacity: 0;

    filter: alpha(opacity=0);
}

.myIcon.icon-ef-3.icon-ef-3b:hover {
    color: white !important;
}

.myIcon.icon-ef-3.icon-ef-3b:hover:before {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);

    opacity: 1;

    filter: alpha(opacity=100);
}

.myIcon.icon-ef-4 {
    background-color: transparent !important;
    box-shadow: 0 0 0 3px;
    /* Effect 4a */
    /* Effect 4b */
}

.myIcon.icon-ef-4:after {
    z-index: 10;
    top: -3px;
    left: -3px;

    padding: 0;

    border: 3px dashed;
}

.myIcon.icon-ef-4:before {
    display: none;
}

.myIcon.icon-ef-4:hover {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
}

.myIcon.icon-ef-4.icon-ef-4a {
    -webkit-transition: box-shadow 0.2s;
    -moz-transition: box-shadow 0.2s;
    transition: box-shadow 0.2s;
}

.myIcon.icon-ef-4.icon-ef-4b:hover {
    -webkit-transition: box-shadow 0.2s;
    -moz-transition: box-shadow 0.2s;
    transition: box-shadow 0.2s;
}

.myIcon.icon-ef-4.icon-ef-4b:hover:after {
    -webkit-animation: spinAround 9s linear infinite;
    -moz-animation: spinAround 9s linear infinite;
    animation: spinAround 9s linear infinite;
}

.myIcon.icon-ef-5 {
    background-color: transparent !important;
    /* Effect 5a */
    /* Effect 5b */
    /* Effect 5c */
    /* Effect 5d */
}

.myIcon.icon-ef-5:after {
    top: 0;
    left: 0;

    -webkit-transition: box-shadow 0.3s;
    -moz-transition: box-shadow 0.3s;
    transition: box-shadow 0.3s;

    box-shadow: 0 0 0 3px;
}

.myIcon.icon-ef-5:before {
    z-index: -1;
    top: -2px;
    left: -2px;

    display: block;
    display: none;

    padding: 2px;

    -webkit-transition: background 0.3s;
    -moz-transition: background 0.3s;
    transition: background 0.3s;
}

.myIcon.icon-ef-5:hover > .fa {
    color: white !important;
}

.myIcon.icon-ef-5:hover:before {
    display: block;
}

.myIcon.icon-ef-5:hover:after {
    opacity: 0.3;
    box-shadow: 0 0 0 6px;

    filter: alpha(opacity=30);
}

.myIcon.icon-ef-5.icon-ef-5a:hover > .fa {
    -webkit-animation: toRightFromLeft 0.3s forwards;
    -moz-animation: toRightFromLeft 0.3s forwards;
    animation: toRightFromLeft 0.3s forwards;
}

.myIcon.icon-ef-5.icon-ef-5b:hover > .fa {
    -webkit-animation: toLeftFromRight 0.3s forwards;
    -moz-animation: toLeftFromRight 0.3s forwards;
    animation: toLeftFromRight 0.3s forwards;
}

.myIcon.icon-ef-5.icon-ef-5c:hover > .fa {
    -webkit-animation: toTopFromBottom 0.3s forwards;
    -moz-animation: toTopFromBottom 0.3s forwards;
    animation: toTopFromBottom 0.3s forwards;
}

.myIcon.icon-ef-5.icon-ef-5d:hover > .fa {
    -webkit-animation: toBottomFromTop 0.3s forwards;
    -moz-animation: toBottomFromTop 0.3s forwards;
    animation: toBottomFromTop 0.3s forwards;
}

.myIcon.icon-ef-6 {
    -webkit-transition: color 0.2s;
    -moz-transition: color 0.2s;
    transition: color 0.2s;

    background-color: transparent !important;
}

.myIcon.icon-ef-6:after {
    top: 0;
    left: 0;

    box-shadow: 0 0 0 3px;
}

.myIcon.icon-ef-6:before {
    top: -2px;
    left: -2px;

    display: block;

    padding: 2px;

    -webkit-transition: opacity 0.2s;
    -moz-transition: opacity 0.2s;
    transition: opacity 0.2s;

    opacity: 0;

    filter: alpha(opacity=0);
}

.myIcon.icon-ef-6:hover {
    color: white !important;
}

.myIcon.icon-ef-6:hover > .fa {
    -webkit-animation: spinAround 2s linear infinite;
    -moz-animation: spinAround 2s linear infinite;
    animation: spinAround 2s linear infinite;
}

.myIcon.icon-ef-6:hover:before {
    opacity: 1;

    filter: alpha(opacity=100);
}

.myIcon.icon-ef-7 {
    -webkit-transition: color 0.2s;
    -moz-transition: color 0.2s;
    transition: color 0.2s;

    background-color: transparent !important;
    box-shadow: 0 0 0 3px;
    /* Effect 7a */
    /* Effect 7b */
}

.myIcon.icon-ef-7:after {
    z-index: -1;
    top: -6px;
    left: -6px;

    padding: 6px;

    opacity: 0;

    filter: alpha(opacity=0);
}

.myIcon.icon-ef-7:before {
    display: none;
}

.myIcon.icon-ef-7 > .fa {
    -webkit-transition: -webkit-transform 0.2s, opacity 0.2s;
    -moz-transition: -moz-transform 0.2s, opacity 0.2s;
    transition: transform 0.2s, opacity 0.2s;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);

    opacity: 0.7;

    filter: alpha(opacity=70);
}

.myIcon.icon-ef-7:hover > .fa {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);

    opacity: 1;

    filter: alpha(opacity=100);
}

.myIcon.icon-ef-7.icon-ef-7a:after {
    -webkit-transition: opacity 0.2s, box-shadow 0.2s;
    -moz-transition: opacity 0.2s, box-shadow 0.2s;
    transition: opacity 0.2s, box-shadow 0.2s;

    box-shadow: 0 0 0;
}

.myIcon.icon-ef-7.icon-ef-7a:hover:after {
    opacity: 1;
    box-shadow: 2px 2px 0;

    filter: alpha(opacity=100);
}

.myIcon.icon-ef-7.icon-ef-7b:after {
    -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
    -moz-transition: opacity 0.2s, -moz-transform 0.2s;
    transition: opacity 0.2s, transform 0.2s;
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    transform: rotate(-90deg);

    box-shadow: 2px 2px;
}

.myIcon.icon-ef-7.icon-ef-7b:hover:after {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);

    opacity: 1;

    filter: alpha(opacity=100);
}

.myIcon.icon-ef-8 {
    -webkit-transition: -webkit-transform ease-out 0.1s, background 0.2s;
    -moz-transition: -moz-transform ease-out 0.1s, background 0.2s;
    transition: transform ease-out 0.1s, background 0.2s;

    background: transparent !important;
}

.myIcon.icon-ef-8:after {
    z-index: -1;
    top: 0;
    left: 0;

    padding: 0;

    opacity: 0;
    box-shadow: 0 0 0 2px;
}

.myIcon.icon-ef-8:before {
    top: 0;
    left: 0;

    display: block;

    opacity: 0.1;

    filter: alpha(opacity=10);
}

.myIcon.icon-ef-8:hover {
    -webkit-transform: scale(0.93);
    -moz-transform: scale(0.93);
    -ms-transform: scale(0.93);
    -o-transform: scale(0.93);
    transform: scale(0.93);
}

.myIcon.icon-ef-8:hover:before {
    opacity: 0.05;

    filter: alpha(opacity=5);
}

.myIcon.icon-ef-8:hover:after {
    -webkit-animation: sonarEffect 1.3s ease-out 75ms;
    -moz-animation: sonarEffect 1.3s ease-out 75ms;
    animation: sonarEffect 1.3s ease-out 75ms;
}

.myIcon.icon-ef-9 {
    -webkit-transition: box-shadow 0.2s;
    -moz-transition: box-shadow 0.2s;
    transition: box-shadow 0.2s;

    background-color: transparent !important;
}

.myIcon.icon-ef-9:after {
    top: 0;
    left: 0;

    padding: 0;

    -webkit-transition: -webkit-transform 0.2s, opacity 0.2s;
    -moz-transition: -moz-transform 0.2s, opacity 0.2s;
    transition: transform 0.2s, opacity 0.2s;

    box-shadow: 0 0 0 3px;
}

.myIcon.icon-ef-9:before {
    display: none;
}

.myIcon.icon-ef-9:hover {
    box-shadow: 0 0 0 5px;
}

.myIcon.icon-ef-9:hover:after {
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);

    opacity: 0.3;

    filter: alpha(opacity=30);
}

.myIcon.icon-cyan {
    color: white;
    background-color: #22BEEF;
}

.myIcon.icon-cyan.transparent {
    color: #22BEEF;
    background-color: rgba(34, 190, 239, 0.1);
}

.myIcon.icon-cyan.transparent:hover {
    color: white;
    background-color: #22BEEF;
}

.myIcon.icon-cyan:after,
.myIcon.icon-cyan.hover-color:hover,
.myIcon.icon-cyan.icon-color {
    color: #22BEEF;
}

.myIcon.icon-cyan:before {
    background-color: #22BEEF;
}

.myIcon.icon-cyan:hover {
    background-color: #10ACDD;
}

.myIcon.icon-cyan:active {
    background-color: #0F9DCA;
}

.myIcon.icon-amethyst {
    color: white;
    background-color: #CD97EB;
}

.myIcon.icon-amethyst.transparent {
    color: #CD97EB;
    background-color: rgba(205, 151, 235, 0.1);
}

.myIcon.icon-amethyst.transparent:hover {
    color: white;
    background-color: #CD97EB;
}

.myIcon.icon-amethyst:after,
.myIcon.icon-amethyst.hover-color:hover,
.myIcon.icon-amethyst.icon-color {
    color: #CD97EB;
}

.myIcon.icon-amethyst:before {
    background-color: #CD97EB;
}

.myIcon.icon-amethyst:hover {
    background-color: #C382E7;
}

.myIcon.icon-amethyst:active {
    background-color: #BA71E4;
}

.myIcon.icon-green {
    color: white;
    background-color: #A2D200;
}

.myIcon.icon-green.transparent {
    color: #A2D200;
    background-color: rgba(162, 210, 0, 0.1);
}

.myIcon.icon-green.transparent:hover {
    color: white;
    background-color: #A2D200;
}

.myIcon.icon-green:after,
.myIcon.icon-green.hover-color:hover,
.myIcon.icon-green.icon-color {
    color: #A2D200;
}

.myIcon.icon-green:before {
    background-color: #A2D200;
}

.myIcon.icon-green:hover {
    background-color: #96C300;
}

.myIcon.icon-green:active {
    background-color: #86AE00;
}

.myIcon.icon-orange {
    color: white;
    background-color: #FFC100;
}

.myIcon.icon-orange.transparent {
    color: #FFC100;
    background-color: rgba(255, 193, 0, 0.1);
}

.myIcon.icon-orange.transparent:hover {
    color: white;
    background-color: #FFC100;
}

.myIcon.icon-orange:after,
.myIcon.icon-orange.hover-color:hover,
.myIcon.icon-orange.icon-color {
    color: #FFC100;
}

.myIcon.icon-orange:before {
    background-color: #FFC100;
}

.myIcon.icon-orange:hover {
    background-color: #F0B500;
}

.myIcon.icon-orange:active {
    background-color: #DBA600;
}

.myIcon.icon-red {
    color: white;
    background-color: #FF4A43;
}

.myIcon.icon-red.transparent {
    color: #FF4A43;
    background-color: rgba(255, 74, 67, 0.1);
}

.myIcon.icon-red.transparent:hover {
    color: white;
    background-color: #FF4A43;
}

.myIcon.icon-red:after,
.myIcon.icon-red.hover-color:hover,
.myIcon.icon-red.icon-color {
    color: #FF4A43;
}

.myIcon.icon-red:before {
    background-color: #FF4A43;
}

.myIcon.icon-red:hover {
    background-color: #FF1910;
}

.myIcon.icon-red:active {
    background-color: #E60900;
}

.myIcon.icon-greensea {
    color: white;
    background-color: #16A085;
}

.myIcon.icon-greensea.transparent {
    color: #16A085;
    background-color: rgba(22, 160, 133, 0.1);
}

.myIcon.icon-greensea.transparent:hover {
    color: white;
    background-color: #16A085;
}

.myIcon.icon-greensea:after,
.myIcon.icon-greensea.hover-color:hover,
.myIcon.icon-greensea.icon-color {
    color: #16A085;
}

.myIcon.icon-greensea:before {
    background-color: #16A085;
}

.myIcon.icon-greensea:hover {
    background-color: #138A72;
}

.myIcon.icon-greensea:active {
    background-color: #107863;
}

.myIcon.icon-dutch {
    color: white;
    background-color: #1693A5;
}

.myIcon.icon-dutch.transparent {
    color: #1693A5;
    background-color: rgba(22, 147, 165, 0.1);
}

.myIcon.icon-dutch.transparent:hover {
    color: white;
    background-color: #1693A5;
}

.myIcon.icon-dutch:after,
.myIcon.icon-dutch.hover-color:hover,
.myIcon.icon-dutch.icon-color {
    color: #1693A5;
}

.myIcon.icon-dutch:before {
    background-color: #1693A5;
}

.myIcon.icon-dutch:hover {
    background-color: #137F8F;
}

.myIcon.icon-dutch:active {
    background-color: #116F7D;
}

.myIcon.icon-hotpink {
    color: white;
    background-color: #FF0066;
}

.myIcon.icon-hotpink.transparent {
    color: #FF0066;
    background-color: rgba(255, 0, 102, 0.1);
}

.myIcon.icon-hotpink.transparent:hover {
    color: white;
    background-color: #FF0066;
}

.myIcon.icon-hotpink:after,
.myIcon.icon-hotpink.hover-color:hover,
.myIcon.icon-hotpink.icon-color {
    color: #FF0066;
}

.myIcon.icon-hotpink:before {
    background-color: #FF0066;
}

.myIcon.icon-hotpink:hover {
    background-color: #E6005C;
}

.myIcon.icon-hotpink:active {
    background-color: #D10054;
}

.myIcon.icon-drank {
    color: white;
    background-color: #A40778;
}

.myIcon.icon-drank.transparent {
    color: #A40778;
    background-color: rgba(164, 7, 120, 0.1);
}

.myIcon.icon-drank.transparent:hover {
    color: white;
    background-color: #A40778;
}

.myIcon.icon-drank:after,
.myIcon.icon-drank.hover-color:hover,
.myIcon.icon-drank.icon-color {
    color: #A40778;
}

.myIcon.icon-drank:before {
    background-color: #A40778;
}

.myIcon.icon-drank:hover {
    background-color: #8C0666;
}

.myIcon.icon-drank:active {
    background-color: #780558;
}

.myIcon.icon-blue {
    color: white;
    background-color: #418BCA;
}

.myIcon.icon-blue.transparent {
    color: #418BCA;
    background-color: rgba(65, 139, 202, 0.1);
}

.myIcon.icon-blue.transparent:hover {
    color: white;
    background-color: #418BCA;
}

.myIcon.icon-blue:after,
.myIcon.icon-blue.hover-color:hover,
.myIcon.icon-blue.icon-color {
    color: #418BCA;
}

.myIcon.icon-blue:before {
    background-color: #418BCA;
}

.myIcon.icon-blue:hover {
    background-color: #357EBD;
}

.myIcon.icon-blue:active {
    background-color: #3074AD;
}

.myIcon.icon-lightred {
    color: white;
    background-color: #E05D6F;
}

.myIcon.icon-lightred.transparent {
    color: #E05D6F;
    background-color: rgba(224, 93, 111, 0.1);
}

.myIcon.icon-lightred.transparent:hover {
    color: white;
    background-color: #E05D6F;
}

.myIcon.icon-lightred:after,
.myIcon.icon-lightred.hover-color:hover,
.myIcon.icon-lightred.icon-color {
    color: #E05D6F;
}

.myIcon.icon-lightred:before {
    background-color: #E05D6F;
}

.myIcon.icon-lightred:hover {
    background-color: #DC485C;
}

.myIcon.icon-lightred:active {
    background-color: #D9364D;
}

.myIcon.icon-slategray {
    color: white;
    background-color: #3F4E62;
}

.myIcon.icon-slategray.transparent {
    color: #3F4E62;
    background-color: rgba(63, 78, 98, 0.1);
}

.myIcon.icon-slategray.transparent:hover {
    color: white;
    background-color: #3F4E62;
}

.myIcon.icon-slategray:after,
.myIcon.icon-slategray.hover-color:hover,
.myIcon.icon-slategray.icon-color {
    color: #3F4E62;
}

.myIcon.icon-slategray:before {
    background-color: #3F4E62;
}

.myIcon.icon-slategray:hover {
    background-color: #354252;
}

.myIcon.icon-slategray:active {
    background-color: #2D3846;
}

.myIcon.icon-darkgray {
    color: white;
    background-color: #333333;
}

.myIcon.icon-darkgray.transparent {
    color: #333333;
    background-color: rgba(51, 51, 51, 0.1);
}

.myIcon.icon-darkgray.transparent:hover {
    color: white;
    background-color: #333333;
}

.myIcon.icon-darkgray:after,
.myIcon.icon-darkgray.hover-color:hover,
.myIcon.icon-darkgray.icon-color {
    color: #333333;
}

.myIcon.icon-darkgray:before {
    background-color: #333333;
}

.myIcon.icon-darkgray:hover {
    background-color: #262626;
}

.myIcon.icon-darkgray:active {
    background-color: #1C1C1C;
}

.myIcon.icon-primary {
    color: white;
    background-color: #428BCA;
}

.myIcon.icon-primary.transparent {
    color: #428BCA;
    background-color: rgba(66, 139, 202, 0.1);
}

.myIcon.icon-primary.transparent:hover {
    color: white;
    background-color: #428BCA;
}

.myIcon.icon-primary:after,
.myIcon.icon-primary.hover-color:hover,
.myIcon.icon-primary.icon-color {
    color: #428BCA;
}

.myIcon.icon-primary:before {
    background-color: #428BCA;
}

.myIcon.icon-primary:hover {
    background-color: #3071A9;
}

.myIcon.icon-primary:active {
    background-color: #245682;
}

.myIcon.icon-success {
    color: white;
    background-color: #5CB85C;
}

.myIcon.icon-success.transparent {
    color: #5CB85C;
    background-color: rgba(92, 184, 92, 0.1);
}

.myIcon.icon-success.transparent:hover {
    color: white;
    background-color: #5CB85C;
}

.myIcon.icon-success:after,
.myIcon.icon-success.hover-color:hover,
.myIcon.icon-success.icon-color {
    color: #5CB85C;
}

.myIcon.icon-success:before {
    background-color: #5CB85C;
}

.myIcon.icon-success:hover {
    background-color: #449D44;
}

.myIcon.icon-success:active {
    background-color: #357935;
}

.myIcon.icon-warning {
    color: white;
    background-color: #F0AD4E;
}

.myIcon.icon-warning.transparent {
    color: #F0AD4E;
    background-color: rgba(240, 173, 78, 0.1);
}

.myIcon.icon-warning.transparent:hover {
    color: white;
    background-color: #F0AD4E;
}

.myIcon.icon-warning:after,
.myIcon.icon-warning.hover-color:hover,
.myIcon.icon-warning.icon-color {
    color: #F0AD4E;
}

.myIcon.icon-warning:before {
    background-color: #F0AD4E;
}

.myIcon.icon-warning:hover {
    background-color: #EC971F;
}

.myIcon.icon-warning:active {
    background-color: #C77C11;
}

.myIcon.icon-danger {
    color: white;
    background-color: #D9534F;
}

.myIcon.icon-danger.transparent {
    color: #D9534F;
    background-color: rgba(217, 83, 79, 0.1);
}

.myIcon.icon-danger.transparent:hover {
    color: white;
    background-color: #D9534F;
}

.myIcon.icon-danger:after,
.myIcon.icon-danger.hover-color:hover,
.myIcon.icon-danger.icon-color {
    color: #D9534F;
}

.myIcon.icon-danger:before {
    background-color: #D9534F;
}

.myIcon.icon-danger:hover {
    background-color: #C9302C;
}

.myIcon.icon-danger:active {
    background-color: #A02622;
}

.myIcon.icon-info {
    color: white;
    background-color: #5BC0DE;
}

.myIcon.icon-info.transparent {
    color: #5BC0DE;
    background-color: rgba(91, 192, 222, 0.1);
}

.myIcon.icon-info.transparent:hover {
    color: white;
    background-color: #5BC0DE;
}

.myIcon.icon-info:after,
.myIcon.icon-info.hover-color:hover,
.myIcon.icon-info.icon-color {
    color: #5BC0DE;
}

.myIcon.icon-info:before {
    background-color: #5BC0DE;
}

.myIcon.icon-info:hover {
    background-color: #31B0D5;
}

.myIcon.icon-info:active {
    background-color: #2390B0;
}

.myIcon.icon-default {
    color: white;
    background-color: #616F77;
}

.myIcon.icon-default.transparent {
    color: #616F77;
    background-color: rgba(97, 111, 119, 0.1);
}

.myIcon.icon-default.transparent:hover {
    color: white;
    background-color: #616F77;
}

.myIcon.icon-default:after,
.myIcon.icon-default.hover-color:hover,
.myIcon.icon-default.icon-color {
    color: #616F77;
}

.myIcon.icon-default:before {
    background-color: #616F77;
}

.myIcon.icon-default:hover {
    background-color: #4A555B;
}

.myIcon.icon-default:active {
    background-color: #333B3F;
}

.icons-list div {
    line-height: 40px;

    position: relative;
    z-index: 1;

    cursor: default;
    white-space: nowrap;
}

.icons-list div::after {
    position: absolute;
    z-index: -1;
    right: 0;
    left: 15px;

    width: 85%;
    height: 100%;

    content: "";
    -webkit-transition: opacity 0.2s, -webkit-transform 0.4s;
    -moz-transition: opacity 0.2s, -moz-transform 0.4s;
    transition: opacity 0.2s, transform 0.4s;
    -webkit-transform: scale(0.5);
    -moz-transform: scale(0.5);
    -ms-transform: scale(0.5);
    -o-transform: scale(0.5);
    transform: scale(0.5);

    opacity: 0;
    background-color: #F8F8F8;

    filter: alpha(opacity=0);
}

.icons-list div i {
    display: inline-block;

    width: 40px;
    margin: 0;

    -webkit-transition: font-size 0.2s;
    -moz-transition: font-size 0.2s;
    transition: font-size 0.2s;
    text-align: center;
    vertical-align: middle;
}

.icons-list div:hover::after {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);

    opacity: 1;

    filter: alpha(opacity=100);
}

.icons-list div:hover i {
    font-size: 26px;
}

.icon-border {
    border: 1px solid;
    border-radius: 50%;
}

@font-face {
    font-family: "Glyphicons Halflings";

    src: url("../fonts/glyphicons-halflings-regular.eot");
    src: url("../fonts/glyphicons-halflings-regular.eot?#iefix") format("embedded-opentype"), url("../fonts/glyphicons-halflings-regular.woff") format("woff"), url("../fonts/glyphicons-halflings-regular.ttf") format("truetype"), url("../fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular") format("svg");
}

ul > li.divided-right,
ol > li.divided-right {
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

ul.list-type,
ol.list-type {
    margin: 7px 0 7px 20px;
    padding: 0;

    list-style-type: none;
}

ul.list-type li,
ol.list-type li {
    position: relative;

    padding: 2px 2px 2px 0;
}

ul.list-type li:before,
ol.list-type li:before {
    font-family: "FontAwesome";
    font-weight: 900;
    font-style: normal;
    line-height: 1;

    position: absolute;
    left: -18px;

    display: inline-block;

    margin-top: 3px;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

ul.list-type.arrow li:before,
ol.list-type.arrow li:before {
    content: "\f061";
}

ul.list-type.circle li:before,
ol.list-type.circle li:before {
    content: "\f10c";
}

ul.list-type.check li:before,
ol.list-type.check li:before {
    content: "\f046";
}

ul.list-type.caret-right li:before,
ol.list-type.caret-right li:before {
    content: "\f0da";
}

.nestable-tree {
    float: none;

    width: auto;
    max-width: none;
}

.dd-item .dd-handle,
.dd-dragel .dd-handle {
    margin-bottom: 5px;
    padding: 4px 20px;

    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    color: #6AA3D5;
    border: 1px solid #DAF1F8;
    border-radius: 0;
    background: #F0F9FC;
}

.dd-item .dd-handle:hover,
.dd-dragel .dd-handle:hover {
    color: #428BCA;
    border-color: #C5E9F3;
    background: #DAF1F8;
}

.dd-item .dd-handle .btn.add,
.dd-item .dd-handle .btn.remove,
.dd-item .dd-handle .btn.edit,
.dd-dragel .dd-handle .btn.add,
.dd-dragel .dd-handle .btn.remove,
.dd-dragel .dd-handle .btn.edit {
    font-size: 12px;
    line-height: 20px;

    padding: 6px;
}

.dd-item .dd-handle .btn:active,
.dd-dragel .dd-handle .btn:active {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.dd-item .dd-handle .tree-handle,
.dd-dragel .dd-handle .tree-handle {
    line-height: 22px;

    padding: 6px 8px;

    border-right: 1px solid #C5E9F3;
    background-color: #DAF1F8;
}

.list-group.no-radius .list-group-item {
    border-radius: 0 !important;
}

.list-group.no-border .list-group-item {
    border-width: 1px 0;
}

.list-group-item.b-primary .controls {
    display: inline-block;
}

.media .media-icon {
    font-size: 20px;
    line-height: 40px;

    width: 40px;
    height: 40px;

    text-align: center;
}

.thumb {
    display: inline-block;

    width: 60px;
}

.thumb img {
    max-width: 100%;
    height: auto;

    cursor: pointer;
    vertical-align: middle;
}

.thumb.thumb-sm {
    width: 40px;
}

.thumb.thumb-md {
    width: 80px;
}

.thumb.thumb-lg {
    width: 100px;
}

.thumb.thumb-xl {
    width: 120px;
}

.thumb.thumb-xxl {
    width: 160px;
}

.thumb .thumb-header,
.thumb .thumb-body {
    padding: 5px;

    border: 1px solid rgba(0, 0, 0, 0.1);
}

.thumb .thumb-header {
    border-bottom: 0;
    background-color: rgba(0, 0, 0, 0.01);
}

.thumb .thumb-body {
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.text-muted {
    color: #95A2A9;
}

.custom-font {
    font-family: "Dosis", "Arial", sans-serif;
}

.filled {
    padding: 10px;

    color: white;
}

.text-thin {
    font-weight: 300;
}

.text-normal {
    font-weight: normal !important;
}

.text-italic {
    font-style: italic;
}

.text-strong {
    font-weight: 700;
}

.text-small {
    font-size: 85%;
}

.text-lowercase {
    text-transform: lowercase !important;
}

.text-active {
    display: none;
}

.active > .text-active {
    display: inline-block;
}

.active > .text-inactive {
    display: none;
}

h1.underline,
h2.underline,
h3.underline,
h4.underline,
h5.underline,
h6.underline,
.h1.underline,
.h2.underline,
.h3.underline,
.h4.underline,
.h5.underline,
.h6.underline {
    padding-bottom: 5px;

    border-bottom: 1px solid #95A2A9;
}

address.filled strong {
    font-weight: 300;

    display: block;

    margin: -10px;
    padding: 5px 10px;

    text-transform: uppercase;
}

address.filled i {
    font-size: 80px;

    margin-top: -50px;

    opacity: 0.05;

    filter: alpha(opacity=5);
}

address.filled.append {
    margin-top: -20px;
}

dl.filled {
    padding: 0;
}

dl.filled dt {
    font-family: "Dosis", "Arial", sans-serif;
}

dl.filled dt,
dl.filled dd {
    padding: 5px 10px;
}

blockquote.filled {
    min-height: 60px;
    padding: 10px;

    border-color: rgba(0, 0, 0, 0.3);
    border-left: 5px solid;
}

blockquote.filled i[class*="fa-quote-"] {
    font-size: 46px;

    margin: 0 10px 10px 0;

    color: rgba(0, 0, 0, 0.1);
}

blockquote.filled .fa.pull-right {
    margin-left: 0.3em;
}

blockquote.filled small {
    color: rgba(0, 0, 0, 0.3);
}

blockquote.filled.text-right {
    border-right: 5px solid rgba(0, 0, 0, 0.3);
    border-left-width: 0;
}

blockquote p {
    line-height: 1.6em;
}

.note {
    font-family: "Dosis", "Arial", sans-serif;
    font-size: 12px;

    position: relative;
    top: -2px;

    display: inline-block;

    margin-top: 5px;
    margin-left: 5px;
    padding: 2px 5px;

    color: #95A2A9;
    background-color: rgba(0, 0, 0, 0.1);
}

.example .link-effect {
    font-family: "Dosis", "Arial", sans-serif;
    font-size: 18px;
    font-weight: 400;

    margin: 15px 25px;

    letter-spacing: 1px;
    text-transform: uppercase;
}

.link-effect {
    font-family: "Dosis", "Arial", sans-serif;
    font-size: 18px;
    font-weight: 400;

    position: relative;

    display: inline-block;

    margin: 15px 25px;

    vertical-align: middle;
    text-decoration: none;
    letter-spacing: 1px;
    text-transform: uppercase;

    outline: none;
    /* Effect 1: Brackets */
    /* Effect 2: 3D rolling links, idea from http://hakim.se/thoughts/rolling-links */
    /* Effect 3: bottom line slides/fades in */
    /* Effect 4: bottom border enlarge */
    /* Effect 5: same word slide in */
    /* Effect 6: same word slide in and border bottom */
    /* Effect 7: second border slides up */
    /* Effect 8: border slight translate */
    /* Effect 9: second text and borders */
    /* Effect 10: reveal, push out */
    /* Effect 11: text fill based on Lea Verou's animation http://dabblet.com/gist/6046779 */
    /* Effect 12: circle */
    /* Effect 13: three circles */
    /* Effect 14: border switch */
    /* Effect 15: scale down, reveal */
    /* Effect 16: fall down */
    /* Effect 17: move up fade out, push border */
    /* Effect 18: cross */
    /* Effect 19: 3D side */
    /* Effect 20: 3D side */
    /* Effect 21: borders slight translate */
}

.link-effect:hover,
.link-effect:focus {
    text-decoration: none;

    outline: 0;
}

.link-effect.link-effect-1::before,
.link-effect.link-effect-1::after {
    display: inline-block;

    -webkit-transition: -webkit-transform 0.3s, opacity 0.2s;
    -moz-transition: -moz-transform 0.3s, opacity 0.2s;
    transition: transform 0.3s, opacity 0.2s;

    opacity: 0;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-1::before {
    margin-right: 10px;

    content: "[";
    -webkit-transform: translateX(20px);
    -moz-transform: translateX(20px);
    -ms-transform: translateX(20px);
    -o-transform: translateX(20px);
    transform: translateX(20px);
}

.link-effect.link-effect-1::after {
    margin-left: 10px;

    content: "]";
    -webkit-transform: translateX(-20px);
    -moz-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    -o-transform: translateX(-20px);
    transform: translateX(-20px);
}

.link-effect.link-effect-1:hover::before,
.link-effect.link-effect-1:hover::after,
.link-effect.link-effect-1:focus::before,
.link-effect.link-effect-1:focus::after {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-2 {
    line-height: 1.8em;

    color: white;

    -webkit-perspective: 1000px;
    -moz-perspective: 1000px;
    perspective: 1000px;
}

.link-effect.link-effect-2 span {
    position: relative;

    display: inline-block;

    padding: 0 10px;

    -webkit-transition: -webkit-transform 0.3s;
    -moz-transition: -moz-transform 0.3s;
    transition: transform 0.3s;
    -webkit-transform-origin: 50% 0;
    -moz-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    -o-transform-origin: 50% 0;
    transform-origin: 50% 0;

    background: #428BCA;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    -o-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.link-effect.link-effect-2 span::before {
    position: absolute;
    top: 100%;
    left: 0;

    width: 100%;
    height: 100%;
    padding: 0 10px;

    content: attr(data-hover);
    -webkit-transition: background 0.3s;
    -moz-transition: background 0.3s;
    transition: background 0.3s;
    -webkit-transform: rotateX(-90deg);
    -moz-transform: rotateX(-90deg);
    -ms-transform: rotateX(-90deg);
    -o-transform: rotateX(-90deg);
    transform: rotateX(-90deg);
    -webkit-transform-origin: 50% 0;
    -moz-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    -o-transform-origin: 50% 0;
    transform-origin: 50% 0;

    background: #245682;
}

.link-effect.link-effect-2:hover span,
.link-effect.link-effect-2:focus span {
    -webkit-transform: rotateX(90deg) translateY(-22px);
    -moz-transform: rotateX(90deg) translateY(-22px);
    -ms-transform: rotateX(90deg) translateY(-22px);
    -o-transform: rotateX(90deg) translateY(-22px);
    transform: rotateX(90deg) translateY(-22px);
}

.link-effect.link-effect-2:hover span::before,
.link-effect.link-effect-2:focus span::before {
    background-color: #428BCA;
}

.link-effect.link-effect-3 {
    padding: 4px 0;
}

.link-effect.link-effect-3::after {
    position: absolute;
    top: 100%;
    left: 0;

    width: 100%;
    height: 4px;

    content: "";
    -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
    -moz-transition: opacity 0.3s, -moz-transform 0.3s;
    transition: opacity 0.3s, transform 0.3s;
    -webkit-transform: translateY(10px);
    -moz-transform: translateY(10px);
    -ms-transform: translateY(10px);
    -o-transform: translateY(10px);
    transform: translateY(10px);

    opacity: 0;
    background: rgba(66, 139, 202, 0.15);

    filter: alpha(opacity=0);
}

.link-effect.link-effect-3:hover::after,
.link-effect.link-effect-3:focus::after {
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-4 {
    padding: 0 0 4px;
}

.link-effect.link-effect-4::after {
    position: absolute;
    top: 100%;
    left: 0;

    width: 100%;
    height: 1px;

    content: "";
    -webkit-transition: height 0.3s, opacity 0.3s, -webkit-transform 0.3s;
    -moz-transition: height 0.3s, opacity 0.3s, -moz-transform 0.3s;
    transition: height 0.3s, opacity 0.3s, transform 0.3s;
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);

    opacity: 0;
    background: rgba(66, 139, 202, 0.15);

    filter: alpha(opacity=0);
}

.link-effect.link-effect-4:hover::after,
.link-effect.link-effect-4:focus::after {
    height: 5px;

    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-5 {
    overflow: hidden;

    padding: 0 4px;
}

.link-effect.link-effect-5 span {
    position: relative;

    display: inline-block;

    -webkit-transition: -webkit-transform 0.3s;
    -moz-transition: -moz-transform 0.3s;
    transition: transform 0.3s;
}

.link-effect.link-effect-5 span::before {
    font-weight: 700;

    position: absolute;
    top: 100%;

    content: attr(data-hover);
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.link-effect.link-effect-5:hover span,
.link-effect.link-effect-5:focus span {
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    -o-transform: translateY(-100%);
    transform: translateY(-100%);
}

.link-effect.link-effect-6 {
    margin: 0 10px;
    padding: 5px 10px;
}

.link-effect.link-effect-6::before {
    position: absolute;
    top: 0;
    left: 0;

    width: 100%;
    height: 2px;

    content: "";
    -webkit-transition: top 0.3s;
    -moz-transition: top 0.3s;
    transition: top 0.3s;

    background: #428BCA;
}

.link-effect.link-effect-6::after {
    position: absolute;
    top: 0;
    left: 0;

    width: 2px;
    height: 2px;

    content: "";
    -webkit-transition: height 0.3s;
    -moz-transition: height 0.3s;
    transition: height 0.3s;

    background: #428BCA;
}

.link-effect.link-effect-6:hover::before {
    top: 100%;

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-6:hover::after {
    height: 100%;
}

.link-effect.link-effect-7 {
    padding: 6px 5px 5px;
}

.link-effect.link-effect-7::before,
.link-effect.link-effect-7::after {
    position: absolute;
    top: 100%;
    left: 0;

    width: 100%;
    height: 2px;

    content: "";
    -webkit-transition: -webkit-transform 0.3s;
    -moz-transition: -moz-transform 0.3s;
    transition: transform 0.3s;
    -webkit-transform: scale(0.85);
    -moz-transform: scale(0.85);
    -ms-transform: scale(0.85);
    -o-transform: scale(0.85);
    transform: scale(0.85);

    background: #428BCA;
}

.link-effect.link-effect-7::after {
    -webkit-transition: top 0.3s, opacity 0.3s, -webkit-transform 0.3s;
    -moz-transition: top 0.3s, opacity 0.3s, -moz-transform 0.3s;
    transition: top 0.3s, opacity 0.3s, transform 0.3s;

    opacity: 0;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-7:hover::before,
.link-effect.link-effect-7:hover::after,
.link-effect.link-effect-7:focus::before,
.link-effect.link-effect-7:focus::after {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
}

.link-effect.link-effect-7:hover::after,
.link-effect.link-effect-7:focus::after {
    top: 0;

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-8 {
    padding: 5px 10px;
}

.link-effect.link-effect-8::before,
.link-effect.link-effect-8::after {
    position: absolute;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;

    content: "";
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;

    border: 2px solid #333333;
}

.link-effect.link-effect-8::after {
    -webkit-transform: translateY(-7px) translateX(6px);
    -moz-transform: translateY(-7px) translateX(6px);
    -ms-transform: translateY(-7px) translateX(6px);
    -o-transform: translateY(-7px) translateX(6px);
    transform: translateY(-7px) translateX(6px);

    opacity: 0;
    border-color: #428BCA;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-8:hover::before,
.link-effect.link-effect-8:focus::before {
    -webkit-transform: translateY(5) translateX(-5px);
    -moz-transform: translateY(5) translateX(-5px);
    -ms-transform: translateY(5) translateX(-5px);
    -o-transform: translateY(5) translateX(-5px);
    transform: translateY(5) translateX(-5px);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-8:hover::after,
.link-effect.link-effect-8:focus::after {
    -webkit-transform: translateY(0) translateX(0);
    -moz-transform: translateY(0) translateX(0);
    -ms-transform: translateY(0) translateX(0);
    -o-transform: translateY(0) translateX(0);
    transform: translateY(0) translateX(0);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-9 {
    margin: 0 20px;
    padding: 8px 10px;

    text-align: center;
}

.link-effect.link-effect-9::before,
.link-effect.link-effect-9::after {
    position: absolute;
    top: 0;
    left: 0;

    width: 100%;
    height: 2px;

    content: "";
    -webkit-transition: opacity 0.3s, height 0.3s;
    -moz-transition: opacity 0.3s, height 0.3s;
    transition: opacity 0.3s, height 0.3s;

    opacity: 0.2;
    background: #428BCA;

    filter: alpha(opacity=20);
}

.link-effect.link-effect-9::after {
    top: 100%;

    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);

    opacity: 0;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-9 span:first-child {
    z-index: 2;

    display: block;
}

.link-effect.link-effect-9 span:last-child {
    font-family: Georgia, serif;
    font-size: 10px;
    font-style: italic;

    z-index: 1;

    display: block;

    padding: 2px 0 0 0;

    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    -o-transform: translateY(-100%);
    transform: translateY(-100%);
    text-transform: none;

    opacity: 0;
    color: rgba(0, 0, 0, 0.4);
    text-shadow: none;
}

.link-effect.link-effect-9:hover::before,
.link-effect.link-effect-9:focus::before {
    height: 6px;
}

.link-effect.link-effect-9:hover::before,
.link-effect.link-effect-9:hover::after,
.link-effect.link-effect-9:focus::before,
.link-effect.link-effect-9:focus::after {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-9:hover span:last-child,
.link-effect.link-effect-9:focus span:last-child {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-10 {
    position: relative;
    z-index: 1;

    overflow: hidden;

    margin: 0 8px;
}

.link-effect.link-effect-10 span {
    display: block;

    padding: 5px 10px;

    -webkit-transition: -webkit-transform 0.3s;
    -moz-transition: -moz-transform 0.3s;
    transition: transform 0.3s;

    background: #CDE1F1;
}

.link-effect.link-effect-10::before {
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;
    padding: 5px 10px;

    content: attr(data-hover);
    -webkit-transition: -webkit-transform 0.3s;
    -moz-transition: -moz-transform 0.3s;
    transition: transform 0.3s;
    -webkit-transform: translateX(-25%);
    -moz-transform: translateX(-25%);
    -ms-transform: translateX(-25%);
    -o-transform: translateX(-25%);
    transform: translateX(-25%);

    color: white;
    background: #428BCA;
}

.link-effect.link-effect-10:hover span,
.link-effect.link-effect-10:focus span {
    -webkit-transform: translateX(100%);
    -moz-transform: translateX(100%);
    -ms-transform: translateX(100%);
    -o-transform: translateX(100%);
    transform: translateX(100%);
}

.link-effect.link-effect-10:hover::before,
.link-effect.link-effect-10:focus::before {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
}

.link-effect.link-effect-11 {
    padding: 5px 0;

    color: rgba(0, 0, 0, 0.4);
    border-top: 2px solid rgba(0, 0, 0, 0.2);
}

.link-effect.link-effect-11::before {
    position: absolute;
    top: 0;
    left: 0;

    overflow: hidden;

    max-width: 0;
    padding: 5px 0;

    content: attr(data-hover);
    -webkit-transition: max-width 0.5s;
    -moz-transition: max-width 0.5s;
    transition: max-width 0.5s;

    color: #428BCA;
    border-bottom: 2px solid #428BCA;
}

.link-effect.link-effect-11:hover::before,
.link-effect.link-effect-11:focus::before {
    max-width: 100%;
}

.link-effect.link-effect-12::before,
.link-effect.link-effect-12::after {
    position: absolute;
    top: 50%;
    left: 50%;

    width: 70px;
    height: 70px;

    content: "";
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    -webkit-transform: translateX(-50%) translateY(-50%) scale(0.2);
    -moz-transform: translateX(-50%) translateY(-50%) scale(0.2);
    -ms-transform: translateX(-50%) translateY(-50%) scale(0.2);
    -o-transform: translateX(-50%) translateY(-50%) scale(0.2);
    transform: translateX(-50%) translateY(-50%) scale(0.2);

    opacity: 0;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-12:after {
    width: 60px;
    height: 60px;

    -webkit-transform: translateX(-50%) translateY(-50%) scale(0.8);
    -moz-transform: translateX(-50%) translateY(-50%) scale(0.8);
    -ms-transform: translateX(-50%) translateY(-50%) scale(0.8);
    -o-transform: translateX(-50%) translateY(-50%) scale(0.8);
    transform: translateX(-50%) translateY(-50%) scale(0.8);

    border-width: 4px;
}

.link-effect.link-effect-12:hover::before,
.link-effect.link-effect-12:hover::after,
.link-effect.link-effect-12:focus::before,
.link-effect.link-effect-12:focus::after {
    -webkit-transform: translateX(-50%) translateY(-50%) scale(1);
    -moz-transform: translateX(-50%) translateY(-50%) scale(1);
    -ms-transform: translateX(-50%) translateY(-50%) scale(1);
    -o-transform: translateX(-50%) translateY(-50%) scale(1);
    transform: translateX(-50%) translateY(-50%) scale(1);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-13 {
    -webkit-transition: color 0.3s;
    -moz-transition: color 0.3s;
    transition: color 0.3s;
}

.link-effect.link-effect-13::before {
    font-size: 1.2em;

    position: absolute;
    top: 70%;
    left: 50%;

    content: "•";
    -webkit-transition: text-shadow 0.3s, color 0.3s;
    -moz-transition: text-shadow 0.3s, color 0.3s;
    transition: text-shadow 0.3s, color 0.3s;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    pointer-events: none;

    color: transparent;
    text-shadow: 0 0 transparent;
}

.link-effect.link-effect-13:hover::before,
.link-effect.link-effect-13:focus::before {
    color: #616F77;
    text-shadow: 10px 0 #616F77, -10px 0 #616F77;
}

.link-effect.link-effect-14 {
    line-height: 30px;

    height: 30px;
    padding: 0 10px;
}

.link-effect.link-effect-14::before,
.link-effect.link-effect-14::after {
    position: absolute;

    width: 30px;
    height: 2px;

    content: "";
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
    pointer-events: none;

    opacity: 0.2;
    background: #428BCA;

    filter: alpha(opacity=20);
}

.link-effect.link-effect-14::before {
    top: 0;
    left: 0;

    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: 0 0;
    -moz-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    -o-transform-origin: 0 0;
    transform-origin: 0 0;
}

.link-effect.link-effect-14::after {
    right: 0;
    bottom: 0;

    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: 100% 0;
    -moz-transform-origin: 100% 0;
    -ms-transform-origin: 100% 0;
    -o-transform-origin: 100% 0;
    transform-origin: 100% 0;
}

.link-effect.link-effect-14:hover::before,
.link-effect.link-effect-14:hover::after,
.link-effect.link-effect-14:focus::before,
.link-effect.link-effect-14:focus::after {
    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-14:hover::before,
.link-effect.link-effect-14:focus::before {
    left: 50%;

    -webkit-transform: rotate(0deg) translateX(-50%);
    -moz-transform: rotate(0deg) translateX(-50%);
    -ms-transform: rotate(0deg) translateX(-50%);
    -o-transform: rotate(0deg) translateX(-50%);
    transform: rotate(0deg) translateX(-50%);
}

.link-effect.link-effect-14:hover::after,
.link-effect.link-effect-14:focus::after {
    right: 50%;

    -webkit-transform: rotate(0deg) translateX(50%);
    -moz-transform: rotate(0deg) translateX(50%);
    -ms-transform: rotate(0deg) translateX(50%);
    -o-transform: rotate(0deg) translateX(50%);
    transform: rotate(0deg) translateX(50%);
}

.link-effect.link-effect-15 {
    font-weight: 700;

    color: rgba(0, 0, 0, 0.2);
}

.link-effect.link-effect-15::before {
    position: absolute;

    content: attr(data-hover);
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;

    color: #428BCA;
}

.link-effect.link-effect-15:hover::before,
.link-effect.link-effect-15:focus::before {
    -webkit-transform: scale(0.9);
    -moz-transform: scale(0.9);
    -ms-transform: scale(0.9);
    -o-transform: scale(0.9);
    transform: scale(0.9);

    opacity: 0;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-16 {
    color: rgba(0, 0, 0, 0.4);
}

.link-effect.link-effect-16::before {
    position: absolute;

    content: attr(data-hover);
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    -webkit-transform: scale(1.1) translateX(10px) translateY(-10px) rotate(4deg);
    -moz-transform: scale(1.1) translateX(10px) translateY(-10px) rotate(4deg);
    -ms-transform: scale(1.1) translateX(10px) translateY(-10px) rotate(4deg);
    -o-transform: scale(1.1) translateX(10px) translateY(-10px) rotate(4deg);
    transform: scale(1.1) translateX(10px) translateY(-10px) rotate(4deg);
    pointer-events: none;

    opacity: 0;
    color: #428BCA;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-16:hover::before,
.link-effect.link-effect-16:focus::before {
    -webkit-transform: scale(1) translateX(0px) translateY(0px) rotate(0deg);
    -moz-transform: scale(1) translateX(0px) translateY(0px) rotate(0deg);
    -ms-transform: scale(1) translateX(0px) translateY(0px) rotate(0deg);
    -o-transform: scale(1) translateX(0px) translateY(0px) rotate(0deg);
    transform: scale(1) translateX(0px) translateY(0px) rotate(0deg);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-17 {
    padding: 10px 0;

    color: rgba(0, 0, 0, 0.4);
}

.link-effect.link-effect-17::before {
    position: absolute;

    content: attr(data-hover);
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    pointer-events: none;

    color: #428BCA;
}

.link-effect.link-effect-17::after {
    position: absolute;
    bottom: 0;
    left: 0;

    width: 100%;
    height: 2px;

    content: "";
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    -webkit-transform: translateY(5px);
    -moz-transform: translateY(5px);
    -ms-transform: translateY(5px);
    -o-transform: translateY(5px);
    transform: translateY(5px);
    pointer-events: none;

    opacity: 0;
    background: #616F77;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-17:hover::before,
.link-effect.link-effect-17:focus::before {
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    transform: translateY(-2px);

    opacity: 0;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-17:hover::after,
.link-effect.link-effect-17:focus::after {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);

    opacity: 1;

    filter: alpha(opacity=100);
}

.link-effect.link-effect-18 {
    font-weight: 700;

    position: relative;
    z-index: 1;

    padding: 0 5px;

    -webkit-transition: color 0.3s;
    -moz-transition: color 0.3s;
    transition: color 0.3s;

    color: rgba(0, 0, 0, 0.2);
}

.link-effect.link-effect-18::before,
.link-effect.link-effect-18::after {
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 0;

    width: 100%;
    height: 2px;
    margin-top: -1px;

    content: "";
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    pointer-events: none;

    background: rgba(0, 0, 0, 0.2);
}

.link-effect.link-effect-18::before {
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
    transform: translateY(-20px);
}

.link-effect.link-effect-18::after {
    -webkit-transform: translateY(20px);
    -moz-transform: translateY(20px);
    -ms-transform: translateY(20px);
    -o-transform: translateY(20px);
    transform: translateY(20px);
}

.link-effect.link-effect-18:hover,
.link-effect.link-effect-18:focus {
    color: #428BCA;
}

.link-effect.link-effect-18:hover::before,
.link-effect.link-effect-18:hover::after,
.link-effect.link-effect-18:focus::before,
.link-effect.link-effect-18:focus::after {
    width: 100%;

    opacity: 0.7;
    background: rgba(0, 0, 0, 0.1);

    filter: alpha(opacity=70);
}

.link-effect.link-effect-18:hover::before,
.link-effect.link-effect-18:focus::before {
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}

.link-effect.link-effect-18:hover::after,
.link-effect.link-effect-18:focus::after {
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.link-effect.link-effect-19 {
    line-height: 1.8em;

    margin: 15px;

    color: white;

    -webkit-perspective: 800px;
    -moz-perspective: 800px;
    perspective: 800px;
}

.link-effect.link-effect-19 span {
    position: relative;

    display: inline-block;

    width: 100%;
    padding: 0 15px;

    -webkit-transition: -webkit-transform 0.4s, background 0.4s;
    -moz-transition: -moz-transform 0.4s, background 0.4s;
    transition: transform 0.4s, background 0.4s;
    -webkit-transform-origin: 50% 50% -100px;
    -moz-transform-origin: 50% 50% -100px;
    -ms-transform-origin: 50% 50% -100px;
    -o-transform-origin: 50% 50% -100px;
    transform-origin: 50% 50% -100px;

    background: #428BCA;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    -o-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.link-effect.link-effect-19 span::before {
    position: absolute;
    top: 0;
    left: 100%;

    width: 100%;
    height: 100%;
    padding: 0 15px;

    content: attr(data-hover);
    -webkit-transition: background 0.4s;
    -moz-transition: background 0.4s;
    transition: background 0.4s;
    -webkit-transform: rotateY(90deg);
    -moz-transform: rotateY(90deg);
    -ms-transform: rotateY(90deg);
    -o-transform: rotateY(90deg);
    transform: rotateY(90deg);
    -webkit-transform-origin: 0 50%;
    -moz-transform-origin: 0 50%;
    -ms-transform-origin: 0 50%;
    -o-transform-origin: 0 50%;
    transform-origin: 0 50%;
    pointer-events: none;

    background: #245682;
}

.link-effect.link-effect-19:hover span,
.link-effect.link-effect-19:focus span {
    -webkit-transform: rotateY(-90deg);
    -moz-transform: rotateY(-90deg);
    -ms-transform: rotateY(-90deg);
    -o-transform: rotateY(-90deg);
    transform: rotateY(-90deg);

    background: #245682;
}

.link-effect.link-effect-19:hover span::before,
.link-effect.link-effect-19:focus span::before {
    background: #428BCA;
}

.link-effect.link-effect-20 {
    line-height: 1.8em;

    -webkit-perspective: 800px;
    -moz-perspective: 800px;
    perspective: 800px;
}

.link-effect.link-effect-20 span {
    position: relative;

    display: inline-block;

    padding: 3px 15px 0;

    -webkit-transition: background 0.6s;
    -moz-transition: background 0.6s;
    transition: background 0.6s;
    -webkit-transform-origin: 0 50%;
    -moz-transform-origin: 0 50%;
    -ms-transform-origin: 0 50%;
    -o-transform-origin: 0 50%;
    transform-origin: 0 50%;

    background: #CDE1F1;
    box-shadow: inset 0 3px #92BCE0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    -o-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.link-effect.link-effect-20 span::before {
    position: absolute;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;
    padding: 3px 15px 0;

    content: attr(data-hover);
    -webkit-transition: -webkit-transform 0.6s;
    -moz-transition: -moz-transform 0.6s;
    transition: transform 0.6s;
    -webkit-transform: rotateX(270deg);
    -moz-transform: rotateX(270deg);
    -ms-transform: rotateX(270deg);
    -o-transform: rotateX(270deg);
    transform: rotateX(270deg);
    -webkit-transform-origin: 0 0;
    -moz-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    -o-transform-origin: 0 0;
    transform-origin: 0 0;
    pointer-events: none;

    color: #FFFFFF;
    background: #428BCA;
}

.link-effect.link-effect-20:hover span,
.link-effect.link-effect-20:focus span {
    background: #92BCE0;
}

.link-effect.link-effect-20:hover span::before,
.link-effect.link-effect-20:focus span::before {
    -webkit-transform: rotateX(10deg);
    -moz-transform: rotateX(10deg);
    -ms-transform: rotateX(10deg);
    -o-transform: rotateX(10deg);
    transform: rotateX(10deg);
}

.link-effect.link-effect-21 {
    font-weight: 700;

    padding: 10px;

    -webkit-transition: color 0.3s;
    -moz-transition: color 0.3s;
    transition: color 0.3s;

    color: rgba(0, 0, 0, 0.2);
}

.link-effect.link-effect-21::before,
.link-effect.link-effect-21::after {
    position: absolute;
    left: 0;

    width: 100%;
    height: 2px;

    content: "";
    -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
    -moz-transition: opacity 0.3s, -moz-transform 0.3s;
    transition: opacity 0.3s, transform 0.3s;
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);

    opacity: 0;
    background: #428BCA;

    filter: alpha(opacity=0);
}

.link-effect.link-effect-21::before {
    top: 0;

    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
}

.link-effect.link-effect-21::after {
    bottom: 0;

    -webkit-transform: translateY(10px);
    -moz-transform: translateY(10px);
    -ms-transform: translateY(10px);
    -o-transform: translateY(10px);
    transform: translateY(10px);
}

.link-effect.link-effect-21:hover,
.link-effect.link-effect-21:focus {
    color: #428BCA;
}

.link-effect.link-effect-21:hover::before,
.link-effect.link-effect-21:hover::after,
.link-effect.link-effect-21:focus::before,
.link-effect.link-effect-21:focus::after {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);

    opacity: 1;

    filter: alpha(opacity=100);
}

#loading-bar {
    position: fixed;
    z-index: 99;
    top: 0;

    width: 100%;
}

#loading-bar .bar {
    position: absolute;
    z-index: 9;

    background-color: #16A085;
}

#loading-bar .peg {
    -webkit-box-shadow: #16A085 1px 0 6px 1px;
    box-shadow: #16A085 1px 0 6px 1px;
}

#loading-bar-spinner {
    position: absolute;
    z-index: 99;
}

#loading-bar-spinner .spinner-icon {
    border-top-color: #16A085;
    border-left-color: #16A085;
}

.daterangepicker .ranges li {
    font-size: 12px;

    border-radius: 0;
}

.daterangepicker .ranges .daterangepicker_start_input label,
.daterangepicker .ranges .daterangepicker_end_input label {
    text-transform: none;

    color: #616F77;
}

.daterangepicker .ranges .input-mini {
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    transition: all 0.5s ease;

    color: #616F77;
    border-radius: 0;
}

.daterangepicker .ranges .input-mini:focus {
    border: 1px solid gray;
    outline: 0;
}

.alert {
    padding: 10px 15px;

    border: 0;
    border-left: 3px solid;
    border-radius: 0;
}

.alert.alert-big {
    padding: 15px;
}

.alert.alert-dismissable {
    padding-right: 35px;
}

.alert.notcloseable > .close {
    display: none;
}

.alert.alert-primary {
    color: #428BCA;
    background-color: #CDE1F1;
}

.alert.alert-primary .alert-link {
    color: #357EBD;
}

.alert.alert-success {
    color: #5CB85C;
    background-color: #EAF6EA;
}

.alert.alert-success .alert-link {
    color: #4CAE4C;
}

.alert.alert-warning {
    color: #F0AD4E;
    background-color: #FEF9F3;
}

.alert.alert-warning .alert-link {
    color: #EEA236;
}

.alert.alert-danger {
    color: #D9534F;
    background-color: #FDF7F7;
}

.alert.alert-danger .alert-link {
    color: #D43F3A;
}

.alert.alert-info {
    color: #5BC0DE;
    background-color: #F0F9FC;
}

.alert.alert-info .alert-link {
    color: #46B8DA;
}

.alert.alert-default {
    color: #616F77;
    background-color: #DBE0E2;
}

.alert.alert-default .alert-link {
    color: #566269;
}

.alert.alert-cyan {
    color: #22BEEF;
    background-color: #E0F6FD;
}

.alert.alert-cyan .alert-link {
    color: #10ACDD;
}

.alert.alert-amethyst {
    color: #CD97EB;
    background-color: #F6EDFB;
}

.alert.alert-amethyst .alert-link {
    color: #C382E7;
}

.alert.alert-green {
    color: #A2D200;
    background-color: #F5FFD2;
}

.alert.alert-green .alert-link {
    color: #96C300;
}

.alert.alert-orange {
    color: #FFC100;
    background-color: #FFF3CC;
}

.alert.alert-orange .alert-link {
    color: #F0B500;
}

.alert.alert-red {
    color: #FF4A43;
    background-color: #FFDDDC;
}

.alert.alert-red .alert-link {
    color: #FF1910;
}

.alert.alert-greensea {
    color: #16A085;
    background-color: #D5F9F2;
}

.alert.alert-greensea .alert-link {
    color: #138A72;
}

.alert.alert-dutch {
    color: #1693A5;
    background-color: #DAF6FA;
}

.alert.alert-dutch .alert-link {
    color: #137F8F;
}

.alert.alert-hotpink {
    color: #FF0066;
    background-color: #FFE5F0;
}

.alert.alert-hotpink .alert-link {
    color: #E6005C;
}

.alert.alert-drank {
    color: #A40778;
    background-color: #FDC7EE;
}

.alert.alert-drank .alert-link {
    color: #8C0666;
}

.alert.alert-blue {
    color: #418BCA;
    background-color: #E1ECF6;
}

.alert.alert-blue .alert-link {
    color: #357EBD;
}

.alert.alert-lightred {
    color: #E05D6F;
    background-color: #F9DDE1;
}

.alert.alert-lightred .alert-link {
    color: #DC485C;
}

.alert.alert-slategray {
    color: #3F4E62;
    background-color: #B6C1D0;
}

.alert.alert-slategray .alert-link {
    color: #354252;
}

.alert.alert-darkgray {
    color: #333333;
    background-color: #A6A6A6;
}

.alert.alert-darkgray .alert-link {
    color: #262626;
}

#toast-container > div {
    position: relative;

    -webkit-transition: opacity 0.25s;
    -moz-transition: opacity 0.25s;
    transition: opacity 0.25s;

    opacity: 0.8 !important;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

#toast-container > div:hover {
    opacity: 1 !important;
    -webkit-box-shadow: none;
    box-shadow: none;
}

#toast-container > div .toast-title {
    font-family: "Dosis", "Arial", sans-serif;
}

#toast-container > div .toast-message {
    font-size: 12px;
}

#toast-container > div > .fa:not(.toast-close-button) {
    font-size: 2em;

    position: absolute;
    left: 13px;
}

#toast-container .toast-close-button {
    font-size: 12px;
    font-weight: 400;

    opacity: 1;
    text-shadow: none;
}

#toast-container .toast-close-button:hover {
    opacity: 0.5;
    color: white;
    text-shadow: none;
}

.nav-tabs {
    border: 0;
    background-color: #F5F5F5;
}

.nav-tabs > li {
    margin-bottom: 0;
}

.nav-tabs > li > a {
    margin-right: 0;

    cursor: pointer;

    border: 0;
    border-radius: 0;
    -webkit-box-shadow: inset 0 -3px 0 transparent;
    box-shadow: inset 0 -3px 0 transparent;
}

.nav-tabs > li > a:hover {
    border: 0;
    background-color: #F0F0F0;
}

.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
    border: 0;
    background-color: #E8E8E8;
    box-shadow: inset 0 -3px 0 #428BCA;
}

.nav-tabs > li.active:after {
    position: absolute;
    left: 50%;

    width: 0;
    height: 0;
    margin-left: -4px;

    content: "";

    border-top: 4px solid #428BCA;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
}

.nav-tabs > li.tabs-title {
    font-size: 16px;
    font-weight: 300;

    padding: 8px 0 0 20px;

    color: white;
}

.nav-tabs.nav-justified > li > a {
    border: 0;
    border-radius: 0;
}

.nav-tabs.nav-justified > li.active > a,
.nav-tabs.nav-justified > li.active > a:hover,
.nav-tabs.nav-justified > li.active > a:focus {
    border: 0;
}

.tab-content .tab-pane {
    padding: 15px;
}

.nav-pills > li > a {
    cursor: pointer;
}

.nav.nav-sm > li > a {
    padding: 7px 10px;
}

.nav-tabs.tabs-dark {
    background-color: #3F4E62;
}

.nav-tabs.tabs-dark > li > a {
    color: rgba(255, 255, 255, 0.5);
}

.nav-tabs.tabs-dark > li > a:hover,
.nav-tabs.tabs-dark > li > a:focus {
    color: rgba(255, 255, 255, 0.8);
    background-color: #354252;
}

.nav-tabs.tabs-dark > li.disabled > a {
    color: rgba(255, 255, 255, 0.2);
}

.nav-tabs.tabs-dark > li.active > a,
.nav-tabs.tabs-dark > li.active > a:hover,
.nav-tabs.tabs-dark > li.active > a:focus {
    color: white;
    background-color: #2B3543;
}

.tab-container .nav-tabs {
    background-color: white;
}

.tab-container .nav-tabs > li {
    border-right: 1px solid #EAEAEA;
}

.tab-container .nav-tabs > li:last-child {
    border-right: 0;
}

.tab-container .nav-tabs > li > a {
    color: #95A2A9;
}

.tab-container .nav-tabs > li > a:hover {
    background-color: #F5F5F5;
}

.tab-container .nav-tabs > li.active > a,
.tab-container .nav-tabs > li.active > a:hover,
.tab-container .nav-tabs > li.active > a:focus {
    color: #4A555B;
}

.tab-container .nav-tabs > li.disabled > a,
.tab-container .nav-tabs > li.disabled > a:hover,
.tab-container .nav-tabs > li.disabled > a:focus {
    color: #CDD3D7;
}

.tab-container .tab-content .tab-pane {
    background-color: white;
}

.tab-nopadding .tab-pane {
    padding: 15px 0;
}

.tab-wizard .nav-tabs > li > a {
    position: relative;

    padding-left: 30px;

    text-align: left;
}

.tab-wizard .nav-tabs > li > a:before {
    position: absolute;
    z-index: 6;
    top: 1px;
    right: -18px;

    width: 0;
    height: 0;

    content: "";
    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    transition: all 0.2s;

    border-top: 19px solid transparent;
    border-bottom: 19px solid transparent;
    border-left: 19px solid #F5F5F5;
}

.tab-wizard .nav-tabs > li > a:after {
    position: absolute;
    z-index: 5;
    top: 0;
    right: -20px;

    width: 0;
    height: 0;

    content: "";

    border-top: 20px solid transparent;
    border-bottom: 20px solid transparent;
    border-left: 20px solid #F5F5F5;
}

.tab-wizard .nav-tabs > li > a .wizard-step {
    font-size: 14px;
    font-weight: 700;
    line-height: 14px;

    width: 26px;
    height: 26px;
    margin-top: -2px;
    margin-right: -10px;
    padding: 5px 8px;

    color: #EAEAEA;
    border: 1px solid #EAEAEA;
    border-radius: 50%;
    background-color: #4A555B;
}

.tab-wizard .nav-tabs > li:first-child > a {
    padding-left: 15px;
}

.tab-wizard .nav-tabs > li:last-child > a:after,
.tab-wizard .nav-tabs > li:last-child > a:before {
    display: none;
}

.tab-wizard .nav-tabs > li:last-child > a .wizard-step {
    margin-right: 0;
}

.tab-wizard .nav-tabs > li > a,
.tab-wizard .nav-tabs > li > a:hover,
.tab-wizard .nav-tabs > li > a:focus {
    background-color: #F5F5F5;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.tab-wizard .nav-tabs > li:after,
.tab-wizard .nav-tabs > li:before {
    position: absolute;
    left: 0;

    width: 100%;
    height: 10px;
    margin-left: 0;

    content: "";
    -webkit-transition: opacity 0.8s;
    -moz-transition: opacity 0.8s;
    transition: opacity 0.8s;

    opacity: 0;
    border: 0;
    background-color: #798992;
}

.tab-wizard .nav-tabs > li:after {
    opacity: 0.1;
}

.tab-wizard .nav-tabs > li:before {
    bottom: -10px;

    -webkit-transition: width 0.8s;
    -moz-transition: width 0.8s;
    transition: width 0.8s;

    opacity: 1;
    background-color: #428BCA;
}

.tab-wizard .nav-tabs > li.active > a:before {
    border-left-color: white;
}

.tab-wizard .nav-tabs > li.active > a .wizard-step {
    color: #4A555B;
    border-color: #4A555B;
    background-color: transparent;
}

.tab-wizard .nav-tabs > li.active > a,
.tab-wizard .nav-tabs > li.active > a:hover {
    background-color: white;
}

.tab-wizard .nav-tabs > li.active:after {
    opacity: 1;
}

.tab-wizard .nav-tabs > li.active:before {
    width: 0;

    opacity: 0;
}

.tab-wizard .nav-tabs > li.disabled > a .wizard-step {
    color: #EAEAEA;
    border-color: #EAEAEA;
    background-color: transparent;
}

.tab-wizard .nav-tabs > li.active ~ li > a:before {
    border-left-color: white;
}

.tab-wizard .nav-tabs > li.active ~ li > a .wizard-step {
    color: #EAEAEA;
    border-color: #EAEAEA;
    background-color: transparent;
}

.tab-wizard .nav-tabs > li.active ~ li > a,
.tab-wizard .nav-tabs > li.active ~ li > a:hover {
    background-color: white;
}

.tab-wizard .nav-tabs > li.active ~ li:before {
    width: 0;

    opacity: 0;
}

.tab-wizard .tab-content {
    background-color: white;
}

.tab-wizard .tab-content .tab-pane {
    margin-top: 10px;
}

.tab-wizard .tab-content .pager.wizard {
    margin: 0;
    padding: 0 15px 15px;
}

.tab-wizard .tab-content .pager.wizard > li.disabled > .btn {
    cursor: not-allowed;

    color: #CCCCCC;
    border-color: #EEEEEE;
}

.tab-wizard .tab-content .pager.wizard > li.disabled > .btn:hover {
    background-color: white;
}

.tab-wizard .tab-content .pager.wizard > li.previous > .btn {
    float: left;
}

.tab-wizard .tab-content .pager.wizard > li.next > .btn {
    float: right;
}

.tab-animation > .tab-content {
    position: relative;
}

.tab-animation > .tab-content > .tab-pane {
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    transition: all 0.2s linear;
}

.tab-animation > .tab-content > .tab-pane.active-remove {
    position: absolute;
    top: 0;

    display: block;

    width: 100%;
}

.tab-animation > .tab-content > .tab-pane.active-remove-active {
    opacity: 0;
}

.tab-animation > .tab-content > .tab-pane.active-add {
    opacity: 0;
}

.tabs-right.nav-tabs {
    text-align: right;
}

.tabs-right.nav-tabs > li {
    display: inline-block;
    float: none;

    margin-right: -3px;
}

.tabs-right.nav-tabs > li:last-child {
    margin-right: 0;
}

@media only screen and (max-width: 767px) {
    .nav-tabs.nav-justified > li a {
        margin-bottom: 0;
    }

    .nav-tabs.nav-justified > li:after {
        display: none;
    }

    .nav-tabs > li.tabs-title {
        display: none;
    }
}

.tabs-menu {
    margin: 0;
    padding: 0;

    list-style: none;
}

.tabs-menu > li {
    position: relative;

    margin-bottom: 1px;
}

.tabs-menu > li > a {
    display: block;

    padding: 10px 15px;

    color: #616F77;
    background-color: #F7F7F7;
}

.tabs-menu > li > a:hover {
    color: #428BCA;
}

.tabs-menu > li.active > a {
    font-weight: 700;

    color: white;
    background-color: #428BCA;
}

.tabs-menu > li.active:after {
    position: absolute;
    top: 15px;
    right: -6px;

    width: 0;
    height: 0;

    content: "";

    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 6px solid #428BCA;
}

.modal-header {
    padding: 10px 15px;
}

.modal-content {
    border-radius: 0;
}

.modal-footer {
    padding: 8px 15px;

    background-color: #F8F8F8;
}

.modal-backdrop {
    bottom: 0;
}

.modal.splash.fade {
    opacity: 1;

    filter: alpha(opacity=100);
}

.modal-open.splash .modal-backdrop.fade {
    opacity: 0.5;

    filter: alpha(opacity=50);
}

.modal.splash.fade .modal-dialog,
.modal.splash.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
}

.modal-open.splash {
    /* Splash Effect 2: Fade in and scale up */
    /* Splash Effect 3: Slide from the right  */
    /* Splash Effect 4: Slide from the bottom  */
    /* Splash Effect 5: Newspaper  */
    /* Splash Effect 6: Fall  */
    /* Splash Effect 7: Side Fall  */
    /* Splash Effect 8: slide and stick to top  */
    /* Splash Effect 9: 3D flip horizontal  */
    /* Splash Effect 10: 3D flip vertical  */
    /* Splash Effect 11: 3D Sign  */
    /* Splash Effect 12: Super Scaled  */
    /* Splash Effect 13: Just me  */
    /* Splash Effect 14: 3D Slit */
    /* Splash Effect 15: 3D Rotate from bottom */
    /* Splash Effect 16:3D Rotate in from left */
}

.modal-open.splash .modal-dialog {
    margin-top: 60px;
}

.modal-open.splash .modal-content {
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.modal-open.splash .modal-backdrop {
    background-color: black;
}

.modal-open.splash .modal-backdrop.fade {
    opacity: 0;

    filter: alpha(opacity=0);
}

.modal-open.splash .modal-backdrop.in {
    opacity: 0.85;

    filter: alpha(opacity=85);
}

.modal-open.splash.modal {
    -webkit-perspective: 1000px;
    -moz-perspective: 1000px;
    perspective: 1000px;
}

.modal-open.splash.splash-1 .modal-content {
    color: white;
    background-color: transparent;
}

.modal-open.splash.splash-1 .modal-header {
    text-align: center;

    border-bottom: 0;
}

.modal-open.splash.splash-1 .modal-header .modal-title {
    font-weight: 700;
}

.modal-open.splash.splash-1 .modal-footer {
    text-align: center;

    border-top: 0;
    background-color: transparent;
}

.modal-open.splash.splash-1 .modal-footer .btn-default {
    color: white;
    border: 2px solid white;
}

.modal-open.splash.splash-1 .modal-footer .btn-default:hover,
.modal-open.splash.splash-1 .modal-footer .btn-default:active,
.modal-open.splash.splash-1 .modal-footer .btn-default.active {
    background-color: rgba(255, 255, 255, 0.1);
}

.modal-open.splash.splash-2 .modal-header {
    text-align: center;

    background-color: #F5F5F5;
}

.modal-open.splash.splash-2 .modal-header .modal-title {
    font-weight: 700;
}

.modal-open.splash.splash-2 .modal-footer {
    text-align: center;

    border-top: 0;
    background-color: white;
}

.modal-open.splash.splash-ef-1 .modal-backdrop {
    -webkit-transition: opacity 0.5s;
    -moz-transition: opacity 0.5s;
    transition: opacity 0.5s;
}

.modal-open.splash.splash-ef-1 .modal .modal-dialog {
    -webkit-transition: opacity 0.5s, -webkit-transform 0.5s;
    -moz-transition: opacity 0.5s, -moz-transform 0.5s;
    transition: opacity 0.5s, transform 0.5s;
}

.modal-open.splash.splash-ef-1 .modal.fade .modal-dialog {
    -webkit-transform: translate3d(0, 0, 150px);
    -moz-transform: translate3d(0, 0, 150px);
    -ms-transform: translate3d(0, 0, 150px);
    -o-transform: translate3d(0, 0, 150px);
    transform: translate3d(0, 0, 150px);

    opacity: 0;

    filter: alpha(opacity=0);
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    -o-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.modal-open.splash.splash-ef-1 .modal.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);

    opacity: 1;

    filter: alpha(opacity=100);
}

.modal-open.splash.splash-ef-2 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-2 .modal .modal-dialog {
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.modal-open.splash.splash-ef-2 .modal.fade .modal-dialog {
    -webkit-transform: scale(0.7);
    -moz-transform: scale(0.7);
    -ms-transform: scale(0.7);
    transform: scale(0.7);

    opacity: 0;
}

.modal-open.splash.splash-ef-2 .modal.in .modal-dialog {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);

    opacity: 1;
}

.modal-open.splash.splash-ef-3 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-3 .modal .modal-dialog {
    -webkit-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
    -moz-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
    transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
}

.modal-open.splash.splash-ef-3 .modal.fade .modal-dialog {
    -webkit-transform: translateX(20%);
    -moz-transform: translateX(20%);
    -ms-transform: translateX(20%);
    transform: translateX(20%);

    opacity: 0;
}

.modal-open.splash.splash-ef-3 .modal.in .modal-dialog {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);

    opacity: 1;
}

.modal-open.splash.splash-ef-4 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-4 .modal .modal-dialog {
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.modal-open.splash.splash-ef-4 .modal.fade .modal-dialog {
    -webkit-transform: translateY(20%);
    -moz-transform: translateY(20%);
    -ms-transform: translateY(20%);
    transform: translateY(20%);

    opacity: 0;
}

.modal-open.splash.splash-ef-4 .modal.in .modal-dialog {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);

    opacity: 1;
}

.modal-open.splash.splash-ef-5 .modal-backdrop {
    -webkit-transition: opacity 0.5s;
    -moz-transition: opacity 0.5s;
    transition: opacity 0.5s;
}

.modal-open.splash.splash-ef-5 .modal .modal-dialog {
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    transition: all 0.5s;
}

.modal-open.splash.splash-ef-5 .modal.fade .modal-dialog {
    -webkit-transform: scale(0) rotate(720deg);
    -moz-transform: scale(0) rotate(720deg);
    -ms-transform: scale(0) rotate(720deg);
    transform: scale(0) rotate(720deg);

    opacity: 0;
}

.modal-open.splash.splash-ef-5 .modal.in .modal-dialog {
    -webkit-transform: scale(1) rotate(0deg);
    -moz-transform: scale(1) rotate(0deg);
    -ms-transform: scale(1) rotate(0deg);
    transform: scale(1) rotate(0deg);

    opacity: 1;
}

.modal-open.splash.splash-ef-6 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-6 .modal {
    -webkit-perspective: 1300px;
    -moz-perspective: 1300px;
    perspective: 1300px;
}

.modal-open.splash.splash-ef-6 .modal.fade .modal-dialog {
    -webkit-transform: translateZ(600px) rotateX(20deg);
    -moz-transform: translateZ(600px) rotateX(20deg);
    -ms-transform: translateZ(600px) rotateX(20deg);
    transform: translateZ(600px) rotateX(20deg);

    opacity: 0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.modal-open.splash.splash-ef-6 .modal.in .modal-dialog {
    -webkit-transition: all 0.3s ease-in;
    -moz-transition: all 0.3s ease-in;
    transition: all 0.3s ease-in;
    -webkit-transform: translateZ(0px) rotateX(0deg);
    -moz-transform: translateZ(0px) rotateX(0deg);
    -ms-transform: translateZ(0px) rotateX(0deg);
    transform: translateZ(0px) rotateX(0deg);

    opacity: 1;
}

.modal-open.splash.splash-ef-7 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-7 .modal {
    -webkit-perspective: 1300px;
    -moz-perspective: 1300px;
    perspective: 1300px;
}

.modal-open.splash.splash-ef-7 .modal.fade .modal-dialog {
    -webkit-transform: translate(30%) translateZ(600px) rotate(10deg);
    -moz-transform: translate(30%) translateZ(600px) rotate(10deg);
    -ms-transform: translate(30%) translateZ(600px) rotate(10deg);
    transform: translate(30%) translateZ(600px) rotate(10deg);

    opacity: 0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.modal-open.splash.splash-ef-7 .modal.in .modal-dialog {
    -webkit-transition: all 0.3s ease-in;
    -moz-transition: all 0.3s ease-in;
    transition: all 0.3s ease-in;
    -webkit-transform: translate(0%) translateZ(0) rotate(0deg);
    -moz-transform: translate(0%) translateZ(0) rotate(0deg);
    -ms-transform: translate(0%) translateZ(0) rotate(0deg);
    transform: translate(0%) translateZ(0) rotate(0deg);

    opacity: 1;
}

.modal-open.splash.splash-ef-8 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-8 .modal .modal-dialog {
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.modal-open.splash.splash-ef-8 .modal.fade .modal-dialog {
    -webkit-transform: translateY(-200%);
    -moz-transform: translateY(-200%);
    -ms-transform: translateY(-200%);
    transform: translateY(-200%);

    opacity: 0;
}

.modal-open.splash.splash-ef-8 .modal.in .modal-dialog {
    -webkit-transform: translateY(0%);
    -moz-transform: translateY(0%);
    -ms-transform: translateY(0%);
    transform: translateY(0%);

    opacity: 1;
    border-radius: 0 0 3px 3px;
}

.modal-open.splash.splash-ef-9 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-9 .modal {
    -webkit-perspective: 1300px;
    -moz-perspective: 1300px;
    perspective: 1300px;
}

.modal-open.splash.splash-ef-9 .modal .modal-dialog {
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.modal-open.splash.splash-ef-9 .modal.fade .modal-dialog {
    -webkit-transform: rotateY(-70deg);
    -moz-transform: rotateY(-70deg);
    -ms-transform: rotateY(-70deg);
    transform: rotateY(-70deg);

    opacity: 0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.modal-open.splash.splash-ef-9 .modal.in .modal-dialog {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    transform: rotateY(0deg);

    opacity: 1;
}

.modal-open.splash.splash-ef-10 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-10 .modal {
    -webkit-perspective: 1300px;
    -moz-perspective: 1300px;
    perspective: 1300px;
}

.modal-open.splash.splash-ef-10 .modal .modal-dialog {
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.modal-open.splash.splash-ef-10 .modal.fade .modal-dialog {
    -webkit-transform: rotateX(-70deg);
    -moz-transform: rotateX(-70deg);
    -ms-transform: rotateX(-70deg);
    transform: rotateX(-70deg);

    opacity: 0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.modal-open.splash.splash-ef-10 .modal.in .modal-dialog {
    -webkit-transform: rotateX(0deg);
    -moz-transform: rotateX(0deg);
    -ms-transform: rotateX(0deg);
    transform: rotateX(0deg);

    opacity: 1;
}

.modal-open.splash.splash-ef-11 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-11 .modal {
    -webkit-perspective: 1300px;
    -moz-perspective: 1300px;
    perspective: 1300px;
}

.modal-open.splash.splash-ef-11 .modal .modal-dialog {
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.modal-open.splash.splash-ef-11 .modal.fade .modal-dialog {
    -webkit-transform: rotateX(-60deg);
    -moz-transform: rotateX(-60deg);
    -ms-transform: rotateX(-60deg);
    transform: rotateX(-60deg);
    -webkit-transform-origin: 50% 0;
    -moz-transform-origin: 50% 0;
    transform-origin: 50% 0;

    opacity: 0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.modal-open.splash.splash-ef-11 .modal.in .modal-dialog {
    -webkit-transform: rotateX(0deg);
    -moz-transform: rotateX(0deg);
    -ms-transform: rotateX(0deg);
    transform: rotateX(0deg);

    opacity: 1;
}

.modal-open.splash.splash-ef-12 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-12 .modal .modal-dialog {
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.modal-open.splash.splash-ef-12 .modal.fade .modal-dialog {
    -webkit-transform: scale(2);
    -moz-transform: scale(2);
    -ms-transform: scale(2);
    transform: scale(2);

    opacity: 0;
}

.modal-open.splash.splash-ef-12 .modal.in .modal-dialog {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);

    opacity: 1;
}

.modal-open.splash.splash-ef-13 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-13 .modal-backdrop.in {
    opacity: 1;
}

.modal-open.splash.splash-ef-13 .modal .modal-dialog {
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.modal-open.splash.splash-ef-13 .modal.fade .modal-dialog {
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    transform: scale(0.8);

    opacity: 0;
}

.modal-open.splash.splash-ef-13 .modal.in .modal-dialog {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);

    opacity: 1;
}

.modal-open.splash.splash-ef-13 .modal .modal-content {
    color: white;
    background-color: transparent;
}

.modal-open.splash.splash-ef-13 .modal .modal-content .modal-header,
.modal-open.splash.splash-ef-13 .modal .modal-content .modal-body,
.modal-open.splash.splash-ef-13 .modal .modal-content .modal-footer {
    border: 0;
    background-color: transparent;
}

.modal-open.splash.splash-ef-13 .modal .modal-content .btn-default {
    color: white;
    border: 2px solid white;
}

.modal-open.splash.splash-ef-13 .modal .modal-content .btn-default:hover,
.modal-open.splash.splash-ef-13 .modal .modal-content .btn-default:active,
.modal-open.splash.splash-ef-13 .modal .modal-content .btn-default.active {
    background-color: rgba(255, 255, 255, 0.1);
}

.modal-open.splash.splash-ef-13 .modal .modal-content a {
    cursor: pointer;

    color: rgba(255, 255, 255, 0.6);
}

.modal-open.splash.splash-ef-13 .modal .modal-content a:hover {
    color: white;
}

.modal-open.splash.splash-ef-14 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-14 .modal {
    -webkit-perspective: 1300px;
    -moz-perspective: 1300px;
    perspective: 1300px;
}

.modal-open.splash.splash-ef-14 .modal.fade .modal-dialog {
    -webkit-transform: translateZ(-3000px) rotateY(90deg);
    -moz-transform: translateZ(-3000px) rotateY(90deg);
    -ms-transform: translateZ(-3000px) rotateY(90deg);
    transform: translateZ(-3000px) rotateY(90deg);

    opacity: 0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.modal-open.splash.splash-ef-14 .modal.in .modal-dialog {
    -webkit-animation: slit 0.7s forwards ease-out;
    -moz-animation: slit 0.7s forwards ease-out;
    animation: slit 0.7s forwards ease-out;
}

.modal-open.splash.splash-ef-15 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-15 .modal {
    -webkit-perspective: 1300px;
    -moz-perspective: 1300px;
    perspective: 1300px;
}

.modal-open.splash.splash-ef-15 .modal .modal-dialog {
    -webkit-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}

.modal-open.splash.splash-ef-15 .modal.fade .modal-dialog {
    -webkit-transform: translateY(100%) rotateX(90deg);
    -moz-transform: translateY(100%) rotateX(90deg);
    -ms-transform: translateY(100%) rotateX(90deg);
    transform: translateY(100%) rotateX(90deg);
    -webkit-transform-origin: 0 100%;
    -moz-transform-origin: 0 100%;
    transform-origin: 0 100%;

    opacity: 0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.modal-open.splash.splash-ef-15 .modal.in .modal-dialog {
    -webkit-transform: translateY(0%) rotateX(0deg);
    -moz-transform: translateY(0%) rotateX(0deg);
    -ms-transform: translateY(0%) rotateX(0deg);
    transform: translateY(0%) rotateX(0deg);

    opacity: 1;
}

.modal-open.splash.splash-ef-16 .modal-backdrop {
    -webkit-transition: opacity 0.3s;
    -moz-transition: opacity 0.3s;
    transition: opacity 0.3s;
}

.modal-open.splash.splash-ef-16 .modal {
    -webkit-perspective: 1300px;
    -moz-perspective: 1300px;
    perspective: 1300px;
}

.modal-open.splash.splash-ef-16 .modal .modal-dialog {
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.modal-open.splash.splash-ef-16 .modal.fade .modal-dialog {
    -webkit-transform: translateZ(100px) translateX(-30%) rotateY(90deg);
    -moz-transform: translateZ(100px) translateX(-30%) rotateY(90deg);
    -ms-transform: translateZ(100px) translateX(-30%) rotateY(90deg);
    transform: translateZ(100px) translateX(-30%) rotateY(90deg);
    -webkit-transform-origin: 0 100%;
    -moz-transform-origin: 0 100%;
    transform-origin: 0 100%;

    opacity: 0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.modal-open.splash.splash-ef-16 .modal.in .modal-dialog {
    -webkit-transform: translateZ(0px) translateX(0%) rotateY(0deg);
    -moz-transform: translateZ(0px) translateX(0%) rotateY(0deg);
    -ms-transform: translateZ(0px) translateX(0%) rotateY(0deg);
    transform: translateZ(0px) translateX(0%) rotateY(0deg);

    opacity: 1;
}

.modal-open.splash.splash-primary.splash-2 .modal-content,
.modal-open.splash.splash-success.splash-2 .modal-content,
.modal-open.splash.splash-warning.splash-2 .modal-content,
.modal-open.splash.splash-danger.splash-2 .modal-content,
.modal-open.splash.splash-info.splash-2 .modal-content,
.modal-open.splash.splash-cyan.splash-2 .modal-content,
.modal-open.splash.splash-amethyst.splash-2 .modal-content,
.modal-open.splash.splash-green.splash-2 .modal-content,
.modal-open.splash.splash-orange.splash-2 .modal-content,
.modal-open.splash.splash-red.splash-2 .modal-content,
.modal-open.splash.splash-greensea.splash-2 .modal-content,
.modal-open.splash.splash-dutc.splash-2h .modal-content,
.modal-open.splash.splash-hotpink.splash-2 .modal-content,
.modal-open.splash.splash-drank.splash-2 .modal-content,
.modal-open.splash.splash-blue.splash-2 .modal-content,
.modal-open.splash.splash-lightred.splash-2 .modal-content,
.modal-open.splash.splash-slategray.splash-2 .modal-content,
.modal-open.splash.splash-darkgray.splash-2 .modal-content {
    color: white;
}

.modal-open.splash.splash-primary.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-success.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-warning.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-danger.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-info.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-cyan.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-amethyst.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-green.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-orange.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-red.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-greensea.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-dutc.splash-2h .modal-content .btn-default,
.modal-open.splash.splash-hotpink.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-drank.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-blue.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-lightred.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-slategray.splash-2 .modal-content .btn-default,
.modal-open.splash.splash-darkgray.splash-2 .modal-content .btn-default {
    color: white;
    border: 2px solid white;
}

.modal-open.splash.splash-primary.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-primary.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-primary.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-success.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-success.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-success.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-warning.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-warning.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-warning.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-danger.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-danger.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-danger.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-info.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-info.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-info.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-cyan.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-cyan.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-cyan.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-amethyst.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-amethyst.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-amethyst.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-green.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-green.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-green.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-orange.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-orange.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-orange.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-red.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-red.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-red.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-greensea.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-greensea.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-greensea.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-dutc.splash-2h .modal-content .btn-default:hover,
.modal-open.splash.splash-dutc.splash-2h .modal-content .btn-default:active,
.modal-open.splash.splash-dutc.splash-2h .modal-content .btn-default.active,
.modal-open.splash.splash-hotpink.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-hotpink.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-hotpink.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-drank.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-drank.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-drank.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-blue.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-blue.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-blue.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-lightred.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-lightred.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-lightred.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-slategray.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-slategray.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-slategray.splash-2 .modal-content .btn-default.active,
.modal-open.splash.splash-darkgray.splash-2 .modal-content .btn-default:hover,
.modal-open.splash.splash-darkgray.splash-2 .modal-content .btn-default:active,
.modal-open.splash.splash-darkgray.splash-2 .modal-content .btn-default.active {
    background-color: rgba(255, 255, 255, 0.1);
}

.modal-open.splash.splash-primary.splash-2 .modal-content a,
.modal-open.splash.splash-success.splash-2 .modal-content a,
.modal-open.splash.splash-warning.splash-2 .modal-content a,
.modal-open.splash.splash-danger.splash-2 .modal-content a,
.modal-open.splash.splash-info.splash-2 .modal-content a,
.modal-open.splash.splash-cyan.splash-2 .modal-content a,
.modal-open.splash.splash-amethyst.splash-2 .modal-content a,
.modal-open.splash.splash-green.splash-2 .modal-content a,
.modal-open.splash.splash-orange.splash-2 .modal-content a,
.modal-open.splash.splash-red.splash-2 .modal-content a,
.modal-open.splash.splash-greensea.splash-2 .modal-content a,
.modal-open.splash.splash-dutc.splash-2h .modal-content a,
.modal-open.splash.splash-hotpink.splash-2 .modal-content a,
.modal-open.splash.splash-drank.splash-2 .modal-content a,
.modal-open.splash.splash-blue.splash-2 .modal-content a,
.modal-open.splash.splash-lightred.splash-2 .modal-content a,
.modal-open.splash.splash-slategray.splash-2 .modal-content a,
.modal-open.splash.splash-darkgray.splash-2 .modal-content a {
    cursor: pointer;

    color: rgba(255, 255, 255, 0.6);
}

.modal-open.splash.splash-primary.splash-2 .modal-content a:hover,
.modal-open.splash.splash-success.splash-2 .modal-content a:hover,
.modal-open.splash.splash-warning.splash-2 .modal-content a:hover,
.modal-open.splash.splash-danger.splash-2 .modal-content a:hover,
.modal-open.splash.splash-info.splash-2 .modal-content a:hover,
.modal-open.splash.splash-cyan.splash-2 .modal-content a:hover,
.modal-open.splash.splash-amethyst.splash-2 .modal-content a:hover,
.modal-open.splash.splash-green.splash-2 .modal-content a:hover,
.modal-open.splash.splash-orange.splash-2 .modal-content a:hover,
.modal-open.splash.splash-red.splash-2 .modal-content a:hover,
.modal-open.splash.splash-greensea.splash-2 .modal-content a:hover,
.modal-open.splash.splash-dutc.splash-2h .modal-content a:hover,
.modal-open.splash.splash-hotpink.splash-2 .modal-content a:hover,
.modal-open.splash.splash-drank.splash-2 .modal-content a:hover,
.modal-open.splash.splash-blue.splash-2 .modal-content a:hover,
.modal-open.splash.splash-lightred.splash-2 .modal-content a:hover,
.modal-open.splash.splash-slategray.splash-2 .modal-content a:hover,
.modal-open.splash.splash-darkgray.splash-2 .modal-content a:hover {
    color: white;
}

.modal-open.splash.splash-primary .modal-backdrop {
    background-color: #428BCA;
}

.modal-open.splash.splash-primary .splash-2 .modal-content,
.modal-open.splash.splash-primary .splash-2 .modal-footer {
    background-color: #428BCA;
}

.modal-open.splash.splash-primary .splash-2 .modal-header {
    border-color: #3071A9;
    background-color: #357EBD;
}

.modal-open.splash.splash-success .modal-backdrop {
    background-color: #5CB85C;
}

.modal-open.splash.splash-success .splash-2 .modal-content,
.modal-open.splash.splash-success .splash-2 .modal-footer {
    background-color: #5CB85C;
}

.modal-open.splash.splash-success .splash-2 .modal-header {
    border-color: #449D44;
    background-color: #4CAE4C;
}

.modal-open.splash.splash-warning .modal-backdrop {
    background-color: #F0AD4E;
}

.modal-open.splash.splash-warning .splash-2 .modal-content,
.modal-open.splash.splash-warning .splash-2 .modal-footer {
    background-color: #F0AD4E;
}

.modal-open.splash.splash-warning .splash-2 .modal-header {
    border-color: #EC971F;
    background-color: #EEA236;
}

.modal-open.splash.splash-danger .modal-backdrop {
    background-color: #D9534F;
}

.modal-open.splash.splash-danger .splash-2 .modal-content,
.modal-open.splash.splash-danger .splash-2 .modal-footer {
    background-color: #D9534F;
}

.modal-open.splash.splash-danger .splash-2 .modal-header {
    border-color: #C9302C;
    background-color: #D43F3A;
}

.modal-open.splash.splash-info .modal-backdrop {
    background-color: #5BC0DE;
}

.modal-open.splash.splash-info .splash-2 .modal-content,
.modal-open.splash.splash-info .splash-2 .modal-footer {
    background-color: #5BC0DE;
}

.modal-open.splash.splash-info .splash-2 .modal-header {
    border-color: #31B0D5;
    background-color: #46B8DA;
}

.modal-open.splash.splash-cyan .modal-backdrop {
    background-color: #22BEEF;
}

.modal-open.splash.splash-cyan .splash-2 .modal-content,
.modal-open.splash.splash-cyan .splash-2 .modal-footer {
    background-color: #22BEEF;
}

.modal-open.splash.splash-cyan .splash-2 .modal-header {
    border-color: #0FA1CF;
    background-color: #11B4E7;
}

.modal-open.splash.splash-amethyst .modal-backdrop {
    background-color: #CD97EB;
}

.modal-open.splash.splash-amethyst .splash-2 .modal-content,
.modal-open.splash.splash-amethyst .splash-2 .modal-footer {
    background-color: #CD97EB;
}

.modal-open.splash.splash-amethyst .splash-2 .modal-header {
    border-color: #B86CE3;
    background-color: #C382E7;
}

.modal-open.splash.splash-green .modal-backdrop {
    background-color: #A2D200;
}

.modal-open.splash.splash-green .splash-2 .modal-content,
.modal-open.splash.splash-green .splash-2 .modal-footer {
    background-color: #A2D200;
}

.modal-open.splash.splash-green .splash-2 .modal-header {
    border-color: #7B9F00;
    background-color: #8EB800;
}

.modal-open.splash.splash-orange .modal-backdrop {
    background-color: #FFC100;
}

.modal-open.splash.splash-orange .splash-2 .modal-content,
.modal-open.splash.splash-orange .splash-2 .modal-footer {
    background-color: #FFC100;
}

.modal-open.splash.splash-orange .splash-2 .modal-header {
    border-color: #CC9A00;
    background-color: #E6AE00;
}

.modal-open.splash.splash-red .modal-backdrop {
    background-color: #FF4A43;
}

.modal-open.splash.splash-red .splash-2 .modal-content,
.modal-open.splash.splash-red .splash-2 .modal-footer {
    background-color: #FF4A43;
}

.modal-open.splash.splash-red .splash-2 .modal-header {
    border-color: #FF1910;
    background-color: #FF3129;
}

.modal-open.splash.splash-greensea .modal-backdrop {
    background-color: #16A085;
}

.modal-open.splash.splash-greensea .splash-2 .modal-content,
.modal-open.splash.splash-greensea .splash-2 .modal-footer {
    background-color: #16A085;
}

.modal-open.splash.splash-greensea .splash-2 .modal-header {
    border-color: #107360;
    background-color: #138A72;
}

.modal-open.splash.splash-dutch .modal-backdrop {
    background-color: #1693A5;
}

.modal-open.splash.splash-dutch .splash-2 .modal-content,
.modal-open.splash.splash-dutch .splash-2 .modal-footer {
    background-color: #1693A5;
}

.modal-open.splash.splash-dutch .splash-2 .modal-header {
    border-color: #106B78;
    background-color: #137F8F;
}

.modal-open.splash.splash-hotpink .modal-backdrop {
    background-color: #FF0066;
}

.modal-open.splash.splash-hotpink .splash-2 .modal-content,
.modal-open.splash.splash-hotpink .splash-2 .modal-footer {
    background-color: #FF0066;
}

.modal-open.splash.splash-hotpink .splash-2 .modal-header {
    border-color: #CC0052;
    background-color: #E6005C;
}

.modal-open.splash.splash-drank .modal-backdrop {
    background-color: #A40778;
}

.modal-open.splash.splash-drank .splash-2 .modal-content,
.modal-open.splash.splash-drank .splash-2 .modal-footer {
    background-color: #A40778;
}

.modal-open.splash.splash-drank .splash-2 .modal-header {
    border-color: #730554;
    background-color: #8C0666;
}

.modal-open.splash.splash-blue .modal-backdrop {
    background-color: #418BCA;
}

.modal-open.splash.splash-blue .splash-2 .modal-content,
.modal-open.splash.splash-blue .splash-2 .modal-footer {
    background-color: #418BCA;
}

.modal-open.splash.splash-blue .splash-2 .modal-header {
    border-color: #2F71A9;
    background-color: #357EBD;
}

.modal-open.splash.splash-lightred .modal-backdrop {
    background-color: #E05D6F;
}

.modal-open.splash.splash-lightred .splash-2 .modal-content,
.modal-open.splash.splash-lightred .splash-2 .modal-footer {
    background-color: #E05D6F;
}

.modal-open.splash.splash-lightred .splash-2 .modal-header {
    border-color: #D83249;
    background-color: #DC485C;
}

.modal-open.splash.splash-slategray .modal-backdrop {
    background-color: #3F4E62;
}

.modal-open.splash.splash-slategray .splash-2 .modal-content,
.modal-open.splash.splash-slategray .splash-2 .modal-footer {
    background-color: #3F4E62;
}

.modal-open.splash.splash-slategray .splash-2 .modal-header {
    border-color: #2B3543;
    background-color: #354252;
}

.modal-open.splash.splash-darkgray .modal-backdrop {
    background-color: #333333;
}

.modal-open.splash.splash-darkgray .splash-2 .modal-content,
.modal-open.splash.splash-darkgray .splash-2 .modal-footer {
    background-color: #333333;
}

.modal-open.splash.splash-darkgray .splash-2 .modal-header {
    border-color: #1A1A1A;
    background-color: #262626;
}

.portlets.sortable {
    min-height: 50px;
}

.ui-sortable-placeholder {
    visibility: visible !important;

    height: 100px;
    margin-bottom: 20px;

    opacity: 0.5;
    border: 3px dashed rgba(0, 0, 0, 0.1);
}

.portlet .tile-header {
    cursor: pointer;
    -webkit-transition: all 0.15s linear;
    -moz-transition: all 0.15s linear;
    transition: all 0.15s linear;
}

.portlet .tile-header:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
}

.progress {
    background-color: #EBEEF2;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.progress .progress-bar {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.progress.progress-xxs {
    height: 2px;

    border-radius: 0;
}

.progress.progress-xs {
    height: 6px;

    border-radius: 2px;
}

.progress.progress-sm {
    height: 12px;

    border-radius: 3px;
}

.progress.progress-sm .progress-bar {
    font-size: 10px;
    line-height: 12px;
}

.progress.not-rounded {
    border-radius: 0;
}

.progress-list {
    position: relative;
}

.progress-list .details {
    display: inline-block;
    float: left;
}

.progress-list .details .title {
    font-family: "Dosis", "Arial", sans-serif;
    line-height: 16px;
}

.progress-list .details .description {
    font-size: 10px;
    line-height: 12px;

    text-transform: uppercase;

    color: #95A2A9;
}

.progress-list .status {
    font-size: 9px;

    display: inline-block;

    padding: 6px;

    background-color: #EBEEF2;
}

.progress-list .clearfix {
    height: 30px;
}

.progress-bar-cyan {
    background-color: #22BEEF;
}

.progress-bar-amethyst {
    background-color: #CD97EB;
}

.progress-bar-green {
    background-color: #A2D200;
}

.progress-bar-orange {
    background-color: #FFC100;
}

.progress-bar-red {
    background-color: #FF4A43;
}

.progress-bar-greensea {
    background-color: #16A085;
}

.progress-bar-dutch {
    background-color: #1693A5;
}

.progress-bar-hotpink {
    background-color: #FF0066;
}

.progress-bar-drank {
    background-color: #A40778;
}

.progress-bar-blue {
    background-color: #418BCA;
}

.progress-bar-lightred {
    background-color: #E05D6F;
}

.progress-bar-slategray {
    background-color: #3F4E62;
}

.progress-bar-darkgray {
    background-color: #333333;
}

.progress.transparent-black {
    background-color: rgba(0, 0, 0, 0.1);
}

.progress.transparent-black .progress-bar {
    background-color: rgba(0, 0, 0, 0.2);
}

.tbox {
    display: table;

    width: 100%;
    height: 100%;

    table-layout: fixed;
    border-spacing: 0;
}

.tbox > .tcol {
    display: table-cell;
    float: none;

    height: 100%;

    vertical-align: top;
}

@media only screen and (max-width: 992px) {
    .tbox-sm {
        display: block;
    }

    .tbox-sm > .tcol {
        display: block;

        width: auto;
        height: auto;
    }
}

@media only screen and (max-width: 767px) {
    .tbox-xs {
        display: block;
    }

    .tbox-xs > .tcol {
        display: block;

        width: auto;
        height: auto;
    }
}

.chosen-container {
    font-size: 14px;
}

.chosen-container .chosen-drop {
    border-color: #22BEEF;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
}

.chosen-container .chosen-results {
    color: #616F77;
}

.chosen-container .chosen-results li {
    font-size: 12px;

    overflow: hidden;

    white-space: nowrap;
    text-overflow: ellipsis;
}

.chosen-container .chosen-results li.group-result {
    color: #4A555B;
}

.chosen-container .chosen-results li.highlighted {
    background: #798992;
}

.chosen-container-single .chosen-single {
    line-height: 1.42857143;

    height: 32px;
    padding: 7px 12px;

    color: #616F77;
    border-color: #DBE0E2;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
    background: white;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.chosen-container-single.chosen-disabled .chosen-single {
    background: #EEEEEE;
}

.chosen-disabled {
    opacity: 1 !important;
}

.chosen-container-single .chosen-single div b {
    background-position: 0 7px;
}

.chosen-container-single .chosen-search input[type=text] {
    font-size: 12px;

    color: #95A2A9;
    border-color: #DBE0E2;
}

.chosen-container-multi .chosen-choices {
    padding: 2px 12px;

    border-color: #DBE0E2;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
    background: white;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.chosen-container-multi .chosen-choices li.search-field input[type="text"] {
    font-family: "Lato", "Arial", sans-serif;
    font-size: 12px;
    font-style: italic;
    line-height: 20px;

    height: 28px;
    margin: 0;

    color: #BFC7CB;
}

.chosen-container-multi .chosen-choices li.search-choice {
    font-size: 12px;

    padding: 5px 20px 4px 10px;

    color: #616F77;
    border: 0;
    border-radius: 0;
    background: #EAECED;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
    top: 6px;
}

.chosen-container-active.chosen-with-drop .chosen-single {
    border-color: #22BEEF;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    background: white;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.chosen-container-active.chosen-with-drop .chosen-single div b {
    background-position: -18px 7px;
}

.chosen-container-active.chosen-with-drop .chosen-choices {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.chosen-container-active .chosen-choices {
    border-color: #22BEEF;
}

.input-underline + .chosen-container .chosen-choices {
    padding-left: 0;

    vertical-align: middle;

    border: 0;
    border-color: #DBE0E2;
    border-bottom: 1px solid;
    border-radius: 0;
    background: none;

    -webkit-appearance: none !important;
}

.input-underline + .chosen-container .chosen-choices:focus:hover {
    border: 0;
    border-bottom: 1px solid #22BEEF;
}

.chosen-rtl .chosen-search input[type="text"],
.chosen-container-single .chosen-single abbr,
.chosen-container-single .chosen-single div b,
.chosen-container-single .chosen-search input[type="text"],
.chosen-container-multi .chosen-choices .search-choice .search-choice-close,
.chosen-container .chosen-results-scroll-down span,
.chosen-container .chosen-results-scroll-up span {
    background-image: url("../images/chosen-sprite.png") !important;
}

/* @group Retina compatibility */
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 144dpi) {
    .chosen-rtl .chosen-search input[type="text"],
    .chosen-container-single .chosen-single abbr,
    .chosen-container-single .chosen-single div b,
    .chosen-container-single .chosen-search input[type="text"],
    .chosen-container-multi .chosen-choices .search-choice .search-choice-close,
    .chosen-container .chosen-results-scroll-down span,
    .chosen-container .chosen-results-scroll-up span {
        background-image: url("../images/<EMAIL>") !important;
    }
}

/* @end */
.table > thead > tr td,
.table > thead > tr th,
.table > tbody > tr td,
.table > tbody > tr th,
.table > tfoot > tr td,
.table > tfoot > tr th {
    border-color: #DBE0E2;
}

.table > thead > tr td:first-child,
.table > thead > tr th:first-child,
.table > tbody > tr td:first-child,
.table > tbody > tr th:first-child,
.table > tfoot > tr td:first-child,
.table > tfoot > tr th:first-child {
    padding-left: 15px;
}

.table > thead > tr td:last-child,
.table > thead > tr th:last-child,
.table > tbody > tr td:last-child,
.table > tbody > tr th:last-child,
.table > tfoot > tr td:last-child,
.table > tfoot > tr th:last-child {
    padding-right: 15px;
}

.table.table-no-border > thead > tr td,
.table.table-no-border > thead > tr th,
.table.table-no-border > tbody > tr td,
.table.table-no-border > tbody > tr th,
.table.table-no-border > tfoot > tr td,
.table.table-no-border > tfoot > tr th {
    border: 0;
}

.table.table-custom {
    border-collapse: separate;
}

.table.table-custom > thead > tr td,
.table.table-custom > thead > tr th,
.table.table-custom > tbody > tr td,
.table.table-custom > tbody > tr th,
.table.table-custom > tfoot > tr td,
.table.table-custom > tfoot > tr th {
    padding: 8px;
}

.table.table-custom > thead > tr td,
.table.table-custom > thead > tr th {
    position: relative;

    border-width: 1px;
    border-color: #798992;
}

.table.table-custom > thead > tr td.sorting:after,
.table.table-custom > thead > tr td.st-sort-ascent:after,
.table.table-custom > thead > tr td.st-sort-descent:after,
.table.table-custom > thead > tr th.sorting:after,
.table.table-custom > thead > tr th.st-sort-ascent:after,
.table.table-custom > thead > tr th.st-sort-descent:after {
    font-family: "FontAwesome";
    font-weight: 900;
    font-style: normal;
    line-height: 1;

    position: absolute;
    top: 12px;
    right: 8px;

    display: block;
    display: inline-block;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.table.table-custom > thead > tr td.sorting:after,
.table.table-custom > thead > tr th.sorting:after {
    content: "\f0dc";

    color: #DBE0E2;
}

.table.table-custom > thead > tr td.st-sort-ascent:after,
.table.table-custom > thead > tr th.st-sort-ascent:after {
    top: 14px;

    content: "\f0de";

    color: #428BCA;
}

.table.table-custom > thead > tr td.st-sort-descent:after,
.table.table-custom > thead > tr th.st-sort-descent:after {
    top: 8px;

    content: "\f0dd";

    color: #428BCA;
}

.table.table-custom > tbody > tr:first-child td,
.table.table-custom > tbody > tr:first-child th {
    border-top: 0;
}

.table.table {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.table-responsive {
    overflow-x: visible;
}

*[class*="bg-"]:not(.bg-default) .table > thead > tr td,
*[class*="bg-"]:not(.bg-default) .table > thead > tr th,
*[class*="bg-"]:not(.bg-default) .table > tbody > tr td,
*[class*="bg-"]:not(.bg-default) .table > tbody > tr th,
*[class*="bg-"]:not(.bg-default) .table > tfoot > tr td,
*[class*="bg-"]:not(.bg-default) .table > tfoot > tr th {
    border-color: rgba(255, 255, 255, 0.15);
}

*[class*="bg-"]:not(.bg-default) .table.table-bordered {
    border-collapse: separate;

    border: 0;
}

*[class*="bg-"]:not(.bg-default) .table.table-bordered > thead > tr td,
*[class*="bg-"]:not(.bg-default) .table.table-bordered > thead > tr th,
*[class*="bg-"]:not(.bg-default) .table.table-bordered > tbody > tr td,
*[class*="bg-"]:not(.bg-default) .table.table-bordered > tbody > tr th,
*[class*="bg-"]:not(.bg-default) .table.table-bordered > tfoot > tr td,
*[class*="bg-"]:not(.bg-default) .table.table-bordered > tfoot > tr th {
    border-right: 0;
    border-bottom: 0;
}

*[class*="bg-"]:not(.bg-default) .table.table-hover > tbody > tr:hover td,
*[class*="bg-"]:not(.bg-default) .table.table-hover > tbody > tr:hover th {
    background-color: rgba(255, 255, 255, 0.1);
}

/******************************************/
/*************** datatables ***************/
/******************************************/
/*
* Sort styling
*/
.dataTables_wrapper.form-inline tbody .form-control {
    width: 100%;
}

table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child:before {
    border: 0;
    border-radius: 0;
    box-shadow: none;
}

table.dataTable thead th {
    position: relative;

    background-image: none !important;
    /* Remove the DataTables bootstrap integration styling */
}

table.dataTable thead th.sorting:after,
table.dataTable thead th.sorting_asc:after,
table.dataTable thead th.sorting_desc:after {
    font-family: "FontAwesome";
    font-weight: 900;
    font-style: normal;
    line-height: 1;

    position: absolute;
    top: 12px;
    right: 8px;

    display: block;
    display: inline-block;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

table.dataTable thead th.sorting:after {
    content: "\f0dc";

    color: #DBE0E2;
}

table.dataTable thead th.sorting_asc:after {
    top: 16px;

    content: "\f0de";
}

table.dataTable thead th.sorting_desc:after {
    content: "\f0dd";
}

div.dataTables_scrollBody table.dataTable thead th.sorting:after,
div.dataTables_scrollBody table.dataTable thead th.sorting_asc:after,
div.dataTables_scrollBody table.dataTable thead th.sorting_desc:after {
    content: "";
}

/*
 * DataTables style pagination controls
 */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    margin: 0;
    padding: 0;

    border: 0;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    border: 0;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
    border: 0;
}

div.dataTables_paginate a.first,
div.dataTables_paginate a.previous {
    position: relative;

    padding-left: 24px !important;
}

div.dataTables_paginate a.next,
div.dataTables_paginate a.last {
    position: relative;

    padding-right: 24px !important;
}

div.dataTables_paginate a.first:before,
div.dataTables_paginate a.previous:before,
div.dataTables_paginate a.next:after,
div.dataTables_paginate a.last:after {
    font-family: "FontAwesome";
    font-weight: 900;
    font-style: normal;
    line-height: 1;

    position: absolute;
    top: 8px;
    left: 10px;

    display: block;
    display: inline-block;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

div.dataTables_paginate a.next:after,
div.dataTables_paginate a.last:after {
    right: 10px;
    left: auto;
}

div.dataTables_paginate a.first:before {
    content: "\f100";
}

div.dataTables_paginate a.previous:before {
    content: "\f104";
}

div.dataTables_paginate a.next:after {
    content: "\f105";
}

div.dataTables_paginate a.last:after {
    content: "\f101";
}

.p-0 .dataTables_wrapper > .row {
    padding: 15px;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    font-size: 12px;

    color: #616F77;
}

.dataTables_wrapper .pagination {
    margin: 0;
}

.dataTables_wrapper .pagination > li > a,
.dataTables_wrapper .pagination > li > span {
    padding: 5px 10px;
}

.dataTables_wrapper .DTTT {
    margin-bottom: 15px;
    margin-left: 10px;
}

.dataTables_wrapper .ColVis {
    margin-left: 10px;
}

.dataTables_wrapper table > tbody .row_selected {
    background-color: rgba(0, 0, 0, 0.05);
}

.dataTables_wrapper table > tbody .parent td,
.dataTables_wrapper table > tbody .parent th {
    background-color: #F5F5F5;
}

.dataTables_wrapper table > tbody .child td,
.dataTables_wrapper table > tbody .child th {
    background-color: #FAFAFA;
}

.dataTables_wrapper table.table-custom + .row {
    padding: 15px 0 0;
}

.dataTables_wrapper table.table-custom > thead td.sorting_asc,
.dataTables_wrapper table.table-custom > thead td.sorting_desc,
.dataTables_wrapper table.table-custom > thead th.sorting_asc,
.dataTables_wrapper table.table-custom > thead th.sorting_desc {
    padding-bottom: 4px;

    color: #428BCA;
    border-bottom: 3px solid #428BCA !important;
}

.dataTables_wrapper table.table-custom > thead td.sorting,
.dataTables_wrapper table.table-custom > thead th.sorting {
    padding-bottom: 6px;

    -webkit-transition: background-color 0.4s, color 0.4s;
    -moz-transition: background-color 0.4s, color 0.4s;
    transition: background-color 0.4s, color 0.4s;
}

.dataTables_wrapper table.table-custom > thead td.sorting:hover,
.dataTables_wrapper table.table-custom > thead th.sorting:hover {
    color: #6AA3D5;
    border-color: #6AA3D5;
    background-color: #FAFAFA;
}

.dataTables_wrapper table > tfoot {
    background-color: #DBE0E2;
}

.dataTables_wrapper table > tfoot .filter_column input {
    width: 100%;
}

.dataTables_wrapper .dataTables_scroll {
    margin-bottom: 10px;
}

.dataTables_wrapper .dataTables_scroll .dataTables_scrollBody {
    border-bottom: 0 !important;
}

.dataTables_wrapper .dataTables_scroll .dataTables_scrollBody table {
    margin-top: 0 !important;
}

.dataTables_wrapper .dataTables_scroll .dataTables_scrollBody table tbody tr:last-child td {
    border-bottom: 1px solid #DBE0E2 !important;
}

.dataTables_wrapper .dataTables_scroll .dataTables_scrollBody thead > tr > th {
    border-bottom: 0 !important;
}

.dataTables_wrapper .inline-controls div.dataTables_paginate,
.dataTables_wrapper .inline-controls div.dataTables_length,
.dataTables_wrapper .inline-controls div.dataTables_info {
    position: static !important;

    display: inline-block !important;
    float: none !important;

    margin: 0 !important;
    padding: 0 !important;
}

.dataTables_wrapper .inline-controls div.dataTables_paginate .seperator,
.dataTables_wrapper .inline-controls div.dataTables_length .seperator,
.dataTables_wrapper .inline-controls div.dataTables_info .seperator {
    padding: 0 5px;
}

.dataTables_wrapper .inline-controls div.dataTables_length label {
    display: inline-block !important;
    float: none !important;

    margin: 0 !important;
    padding: 0 !important;
}

table.dataTable.no-footer {
    border-bottom-color: #DBE0E2;
}

.table-custom .dataTables_wrapper .dataTables_filter input {
    padding-left: 0;

    vertical-align: middle;

    border: 0;
    border-color: #DBE0E2;
    border-bottom: 1px solid;
    border-radius: 0;
    background: none;

    -webkit-appearance: none !important;
}

.table-custom .dataTables_wrapper .dataTables_filter input:focus:hover {
    border: 0;
    border-bottom: 1px solid #22BEEF;
}

.table-custom .dataTables_wrapper .dataTables_filter input:hover:not(:disabled) {
    border-color: #B1BAC0;
}

.table-custom .dataTables_wrapper .dataTables_filter input:focus,
.table-custom .dataTables_wrapper .dataTables_filter input:focus:hover {
    border-color: #22BEEF;
}

div.dataTables_paginate.paging_input {
    float: none !important;

    margin: 0 !important;
    padding: 0 !important;
}

div.dataTables_paginate.paging_input .next {
    margin-right: 5px;
    padding-right: 10px !important;
}

div.dataTables_paginate.paging_input .next:after {
    content: "";
}

div.dataTables_paginate.paging_input .prev {
    margin-left: 5px;
}

.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-top: 10px;
}

@media (max-width: 767px) {
    .dataTables_wrapper .inline-controls div.dataTables_paginate,
    .dataTables_wrapper .inline-controls div.dataTables_length,
    .dataTables_wrapper .inline-controls div.dataTables_info {
        display: block !important;

        margin: 10px !important;

        text-align: left;
    }

    .dataTables_wrapper .inline-controls div.dataTables_length label {
        line-height: 30px;

        display: block !important;
    }

    .dataTables_wrapper .inline-controls div.dataTables_length select {
        display: inline-block;
    }

    .dataTables_wrapper .inline-controls .seperator {
        display: none !important;
    }

    .dataTables_wrapper div.dataTables_filter,
    .dataTables_wrapper div.dataTables_paginate {
        display: block !important;

        margin: 10px !important;

        text-align: left;
    }

    .dataTables_wrapper div.dataTables_filter .pagination-panel-input,
    .dataTables_wrapper div.dataTables_paginate .pagination-panel-input {
        display: inline-block;
    }

    .dataTables_wrapper div.dataTables_filter label,
    .dataTables_wrapper div.dataTables_paginate label {
        line-height: 30px;
    }

    .dataTables_wrapper div.dataTables_filter label input,
    .dataTables_wrapper div.dataTables_paginate label input {
        display: inline-block;

        width: 60%;
    }
}

/***************************************/
/*************** ui grid ***************/
/***************************************/
@font-face {
    font-family: "ui-grid";
    font-weight: normal;
    font-style: normal;

    src: url("../fonts/ui-grid.eot");
    src: url("../fonts/ui-grid.eot#iefix") format("embedded-opentype"), url("../fonts/ui-grid.woff") format("woff"), url("../fonts/ui-grid.ttf?") format("truetype"), url("../fonts/ui-grid.svg?#ui-grid") format("svg");
}

.ui-grid-column-menu-button {
    top: 2px;
}

.ui-grid-row:nth-child(even) .ui-grid-cell {
    background-color: #F5F5F5;
}

input[type="text"].ui-grid-filter-input {
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;

    padding-left: 5px;

    border: 1px solid #DBE0E2;
}

input[type="text"].ui-grid-filter-input::-webkit-input-placeholder {
    font-family: "Lato", "Arial", sans-serif;
    font-style: italic;

    -webkit-transition: color 0.2s linear;
    -moz-transition: color 0.2s linear;
    transition: color 0.2s linear;

    color: #BFC7CB;
}

input[type="text"].ui-grid-filter-input::-moz-placeholder {
    font-family: "Lato", "Arial", sans-serif;
    font-style: italic;

    -webkit-transition: color 0.2s linear;
    -moz-transition: color 0.2s linear;
    transition: color 0.2s linear;

    color: #BFC7CB;
}

input[type="text"].ui-grid-filter-input:-moz-placeholder {
    font-family: "Lato", "Arial", sans-serif;
    font-style: italic;

    -webkit-transition: color 0.2s linear;
    -moz-transition: color 0.2s linear;
    transition: color 0.2s linear;

    color: #BFC7CB;
}

input[type="text"].ui-grid-filter-input:-ms-input-placeholder {
    font-family: "Lato", "Arial", sans-serif;
    font-style: italic;

    -webkit-transition: color 0.2s linear;
    -moz-transition: color 0.2s linear;
    transition: color 0.2s linear;

    color: #BFC7CB;
}

input[type="text"].ui-grid-filter-input:hover {
    border-color: #B1BAC0;
}

input[type="text"].ui-grid-filter-input:focus,
input[type="text"].ui-grid-filter-input:focus:hover {
    border: 1px solid;
    border-color: #22BEEF;
    outline: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.ui-grid-menu .ui-grid-menu-inner {
    background: white;
}

.ui-grid-menu .ui-grid-menu-inner ul li {
    border-bottom: 0 !important;
}

.ui-grid-menu .ui-grid-menu-inner ul li:hover {
    color: white;
    background: #428BCA;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.ui-grid-menu .ui-grid-menu-inner ul li.ui-grid-menu-item-active {
    color: white;
    background: #428BCA;
}

/**************************************/
/*************** ngTable***************/
/**************************************/
.ng-table.table-custom th {
    text-align: left;
}

.ng-table.table-custom th.sortable {
    padding-bottom: 6px;

    -webkit-transition: background-color 0.4s, color 0.4s;
    -moz-transition: background-color 0.4s, color 0.4s;
    transition: background-color 0.4s, color 0.4s;
}

.ng-table.table-custom th.sortable:hover {
    color: #6AA3D5;
    border-color: #6AA3D5;
    background-color: #FAFAFA;
}

.ng-table.table-custom th.sortable.sort-desc,
.ng-table.table-custom th.sortable.sort-asc {
    padding-bottom: 4px;

    color: #428BCA;
    border-bottom: 3px solid #428BCA;
    background-color: transparent;
    text-shadow: none;
}

.ng-table.table-custom th.sortable.sort-desc div:after,
.ng-table.table-custom th.sortable.sort-desc div:before,
.ng-table.table-custom th.sortable.sort-asc div:after,
.ng-table.table-custom th.sortable.sort-asc div:before {
    opacity: 1;
    border-color: #428BCA transparent;
}

.ng-table.table-custom th.sortable.sort-desc div:before,
.ng-table.table-custom th.sortable.sort-asc div:before {
    border-top-color: #428BCA;
}

.ng-table.table-custom th.sortable div:after,
.ng-table.table-custom th.sortable div:before {
    border-color: #616F77 transparent;
}

.ng-table.table-custom .ng-table-filters th {
    font-weight: 400;

    border-top: 0;
}

/******************************************/
/*************** smartTable ***************/
/******************************************/
.st-table > thead {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.st-table > thead td.sorting,
.st-table > thead th.sorting {
    padding-bottom: 6px;

    cursor: pointer;
    -webkit-transition: background-color 0.4s, color 0.4s;
    -moz-transition: background-color 0.4s, color 0.4s;
    transition: background-color 0.4s, color 0.4s;
}

.st-table > thead td.sorting:hover,
.st-table > thead th.sorting:hover {
    color: #6AA3D5;
    border-bottom: 1px solid #6AA3D5;
    background-color: #FAFAFA;
}

.st-table > thead td.sorting.st-sort-ascent,
.st-table > thead td.sorting.st-sort-descent,
.st-table > thead th.sorting.st-sort-ascent,
.st-table > thead th.sorting.st-sort-descent {
    padding-bottom: 6px;

    color: #428BCA;
    border-bottom: 3px solid #428BCA;
}

.st-table > tbody .st-selected td,
.st-table > tbody .st-selected th {
    background-color: rgba(0, 0, 0, 0.05);
}

.st-table .st-filters td,
.st-table .st-filters th {
    font-weight: 400;

    border-top: 0;
}

.st-table .pagination {
    margin: 0;
    margin-top: 10px;
}

.st-table .pagination a {
    cursor: pointer;
}

.st-table .loading-indicator {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    padding: 0.7em;

    text-align: center;
}

.st-table .loading-indicator:before {
    display: inline-block;

    min-width: 1em;
    min-height: 1em;
    margin: 0 0.4em;

    content: "";
    -webkit-animation: halfspin 1s ease infinite;
    -moz-animation: halfspin 1s ease infinite;
    -o-animation: halfspin 1s ease infinite;
    animation: halfspin 1s ease infinite;

    border-top: 4px solid #646464;
    border-right: 4px solid #E6E6E6;
    border-bottom: 4px solid #646464;
    border-left: 4px solid #E6E6E6;
    border-radius: 100%;
}

@-webkit-keyframes halfspin {
    to {
        -webkit-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        transform: rotate(180deg);
    }
}

@-moz-keyframes halfspin {
    to {
        -webkit-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        transform: rotate(180deg);
    }
}

@keyframes halfspin {
    to {
        -webkit-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        transform: rotate(180deg);
    }
}

@media screen and (max-width: 1360px) {
    .table-responsive-wide {
        overflow-x: auto;
        overflow-y: hidden;

        width: 100%;
        margin-bottom: 15px;

        border: 0;

        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }
}

/*********************************/
/************* CARDS *************/
/*********************************/
.card-container {
    position: relative;
}

.card-container .card {
    position: relative;
    z-index: 9;

    width: 100%;
    min-height: 110px;
    margin-bottom: 20px;

    cursor: pointer;

    -webkit-perspective: 600px;
    -moz-perspective: 600px;
    perspective: 600px;
}

.card-container .card .front {
    position: relative;
    z-index: 11;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;
    padding: 30px 20px;
    /* -- transition is the magic sauce for animation -- */

    -webkit-transition: all 0.4s ease-in-out;
    -moz-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
    -webkit-transform: rotateX(0deg) rotateY(0deg);
    -moz-transform: rotateX(0deg) rotateY(0deg);
    -ms-transform: rotateX(0deg) rotateY(0deg);
    -o-transform: rotateX(0deg) rotateY(0deg);
    transform: rotateX(0deg) rotateY(0deg);

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    -o-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.card-container .card .front > .row {
    height: 100%;
    margin: 0;
    padding: 0;
}

.card-container .card:hover .front {
    z-index: 11;

    -webkit-transform: rotateY(180deg);
    -moz-transform: rotateY(180deg);
    -ms-transform: rotateY(180deg);
    -o-transform: rotateY(180deg);
    transform: rotateY(180deg);
}

.card-container .card:hover .back {
    z-index: 12;

    -webkit-transform: rotateX(0deg) rotateY(0deg);
    -moz-transform: rotateX(0deg) rotateY(0deg);
    -ms-transform: rotateX(0deg) rotateY(0deg);
    -o-transform: rotateX(0deg) rotateY(0deg);
    transform: rotateX(0deg) rotateY(0deg);
}

.card-container .card .back {
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;
    /* -- transition is the magic sauce for animation -- */

    -webkit-transition: all 0.4s ease-in-out;
    -moz-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
    -webkit-transform: rotateY(-180deg);
    -moz-transform: rotateY(-179deg);
    /* setting to 180 causes an unnatural-looking half-flip */
    transform: rotateY(-179deg);

    border: 0;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    -o-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.card-container .card .back > .row {
    height: 100%;
    margin: 0;
    padding: 0;
}

.card-container .card .back > .row > div {
    height: 100%;
    padding: 0;
}

.card-container .card .back > .row > div a {
    display: block;

    width: 100%;
    height: 100%;
    padding: 30px 5px 10px;

    text-align: center;
}

.card-container .card .back > .row > div a i {
    display: block;

    margin-bottom: 5px;
}

.card-container .card .back > .row > div a:hover {
    text-decoration: none;
}

.card-container .card .back > .row > div:first-of-type a {
    background-color: rgba(0, 0, 0, 0.1);
}

.card-container .card .back > .row > div:nth-of-type(2) a {
    background-color: rgba(0, 0, 0, 0.05);
}

@media only screen and (max-width: 420px) {
    .card-container .card .front {
        padding: 28px 10px;
    }
}

.owl-theme .owl-controls .owl-page span {
    width: 10px;
    height: 10px;
    margin: 5px 4px;

    opacity: 0.3;
    background: #616F77;
}

.owl-theme .owl-controls .owl-page.active span {
    width: 14px;
    height: 14px;
    margin: 3px 4px;

    opacity: 0.6;
}

.tile-simple .owl-theme .owl-controls {
    margin-top: 0;
}

.widget-todo form .form-control {
    height: 30px;
    margin-bottom: 30px;
}

.widget-todo .todo-list > li {
    position: relative;
}

.widget-todo .todo-list > li .checkbox-custom > input {
    display: none;
}

.widget-todo .todo-list > li .checkbox-custom > i {
    margin-top: -3px;
    margin-right: 33px;
    margin-left: -74px;
}

.widget-todo .todo-list > li .remove-todo {
    display: none;
}

.widget-todo .todo-list > li:hover .remove-todo {
    display: block;
}

.widget-todo .todo-list > li span {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.widget-todo .todo-list > li.completed span {
    text-decoration: line-through;

    color: #BFC7CB;
}

.widget-todo .todo-list > li .edit {
    display: none;
}

.widget-todo .todo-list > li.editing .view {
    display: none;
}

.widget-todo .todo-list > li.editing .edit {
    display: block;
}

.widget-calendar .datepicker table {
    width: 100%;
}

.widget-calendar .datepicker table thead tr th {
    border-radius: 0;
}

.widget-calendar .datepicker table thead tr:first-child th.prev,
.widget-calendar .datepicker table thead tr:first-child th.next {
    font-size: 24px;

    padding: 15px 10px;

    color: rgba(255, 255, 255, 0.5);
}

.widget-calendar .datepicker table thead tr:first-child th.prev:hover,
.widget-calendar .datepicker table thead tr:first-child th.next:hover {
    color: white;
    background-color: transparent;
}

.widget-calendar .datepicker table thead tr:first-child th.picker-switch {
    font-size: 18px;
    font-weight: 300;
}

.widget-calendar .datepicker table thead tr:first-child th.picker-switch:hover {
    background-color: #394759;
}

.widget-calendar .datepicker table thead tr:nth-child(2) th {
    font-size: 14px;
    line-height: 35px;

    background-color: #354252;
}

.widget-calendar .datepicker table tbody tr td {
    font-size: 14px;
    font-weight: 300;

    position: relative;

    padding: 8px 10px;
}

.widget-calendar .datepicker table tbody tr td.old,
.widget-calendar .datepicker table tbody tr td.new {
    color: rgba(255, 255, 255, 0.3);
}

.widget-calendar .datepicker table tbody tr td.active {
    border-radius: 50px;
    background-color: #E05D6F !important;
    text-shadow: none;
}

.widget-calendar .datepicker table tbody tr td:hover {
    background-color: #394759;
}

.widget-calendar .datepicker table tbody tr td span:hover {
    background-color: #333F4F;
}

.widget-calendar .datepicker table tbody tr td span.active {
    border-radius: 50px;
    background-color: #E05D6F !important;
}

.widget-calendar .picker-switch table td span {
    margin: 2px 0;
}

.widget-calendar .picker-switch table td span:hover {
    background-color: #394759;
}

.widget-calendar .timepicker table td span:hover {
    background-color: #394759;
}

.widget-calendar .bootstrap-datetimepicker-widget table td.day:hover,
.widget-calendar .bootstrap-datetimepicker-widget table td.hour:hover,
.widget-calendar .bootstrap-datetimepicker-widget table td.minute:hover,
.widget-calendar .bootstrap-datetimepicker-widget table td.second:hover {
    background-color: #394759;
}

.widget-message .ta-toolbar {
    margin: 0;
    padding: 5px;

    background-color: #F8F8F8;
}

.widget-message .ta-scroll-window.form-control {
    min-height: 160px;

    border: 0;
    border-bottom: 10px solid #F8F8F8;
    border-radius: 0;
}

.widget-message .ta-scroll-window > .ta-bind {
    min-height: 160px;
}

.widget-message .ta-root.focussed .ta-scroll-window.form-control {
    border-color: #E2E2E2;
}

.widget-message .ta-root.focussed .ta-toolbar {
    background-color: #E2E2E2;
}

.widget-appointments .day {
    font-size: 120px;
    font-weight: 300;
    line-height: 120px;
}

.widget-appointments .month {
    font-size: 36px;
    font-weight: 700;
    line-height: 56px;
}

.widget-appointments .b-l {
    border-left: 2px solid rgba(255, 255, 255, 0.2);
}

.widget-appointments .owl-carousel .owl-item {
    padding: 0 60px;
}

.widget-appointments .owl-carousel .owl-controls .owl-buttons {
    position: absolute;
    top: 50%;

    width: 100%;
    margin-top: -20px;

    text-align: left;
}

.widget-appointments .owl-carousel .owl-controls .owl-buttons .owl-next {
    float: right;
}

.widget-appointments .owl-carousel .owl-controls .owl-buttons div {
    font-size: 18px;

    padding: 4px 10px;

    border: 1px solid rgba(255, 255, 255, 0.5);
    background: transparent;
}

.streamline {
    position: relative;
    z-index: 1;
}

.streamline:before {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 20px;

    width: 1px;
    height: 100%;

    content: "";

    background-color: #DDDDDD;
}

.streamline:after {
    position: absolute;
    z-index: 2;
    bottom: 0;
    left: 15px;

    width: 10px;
    height: 10px;

    content: "";

    border: 1px solid #DDDDDD;
    border-radius: 50%;
    background-color: white;
}

.streamline .streamline-post {
    position: relative;
    z-index: 2;

    margin-bottom: 20px;
}

.streamline .streamline-post > aside {
    float: left;

    vertical-align: top;
}

.streamline .streamline-post .post-container,
.streamline .streamline-post .reply-container {
    position: relative;
    z-index: 2;

    padding-left: 55px;

    vertical-align: top;
}

.streamline .streamline-post .post-container:before,
.streamline .streamline-post .reply-container:before {
    position: absolute;
    top: 13px;
    left: 48px;

    width: 0;
    height: 0;

    content: "";

    border-top: 7px solid transparent;
    border-right: 7px solid #DDDDDD;
    border-bottom: 7px solid transparent;
}

.streamline .streamline-post .post-container:after,
.streamline .streamline-post .reply-container:after {
    position: absolute;
    top: 14px;
    left: 50px;

    width: 0;
    height: 0;

    content: "";

    border-top: 6px solid transparent;
    border-right: 6px solid #FFFFFF;
    border-bottom: 6px solid transparent;
}

.streamline .streamline-post .post-replies > li > aside {
    float: left;

    vertical-align: top;
}

.streamline .streamline-form {
    position: absolute;
    z-index: 2;

    width: 100%;
    padding-bottom: 20px;
}

.streamline .streamline-form .thumb {
    float: left;
}

.streamline .streamline-form form {
    padding-left: 55px;
}

.streamline.timeline {
    position: relative;

    overflow: hidden;
}

.streamline.timeline ul {
    position: relative;
    z-index: 2;

    margin: 0 auto;
    padding: 0 0 30px;

    list-style: none;

    text-align: center;
}

.streamline.timeline ul > li {
    margin: 20px 0;
}

.streamline.timeline ul > li:first-child {
    margin-top: 0;
}

.streamline.timeline ul .heading {
    display: block;

    margin: 0 auto;

    text-align: center;

    color: white;
}

.streamline.timeline ul .heading:after,
.streamline.timeline ul .heading:before {
    display: table;
    clear: both;

    content: "";
}

.streamline.timeline ul .timeline-post {
    position: relative;

    float: left;
    clear: left;

    width: 50%;
    padding-right: 40px;

    text-align: right;
}

.streamline.timeline ul .timeline-post aside {
    position: absolute;
    right: -15px;

    float: right;
}

.streamline.timeline ul .timeline-post .time {
    font-size: 12px;

    position: absolute;
    top: 10px;
    left: 10px;
}

.streamline.timeline ul .timeline-post .time > i {
    margin-right: 5px;
}

.streamline.timeline ul .timeline-post:nth-of-type(even):after {
    position: absolute;
    top: 10px;
    right: 32px;

    width: 0;
    height: 0;

    content: "";

    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid #FFFFFF;
}

.streamline.timeline ul .timeline-post:nth-of-type(odd) {
    float: right;
    clear: right;

    padding-right: 0;
    padding-left: 40px;

    text-align: left;
}

.streamline.timeline ul .timeline-post:nth-of-type(odd) aside {
    top: 40px;
    right: auto;
    left: -15px;

    float: left;
}

.streamline.timeline ul .timeline-post:nth-of-type(odd) .time {
    right: 10px;
    left: auto;
}

.streamline.timeline ul .timeline-post:nth-of-type(odd):after {
    position: absolute;
    top: 50px;
    left: 32px;

    width: 0;
    height: 0;

    content: "";

    border-top: 8px solid transparent;
    border-right: 8px solid #FFFFFF;
    border-bottom: 8px solid transparent;
}

.streamline.timeline ul .timeline-post .thumb {
    font-size: 18px;
    line-height: 30px;

    text-align: center;
}

.streamline.timeline ul .timeline-post .post-container > .panel {
    padding: 10px 20px;
}

.streamline.timeline:before,
.streamline.timeline:after {
    left: 50%;
}

.streamline.timeline:before {
    background-color: #BBBBBB;
}

.streamline.timeline:after {
    margin-left: -5px;

    border-color: #BBBBBB;
}

.streamline-form .post-toolbar {
    padding: 5px 10px;

    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    transition: all 0.2s linear;

    border: 1px solid #E2E2E2;
    border-top: 0;
    border-radius: 0 0 2px 2px;
}

.streamline-form .post-toolbar > a {
    padding: 2px 5px;

    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    transition: all 0.2s linear;

    color: rgba(0, 0, 0, 0.2);
}

.streamline-form .post-toolbar > a:hover {
    color: rgba(0, 0, 0, 0.4);
}

@media only screen and (max-width: 767px) {
    .streamline.timeline ul .heading {
        text-align: left;
    }

    .streamline.timeline ul .timeline-post,
    .streamline.timeline ul .timeline-post:nth-of-type(odd),
    .streamline.timeline ul .timeline-post:nth-of-type(even) {
        float: none;

        width: 100%;
        padding-right: 0;
        padding-left: 40px;

        text-align: left;
    }

    .streamline.timeline ul .timeline-post aside,
    .streamline.timeline ul .timeline-post:nth-of-type(odd) aside,
    .streamline.timeline ul .timeline-post:nth-of-type(even) aside {
        top: 0;
        right: auto;
        left: 0;

        float: none;
    }

    .streamline.timeline ul .timeline-post:after,
    .streamline.timeline ul .timeline-post:nth-of-type(odd):after,
    .streamline.timeline ul .timeline-post:nth-of-type(even):after {
        position: absolute;
        top: 10px;
        right: auto;
        left: 32px;

        width: 0;
        height: 0;

        border: 0;
        border-top: 8px solid transparent;
        border-right: 8px solid #FFFFFF;
        border-bottom: 8px solid transparent;
    }

    .streamline.timeline ul .timeline-post .time,
    .streamline.timeline ul .timeline-post:nth-of-type(odd) .time,
    .streamline.timeline ul .timeline-post:nth-of-type(even) .time {
        right: 10px;
        left: auto;
    }

    .streamline.timeline:before,
    .streamline.timeline:after {
        left: 15px;
    }
}

.ui-map,
.angular-google-map-container {
    display: block;

    height: 400px;
}

.jqvmap-zoomin,
.jqvmap-zoomout {
    line-height: 10px;

    position: absolute;
    left: 10px;

    padding: 3px 4px;

    cursor: pointer;
    text-align: center;

    color: #FFFFFF;
    border-radius: 2px;
    background: #566269;
}

.jqvmap-zoomin {
    top: 10px;
}

.jqvmap-zoomout {
    top: 30px;
}

.jqvmap-region {
    cursor: pointer;
}

.jqvmap-ajax_response {
    width: 100%;
    height: 400px;
}

.jqvmap-label {
    font-size: smaller;

    position: absolute;
    z-index: 99;

    display: none;

    padding: 3px 5px;

    color: white;
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.8);
}

.event-control {
    font-size: 12px;

    cursor: pointer;

    background-color: #F8F8F8;
}

.event-control:hover {
    background-color: white;
}

.event-control a {
    cursor: pointer;

    opacity: 0.5;
    color: #616F77;
}

.event-control a:hover {
    opacity: 1;
}

.event-control[class*="bg-"]:not(.bg-default):not(.bg-white) a {
    opacity: 1;
    color: rgba(255, 255, 255, 0.5) !important;
}

.event-control[class*="bg-"]:not(.bg-default):not(.bg-white) a:hover {
    color: white !important;
}

.fc-overlay {
    position: absolute;
    z-index: 1000;
    top: auto;
    left: 50%;

    display: none;

    min-width: 260px;
    margin-left: -150px;
    padding: 15px 0;

    color: #616F77 !important;
}

.fc-overlay.left {
    top: -15px;
    left: 100%;

    margin: 0;
    padding: 0 10px;
}

.fc-overlay.right {
    top: -15px;
    right: 100%;
    left: auto;

    margin: 0;
    padding: 0 10px;
}

.fc-overlay .panel {
    padding: 10px;
}

.fc .fc-toolbar {
    height: 50px;
    margin: 0;

    color: #FFFFFF;
    border-radius: 0;
    background-color: #009ecb;
}

.fc .fc-toolbar .fc-button {
    line-height: 28px;

    height: 50px;
    margin: 0;
    padding: 10px;

    color: rgba(255, 255, 255, 0.5);
    border: none;
    background: transparent;
    box-shadow: none;
    text-shadow: none;
}

.fc .fc-toolbar .fc-button.active {
    color: #FFFFFF;
}

.fc .fc-toolbar .fc-button:hover {
    color: white;
}

.fc .fc-toolbar h2 {
    font-family: "Dosis", "Arial", sans-serif;
    font-size: 24px;
    font-weight: 300;
    line-height: 50px;

    margin: 0;

    text-transform: uppercase;
}

/*.fc .fc-view-container .fc-event {*/
/*font-size: 12px;*/

/*padding: 3px 5px;*/

/*color: #616F77;*/
/*border: 0;*/
/*border-radius: 6px;*/
/*background-color: white;*/
/*}*/

/*.fc .fc-view-container .fc-event.fc-event-end:not(.fc-event-start) {*/
/*border: 0 !important;*/
/*}*/

.fc .fc-view-container .fc-event:focus,
.fc .fc-view-container .fc-event:hover,
.fc .fc-view-container .fc-event:active {
    z-index: 1000;
}

.fc .fc-view-container .fc-event:focus .fc-overlay,
.fc .fc-view-container .fc-event:hover .fc-overlay,
.fc .fc-view-container .fc-event:active .fc-overlay {
    display: block;
}

.fc .fc-view-container .fc-view.fc-basic-view > table > thead tr th.fc-widget-header,
.fc .fc-view-container .fc-view.fc-basic-view > table > thead tr td.fc-widget-header,
.fc .fc-view-container .fc-view.fc-agenda > table > thead tr th.fc-widget-header,
.fc .fc-view-container .fc-view.fc-agenda > table > thead tr td.fc-widget-header {
    font-size: 12px;
    font-weight: 400;

    padding: 10px;

    color: #FFFFFF;
    border: 0;
    background-color: #FF623F;
}

.fc .fc-view-container .fc-view.fc-basic-view > table > thead tr th.fc-agenda-gutter,
.fc .fc-view-container .fc-view.fc-basic-view > table > thead tr td.fc-agenda-gutter,
.fc .fc-view-container .fc-view.fc-agenda > table > thead tr th.fc-agenda-gutter,
.fc .fc-view-container .fc-view.fc-agenda > table > thead tr td.fc-agenda-gutter {
    padding: 0;
}

.fc .fc-view-container .fc-view.fc-basic-view > table > thead tr td.fc-widget-header,
.fc .fc-view-container .fc-view.fc-agenda > table > thead tr td.fc-widget-header {
    padding: 2px;
}

.fc .fc-view-container .fc-view.fc-basic-view > table tbody tr td.fc-widget-content,
.fc .fc-view-container .fc-view.fc-agenda > table tbody tr td.fc-widget-content {
    border: 0;
    border-right: 1px solid #D2D2D2;
    border-bottom: 1px solid #D2D2D2;
}

.fc .fc-view-container .fc-view.fc-basic-view > table tbody tr td.fc-widget-content.fc-state-highlight,
.fc .fc-view-container .fc-view.fc-agenda > table tbody tr td.fc-widget-content.fc-state-highlight {
    background: rgba(255, 255, 255, 0.5);
}

.fc .fc-view-container .fc-view.fc-basic-view > table tbody tr td.fc-day-number,
.fc .fc-view-container .fc-view.fc-agenda > table tbody tr td.fc-day-number {
    font-size: 12px;

    margin: 5px;
    padding: 5px 10px;

    color: #616F77;
}

.fc .fc-view-container .fc-view.fc-basic-view > table tbody tr td.fc-day-number.fc-state-highlight,
.fc .fc-view-container .fc-view.fc-agenda > table tbody tr td.fc-day-number.fc-state-highlight {
    float: right;

    padding: 4px 6px;

    color: white;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    background-color: #E05D6F;

    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.fc .fc-view-container .fc-view.fc-basic-view > table tbody tr td:first-child.fc-widget-content,
.fc .fc-view-container .fc-view.fc-agenda > table tbody tr td:first-child.fc-widget-content {
    border-left: 1px solid #D2D2D2;
}

.fc .fc-view-container .fc-view.fc-basic-view .fc-body .fc-row .fc-bg,
.fc .fc-view-container .fc-view.fc-agenda .fc-body .fc-row .fc-bg {
    border-bottom: 1px solid #D2D2D2;
}

.fc .fc-view-container .fc-view.fc-basic-view .fc-body .fc-row:last-of-type .fc-bg,
.fc .fc-view-container .fc-view.fc-agenda .fc-body .fc-row:last-of-type .fc-bg {
    border-bottom: 0;
}

.fc .fc-view-container .fc-view.fc-agenda .fc-agenda-allday .fc-agenda-axis,
.fc .fc-view-container .fc-view.fc-agenda .fc-agenda-allday .fc-agenda-gutter {
    font-size: 10px;
    font-weight: 300;

    padding: 10px 5px;

    color: #616F77;
    border-right: 1px solid #E2E2E2;
    border-left: 1px solid #E2E2E2;
    background-color: #F2F2F2;
}

.fc .fc-view-container .fc-view.fc-agenda > table > tbody > tr > td.fc-state-highlight {
    background: #F2F2F2;
}

.fc .fc-view-container .fc-view.fc-agenda > table > tbody > tr > td.fc-sat {
    border-right: 0;
}

.fc .fc-view-container .fc-view.fc-agenda .fc-agenda-slots tr {
    line-height: 35px;
}

.fc .fc-view-container .fc-view.fc-agenda .fc-agenda-slots tr th {
    font-size: 12px;
    font-weight: 300;

    padding-top: 10px;

    color: #616F77;
    border: 0;
    border-right: 1px solid #E2E2E2;
    border-left: 1px solid #E2E2E2;
    background-color: #F2F2F2;
}

.fc .fc-view-container .fc-view.fc-agenda .fc-agenda-slots tr td.fc-widget-content {
    line-height: 50px;

    border-bottom: 1px solid #E2E2E2;
}

.fc .fc-view-container .fc-view.fc-agenda .fc-agenda-slots tr td.fc-widget-content:last-child {
    border-right: 0;
}

.fc .fc-view-container .fc-view.fc-agenda .fc-agenda-slots tr.fc-minor td.fc-widget-content {
    border-bottom: 1px solid #E2E2E2;
}

.fc .fc-view-container .fc-view.fc-agenda .fc-agenda-divider {
    border: 1px solid #E2E2E2;
}

.fc .fc-view-container .fc-view.fc-agenda .fc-agenda-divider .fc-agenda-divider-inner {
    height: 3px;

    background-color: #F2F2F2;
}

.mix-filter,
.mix-controls {
    margin: 15px 0;
    padding: 0;

    list-style: none;
}

.mix-filter li,
.mix-controls li {
    font-size: 12px;

    display: inline-block;

    margin-right: 2px;
    margin-bottom: 5px;
    padding: 6px 15px;

    cursor: pointer;

    background: #DBE0E2;
}

.mix-filter li:hover:not(.disabled),
.mix-filter li.active:not(.disabled),
.mix-controls li:hover:not(.disabled),
.mix-controls li.active:not(.disabled) {
    color: #FFFFFF;
    background: #16A085;
}

.mix-controls li.select-all {
    padding-left: 0;

    background: none;
}

.mix-controls li.select-all .checkbox {
    min-height: 17px;
}

.mix-controls li.select-all:hover {
    color: #566269;
    background: none;
}

.mix-controls li.disabled,
.mix-controls li.disabled a {
    cursor: not-allowed;

    opacity: 0.5;
}

.mix-controls li a {
    color: #616F77;
}

.mix-controls li a i {
    margin-right: 5px;
}

.mix-controls li a:hover {
    text-decoration: none;
}

.mix-controls li:hover:not(.disabled) a {
    text-decoration: none;

    color: white;
}

.mix-grid .mix {
    position: relative;

    display: none;
}

.mix-grid .mix .img-container {
    position: relative;

    width: 100%;
}

.mix-grid .mix .img-details {
    position: absolute;
    bottom: 0;
    left: 50%;

    overflow: hidden;

    width: 0;
    height: 0;
    margin-left: -10px;
    padding: 0;

    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    transition: all 0.5s ease;
    text-align: center;

    color: white;
    background: transparent;
}

.mix-grid .mix .img-details h4 {
    margin-top: 30px;
}

.mix-grid .mix .img-details .img-controls {
    margin-top: 15px;
}

.mix-grid .mix .img-details .img-controls .img-select i:last-child {
    display: none;
}

.mix-grid .mix .img-details .img-controls > a {
    line-height: 22px;

    display: inline-block;

    width: 42px;
    height: 42px;
    margin-top: 10px;
    margin-right: 5px;
    padding: 10px 15px;

    cursor: pointer;

    color: white;
    border-radius: 50%;
    background: black;
}

.mix-grid .mix .img-details .img-controls > a:hover {
    text-decoration: none;

    background-color: #5BC0DE;
}

.mix-grid .mix .img-container:hover .img-details,
.mix-grid .mix.selected .img-details {
    bottom: 0;
    left: 0;

    width: 100%;
    height: 100%;
    margin-left: 0;

    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    transition: all 0.5s ease;

    background: rgba(0, 0, 0, 0.6);
}

.mix-grid .mix.selected .img-controls .img-select {
    background: #5BC0DE;
}

.mix-grid .mix.selected .img-controls .img-select i:last-child {
    display: inline-block;
}

.mix-grid .mix.selected .img-controls .img-select i:first-child {
    display: none;
}

.mfp-arrow-right:before,
.mfp-arrow-left:before {
    display: none !important;
}

body {
    font-family: "Lato", "Arial", sans-serif;
    font-size: 14px;
    font-weight: 400;

    color: #616F77;
    background-color: #E7EAEB;

    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    -ms-overflow-style: scrollbar;
}

body a {
    -webkit-transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
    transition: all 0.2s ease-out;
}

*:focus {
    outline: 0 !important;
}

.bg-body {
    background: #E7EAEB !important;
}

.bg-white {
    background-color: white !important;
}

.bg-cyan {
    color: white !important;
    background-color: #22BEEF !important;
}

.bg-cyan.dk {
    background-color: #10ACDD !important;
}

.bg-cyan.dker {
    background-color: #0F9DCA !important;
}

.bg-cyan.lt {
    background-color: #3AC5F1 !important;
}

.bg-cyan.lter {
    background-color: #52CCF2 !important;
}

.bg-amethyst {
    color: white !important;
    background-color: #CD97EB !important;
}

.bg-amethyst.dk {
    background-color: #C382E7 !important;
}

.bg-amethyst.dker {
    background-color: #BA71E4 !important;
}

.bg-amethyst.lt {
    background-color: #D1A0ED !important;
}

.bg-amethyst.lter {
    background-color: #D5A8EE !important;
}

.bg-green {
    color: white !important;
    background-color: #A2D200 !important;
}

.bg-green.dk {
    background-color: #96C300 !important;
}

.bg-green.dker {
    background-color: #86AE00 !important;
}

.bg-green.lt {
    background-color: #AADC00 !important;
}

.bg-green.lter {
    background-color: #B2E600 !important;
}

.bg-orange {
    color: white !important;
    background-color: #FFC100 !important;
}

.bg-orange.dk {
    background-color: #F0B500 !important;
}

.bg-orange.dker {
    background-color: #DBA600 !important;
}

.bg-orange.lt {
    background-color: #FFC71A !important;
}

.bg-orange.lter {
    background-color: #FFCD33 !important;
}

.bg-red {
    color: white !important;
    background-color: #FF4A43 !important;
}

.bg-red.dk {
    background-color: #FF1910 !important;
}

.bg-red.dker {
    background-color: #E60900 !important;
}

.bg-red.lt {
    background-color: #FF635C !important;
}

.bg-red.lter {
    background-color: #FF7B76 !important;
}

.bg-greensea {
    color: white !important;
    background-color: #16A085 !important;
}

.bg-greensea.dk {
    background-color: #138A72 !important;
}

.bg-greensea.dker {
    background-color: #107863 !important;
}

.bg-greensea.lt {
    background-color: #19B698 !important;
}

.bg-greensea.lter {
    background-color: #1CCDAA !important;
}

.bg-dutch {
    color: white !important;
    background-color: #1693A5 !important;
}

.bg-dutch.dk {
    background-color: #137F8F !important;
}

.bg-dutch.dker {
    background-color: #116F7D !important;
}

.bg-dutch.lt {
    background-color: #19A7BC !important;
}

.bg-dutch.lter {
    background-color: #1CBBD2 !important;
}

.bg-hotpink {
    color: white !important;
    background-color: #FF0066 !important;
}

.bg-hotpink.dk {
    background-color: #E6005C !important;
}

.bg-hotpink.dker {
    background-color: #D10054 !important;
}

.bg-hotpink.lt {
    background-color: #FF1A75 !important;
}

.bg-hotpink.lter {
    background-color: #FF3385 !important;
}

.bg-drank {
    color: white !important;
    background-color: #A40778 !important;
}

.bg-drank.dk {
    background-color: #8C0666 !important;
}

.bg-drank.dker {
    background-color: #780558 !important;
}

.bg-drank.lt {
    background-color: #BC088A !important;
}

.bg-drank.lter {
    background-color: #D5099C !important;
}

.bg-blue {
    color: white !important;
    background-color: #418BCA !important;
}

.bg-blue.dk {
    background-color: #357EBD !important;
}

.bg-blue.dker {
    background-color: #3074AD !important;
}

.bg-blue.lt {
    background-color: #5597D0 !important;
}

.bg-blue.lter {
    background-color: #69A3D5 !important;
}

.bg-lightred {
    color: white !important;
    background-color: #E05D6F !important;
}

.bg-lightred.dk {
    background-color: #DC485C !important;
}

.bg-lightred.dker {
    background-color: #D9364D !important;
}

.bg-lightred.lt {
    background-color: #E47282 !important;
}

.bg-lightred.lter {
    background-color: #E88895 !important;
}

.bg-slategray {
    color: white !important;
    background-color: #3F4E62 !important;
}

.bg-slategray.dk {
    background-color: #354252 !important;
}

.bg-slategray.dker {
    background-color: #2D3846 !important;
}

.bg-slategray.lt {
    background-color: #495A72 !important;
}

.bg-slategray.lter {
    background-color: #536781 !important;
}

.bg-darkgray {
    color: white !important;
    background-color: #333333 !important;
}

.bg-darkgray.dk {
    background-color: #262626 !important;
}

.bg-darkgray.dker {
    background-color: #1C1C1C !important;
}

.bg-darkgray.lt {
    background-color: #404040 !important;
}

.bg-darkgray.lter {
    background-color: #4D4D4D !important;
}

.bg-primary {
    color: white !important;
    background-color: #428BCA !important;
}

.bg-primary.dk {
    background-color: #3071A9 !important;
}

.bg-primary.dker {
    background-color: #245682 !important;
}

.bg-primary.lt {
    background-color: #5697D0 !important;
}

.bg-primary.lter {
    background-color: #6AA3D5 !important;
}

.bg-success {
    color: white !important;
    background-color: #5CB85C !important;
}

.bg-success.dk {
    background-color: #449D44 !important;
}

.bg-success.dker {
    background-color: #357935 !important;
}

.bg-success.lt {
    color: #357935 !important;
    background-color: #A3D7A3 !important;
}

.bg-success.lter {
    color: #357935 !important;
    background-color: #EAF6EA !important;
}

.bg-warning {
    color: white !important;
    background-color: #F0AD4E !important;
}

.bg-warning.dk {
    background-color: #EC971F !important;
}

.bg-warning.dker {
    background-color: #C77C11 !important;
}

.bg-warning.lt {
    color: #C77C11 !important;
    background-color: #F6CE95 !important;
}

.bg-warning.lter {
    color: #C77C11 !important;
    background-color: #FEF9F3 !important;
}

.bg-danger {
    color: white !important;
    background-color: #D9534F !important;
}

.bg-danger.dk {
    background-color: #C9302C !important;
}

.bg-danger.dker {
    background-color: #A02622 !important;
}

.bg-danger.lt {
    color: #A02622 !important;
    background-color: #EBA5A3 !important;
}

.bg-danger.lter {
    color: #A02622 !important;
    background-color: #FDF7F7 !important;
}

.bg-info {
    color: white !important;
    background-color: #5BC0DE !important;
}

.bg-info.dk {
    background-color: #31B0D5 !important;
}

.bg-info.dker {
    background-color: #2390B0 !important;
}

.bg-info.lt {
    color: #2390B0 !important;
    background-color: #9BD8EB !important;
}

.bg-info.lter {
    color: #2390B0 !important;
    background-color: #F0F9FC !important;
}

.bg-default {
    color: white !important;
    background-color: #616F77 !important;
}

.bg-default.dk {
    color: white !important;
    background-color: #4A555B !important;
}

.bg-default.dker {
    color: white !important;
    background-color: #333B3F !important;
}

.bg-default.lt {
    color: #616F77 !important;
    background-color: #A3AEB4 !important;
}

.bg-default.lter {
    color: #616F77 !important;
    background-color: #E1E5E7 !important;
}

.bg-tr-black {
    color: #F2F2F2 !important;
    background-color: rgba(0, 0, 0, 0.1) !important;
}

.bg-tr-black.btn:hover,
.bg-tr-black.btn:focus,
.bg-tr-black.btn:active,
.bg-tr-black.btn.active {
    color: white !important;
    background-color: rgba(0, 0, 0, 0.2) !important;
}

.bg-tr-black.dk {
    background-color: rgba(0, 0, 0, 0.2) !important;
}

.bg-tr-black.dker {
    color: #616F77 !important;
    background-color: rgba(0, 0, 0, 0.3) !important;
}

.bg-tr-black.lt {
    color: #616F77 !important;
    background-color: rgba(0, 0, 0, 0.05) !important;
}

.bg-tr-black.lter {
    color: #616F77 !important;
    background-color: rgba(0, 0, 0, 0.03) !important;
}

.bg-tr-white {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.bg-tr-white.btn:hover,
.bg-tr-white.btn:focus,
.bg-tr-white.btn:active,
.bg-tr-white.btn.active {
    color: #616F77 !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
}

.bg-tr-white.dk {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

.bg-tr-white.dker {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.bg-tr-white.lt {
    background-color: rgba(255, 255, 255, 0.3) !important;
}

.bg-tr-white.lter {
    background-color: rgba(255, 255, 255, 0.4) !important;
}

.no-bg {
    background-color: transparent !important;
}

*[class*="bg-"]:not(.bg-default):not(.bg-white):not(.bg-tr-white) a:not(.ui-select-choices-row-inner):not(.event-remove) {
    color: rgba(255, 255, 255, 0.7);
}

*[class*="bg-"]:not(.bg-default):not(.bg-white):not(.bg-tr-white) a:not(.ui-select-choices-row-inner):not(.event-remove):hover {
    color: white;
}

*[class*="bg-"]:not(.bg-default):not(.bg-white):not(.bg-tr-white) .dropdown-menu > li > a {
    color: #585858 !important;
}

*[class*="bg-"]:not(.bg-default):not(.bg-white):not(.bg-tr-white) .dropdown-menu > li > a:hover {
    color: #262626 !important;
}

*[class*="bg-"]:not(.bg-default):not(.bg-white):not(.bg-tr-white) > .form-control.input-unstyled {
    color: rgba(255, 255, 255, 0.7);
}

*[class*="bg-"]:not(.bg-default):not(.bg-white):not(.bg-tr-white) > .text-muted {
    color: rgba(255, 255, 255, 0.4);
}

.text-cyan {
    color: #22BEEF;
}

.text-amethyst {
    color: #CD97EB;
}

.text-green {
    color: #A2D200;
}

.text-orange {
    color: #FFC100;
}

.text-red {
    color: #FF4A43;
}

.text-greensea {
    color: #16A085;
}

.text-dutch {
    color: #1693A5;
}

.text-hotpink {
    color: #FF0066;
}

.text-drank {
    color: #A40778;
}

.text-blue {
    color: #418BCA;
}

.text-lightred {
    color: #E05D6F;
}

.text-slategray {
    color: #3F4E62;
}

.text-darkgray {
    color: #333333;
}

.text-primary {
    color: #428BCA;
}

.text-success {
    color: #5CB85C;
}

.text-warning {
    color: #F0AD4E;
}

.text-danger {
    color: #D9534F;
}

.text-info {
    color: #5BC0DE;
}

.text-default {
    color: #616F77;
}

.text-default.dk {
    color: #4A555B;
}

.text-default.dker {
    color: #333B3F;
}

.text-default.lt {
    color: #A3AEB4;
}

.text-default.lter {
    color: #E1E5E7;
}

.text-transparent-white {
    color: rgba(255, 255, 255, 0.5) !important;
}

.text-transparent-black {
    color: rgba(0, 0, 0, 0.5) !important;
}

.text-white {
    color: white;
}

.text-xs {
    font-size: 10px !important;
}

.text-sm {
    font-size: 12px !important;
}

.text-sm-green {
    color: black;
    font-size: 12px !important;
    background: lightgreen;
    padding-left: 10px;
}

.text-sm-red {
    color: black;
    font-size: 12px !important;
    background: pink;
    padding-left: 10px;
}

.text-sm-overflow {
    font-size: 12px !important;
    overflow: clip;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.text-sm-overflow:hover {
    font-size: 12px !important;
    overflow: visible;
    white-space: normal;
    width: auto;
    background: white;
}

.text-sm-green {
  color: black;
  font-size: 12px !important;
  background: lightgreen;
  padding-left: 10px;
}

.text-sm-red {
  color: black;
  font-size: 12px !important;
  background: pink;
  padding-left: 10px;
}

.text-sm-overflow {
  font-size: 12px !important;
  overflow: clip;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-sm-overflow:hover {
  font-size: 12px !important;
  overflow: visible;
  white-space: normal;
  width: auto;
  background: white;
}

.text-md {
    font-size: 16px !important;
}

.text-lg {
    font-size: 18px !important;
}

.text-elg {
    font-size: 32px !important;
    line-height: 34px;
}

.text-light {
    font-weight: 300 !important;
}

.animated {
    -webkit-animation-duration: 0.5s;
    -moz-animation-duration: 0.5s;
    animation-duration: 0.5s;
}

.block {
    display: block;
}

.inline {
    display: inline !important;
}

.inline-block {
    display: inline-block !important;
}

.text-left {
    text-align: left !important;
}

.wrap-reset {
    margin: -15px;
    padding: 15px;
}

.hidden-xs.show,
.hidden-sm.show {
    display: block !important;
}

.m-auto {
    margin: auto;
}

.m-40 {
    margin: 40px !important;
}

.m-20 {
    margin: 20px !important;
}

.m-15 {
    margin: 15px !important;
}

.m-10 {
    margin: 10px !important;
}

.m-5 {
    margin: 5px !important;
}

.m-0 {
    margin: 0 !important;
}

.mb-40 {
    margin-bottom: 40px !important;
}

.mb-20 {
    margin-bottom: 20px !important;
}

.mb-15 {
    margin-bottom: 15px !important;
}

.mb-10 {
    margin-bottom: 10px !important;
}

.mb-5 {
    margin-bottom: 5px !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mt-40 {
    margin-top: 40px !important;
}

.mt-20 {
    margin-top: 20px !important;
}

.mt-15 {
    margin-top: 15px !important;
}

.mt-10 {
    margin-top: 10px !important;
}

.mt-6 {
    margin-top: 6px !important;
}

.mt-5 {
    margin-top: 5px !important;
}

.mt-2 {
    margin-top: 2px !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mt-n3 {
    margin-top: -3px !important;
}

.mt-n7 {
    margin-top: -7px !important;
}

.ml-40 {
    margin-left: 40px !important;
}

.ml-20 {
    margin-left: 20px !important;
}

.ml-15 {
    margin-left: 15px !important;
}

.ml-10 {
    margin-left: 10px !important;
}

.ml-5 {
    margin-left: 5px !important;
}

.ml-0 {
    margin-left: 0 !important;
}

.mr-40 {
    margin-right: 40px !important;
}

.mr-20 {
    margin-right: 20px !important;
}

.mr-15 {
    margin-right: 15px !important;
}

.mr-10 {
    margin-right: 10px !important;
}

.mr-5 {
    margin-right: 5px !important;
}

.mr-3 {
    margin-right: 3px !important;
}

.mr-0 {
    margin-right: 0 !important;
}

.p-30 {
    padding: 30px !important;
}

.p-20 {
    padding: 20px !important;
}

.p-15 {
    padding: 15px !important;
}

.p-10 {
    padding: 10px !important;
}

.p-5 {
    padding: 5px !important;
}

.p-0 {
    padding: 0 !important;
}

.pb-30 {
    padding-bottom: 30px !important;
}

.pb-20 {
    padding-bottom: 20px !important;
}

.pb-15 {
    padding-bottom: 15px !important;
}

.pb-10 {
    padding-bottom: 10px !important;
}

.pb-5 {
    padding-bottom: 5px !important;
}

.pb-0 {
    padding-bottom: 0 !important;
}

.pt-30 {
    padding-top: 30px !important;
}

.pt-20 {
    padding-top: 20px !important;
}

.pt-15 {
    padding-top: 15px !important;
}

.pt-12 {
    padding-top: 12px !important;
}

.pt-10 {
    padding-top: 10px !important;
}

.pt-5 {
    padding-top: 5px !important;
}

.pt-3 {
    padding-top: 3px !important;
}

.pt-0 {
    padding-top: 0 !important;
}

.pl-30 {
    padding-left: 30px !important;
}

.pl-20 {
    padding-left: 20px !important;
}

.pl-15 {
    padding-left: 15px !important;
}

.pl-10 {
    padding-left: 10px !important;
}

.pl-5 {
    padding-left: 5px !important;
}

.pl-0 {
    padding-left: 0 !important;
}

.pr-30 {
    padding-right: 30px !important;
}

.pr-25 {
    padding-right: 25px !important;
}

.pr-24 {
    padding-right: 24px !important;
}

.pr-20 {
    padding-right: 20px !important;
}

.pr-15 {
    padding-right: 15px !important;
}

.pr-10 {
    padding-right: 10px !important;
}

.pr-5 {
    padding-right: 5px !important;
}

.pr-0 {
    padding-right: 0 !important;
}

.b-0 {
    border: 0 !important;
}

.bt-0 {
    border-top: 0 !important;
}

.bb-0 {
    border-bottom: 0 !important;
}

.b-a {
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.b-r {
    border-right: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.b-b {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.b-l {
    border-left: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.b-t {
    border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.b-dashed {
    border-style: dashed !important;
}

.b-solid {
    border-style: solid !important;
}

.b-2x {
    border-width: 2px !important;
}

.b-3x {
    border-width: 3px !important;
}

.b-cyan {
    border-color: #22BEEF !important;
}

.b-amethyst {
    border-color: #CD97EB !important;
}

.b-green {
    border-color: #A2D200 !important;
}

.b-orange {
    border-color: #FFC100 !important;
}

.b-red {
    border-color: #FF4A43 !important;
}

.b-greensea {
    border-color: #16A085 !important;
}

.b-dutch {
    border-color: #1693A5 !important;
}

.b-hotpink {
    border-color: #FF0066 !important;
}

.b-drank {
    border-color: #A40778 !important;
}

.b-blue {
    border-color: #418BCA !important;
}

.b-lightred {
    border-color: #E05D6F !important;
}

.b-slategray {
    border-color: #3F4E62 !important;
}

.b-darkgray {
    border-color: #333333 !important;
}

.b-primary {
    border-color: #428BCA !important;
}

.b-success {
    border-color: #5CB85C !important;
}

.b-warning {
    border-color: #F0AD4E !important;
}

.b-danger {
    border-color: #D9534F !important;
}

.b-info {
    border-color: #5BC0DE !important;
}

.b-default {
    border-color: #616F77 !important;
}

.br-0 {
    border-radius: 0 !important;
}

.br-2 {
    border-radius: 2px !important;
}

.br-2-l {
    border-radius: 2px 0 0 2px !important;
}

.br-2-r {
    border-radius: 0 2px 2px 0 !important;
}

.br-2-t {
    border-radius: 2px 2px 0 0 !important;
}

.br-2-b {
    border-radius: 0 0 2px 2px !important;
}

.size-30x30,
.wh30 {
    width: 30px;
    height: 30px;
}

.size-45x45,
.wh45 {
    width: 45px;
    height: 45px;
}

.size-50x50,
.wh50 {
    width: 50px;
    height: 50px;
}

.w-30 {
    width: 30px !important;
}

.w-40 {
    width: 40px !important;
}

.w-45 {
    width: 45px !important;
}

.w-50 {
    width: 50px !important;
}

.w-xxs,
.w-60 {
    width: 60px !important;
}

.w-70 {
    width: 70px !important;
}

.w-80 {
    width: 80px !important;
}

.w-90 {
    width: 90px !important;
}

.w-xs,
.w-100 {
    width: 100px;
}

.w-110 {
    width: 110px;
}

.w-sm,
.w-150 {
    width: 150px;
}

.w-md,
.w-240 {
    width: 240px;
}

.w-lg,
.w-280 {
    width: 280px;
}

.w-xl,
.w-360 {
    width: 360px;
}

.w-xxl,
.w-420 {
    width: 420px;
}

.fs-10 {
    font-size: 10px !important;
}

.fs-11 {
    font-size: 11px !important;
}

.fs-12 {
    font-size: 12px !important;
}

.fs-14 {
    font-size: 14px !important;
}

hr.line-dashed {
    border-style: dashed;
}

hr.line-full {
    margin: 20px -20px;
}

.perspective {
    display: inline-block;

    -webkit-perspective: 800px;
    -moz-perspective: 800px;
    perspective: 800px;
}

.lined-paper {
    font-family: Georgia, serif;
    font-style: italic;
    line-height: 30px;

    position: relative;

    padding: 30px 15px 30px 75px !important;

    background-color: white;
    background-image: -webkit-linear-gradient(#F6ABCA 1px, transparent 1px), -webkit-linear-gradient(#F6ABCA 1px, transparent 1px), -webkit-linear- gradient(#E8E8E8 1px, transparent 1px);
    background-image: -moz-linear-gradient(#F6ABCA 1px, transparent 1px), -moz-linear-gradient(#F6ABCA 1px, transparent 1px), -moz-linear-gradient #E8E8E8 1px, transparent 1px;
    background-image: -o-linear-gradient(#F6ABCA 1px, transparent 1px), -o-linear-gradient(#F6ABCA 1px, transparent 1px), -o-linear-gradient(#E8E8E8 1px, transparent 1px);
    background-image: linear-gradient(#F6ABCA 1px, transparent 1px), linear-gradient(#F6ABCA 1px, transparent 1px), linear-gradient(#E8E8E8 1px, transparent 1px);
    background-repeat: repeat-y, repeat-y, repeat;
    background-position: 62px 0, 66px 0, 0 -1px;
    background-size: 1px 1px, 1px 1px, 30px 30px;
}

.lined-paper p,
.lined-paper ul,
.lined-paper ol {
    margin-bottom: 30px;
}

@media only screen and (min-width: 993px) {
    .collapse.collapse-sm,
    .collapse.collapse-xs {
        display: block;
    }
}

.example [class^="col-"] {
    margin-bottom: 10px;
    padding: 10px 15px;

    border-right: 5px solid white;
    background-color: #F2F2F2;
}

.example .row {
    margin: 0;
    margin-bottom: 10px;
}

@media only screen and (max-width: 767px) {
    .text-left-sm {
        text-align: left;
    }
}

@media only screen and (max-width: 480px) {
    .text-left-xs {
        text-align: left;
    }

    .w-xxl,
    .w-420 {
        width: 100%;
    }
}

.color-schemes a.scheme-black {
    background-color: #1C2B36;
}

.color-schemes a.scheme-drank {
    background-color: #51445F;
}

.color-schemes a.scheme-greensea {
    background-color: #16A085;
}

.color-schemes a.scheme-cyan {
    background-color: #22BEEF;
}

.color-schemes a.scheme-lightred {
    background-color: #E05D6F;
}

.color-schemes a.scheme-light {
    background-color: #FFFFFF;
}

.sidebar-sm #sidebar.scheme-black,
.sidebar-sm #sidebar.scheme-drank,
.sidebar-sm #sidebar.scheme-greensea,
.sidebar-sm #sidebar.scheme-cyan,
.sidebar-sm #sidebar.scheme-lightred,
.sidebar-sm #sidebar.scheme-light,
.sidebar-xs #sidebar.scheme-black,
.sidebar-xs #sidebar.scheme-drank,
.sidebar-xs #sidebar.scheme-greensea,
.sidebar-xs #sidebar.scheme-cyan,
.sidebar-xs #sidebar.scheme-lightred,
.sidebar-xs #sidebar.scheme-light {
    background-color: transparent !important;
}

#header.scheme-black {
    background-color: #1C2B36;
}

#header .branding.scheme-black,
#sidebar.scheme-black,
#sidebar.scheme-black #navigation,
#sidebar.scheme-black #sidebar-wrap:before,
#header.scheme-black li.toggle-right-sidebar > a,
.appWrapper.scheme-black .page-breadcrumb > li:first-child {
    background-color: #131E25 !important;
}

.appWrapper.black-scheme-color #navigation > li.active > a,
.appWrapper.black-scheme-color #loading-bar .bar {
    background-color: #1C2B36 !important;
}

.appWrapper.black-scheme-color #loading-bar-spinner .spinner-icon {
    border-top-color: #1C2B36;
    border-left-color: #1C2B36;
}

.appWrapper.black-scheme-color #loading-bar .peg {
    -webkit-box-shadow: #1C2B36 1px 0 6px 1px;
    box-shadow: #1C2B36 1px 0 6px 1px;
}

.appWrapper.black-scheme-color #navigation .dropdown > ul li:hover > a,
.appWrapper.black-scheme-color #navigation .dropdown > ul li.active > a {
    color: #1C2B36 !important;
}

.appWrapper.black-scheme-color #navigation .dropdown.submenu.open > a {
    color: white !important;
}

.appWrapper.black-scheme-color .pageheader .page-bar .page-breadcrumb > li:not(:first-child) > a:hover {
    color: #1C2B36 !important;
}

.sidebar-sm #sidebar.scheme-black #navigation li.dropdown > ul,
.sidebar-sm #sidebar.scheme-black #navigation li.submenu > ul,
.sidebar-xs #sidebar.scheme-black #navigation li.dropdown > ul,
.sidebar-xs #sidebar.scheme-black #navigation li.submenu > ul,
.hz-menu #sidebar.scheme-black #navigation li.dropdown > ul,
.hz-menu #sidebar.scheme-black #navigation li.submenu > ul {
    background-color: #10181E;
}

#header.scheme-drank {
    background-color: #51445F;
}

#header .branding.scheme-drank,
#sidebar.scheme-drank,
#sidebar.scheme-drank #navigation,
#sidebar.scheme-drank #sidebar-wrap:before,
#header.scheme-drank li.toggle-right-sidebar > a,
.appWrapper.scheme-drank .page-breadcrumb > li:first-child {
    background-color: #493D55 !important;
}

.appWrapper.drank-scheme-color #navigation > li.active > a,
.appWrapper.drank-scheme-color #loading-bar .bar {
    background-color: #51445F !important;
}

.appWrapper.drank-scheme-color #loading-bar-spinner .spinner-icon {
    border-top-color: #51445F;
    border-left-color: #51445F;
}

.appWrapper.drank-scheme-color #loading-bar .peg {
    -webkit-box-shadow: #51445F 1px 0 6px 1px;
    box-shadow: #51445F 1px 0 6px 1px;
}

.appWrapper.drank-scheme-color #navigation .dropdown > ul li:hover > a,
.appWrapper.drank-scheme-color #navigation .dropdown > ul li.active > a {
    color: #51445F !important;
}

.appWrapper.drank-scheme-color #navigation .dropdown.submenu.open > a {
    color: white !important;
}

.appWrapper.drank-scheme-color .pageheader .page-bar .page-breadcrumb > li:not(:first-child) > a:hover {
    color: #51445F !important;
}

.sidebar-sm #sidebar.scheme-drank #navigation li.dropdown > ul,
.sidebar-sm #sidebar.scheme-drank #navigation li.submenu > ul,
.sidebar-xs #sidebar.scheme-drank #navigation li.dropdown > ul,
.sidebar-xs #sidebar.scheme-drank #navigation li.submenu > ul,
.hz-menu #sidebar.scheme-drank #navigation li.dropdown > ul,
.hz-menu #sidebar.scheme-drank #navigation li.submenu > ul {
    background-color: #3C3246;
}

#header.scheme-greensea {
    background-color: #16A085;
}

#header .branding.scheme-greensea,
#sidebar.scheme-greensea,
#sidebar.scheme-greensea #navigation,
#sidebar.scheme-greensea #sidebar-wrap:before,
#header.scheme-greensea li.toggle-right-sidebar > a,
.appWrapper.scheme-greensea .page-breadcrumb > li:first-child {
    background-color: #138A72 !important;
}

.appWrapper.greensea-scheme-color #navigation > li.active > a,
.appWrapper.greensea-scheme-color #loading-bar .bar {
    background-color: #16A085 !important;
}

.appWrapper.greensea-scheme-color #loading-bar-spinner .spinner-icon {
    border-top-color: #16A085;
    border-left-color: #16A085;
}

.appWrapper.greensea-scheme-color #loading-bar .peg {
    -webkit-box-shadow: #16A085 1px 0 6px 1px;
    box-shadow: #16A085 1px 0 6px 1px;
}

.appWrapper.greensea-scheme-color #navigation .dropdown > ul li:hover > a,
.appWrapper.greensea-scheme-color #navigation .dropdown > ul li.active > a {
    color: #16A085 !important;
}

.appWrapper.greensea-scheme-color #navigation .dropdown.submenu.open > a {
    color: white !important;
}

.appWrapper.greensea-scheme-color .pageheader .page-bar .page-breadcrumb > li:not(:first-child) > a:hover {
    color: #16A085 !important;
}

.sidebar-sm #sidebar.scheme-greensea #navigation li.dropdown > ul,
.sidebar-sm #sidebar.scheme-greensea #navigation li.submenu > ul,
.sidebar-xs #sidebar.scheme-greensea #navigation li.dropdown > ul,
.sidebar-xs #sidebar.scheme-greensea #navigation li.submenu > ul,
.hz-menu #sidebar.scheme-greensea #navigation li.dropdown > ul,
.hz-menu #sidebar.scheme-greensea #navigation li.submenu > ul {
    background-color: #0F6F5C;
}

#header.scheme-cyan {
    background-color: #22BEEF;
}

#header .branding.scheme-cyan,
#sidebar.scheme-cyan,
#sidebar.scheme-cyan #navigation,
#sidebar.scheme-cyan #sidebar-wrap:before,
#header.scheme-cyan li.toggle-right-sidebar > a,
.appWrapper.scheme-cyan .page-breadcrumb > li:first-child {
    background-color: #11B4E7 !important;
}

.appWrapper.cyan-scheme-color #navigation > li.active > a,
.appWrapper.cyan-scheme-color #loading-bar .bar {
    background-color: #22BEEF !important;
}

.appWrapper.cyan-scheme-color #loading-bar-spinner .spinner-icon {
    border-top-color: #22BEEF;
    border-left-color: #22BEEF;
}

.appWrapper.cyan-scheme-color #loading-bar .peg {
    -webkit-box-shadow: #22BEEF 1px 0 6px 1px;
    box-shadow: #22BEEF 1px 0 6px 1px;
}

.appWrapper.cyan-scheme-color #navigation .dropdown > ul li:hover > a,
.appWrapper.cyan-scheme-color #navigation .dropdown > ul li.active > a {
    color: #22BEEF !important;
}

.appWrapper.cyan-scheme-color #navigation .dropdown.submenu.open > a {
    color: white !important;
}

.appWrapper.cyan-scheme-color .pageheader .page-bar .page-breadcrumb > li:not(:first-child) > a:hover {
    color: #22BEEF !important;
}

.sidebar-sm #sidebar.scheme-cyan #navigation li.dropdown > ul,
.sidebar-sm #sidebar.scheme-cyan #navigation li.submenu > ul,
.sidebar-xs #sidebar.scheme-cyan #navigation li.dropdown > ul,
.sidebar-xs #sidebar.scheme-cyan #navigation li.submenu > ul,
.hz-menu #sidebar.scheme-cyan #navigation li.dropdown > ul,
.hz-menu #sidebar.scheme-cyan #navigation li.submenu > ul {
    background-color: #0D8FB7;
}

#header.scheme-lightred {
    background-color: #E05D6F;
}

#header .branding.scheme-lightred,
#sidebar.scheme-lightred,
#sidebar.scheme-lightred #navigation,
#sidebar.scheme-lightred #sidebar-wrap:before,
#header.scheme-lightred li.toggle-right-sidebar > a,
.appWrapper.scheme-lightred .page-breadcrumb > li:first-child {
    background-color: #DC485C !important;
}

.appWrapper.lightred-scheme-color #navigation > li.active > a,
.appWrapper.lightred-scheme-color #loading-bar .bar {
    background-color: #E05D6F !important;
}

.appWrapper.lightred-scheme-color #loading-bar-spinner .spinner-icon {
    border-top-color: #E05D6F;
    border-left-color: #E05D6F;
}

.appWrapper.lightred-scheme-color #loading-bar .peg {
    -webkit-box-shadow: #E05D6F 1px 0 6px 1px;
    box-shadow: #E05D6F 1px 0 6px 1px;
}

.appWrapper.lightred-scheme-color #navigation .dropdown > ul li:hover > a,
.appWrapper.lightred-scheme-color #navigation .dropdown > ul li.active > a {
    color: #E05D6F !important;
}

.appWrapper.lightred-scheme-color #navigation .dropdown.submenu.open > a {
    color: white !important;
}

.appWrapper.lightred-scheme-color .pageheader .page-bar .page-breadcrumb > li:not(:first-child) > a:hover {
    color: #E05D6F !important;
}

.sidebar-sm #sidebar.scheme-lightred #navigation li.dropdown > ul,
.sidebar-sm #sidebar.scheme-lightred #navigation li.submenu > ul,
.sidebar-xs #sidebar.scheme-lightred #navigation li.dropdown > ul,
.sidebar-xs #sidebar.scheme-lightred #navigation li.submenu > ul,
.hz-menu #sidebar.scheme-lightred #navigation li.dropdown > ul,
.hz-menu #sidebar.scheme-lightred #navigation li.submenu > ul {
    background-color: #D83249;
}

#header.scheme-light {
    background-color: #FFFFFF;
}

#header .branding.scheme-light,
#sidebar.scheme-light,
#sidebar.scheme-light #navigation,
#sidebar.scheme-light #sidebar-wrap:before,
#header.scheme-light li.toggle-right-sidebar > a {
    background-color: #FFFFFF !important;
}

.appWrapper.scheme-light .page-breadcrumb > li:first-child {
    background-color: rgba(0, 0, 0, 0.7);
}

#header.scheme-light {
    -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(0, 0, 0, 0.05);
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(0, 0, 0, 0.05);
}

#header.scheme-light .nav-right > li > a,
#header.scheme-light .nav-left > li > a {
    color: rgba(0, 0, 0, 0.7);
}

#header.scheme-light .nav-right > li > a:hover,
#header.scheme-light .nav-left > li > a:hover {
    color: rgba(0, 0, 0, 0.9);
}

#header.scheme-light .nav-left > li.divided-right {
    border-color: rgba(0, 0, 0, 0.1);
}

#header.scheme-light .search .form-control {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.2);
}

#header.scheme-light .search .form-control::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.3);
}

#header.scheme-light .search .form-control::-moz-placeholder {
    color: rgba(0, 0, 0, 0.3);
}

#header.scheme-light .search .form-control:-moz-placeholder {
    color: rgba(0, 0, 0, 0.3);
}

#header.scheme-light .search .form-control:-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.3);
}

#header.scheme-light .search .form-control:focus {
    border-color: rgba(0, 0, 0, 0.5);
}

#header.scheme-light .search .form-control:focus::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.5);
}

#header.scheme-light .search .form-control:focus::-moz-placeholder {
    color: rgba(0, 0, 0, 0.5);
}

#header.scheme-light .search .form-control:focus:-moz-placeholder {
    color: rgba(0, 0, 0, 0.5);
}

#header.scheme-light .search .form-control:focus:-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.5);
}

#header.scheme-light .search:after {
    color: rgba(0, 0, 0, 0.3);
}

#header.scheme-light li.toggle-right-sidebar > a {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

#header .branding.scheme-light {
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

#header .branding.scheme-light .brand {
    color: #4A555B;
    background-image: url(../images/minovate-logo-color.png);
}

#sidebar.scheme-light,
#sidebar.scheme-light #sidebar-wrap:before {
    border-right: 1px solid rgba(0, 0, 0, 0.15);
}

#sidebar.scheme-light accordion .panel-group .panel > .panel-heading .panel-title,
#sidebar.scheme-light #sidebar-wrap:before accordion .panel-group .panel > .panel-heading .panel-title {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

#sidebar.scheme-light accordion .panel-group .panel > .panel-heading .panel-title > a,
#sidebar.scheme-light #sidebar-wrap:before accordion .panel-group .panel > .panel-heading .panel-title > a {
    color: rgba(0, 0, 0, 0.5);
}

#sidebar.scheme-light accordion .panel-group .panel > .panel-heading .panel-title > a:hover,
#sidebar.scheme-light #sidebar-wrap:before accordion .panel-group .panel > .panel-heading .panel-title > a:hover {
    color: rgba(0, 0, 0, 0.8);
}

#sidebar.scheme-light accordion .panel-group .panel.closed > .panel-heading,
#sidebar.scheme-light #sidebar-wrap:before accordion .panel-group .panel.closed > .panel-heading {
    background-color: rgba(0, 0, 0, 0.05);
}

#sidebar.scheme-light accordion .panel-group .panel.closed > .panel-heading .panel-title > a,
#sidebar.scheme-light #sidebar-wrap:before accordion .panel-group .panel.closed > .panel-heading .panel-title > a {
    color: rgba(0, 0, 0, 0.8);
}

#sidebar.scheme-light accordion .panel-group .panel.closed > .panel-heading .panel-title > a:hover,
#sidebar.scheme-light #sidebar-wrap:before accordion .panel-group .panel.closed > .panel-heading .panel-title > a:hover {
    color: black;
}

#sidebar.scheme-light accordion .panel-group .panel .panel-body,
#sidebar.scheme-light #sidebar-wrap:before accordion .panel-group .panel .panel-body {
    color: rgba(0, 0, 0, 0.7);
}

#sidebar.scheme-light .summary .media .media-body,
#sidebar.scheme-light #sidebar-wrap:before .summary .media .media-body {
    color: rgba(0, 0, 0, 0.3);
}

#sidebar.scheme-light .summary .media .media-body .media-heading,
#sidebar.scheme-light #sidebar-wrap:before .summary .media .media-body .media-heading {
    color: rgba(0, 0, 0, 0.5);
}

#sidebar.scheme-light #navigation > li.open > a,
#sidebar.scheme-light #navigation > li:hover > a {
    color: rgba(0, 0, 0, 0.7);
    background-color: rgba(0, 0, 0, 0.05);
}

#sidebar.scheme-light #navigation > li.open > i,
#sidebar.scheme-light #navigation > li:hover > i {
    color: rgba(0, 0, 0, 0.7);
}

#sidebar.scheme-light #navigation > li.active > a {
    color: white;
    background-color: #16A085;
}

#sidebar.scheme-light #navigation > li.active > i {
    color: white;
}

#sidebar.scheme-light #navigation > li a {
    color: rgba(0, 0, 0, 0.5);
}

#sidebar.scheme-light #navigation .dropdown > ul {
    background-color: rgba(0, 0, 0, 0.1);
}

#sidebar.scheme-light #navigation .dropdown > ul li:hover > a,
#sidebar.scheme-light #navigation .dropdown > ul li.active > a {
    color: #16A085;
}

#sidebar.scheme-light #navigation .submenu.open > a {
    color: rgba(0, 0, 0, 0.7) !important;
}

#sidebar.scheme-light #navigation .submenu > ul {
    background-color: transparent;
}

.appWrapper.light-scheme-color #navigation > li.active > a {
    background-color: rgba(255, 255, 255, 0.1);
}

.appWrapper.light-scheme-color #loading-bar .bar {
    background-color: #16A085;
}

.appWrapper.light-scheme-color #loading-bar-spinner .spinner-icon {
    border-top-color: #16A085;
    border-left-color: #16A085;
}

.appWrapper.light-scheme-color #loading-bar .peg {
    -webkit-box-shadow: #16A085 1px 0 6px 1px;
    box-shadow: #16A085 1px 0 6px 1px;
}

.appWrapper.light-scheme-color #navigation .dropdown > ul li:hover a,
.appWrapper.light-scheme-color #navigation .dropdown > ul li.active a {
    color: rgba(255, 255, 255, 0.8);
}

.appWrapper.light-scheme-color .pageheader .page-bar .page-breadcrumb > li:not(:first-child) > a:hover {
    color: rgba(255, 255, 255, 0.8);
}

.sidebar-sm #sidebar.scheme-light,
.sidebar-xs #sidebar.scheme-light,
.hz-menu #sidebar.scheme-light {
    border-right: 0;
}

.sidebar-sm #sidebar.scheme-light #navigation,
.sidebar-xs #sidebar.scheme-light #navigation,
.hz-menu #sidebar.scheme-light #navigation {
    border-right: 1px solid rgba(0, 0, 0, 0.15);
}

.sidebar-sm #sidebar.scheme-light #navigation li > a,
.sidebar-xs #sidebar.scheme-light #navigation li > a,
.hz-menu #sidebar.scheme-light #navigation li > a {
    font-weight: 400;
}

.sidebar-sm #sidebar.scheme-light #navigation li.dropdown > ul,
.sidebar-sm #sidebar.scheme-light #navigation li.submenu > ul,
.sidebar-xs #sidebar.scheme-light #navigation li.dropdown > ul,
.sidebar-xs #sidebar.scheme-light #navigation li.submenu > ul,
.hz-menu #sidebar.scheme-light #navigation li.dropdown > ul,
.hz-menu #sidebar.scheme-light #navigation li.submenu > ul {
    background-color: #F2F2F2;
}

/* printing */
@media print {
    body,
    .custom-font {
        font-family: "Times New Roman", Times, serif;
        font-size: 12px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-size: 14px;
    }

    .no-print, .no-print * {
        display: none !important;
    }

    [class*="col-md-"] {
        display: inline-block;
        float: left;
    }

    .col-md-12 {
        width: 100%;
    }

    .col-md-11 {
        width: 91.66666666666666%;
    }

    .col-md-10 {
        width: 83.33333333333334%;
    }

    .col-md-9 {
        width: 75%;
    }

    .col-md-8 {
        width: 66.66666666666666%;
    }

    .col-md-7 {
        width: 58.333333333333336%;
    }

    .col-md-6 {
        width: 50%;
    }

    .col-md-5 {
        width: 41.66666666666667%;
    }

    .col-md-4 {
        width: 33.33333333333333%;
    }

    .col-md-3 {
        width: 25%;
    }

    .col-md-2 {
        width: 16.666666666666664%;
    }

    .col-md-1 {
        width: 8.333333333333332%;
    }

    .visible-md-block,
    .visible-lg-block {
        display: block !important;
    }

    #header,
    #sidebar,
    #rightbar {
        display: none;
    }

    #content {
        top: 0 !important;
        right: 0 !important;
        left: 0 !important;

        margin: 0 !important;
        padding: 0 !important;
    }

    #content .pageheader,
    #content .alert {
        display: none;
    }

    #content .add-nav .nav-heading,
    #content .add-nav .nav-tabs {
        display: none;
    }

    #content .price-total {
        float: right !important;

        width: 380px;
    }

    #content .page {
        padding: 0 !important;
    }

    .tile > .tile-header {
        display: none;
    }
}

@-webkit-keyframes littleFadeInLeft {
    0% {
        -webkit-transform: translate3d(-20px, 0, 0);
        transform: translate3d(-20px, 0, 0);

        opacity: 0;
    }
    100% {
        -webkit-transform: none;
        transform: none;

        opacity: 1;
    }
}

@keyframes littleFadeInLeft {
    0% {
        -webkit-transform: translate3d(-20px, 0, 0);
        -ms-transform: translate3d(-20px, 0, 0);
        transform: translate3d(-20px, 0, 0);

        opacity: 0;
    }
    100% {
        -webkit-transform: none;
        -ms-transform: none;
        transform: none;

        opacity: 1;
    }
}

.littleFadeInLeft {
    -webkit-animation-name: littleFadeInLeft;
    animation-name: littleFadeInLeft;
}

@-webkit-keyframes littleFadeInRight {
    0% {
        -webkit-transform: translate3d(20px, 0, 0);
        transform: translate3d(20px, 0, 0);

        opacity: 0;
    }
    100% {
        -webkit-transform: none;
        transform: none;

        opacity: 1;
    }
}

@keyframes littleFadeInRight {
    0% {
        -webkit-transform: translate3d(20px, 0, 0);
        -ms-transform: translate3d(20px, 0, 0);
        transform: translate3d(20px, 0, 0);

        opacity: 0;
    }
    100% {
        -webkit-transform: none;
        -ms-transform: none;
        transform: none;

        opacity: 1;
    }
}

.littleFadeInRight {
    -webkit-animation-name: littleFadeInRight;
    animation-name: littleFadeInRight;
}

@-webkit-keyframes littleFadeInUp {
    0% {
        -webkit-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0);

        opacity: 0;
    }
    100% {
        -webkit-transform: none;
        transform: none;

        opacity: 1;
    }
}

@keyframes littleFadeInUp {
    0% {
        -webkit-transform: translate3d(0, 20px, 0);
        -ms-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0);

        opacity: 0;
    }
    100% {
        -webkit-transform: none;
        -ms-transform: none;
        transform: none;

        opacity: 1;
    }
}

.littleFadeInUp {
    -webkit-animation-name: littleFadeInUp;
    animation-name: littleFadeInUp;
}

@-webkit-keyframes littleFadeInDown {
    0% {
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0);

        opacity: 0;
    }
    100% {
        -webkit-transform: none;
        transform: none;

        opacity: 1;
    }
}

@keyframes littleFadeInDown {
    0% {
        -webkit-transform: translate3d(0, -20px, 0);
        -ms-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0);

        opacity: 0;
    }
    100% {
        -webkit-transform: none;
        -ms-transform: none;
        transform: none;

        opacity: 1;
    }
}

.littleFadeInDown {
    -webkit-animation-name: littleFadeInDown;
    animation-name: littleFadeInDown;
}

@-webkit-keyframes ripple {
    100% {
        transform: scale(2.5);

        opacity: 0;
    }
}

@keyframes ripple {
    100% {
        transform: scale(2.5);

        opacity: 0;
    }
}

.ripple {
    -webkit-animation-name: ripple;
    animation-name: ripple;
}

@-webkit-keyframes fadeOutText {
    0% {
        color: transparent;
    }
    80% {
        color: transparent;
    }
    100% {
        color: #FFFFFF;
    }
}

@-moz-keyframes fadeOutText {
    0% {
        color: transparent;
    }
    80% {
        color: transparent;
    }
    100% {
        color: #FFFFFF;
    }
}

@keyframes fadeOutText {
    0% {
        color: transparent;
    }
    80% {
        color: transparent;
    }
    100% {
        color: #FFFFFF;
    }
}

@-webkit-keyframes moveToRight {
    80% {
        -webkit-transform: translateX(350%);
    }
    81% {
        -webkit-transform: translateX(350%);

        opacity: 1;
    }
    82% {
        -webkit-transform: translateX(350%);

        opacity: 0;
    }
    83% {
        -webkit-transform: translateX(-50%);

        opacity: 0;
    }
    84% {
        -webkit-transform: translateX(-50%);

        opacity: 1;
    }
    100% {
        -webkit-transform: translateX(0%);
    }
}

@-moz-keyframes moveToRight {
    80% {
        -moz-transform: translateX(350%);
    }
    81% {
        -moz-transform: translateX(350%);

        opacity: 1;
    }
    82% {
        -moz-transform: translateX(350%);

        opacity: 0;
    }
    83% {
        -moz-transform: translateX(-50%);

        opacity: 0;
    }
    84% {
        -moz-transform: translateX(-50%);

        opacity: 1;
    }
    100% {
        -moz-transform: translateX(0%);
    }
}

@keyframes moveToRight {
    80% {
        transform: translateX(350%);
    }
    81% {
        transform: translateX(350%);

        opacity: 1;
    }
    82% {
        transform: translateX(350%);

        opacity: 0;
    }
    83% {
        transform: translateX(-50%);

        opacity: 0;
    }
    84% {
        transform: translateX(-50%);

        opacity: 1;
    }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes scaleUp {
    80% {
        -webkit-transform: scale(2);

        opacity: 0;
    }
    100% {
        -webkit-transform: scale(2);

        opacity: 0;
    }
}

@-moz-keyframes scaleUp {
    80% {
        -moz-transform: scale(2);

        opacity: 0;
    }
    100% {
        -moz-transform: scale(2);

        opacity: 0;
    }
}

@keyframes scaleUp {
    80% {
        transform: scale(2);

        opacity: 0;
    }
    100% {
        transform: scale(2);

        opacity: 0;
    }
}

@-webkit-keyframes fillToRight {
    to {
        width: 100%;
    }
}

@-moz-keyframes fillToRight {
    to {
        width: 100%;
    }
}

@keyframes fillToRight {
    to {
        width: 100%;
    }
}

@-webkit-keyframes emptyBottom {
    to {
        height: 100%;
    }
}

@-moz-keyframes emptyBottom {
    to {
        height: 100%;
    }
}

@keyframes emptyBottom {
    to {
        height: 100%;
    }
}

@-webkit-keyframes scaleFade {
    50% {
        -webkit-transform: scale(1);

        opacity: 1;
    }
    100% {
        -webkit-transform: scale(2.5);

        opacity: 0;
    }
}

@-moz-keyframes scaleFade {
    50% {
        -moz-transform: scale(1);

        opacity: 1;
    }
    100% {
        -moz-transform: scale(2.5);

        opacity: 0;
    }
}

@keyframes scaleFade {
    50% {
        transform: scale(1);

        opacity: 1;
    }
    100% {
        transform: scale(2.5);

        opacity: 0;
    }
}

@-webkit-keyframes dropDown {
    to {
        -webkit-transform: scale(1);

        opacity: 1;
    }
}

@-moz-keyframes dropDown {
    to {
        -moz-transform: scale(1);

        opacity: 1;
    }
}

@keyframes dropDown {
    to {
        transform: scale(1);

        opacity: 1;
    }
}

@-webkit-keyframes dropDownFade {
    50% {
        -webkit-transform: scale(1);

        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1.5);

        opacity: 0;
    }
}

@-moz-keyframes dropDownFade {
    50% {
        -moz-transform: scale(1);

        opacity: 1;
    }
    100% {
        -moz-transform: scale(1.5);

        opacity: 0;
    }
}

@keyframes dropDownFade {
    50% {
        transform: scale(1);

        opacity: 1;
    }
    100% {
        transform: scale(1.5);

        opacity: 0;
    }
}

@-webkit-keyframes moveUp {
    0% {
        -webkit-transform: translateY(50%);

        opacity: 0;
    }
    100% {
        -webkit-transform: translateY(0);

        opacity: 1;
    }
}

@-moz-keyframes moveUp {
    0% {
        -moz-transform: translateY(50%);

        opacity: 0;
    }
    100% {
        -moz-transform: translateY(0);

        opacity: 1;
    }
}

@keyframes moveUp {
    0% {
        transform: translateY(50%);

        opacity: 0;
    }
    100% {
        transform: translateY(0);

        opacity: 1;
    }
}

@-webkit-keyframes scaleFromUp {
    0% {
        -webkit-transform: scale(0);

        opacity: 0;
    }
    100% {
        -webkit-transform: scale(1);

        opacity: 1;
    }
}

@-moz-keyframes scaleFromUp {
    0% {
        -moz-transform: scale(0);

        opacity: 0;
    }
    100% {
        -moz-transform: scale(1);

        opacity: 1;
    }
}

@keyframes scaleFromUp {
    0% {
        transform: scale(0);

        opacity: 0;
    }
    100% {
        transform: scale(1);

        opacity: 1;
    }
}

@-webkit-keyframes spinAround {
    from {
        -webkit-transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
    }
}

@-moz-keyframes spinAround {
    from {
        -moz-transform: rotate(0deg);
    }
    to {
        -moz-transform: rotate(360deg);
    }
}

@keyframes spinAround {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@-webkit-keyframes toRightFromLeft {
    49% {
        -webkit-transform: translate(100%);
    }
    50% {
        -webkit-transform: translate(-100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@-moz-keyframes toRightFromLeft {
    49% {
        -moz-transform: translate(100%);
    }
    50% {
        -moz-transform: translate(-100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@keyframes toRightFromLeft {
    49% {
        transform: translate(100%);
    }
    50% {
        transform: translate(-100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@-webkit-keyframes toLeftFromRight {
    49% {
        -webkit-transform: translate(-100%);
    }
    50% {
        -webkit-transform: translate(100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@-moz-keyframes toLeftFromRight {
    49% {
        -moz-transform: translate(-100%);
    }
    50% {
        -moz-transform: translate(100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@keyframes toLeftFromRight {
    49% {
        transform: translate(-100%);
    }
    50% {
        transform: translate(100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@-webkit-keyframes toTopFromBottom {
    49% {
        -webkit-transform: translateY(-100%);
    }
    50% {
        -webkit-transform: translateY(100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@-moz-keyframes toTopFromBottom {
    49% {
        -moz-transform: translateY(-100%);
    }
    50% {
        -moz-transform: translateY(100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@keyframes toTopFromBottom {
    49% {
        transform: translateY(-100%);
    }
    50% {
        transform: translateY(100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@-webkit-keyframes toBottomFromTop {
    49% {
        -webkit-transform: translateY(100%);
    }
    50% {
        -webkit-transform: translateY(-100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@-moz-keyframes toBottomFromTop {
    49% {
        -moz-transform: translateY(100%);
    }
    50% {
        -moz-transform: translateY(-100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@keyframes toBottomFromTop {
    49% {
        transform: translateY(100%);
    }
    50% {
        transform: translateY(-100%);

        opacity: 0;
    }
    51% {
        opacity: 1;
    }
}

@-webkit-keyframes sonarEffect {
    0% {
        opacity: 0.1;
    }
    40% {
        opacity: 0.2;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1), 0 0 5px 5px, 0 0 0 5px rgba(255, 255, 255, 0.5);
    }
    100% {
        -webkit-transform: scale(1.5);

        opacity: 0;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1), 0 0 5px 5px, 0 0 0 5px rgba(255, 255, 255, 0.5);
    }
}

@-moz-keyframes sonarEffect {
    0% {
        opacity: 0.1;
    }
    40% {
        opacity: 0.2;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1), 0 0 5px 5px, 0 0 0 5px rgba(255, 255, 255, 0.5);
    }
    100% {
        -moz-transform: scale(1.5);

        opacity: 0;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1), 0 0 5px 5px, 0 0 0 5px rgba(255, 255, 255, 0.5);
    }
}

@keyframes sonarEffect {
    0% {
        opacity: 0.1;
    }
    40% {
        opacity: 0.2;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1), 0 0 5px 5px, 0 0 0 5px rgba(255, 255, 255, 0.5);
    }
    100% {
        transform: scale(1.5);

        opacity: 0;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1), 0 0 5px 5px, 0 0 0 5px rgba(255, 255, 255, 0.5);
    }
}

@-webkit-keyframes slit {
    50% {
        -webkit-transform: translateZ(-250px) rotateY(89deg);
        -webkit-animation-timing-function: ease-out;

        opacity: 0.5;
    }
    100% {
        -webkit-transform: translateZ(0) rotateY(0deg);

        opacity: 1;
    }
}

@-moz-keyframes slit {
    50% {
        -moz-transform: translateZ(-250px) rotateY(89deg);
        -moz-animation-timing-function: ease-out;

        opacity: 0.5;
    }
    100% {
        -moz-transform: translateZ(0) rotateY(0deg);

        opacity: 1;
    }
}

@keyframes slit {
    50% {
        transform: translateZ(-250px) rotateY(89deg);
        animation-timing-function: ease-in;

        opacity: 1;
    }
    100% {
        transform: translateZ(0) rotateY(0deg);

        opacity: 1;
    }
}

.purchasing-empire-modal {
    margin-left: -22%;
}

@media (min-width: 1102px) {
    .modal-xlg {
        width: 1100px;
    }

    .modal-half {
        width: 50%;
    }

    .modal-full {
        width: 90%;
    }

    .modal-full-100 {
        width: 100%;
        margin: 0;
    }
}

.dropdown-menu {
    z-index: 200000;
}

.typeahead.dropdown-menu {
    z-index: 200000;
}

/*.modal-body{overflow-y: inherit;}*/

*[class*="bg-"]:not(.bg-default):not(.bg-white):not(.bg-tr-white) .dropdown-menu > .active > a,
*[class*="bg-"]:not(.bg-default):not(.bg-white):not(.bg-tr-white) .dropdown-menu > .active > a:hover,
*[class*="bg-"]:not(.bg-default):not(.bg-white):not(.bg-tr-white) .dropdown-menu > .active > a:focus {
    color: white !important;
    cursor: pointer;
}

@media only screen and (max-width: 767px) {
    .weighted-xs {
        padding-left: 0;
    }
}

@media only screen and (max-width: 767px) {
    .xs-mb-10 {
        margin-bottom: 10px;
    }
}

/*.nav-accordion > div[role="tablist"] {*/
/*display: flex;*/
/*flex-direction: column;*/
/*height: 100%;*/
/*}*/

/*.nav-accordion > div[role="tablist"] > div.panel {*/
/*flex: 1;*/
/*}*/


/*#nymblSupport {*/
/*margin-bottom: 10px;*/
/*}*/

/*#nymblContact {*/
/*padding: 15px 15px 15px 27px;*/
/*color: rgba(255, 255, 255, 0.5);*/
/*}*/

/*.nymblContact-item {*/
/*margin-left: 10px;*/
/*}*/

/*a.nymblContact-item {*/
/*color: inherit;*/
/*}*/


.purchase-order-item-row {
    border-top: 1px solid gray;
    padding: 20px;
}

.purchase-order-item-last-row {
    border-bottom: 1px solid gray;
}

.remove-line-item-icon {
    color: red;
    font-size: 20px;
}


.typeahead-demo .custom-popup-wrapper {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    background-color: #f9f9f9;
}

.typeahead-demo .custom-popup-wrapper > .message {
    padding: 10px 20px;
    border-bottom: 1px solid #ddd;
    color: #868686;
}

.typeahead-demo .custom-popup-wrapper > .dropdown-menu {
    position: static;
    float: none;
    display: block;
    min-width: 160px;
    background-color: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
}

input[type="text"]:disabled {
    background: #eee !important;
}

#send-message-modal-form > div:nth-child(2) > div > ul {
    top: 35px !important;
}


td {
    position: relative;
}

tr.strikeout td:before {
    content: " ";
    position: absolute;
    top: 50%;
    left: 0;
    border-bottom: 1px solid #111;
    width: 100%;
}

tr.strikeout td:after {
    content: "\00B7";
    font-size: 1px;
}

@media only screen and (max-width: 993px) {
    .col-md-mt-10 {
        margin-top: 15px;
    }
}

.alert-error-link {
    color: #721c24;
}

pagination-link {
    font-size: 16px;
}

.pagination-link:hover {
    cursor: pointer;
}

.boldPage {
    font-weight: bold;
}

@media screen and (max-width: 767px) {
    .new-appointment-button {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .new-appointment-button-container {
        text-align: center;
    }
}

.dropdown-menu {
    min-width: calc(100% - 15px) !important;
}

.nymbl-icon-button {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    outline: none;
    border: 0;
    background: transparent;
}

.inactive-patient-typeahead-result {
    color: red;
}

.inactive-patient-typeahead-result > div > span {
    text-decoration: line-through;
}

.status-history-modal-link:hover {
    text-decoration: underline;
}


.financial-noscroll::-webkit-outer-spin-button,
.financial-noscroll::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

input[type="date"] {
    position: relative;
    padding: 10px;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    color: transparent;
    background: none;
    z-index: 1;
}

input[type="date"]:before {
    background: none;
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    padding: 0px 5px 0px 0px;
    font-size: 1.33333333em;
    line-height: 1.75em;
    vertical-align: -15%;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    content: '\f073';
    position: absolute;
    right: 8px;
    color: #51445F;
}

.allowableExceedsBillableError {
    border-color: #FF7B76;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}

.focus-button:focus {
    border-color: rgba(82, 168, 236, .8) !important;
    border-width: medium;
}

td.break {
    width: 30%;
    word-break: break-all;
}

.focusable-dropdown:focus {
    border: 1px solid;
    border-color: #22BEEF;
    outline: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

@media (min-width: 0px) {
    .sticky-row {
        position: sticky;
        top: 90px;
        background: #e7eaeb;
        z-index: 1000;
    }
}

@media (min-width: 768px) {
    .sticky-row {
        position: sticky;
        top: 78px;
        background: #e7eaeb;
        z-index: 1000;
    }
}

@media (min-width: 1150px) {
    .sticky-row {
        position: sticky;
        top: 45px;
        background: #e7eaeb;
        z-index: 1000;
    }
}

.stripe-company-container {
    display: flex;
    align-items: center;
    justify-content: right;
}

.stripe-company-child p {
    margin: 0;
    padding: 0;
}

.hcpcs-selection-table {
    .modifier-header {
        padding-left: 5px;
        gap: 5px;    
    }

    .code-row {
        padding: 5px;
    }

    .modifiers-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .code-row .modifiers {
        display: flex;
        gap: 5px;
    }

    .modifier-width {
        width: 60px;
        min-width: 60px;
    }

    .rental-width {
        width: 50px;
        min-width: 50px;
    }

    .rental-desktop {
        display: none;
    }

    .rental-tablet {
        display: flex;
        gap: 5px;
        padding-left: 65px;
    }

    .date-modifiers {
        padding-left: 65px;
    }

    .search {
        gap: 10px;
    }

    .search input.ui-select-search {
        width: fit-content !important;
    }

    @media (min-width: 992px) {
        .modifier-header {
            padding-left: 10px;
            gap: 10px;    
        }

        .modifier-width {
            width: 80px;
            min-width: 60px;
        }

        .rental-width {
            width: 60px;
            min-width: 60px;
        }

        .code-row {
            padding: 5px 10px;
        }

        .code-row .modifiers {
            gap: 10px;
        }

        .rental-tablet, .date-modifiers {
            padding-left: 90px;
        }

        .search {
            gap: 20px;
        }
    }

    @media (min-width: 1102px) {
        .rental-desktop {
            display: initial;
        }

        .rental-tablet {
            display: none;
        }

        .date-modifiers {
            padding-left: 0;
        }

        .modifiers-group {
            flex-direction: row;
        }

        .search {
            gap: 40px;
        }
    }
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.gap-10 {
    gap: 10px;
}


