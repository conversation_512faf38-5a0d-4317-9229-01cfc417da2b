-- START NYM-3366
ALTER TABLE `demo`.`patient_medical_history`
    ADD COLUMN `latex_allergy` TINYINT(1) DEFAULT '0' COMMENT 'Latex Allergy' AFTER `mrsa_staph_infection`;

ALTER TABLE `demo`.`patient_medical_history_audit`
    ADD COLUMN `latex_allergy` TINYINT(1) DEFAULT '0' COMMENT 'Latex Allergy' AFTER `mrsa_staph_infection`;
-- END NYM-3366

-- START NYM-3275: Password History Tracking
-- Add new password history policy settings (matching existing column sizes: field=40, section=30, value=300)
INSERT INTO `demo`.`system_setting` (field, section, value) VALUES
    ('password_history_depth', 'password', '6'),
    ('password_reuse_days', 'password', '365')
ON DUPLICATE KEY UPDATE value = VALUES(value);
-- END NYM-3275

ALTER TABLE `demo`.`lmn_evaluation_task`
    ADD COLUMN `final_lmn_note` LONGTEXT NULL AFTER `lmn_notes`;

ALTER TABLE `demo`.`lmn_evaluation_task_audit`
    ADD COLUMN `final_lmn_note` LONGTEXT NULL AFTER `lmn_notes`;

delete from `demo`.`custom_form_mapping` where custom_form_id = (select id from `demo`.`custom_form` WHERE (`file_name` = 'SOAP-Letter of Medical Necessity Template.pdf'));
DELETE FROM `demo`.`custom_form` WHERE (`file_name` = 'SOAP-Letter of Medical Necessity Template.pdf') and id > 0;
delete from `demo`.`custom_form_mapping` where custom_form_id = (select id from `demo`.`custom_form` WHERE (`file_name` = 'SUMMARY-Letter of Medical Necessity Template.pdf'));
DELETE FROM `demo`.`custom_form` WHERE (`file_name` = 'SUMMARY-Letter of Medical Necessity Template.pdf') and id > 0;

-- START NYM-2746 --
INSERT INTO `demo`.`file_type`
(`key`, `name`, `color`, `not_editable`, `is_physician_documentation`)
VALUES ('print_screen', 'Print Screen', 'default', 1, 0);
-- END NYM-2746 --
