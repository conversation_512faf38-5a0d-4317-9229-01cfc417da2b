package com.nymbl.tenant;

import com.nymbl.master.model.UserDto;
import com.nymbl.tenant.dashboard.dto.*;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

public class ProjectionUtil {

    public static PrescriptionInsuranceVerifDto makePrescriptionInsuranceVerifDto(Long id) {
        return new PrescriptionInsuranceVerifDto() {
            @Override
            public Long getPrescriptionId() {
                return id;
            }

            @Override
            public Long getInsuranceVerificationId() {
                return id;
            }

            @Override
            public Long getPractitionerId() {
                return id;
            }

            @Override
            public String getFirstName() {
                return "Test " + id;
            }

            @Override
            public String getLastName() {
                return "Last " + id;
            }

            @Override
            public String getMiddleName() {
                return null;
            }

            @Override
            public String getCredentials() {
                return null;
            }
        };
    }

    public static  UserDto makeUserDto(Long id) {
        UserDto dto = new UserDto() {
            @Override
            public Long getId() {
                return id;
            }

            @Override
            public String getFirstName() {
                return "firstName" + id;
            }

            @Override
            public String getLastName() {
                return "lastName" + id;
            }

            @Override
            public String getMiddleName() {
                return "Test" + id;
            }

            @Override
            public String getCredentials() {
                return "Trainer";
            }

            @Override
            public String getEmail() {
                return null;
            }

            @Override
            public String getPhoneNumber() {
                return null;
            }

            @Override
            public Timestamp getCreatedAt() {
                return null;
            }

            @Override
            public Timestamp getUpdatedAt() {
                return null;
            }
        };

        return dto;
    }

    public static TaskDto makeTaskDto(Long id) {
        return new TaskDto() {
            @Override
            public Long getId() {
                return id;
            }

            @Override
            public String getName() {
                return "test";
            }

            @Override
            public Long getRoleId() {
                return 1L;
            }

            @Override
            public String getPriority() {
                return "test";
            }

            @Override
            public Date getDueDate() {
                return new Date(System.currentTimeMillis());
            }

            @Override
            public String getDescription() {
                return "test";
            }

            @Override
            public Long getPatientId() {
                return id;
            }

            @Override
            public String getFirstName() {
                return "test";
            }

            @Override
            public String getLastName() {
                return "test";
            }

            @Override
            public String getEmail() {
                return null;
            }

            @Override
            public Long getPrimaryBranchId() {
                return null;
            }

            @Override
            public String getPrimaryBranchName() {
                return "test";
            }

            @Override
            public java.util.Date getPatientCreatedDate() {
                return null;
            }
        };
    }

    public static BranchDto makeBranchDto() {
        return new BranchDto() {
            @Override
            public Long getId() {
                return 1L;
            }

            @Override
            public String getName() {
                return "Test";
            }

            @Override
            public BigDecimal getMonthlySalesGoal() {
                return BigDecimal.TEN;
            }

            @Override
            public String getStripeAccountId() {
                return null;
            }

            @Override
            public String getStripeLocationId() {
                return null;
            }

            @Override
            public String getBillingEmail() {
                return null;
            }
        };
    }


    public static ClaimDto makeClaimDto(Long id) {
        return new ClaimDto() {
            @Override
            public String getDeviceType() {
                return "test";
            }

            @Override
            public Long getId() {
                return id;
            }

            @Override
            public Date getUpdatedAt() {
                return new Date(System.currentTimeMillis());
            }

            @Override
            public Date getDateOfService() {
                return new Date(System.currentTimeMillis());
            }

            @Override
            public BigDecimal getTotalClaimBalance() {
                return BigDecimal.TEN;
            }

            @Override
            public BigDecimal getTotalPtResponsibilityBalance() {
                return null;
            }

            @Override
            public Date getCreatedAt() {
                return new Date(System.currentTimeMillis());
            }

            @Override
            public Boolean getPaymentPlan() {
                return true;
            }

            @Override
            public Long getPrescriptionId() {
                return id;
            }

            @Override
            public Long getPatientInsuranceId() {
                return id;
            }

            @Override
            public Date getClaimSubmission() {
                return null;
            }

            @Override
            public String getStatus() {
                return null;
            }

            @Override
            public String getBillingBranchName() {
                return null;
            }

            @Override
            public Long getPatientId() {
                return null;
            }

            @Override
            public String getFirstName() {
                return "Test";
            }

            @Override
            public String getLastName() {
                return "Test";
            }

            @Override
            public String getEmail() {
                return null;
            }


            @Override
            public Long getPrimaryBranchId() {
                return null;
            }

            @Override
            public String getPrimaryBranchName() {
                return null;
            }

            @Override
            public java.util.Date getPatientCreatedDate() {
                return null;
            }


        };
    }

    public static AppointmentDto makeAppointmentDto(Long id) {
        return new AppointmentDto() {
            @Override
            public Long getId() {
                return id;
            }

            @Override
            public Long getPatientId() {
                return id;
            }

            @Override
            public OffsetDateTime getStartDateTime() {
                return OffsetDateTime.now();
            }

            @Override
            public OffsetDateTime getEndDateTime() {
                return OffsetDateTime.now();
            }

            @Override
            public String getAppointmentType() {
                return "Test Type";
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Long getUserFourId() {
                return 1L;
            }

            @Override
            public String getFirstName() {
                return "Dwayne";
            }

            @Override
            public String getLastName() {
                return "Johnson";
            }

            @Override
            public String getEmail() {
                return null;
            }


            @Override
            public Long getPrimaryBranchId() {
                return null;
            }

            @Override
            public String getPrimaryBranchName() {
                return null;
            }

            @Override
            public java.util.Date getPatientCreatedDate() {
                return null;
            }
        };
    }


    public static  List<MissingAppointmentNotesDto> makeResponseList(Long userId) {
        List<MissingAppointmentNotesDto> list =  new ArrayList<>();
        if(userId == null){
            MissingAppointmentNotesDto notesDto = makeUserDto(1L, 1);
            MissingAppointmentNotesDto notesDto1 = makeUserDto(2L, 40);
            MissingAppointmentNotesDto notesDto2 = makeUserDto(3L, 0);
            list.add(notesDto2);
            list.add(notesDto1);
            list.add(notesDto);
        } else {
            list.add(makeUserDto(userId, 0));
        }

        return list;
    }

    public static MissingAppointmentNotesDto makeUserDto(Long id, int dateAdjust) {
        return new MissingAppointmentNotesDto() {
            @Override
            public Long getId() {
                return id;
            }

            @Override
            public Long getPatientId() {
                return id;
            }

            @Override
            public OffsetDateTime getStartDateTime() {
                return OffsetDateTime.now();
            }

            @Override
            public OffsetDateTime getEndDateTime() {
                return OffsetDateTime.now();
            }

            @Override
            public Long getAttendingUserId() {
                return id;
            }

            @Override
            public Long getUserTwoId() {
                return id;
            }

            @Override
            public Long getUserThreeId() {
                return id;
            }

            @Override
            public Long getSupervisingUserId() {
                return id;
            }

            @Override
            public Long getPrescriptionId() {
                return id;
            }

            @Override
            public Long getPrescriptionTwoId() {
                return id;
            }
            @Override
            public String getAppointmentType() {
                return "Test Type";
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Long getUserFourId() {
                return 0L;
            }

            @Override
            public String getFirstName() {
                return "Rob";
            }

            @Override
            public String getLastName() {
                return "Brown";
            }

            @Override
            public String getEmail() {
                return null;
            }

            @Override
            public Long getPrimaryBranchId() {
                return null;
            }

            @Override
            public String getPrimaryBranchName() {
                return null;
            }

            @Override
            public java.util.Date getPatientCreatedDate() {
                return null;
            }

            @Override
            public String getBranch() {
                return "Test";
            }

            @Override
            public String getStatus() {
                return "checked_out";
            }
        };
    }

    public static PrescriptionDto makePrescriptionDto(Long id) {
        return new PrescriptionDto() {
            @Override
            public Long getId() {
                return id;
            }

            @Override
            public String getDeviceType() {
                return null;
            }

            @Override
            public BigDecimal getTotalAllowable() {
                return null;
            }

            @Override
            public Date getProjectedDeliveryDate() {
                return null;
            }

            @Override
            public Date getPrescriptionDate() {
                return new Date(System.currentTimeMillis());
            }

            @Override
            public Timestamp getCreatedAt() {
                Date date = new Date(System.currentTimeMillis());
                long time = date.getTime();
                return new Timestamp(time);
            }

            @Override
            public String getSection() {
                return "test";
            }

            @Override
            public String getOrthoticOrProsthetic() {
                return "test";
            }

            @Override
            public Long getPatientId() {
                return id;
            }

            @Override
            public String getFirstName() {
                return "Test";
            }

            @Override
            public String getLastName() {
                return "Test";
            }

            @Override
            public String getEmail() {
                return null;
            }


            @Override
            public Long getPrimaryBranchId() {
                return null;
            }

            @Override
            public String getPrimaryBranchName() {
                return null;
            }

            @Override
            public java.util.Date getPatientCreatedDate() {
                return null;
            }

        };
    }
}
