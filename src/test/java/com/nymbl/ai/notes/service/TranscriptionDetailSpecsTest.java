package com.nymbl.ai.notes.service;

import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.tenant.model.Appointment;
import jakarta.persistence.criteria.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.jpa.domain.Specification;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

class TranscriptionDetailSpecsTest {

    @Mock
    private CriteriaBuilder criteriaBuilder;

    @Mock
    private CriteriaQuery<?> criteriaQuery;

    @Mock
    private Root<TranscriptionDetail> root;

    @Mock
    private Join<TranscriptionDetail, Appointment> appointmentJoin;

    @Mock
    private Path<Long> patientIdPath;

    @Mock
    private Path<Long> practitionerIdPath;

    @Mock
    private Path<Long> appointmentIdPath;

    @Mock
    private Path<String> stringPath;

    @Mock
    private Path<Timestamp> timestampPath;

    @Mock
    private Path<Boolean> booleanPath;

    @Mock
    private CriteriaBuilder.In<Long> inPredicate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Mock return types for root.get(...) with proper generics
        when(root.<Long>get("patientId")).thenReturn(patientIdPath);
        when(root.<Long>get("practitionerId")).thenReturn(practitionerIdPath);
        when(root.<Long>get("appointmentId")).thenReturn(appointmentIdPath);
        when(root.<String>get("status")).thenReturn(stringPath);
        when(root.<Boolean>get("isArchived")).thenReturn(booleanPath);

        // Mock join
        when(root.<TranscriptionDetail, Appointment>join("appointment", JoinType.INNER)).thenReturn(appointmentJoin);

        // Mock return types for join.get(...) with proper generics
        when(appointmentJoin.<Timestamp>get("startDateTime")).thenReturn(timestampPath);
        when(appointmentJoin.<Timestamp>get("endDateTime")).thenReturn(timestampPath);

        // Mock CriteriaBuilder methods
        when(criteriaBuilder.equal(any(), any())).thenReturn(mock(Predicate.class));
        when(criteriaBuilder.in(any(Path.class))).thenReturn(inPredicate);
        when(inPredicate.value((Long) any())).thenReturn(inPredicate);
        when(criteriaBuilder.and(any(Predicate.class))).thenReturn(mock(Predicate.class));
        when(criteriaBuilder.and(any(Predicate[].class))).thenReturn(mock(Predicate.class));
        when(criteriaBuilder.greaterThanOrEqualTo(any(), any(Timestamp.class))).thenReturn(mock(Predicate.class));
        when(criteriaBuilder.lessThanOrEqualTo(any(), any(Timestamp.class))).thenReturn(mock(Predicate.class));
    }

    @Nested
    @DisplayName("Search with all parameters")
    class SearchWithAllParameters {
        private final List<Long> practitionerId = Arrays.asList(1L, 2L);
        private final Long patientId = 1L;
        private final Long appointmentId = 1L;
        private final Date startDate = new Date();
        private final Date endDate = new Date();
        private final String status = "completed";

        @Test
        @DisplayName("Should create predicate with all parameters")
        void testSearchWithAllParameters() {
            Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(
                practitionerId, patientId, appointmentId, startDate, endDate, status, false);
            Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

            assertNotNull(predicate);
            verifyAllParameters();
        }

        @Test
        @DisplayName("Should create predicate with archived status")
        void testSearchWithArchivedStatus() {
            Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(
                practitionerId, patientId, appointmentId, startDate, endDate, status, true);
            Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

            assertNotNull(predicate);
            verifyAllParameters();
            verify(criteriaBuilder, times(1)).equal(root.get("isArchived"), true);
        }

        private void verifyAllParameters() {
            verify(criteriaBuilder, times(1)).equal(root.get("patientId"), patientId);
            verify(root, times(1)).get("practitionerId");
            verify(criteriaBuilder, times(1)).in(root.get("practitionerId"));
            verify(inPredicate, times(1)).value(1L);
            verify(inPredicate, times(1)).value(2L);
            verify(criteriaBuilder, times(1)).equal(root.get("appointmentId"), appointmentId);
            verify(criteriaBuilder, times(1)).greaterThanOrEqualTo(appointmentJoin.get("startDateTime"), new Timestamp(startDate.getTime()));
            verify(criteriaBuilder, times(1)).lessThanOrEqualTo(appointmentJoin.get("endDateTime"), new Timestamp(endDate.getTime()));
            verify(criteriaBuilder, times(1)).equal(root.get("status"), status);
        }
    }

    @Nested
    @DisplayName("Search with empty/null parameters")
    class SearchWithEmptyParameters {
        @Test
        @DisplayName("Should handle empty practitioner list")
        void testSearchWithEmptyPractitionerList() {
            Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(
                Collections.emptyList(), 1L, 1L, new Date(), new Date(), "completed", false);
            Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

            assertNotNull(predicate);
            verify(criteriaBuilder, times(0)).in(any(Path.class));
        }

        @Test
        @DisplayName("Should handle null practitioner list")
        void testSearchWithNullPractitionerList() {
            Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(
                null, 1L, 1L, new Date(), new Date(), "completed", false);
            Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

            assertNotNull(predicate);
            verify(criteriaBuilder, times(0)).in(any(Path.class));
        }

        @Test
        @DisplayName("Should handle null status")
        void testSearchWithNullStatus() {
            Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(
                    List.of(1L), 1L, 1L, new Date(), new Date(), null, false);
            Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

            assertNotNull(predicate);
            verify(criteriaBuilder, times(0)).equal(root.get("status"), null);
        }
    }

    @Nested
    @DisplayName("Search with date parameters")
    class SearchWithDateParameters {
        @Test
        @DisplayName("Should handle only start date")
        void testSearchWithOnlyStartDate() {
            Date startDate = new Date();
            Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(
                    List.of(1L), 1L, 1L, startDate, null, "completed", false);
            Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

            assertNotNull(predicate);
            verify(criteriaBuilder, times(1)).greaterThanOrEqualTo(appointmentJoin.get("startDateTime"), new Timestamp(startDate.getTime()));
            verify(criteriaBuilder, times(0)).lessThanOrEqualTo(any(), any(Timestamp.class));
        }

        @Test
        @DisplayName("Should handle only end date")
        void testSearchWithOnlyEndDate() {
            Date endDate = new Date();
            Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(
                    List.of(1L), 1L, 1L, null, endDate, "completed", false);
            Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

            assertNotNull(predicate);
            verify(criteriaBuilder, times(0)).greaterThanOrEqualTo(any(), any(Timestamp.class));
            verify(criteriaBuilder, times(1)).lessThanOrEqualTo(appointmentJoin.get("endDateTime"), new Timestamp(endDate.getTime()));
        }

        @Test
        @DisplayName("Should handle same start and end date")
        void testSearchWithSameStartAndEndDate() {
            Date date = new Date();
            Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(
                    List.of(1L), 1L, 1L, date, date, "completed", false);
            Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

            assertNotNull(predicate);
            verify(criteriaBuilder, times(1)).greaterThanOrEqualTo(appointmentJoin.get("startDateTime"), new Timestamp(date.getTime()));
            verify(criteriaBuilder, times(1)).lessThanOrEqualTo(appointmentJoin.get("endDateTime"), new Timestamp(date.getTime()));
        }
    }
}
