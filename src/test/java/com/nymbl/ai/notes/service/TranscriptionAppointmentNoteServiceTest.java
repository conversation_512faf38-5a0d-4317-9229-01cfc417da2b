package com.nymbl.ai.notes.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentation;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentationContainer;
import com.nymbl.ai.notes.data.clinicaldoc.Section;
import com.nymbl.ai.notes.data.conversation.Conversation;
import com.nymbl.ai.notes.data.conversation.ConversationContainer;
import com.nymbl.ai.notes.data.conversation.TranscriptSegment;
import com.nymbl.ai.notes.dto.*;
import com.nymbl.ai.notes.exception.TranscriptionAppointmentNotesException;
import com.nymbl.ai.notes.model.TranscriptionAppointmentNote;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.repository.TranscriptionAppointmentNoteRepository;
import com.nymbl.ai.notes.repository.TranscriptionDetailRepository;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Appointment;
import com.nymbl.tenant.model.Note;
import com.nymbl.tenant.model.Prescription;
import com.nymbl.tenant.service.AppointmentService;
import com.nymbl.tenant.service.NoteService;
import com.nymbl.tenant.service.NotificationService;
import com.nymbl.tenant.service.PrescriptionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class TranscriptionAppointmentNoteServiceTest {

    @Mock
    private TranscriptionDetailRepository transcriptionDetailRepository;

    @Mock
    private TranscriptionAppointmentNoteRepository transcriptionAppointmentNoteRepository;

    @Mock
    private TranscriptionUtil transcriptionUtil;

    @Mock
    private NotificationService notificationService;

    @Mock
    private UserService userService;

    @Mock
    private NoteService noteService;

    @Mock
    private AwsUtil awsUtil;

    @Mock
    private AWSS3Service awsS3Service;

    @Mock
    private PrescriptionService prescriptionService;

    @Mock
    private AppointmentService appointmentService;

    private TranscriptionAppointmentNoteService transcriptionAppointmentNoteService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        transcriptionAppointmentNoteService = new TranscriptionAppointmentNoteService(
                transcriptionDetailRepository,
                transcriptionAppointmentNoteRepository,
                transcriptionUtil,
                notificationService,
                prescriptionService,
                appointmentService,
                noteService,
                awsUtil,
                userService
        );
    }

    @Test
    void testSearch() {
        List<Long> practitionerId = Arrays.asList(1L, 2L);
        Long patientId = 1L;
        Long appointmentId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        String status = "completed";
        Pageable pageable = mock(Pageable.class);
        Page<TranscriptionDetail> page = mock(Page.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);

        when(transcriptionDetailRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);
        when(page.getContent()).thenReturn(Collections.singletonList(transcriptionDetail));
        when(transcriptionUtil.convertToDto(transcriptionDetail)).thenReturn(mock(TranscriptionDetailsDto.class));

        Optional<List<TranscriptionDetailsDto>> result = transcriptionAppointmentNoteService.search(practitionerId, patientId, appointmentId, startDate, endDate, status, false,pageable);

        assertTrue(result.isPresent());
        verify(transcriptionDetailRepository, times(1)).findAll(any(Specification.class), eq(pageable));
        verify(transcriptionUtil, times(1)).convertToDto(transcriptionDetail);
    }

    @Test
    void testGetDetailsById() throws TranscriptionAppointmentNotesException {
        Long detailsId = 1L;
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);

        when(transcriptionDetailRepository.findById(detailsId)).thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionUtil.convertToDto(transcriptionDetail)).thenReturn(mock(TranscriptionDetailsDto.class));

        Optional<TranscriptionDetailsDto> result = transcriptionAppointmentNoteService.getDetailsById(detailsId);

        assertTrue(result.isPresent());
        verify(transcriptionDetailRepository, times(1)).findById(detailsId);
        verify(transcriptionUtil, times(1)).convertToDto(transcriptionDetail);
    }

    @Test
    void testWriteCompletedNotesFromBucket() throws TranscriptionAppointmentNotesException {
        AiNoteWebhookDto aiNoteWebhookDto = mock(AiNoteWebhookDto.class);
        byte[] summaryBytes = "summary".getBytes(StandardCharsets.UTF_8);
        byte[] transcriptBytes = "transcript".getBytes(StandardCharsets.UTF_8);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        String jobName = "123456";
        Long detailId = 123456L;

        when(aiNoteWebhookDto.getBucketName()).thenReturn("bucketName");
        when(aiNoteWebhookDto.getEventTime()).thenReturn("2034-02-25T00:00:00.000Z");
        when(aiNoteWebhookDto.getJobName()).thenReturn(jobName);
        when(awsUtil.getUsEast1Client()).thenReturn(awsS3Service);
        when(awsS3Service.getFile(anyString(), anyString())).thenReturn(Optional.of(summaryBytes), Optional.of(transcriptBytes));
        when(transcriptionUtil.getTranscriptionDetailsId(jobName)).thenReturn(detailId);
        when(transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(detailId)).thenReturn(null);
        when(transcriptionDetailRepository.findById(detailId)).thenReturn(Optional.of(transcriptionDetail));

        boolean result = transcriptionAppointmentNoteService.writeCompletedNotesFromBucket(aiNoteWebhookDto);

        assertTrue(result);
        verify(awsS3Service, times(1)).getFile(aiNoteWebhookDto.getBucketName(), jobName + "/summary.json");
        verify(awsS3Service, times(1)).getFile(aiNoteWebhookDto.getBucketName(), jobName + "/transcript.json");
        verify(transcriptionDetailRepository, times(1)).findById(detailId);
        verify(transcriptionAppointmentNoteRepository, times(1)).save(any(TranscriptionAppointmentNote.class));
        verify(transcriptionDetailRepository, times(1)).save(any(TranscriptionDetail.class));
        verify(notificationService, times(1)).createAINoteNotification(transcriptionDetail);
    }

    @Test
    void testViewGeneratedNotes() throws JsonProcessingException, TranscriptionAppointmentNotesException {
        Long transcriptionDetailsId = 1L;

        // Mock conversation data
        ConversationContainer conversation = new ConversationContainer();
        Conversation conversation1 = new Conversation();
        List<TranscriptSegment> segments = new ArrayList<>();
        conversation1.setTranscriptSegments(segments);
        conversation.setConversation(conversation1);

        // Mock clinical documentation data
        ClinicalDocumentationContainer clinicalDocumentation = new ClinicalDocumentationContainer();
        ClinicalDocumentation documentation = new ClinicalDocumentation();
        List<Section> sections = new ArrayList<>();
        documentation.setSections(sections);
        clinicalDocumentation.setClinicalDocumentation(documentation);

        // Mock transcription note
        TranscriptionAppointmentNote transcriptionAppointmentNote = new TranscriptionAppointmentNote();
        transcriptionAppointmentNote.setId(1L);
        transcriptionAppointmentNote.setGeneratedConversation("conversation");
        transcriptionAppointmentNote.setGeneratedClinicalNotes("notes");
        transcriptionAppointmentNote.setReviewed(false);
        transcriptionAppointmentNote.setSubject("Test Subject");

        // Mock transcription detail
        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setStatus(AINOTE.READY);
        transcriptionDetail.setArchived(false);
        transcriptionAppointmentNote.setTranscriptionDetail(transcriptionDetail);

        // Setup mocks
        when(transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(transcriptionDetailsId))
            .thenReturn(transcriptionAppointmentNote);
        when(transcriptionUtil.fromJson(anyString(), eq(ConversationContainer.class))).thenReturn(conversation);
        when(transcriptionUtil.fromJson(anyString(), eq(ClinicalDocumentationContainer.class)))
            .thenReturn(clinicalDocumentation);

        // Execute test
        Optional<TranscriptionAppointmentNotesDto> result = transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);

        // Verify results
        assertTrue(result.isPresent());
        TranscriptionAppointmentNotesDto dto = result.get();
        assertEquals(transcriptionDetailsId, dto.getTranscriptionDetailsId());
        assertEquals(transcriptionAppointmentNote.getId(), dto.getTranscriptionAppointmentNotesId());
        assertEquals(transcriptionAppointmentNote.isReviewed(), dto.isReviewed());
        assertEquals(transcriptionAppointmentNote.getSubject(), dto.getSubject());
        assertEquals(AINOTE.READY, dto.getStatus());
        assertFalse(dto.isArchived());
        assertNotNull(dto.getConversation());
        //assertNotNull(dto.getClinicalDocumentation());

        // Verify interactions
        verify(transcriptionAppointmentNoteRepository, times(1)).findByTranscriptionDetailId(transcriptionDetailsId);
        verify(transcriptionUtil, times(1)).fromJson(anyString(), eq(ConversationContainer.class));
        verify(transcriptionUtil, times(1)).fromJson(anyString(), eq(ClinicalDocumentationContainer.class));
    }

    @Test
    void testReviewNotes() throws TranscriptionAppointmentNotesException {
        // Setup request
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionAppointmentNoteId(1L);
        request.setAction(AINOTE.DRAFT);
        request.setSubject("Test Subject");
        request.setReviewedNoteString("Test note content");
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);

        // Mock transcription note and detail
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(transcriptionAppointmentNote.getTranscriptionDetail()).thenReturn(transcriptionDetail);

        // Mock user
        User user = mock(User.class);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);

        // Mock note service response
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        savedNote.setId(123L);
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);

        // Setup mocks
        when(transcriptionAppointmentNoteRepository.findById(request.getTranscriptionAppointmentNoteId()))
            .thenReturn(Optional.of(transcriptionAppointmentNote));
        when(userService.getCurrentUser()).thenReturn(user);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        // Execute test
        String result = transcriptionAppointmentNoteService.reviewNotes(request);

        // Verify results
        assertEquals("success", result);
        
        // Verify interactions
        verify(transcriptionAppointmentNoteRepository).findById(request.getTranscriptionAppointmentNoteId());
        verify(transcriptionAppointmentNote).setSubject(request.getSubject());
        verify(transcriptionAppointmentNoteRepository).save(transcriptionAppointmentNote);
        verify(transcriptionDetail).setStatus(request.getAction());
        verify(transcriptionDetailRepository).save(transcriptionDetail);
        verify(noteService).saveNote(any(Note.class), eq("draft"));
    }

    @Test
    void testGetClinicalNotesString_printsInOrderAndSkipsInvalidSections() {
        ClinicalDocumentation clinicalDocumentation = mock(ClinicalDocumentation.class);

        Section chiefComplaint = mock(Section.class);
        Section historyOfPresentIllness = mock(Section.class);
        Section pastMedicalHistory = mock(Section.class);
        Section reviewOfSystems = mock(Section.class);
        Section physicalExam = mock(Section.class);
        Section plan = mock(Section.class);
        Section irrelevantSection = mock(Section.class); // e.g., PAST_FAMILY_HISTORY

        when(chiefComplaint.getSectionName()).thenReturn("CHIEF_COMPLAINT");
        when(historyOfPresentIllness.getSectionName()).thenReturn("HISTORY_OF_PRESENT_ILLNESS");
        when(pastMedicalHistory.getSectionName()).thenReturn("PAST_MEDICAL_HISTORY");
        when(reviewOfSystems.getSectionName()).thenReturn("REVIEW_OF_SYSTEMS");
        when(physicalExam.getSectionName()).thenReturn("PHYSICAL_EXAMINATION");
        when(plan.getSectionName()).thenReturn("PLAN");
        when(irrelevantSection.getSectionName()).thenReturn("PAST_FAMILY_HISTORY");

        when(chiefComplaint.printSectionDetails()).thenReturn("Chief Complaint");
        when(historyOfPresentIllness.printSectionDetails()).thenReturn("History of Present Illness");
        when(pastMedicalHistory.printSectionDetails()).thenReturn("Past Medical History");
        when(reviewOfSystems.printSectionDetails()).thenReturn("Review of Systems");
        when(physicalExam.printSectionDetails()).thenReturn("Physical Examination");
        when(plan.printSectionDetails()).thenReturn("Plan");
        when(irrelevantSection.printSectionDetails()).thenReturn("Should not appear");

        List<Section> allSections = Arrays.asList(
                irrelevantSection, // intentionally first
                reviewOfSystems,
                chiefComplaint,
                plan,
                historyOfPresentIllness,
                physicalExam,
                pastMedicalHistory
        );

        when(clinicalDocumentation.getSections()).thenReturn(allSections);

        String result = transcriptionAppointmentNoteService.getClinicalNotesString(clinicalDocumentation);

        // Check inclusion
        assertTrue(result.contains("Chief Complaint"));
        assertTrue(result.contains("History of Present Illness"));
        assertTrue(result.contains("Past Medical History"));
        assertTrue(result.contains("Review of Systems"));
        assertTrue(result.contains("Physical Examination"));
        assertTrue(result.contains("Plan"));

        // Ensure excluded section is not present
        assertFalse(result.contains("Should not appear"));

        // Check ordering via index positions
        assertTrue(result.indexOf("Chief Complaint") < result.indexOf("History of Present Illness"));
        assertTrue(result.indexOf("History of Present Illness") < result.indexOf("Past Medical History"));
        assertTrue(result.indexOf("Past Medical History") < result.indexOf("Review of Systems"));
        assertTrue(result.indexOf("Review of Systems") < result.indexOf("Physical Examination"));
        assertTrue(result.indexOf("Physical Examination") < result.indexOf("Plan"));

        // Check escaped output
        assertTrue(result.contains("<br />"));
    }

    @Test
    void testGetTranscriptsString() {
        Conversation conversation = mock(Conversation.class);
        TranscriptSegment segment = mock(TranscriptSegment.class);
        List<TranscriptSegment> segments = Arrays.asList(segment, segment);

        when(conversation.getTranscriptSegments()).thenReturn(segments);
        when(segment.printTranscriptSegment()).thenReturn("segment");

        String result = transcriptionAppointmentNoteService.getTranscriptsString(conversation);

        assertTrue(result.contains("segment"));
    }

    @Test
    void testArchiveTranscription() throws TranscriptionAppointmentNotesException {
        ArchiveRequest request = new ArchiveRequest();
        request.setTranscriptionDetailId(1L);
        request.setArchiveNote(true);

        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetailRepository.findById(1L)).thenReturn(Optional.of(transcriptionDetail));

        String result = transcriptionAppointmentNoteService.archiveTranscription(request);

        assertEquals("success", result);
        verify(transcriptionDetailRepository, times(1)).findById(1L);
        verify(transcriptionDetailRepository, times(1)).save(transcriptionDetail);
    }

    @Test
    void testArchiveTranscription_NotFound() {
        ArchiveRequest request = new ArchiveRequest();
        request.setTranscriptionDetailId(1L);

        when(transcriptionDetailRepository.findById(1L)).thenReturn(Optional.empty());

        assertThrows(TranscriptionAppointmentNotesException.class, () -> {
            transcriptionAppointmentNoteService.archiveTranscription(request);
        });
    }

    @Test
    void testIsNotValidTreatingPractitioner() throws TranscriptionAppointmentNotesException {
        // Setup request
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);
        Long userId = 1L;

        // Mock prescription and appointment
        Prescription prescription = mock(Prescription.class);
        Appointment appointment = mock(Appointment.class);

        // Setup mocks
        when(prescriptionService.findOne(request.getPrescriptionId())).thenReturn(prescription);
        when(appointmentService.findOne(request.getAppointmentId())).thenReturn(appointment);
        
        // Test case 1: User is not the treating practitioner from appointment's userFourId
        when(appointment.getUserFourId()).thenReturn(2L); // Different from userId
        when(appointment.getUserId()).thenReturn(null);
        when(prescription.getTreatingPractitionerId()).thenReturn(null);

        boolean result = transcriptionAppointmentNoteService.isNotValidTreatingPractitioner(request, userId);
        assertTrue(result);

        // Test case 2: User is not the treating practitioner from appointment's userId
        when(appointment.getUserFourId()).thenReturn(null);
        when(appointment.getUserId()).thenReturn(2L); // Different from userId
        when(prescription.getTreatingPractitionerId()).thenReturn(null);

        result = transcriptionAppointmentNoteService.isNotValidTreatingPractitioner(request, userId);
        assertTrue(result);

        // Test case 3: User is not the treating practitioner from prescription
        when(appointment.getUserFourId()).thenReturn(null);
        when(appointment.getUserId()).thenReturn(null);
        when(prescription.getTreatingPractitionerId()).thenReturn(2L); // Different from userId

        result = transcriptionAppointmentNoteService.isNotValidTreatingPractitioner(request, userId);
        assertTrue(result);

        // Test case 4: User is the treating practitioner (should return false)
        when(appointment.getUserFourId()).thenReturn(1L); // Same as userId
        when(appointment.getUserId()).thenReturn(null);
        when(prescription.getTreatingPractitionerId()).thenReturn(null);

        result = transcriptionAppointmentNoteService.isNotValidTreatingPractitioner(request, userId);
        assertFalse(result);
    }

    @Test
    void testIsNotValidTreatingPractitioner_Error() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);
        Long userId = 1L;

        when(prescriptionService.findOne(request.getPrescriptionId())).thenReturn(null);
        when(appointmentService.findOne(request.getAppointmentId())).thenReturn(null);

        assertThrows(NullPointerException.class, () -> {
            transcriptionAppointmentNoteService.isNotValidTreatingPractitioner(request, userId);
        });
    }

    @Test
    void testIsInValidCareExtenderOrResident_WithNote() throws TranscriptionAppointmentNotesException {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setNoteId(1L);

        Note note = mock(Note.class);
        Appointment appointment = mock(Appointment.class);
        User user = mock(User.class);

        when(noteService.findOne(1L)).thenReturn(note);
        when(note.getAppointment()).thenReturn(appointment);
        when(appointment.getUserFourId()).thenReturn(2L);
        when(appointment.getUserId()).thenReturn(3L);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);

        boolean result = transcriptionAppointmentNoteService.isInValidCareExtenderOrResident(request, 1L);

        assertTrue(result);
    }

    @Test
    void testIsInValidCareExtenderOrResident_WithPrescription() throws TranscriptionAppointmentNotesException {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);

        Prescription prescription = mock(Prescription.class);
        Appointment appointment = mock(Appointment.class);
        User user = mock(User.class);

        when(prescriptionService.findOne(1L)).thenReturn(prescription);
        when(appointmentService.findOne(1L)).thenReturn(appointment);
        when(prescription.getTreatingPractitionerId()).thenReturn(2L);
        when(prescription.getResidentId()).thenReturn(3L);
        when(appointment.getUserFourId()).thenReturn(4L);
        when(appointment.getUserId()).thenReturn(5L);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);

        boolean result = transcriptionAppointmentNoteService.isInValidCareExtenderOrResident(request, 1L);

        assertTrue(result);
    }

    @Test
    void testIsInValidCareExtenderOrResident_Error() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        User user = mock(User.class);
        when(prescriptionService.findOne(any())).thenReturn(null);
        when(appointmentService.findOne(any())).thenReturn(null);

        when(userService.getCurrentUser()).thenReturn(user);
        assertThrows(TranscriptionAppointmentNotesException.class, () -> {
            transcriptionAppointmentNoteService.isInValidCareExtenderOrResident(request, 1L);
        });
    }

    @Test
    void testSaveNote_NewNote() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);
        request.setPatientId(1L);
        request.setTreatingPractitionerId(1L);
        request.setReviewedNoteString("Test note");
        request.setSubject("Test subject");
        request.setAction(AINOTE.PUBLISHED);

        User user = mock(User.class);
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);

        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        Note result = transcriptionAppointmentNoteService.saveNote(request, 1L, null, 1L);

        assertEquals(savedNote, result);
        verify(noteService, times(1)).saveNote(any(Note.class), eq("publish"));
    }

    @Test
    void testSaveNote_ExistingNote() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setReviewedNoteString("Updated note");
        request.setSubject("Updated subject");
        request.setAction(AINOTE.DRAFT);

        Note existingNote = new Note();
        User user = mock(User.class);
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);

        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        Note result = transcriptionAppointmentNoteService.saveNote(request, 1L, existingNote, 1L);

        assertEquals(savedNote, result);
        verify(noteService, times(1)).saveNote(any(Note.class), eq("draft"));
    }

    @Test
    void testSearch_EmptyResults() {
        List<Long> practitionerId = Arrays.asList(1L, 2L);
        Long patientId = 1L;
        Long appointmentId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        String status = "completed";
        Pageable pageable = mock(Pageable.class);
        Page<TranscriptionDetail> page = mock(Page.class);

        when(transcriptionDetailRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);
        when(page.getContent()).thenReturn(Collections.emptyList());

        Optional<List<TranscriptionDetailsDto>> result = transcriptionAppointmentNoteService.search(
            practitionerId, patientId, appointmentId, startDate, endDate, status, false, pageable);

        assertTrue(result.isPresent());
        assertTrue(result.get().isEmpty());
    }

    @Test
    void testGetDetailsById_NotFound() throws TranscriptionAppointmentNotesException {
        Long detailsId = 1L;
        when(transcriptionDetailRepository.findById(detailsId)).thenReturn(Optional.empty());

        Optional<TranscriptionDetailsDto> result = transcriptionAppointmentNoteService.getDetailsById(detailsId);

        assertTrue(result.isEmpty());
    }

    @Test
    void testWriteCompletedNotesFromBucket_AlreadyProcessed() throws TranscriptionAppointmentNotesException {
        AiNoteWebhookDto aiNoteWebhookDto = mock(AiNoteWebhookDto.class);
        String jobName = "123456";
        Long detailId = 123456L;
        TranscriptionAppointmentNote existingNote = mock(TranscriptionAppointmentNote.class);

        when(aiNoteWebhookDto.getJobName()).thenReturn(jobName);
        when(transcriptionUtil.getTranscriptionDetailsId(jobName)).thenReturn(detailId);
        when(transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(detailId))
            .thenReturn(existingNote);
        when(awsUtil.getUsEast1Client()).thenReturn(awsS3Service);

        boolean result = transcriptionAppointmentNoteService.writeCompletedNotesFromBucket(aiNoteWebhookDto);

        assertFalse(result);
        verify(awsS3Service, never()).getFile(anyString(), anyString());
    }

    @Test
    void testReviewNotes_ArchivedNote() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionAppointmentNoteId(1L);
        request.setAction(AINOTE.DRAFT);
        
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        
        when(transcriptionAppointmentNoteRepository.findById(request.getTranscriptionAppointmentNoteId()))
            .thenReturn(Optional.of(transcriptionAppointmentNote));
        when(transcriptionAppointmentNote.getTranscriptionDetail()).thenReturn(transcriptionDetail);
        when(transcriptionDetail.isArchived()).thenReturn(true);
        when(userService.getCurrentUser()).thenReturn(user);

        assertThrows(TranscriptionAppointmentNotesException.class, () -> {
            transcriptionAppointmentNoteService.reviewNotes(request);
        });
    }

    @Test
    void testReviewNotes_SignActionNotAllowed() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionAppointmentNoteId(1L);
        request.setAction(AINOTE.SIGNED);
        request.setPrescriptionId(1L);
        
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        
        when(transcriptionAppointmentNoteRepository.findById(request.getTranscriptionAppointmentNoteId()))
            .thenReturn(Optional.of(transcriptionAppointmentNote));
        when(transcriptionAppointmentNote.getTranscriptionDetail()).thenReturn(transcriptionDetail);
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);
        
        // Mock the validation to return true (invalid)
        when(prescriptionService.findOne(any())).thenReturn(mock(Prescription.class));
        when(appointmentService.findOne(any())).thenReturn(mock(Appointment.class));

        assertThrows(TranscriptionAppointmentNotesException.class, () -> {
            transcriptionAppointmentNoteService.reviewNotes(request);
        });
    }

    @Test
    void testReviewNotes_SignActionAllowedForSuperAdmin() throws TranscriptionAppointmentNotesException {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionAppointmentNoteId(1L);
        request.setAction(AINOTE.SIGNED);
        request.setPrescriptionId(1L);
        request.setSubject("Test Subject");
        request.setReviewedNoteString("Test note content");
        
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        
        when(transcriptionAppointmentNoteRepository.findById(request.getTranscriptionAppointmentNoteId()))
            .thenReturn(Optional.of(transcriptionAppointmentNote));
        when(transcriptionAppointmentNote.getTranscriptionDetail()).thenReturn(transcriptionDetail);
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(true);
        
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        savedNote.setId(123L);
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        String result = transcriptionAppointmentNoteService.reviewNotes(request);

        assertEquals("success", result);
        verify(transcriptionAppointmentNote).setSubject(request.getSubject());
        verify(transcriptionAppointmentNoteRepository).save(transcriptionAppointmentNote);
        verify(transcriptionDetail).setStatus(request.getAction());
        verify(transcriptionDetailRepository).save(transcriptionDetail);
    }

    @Test
    void testReviewNotes_PublishActionNotAllowed() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionAppointmentNoteId(1L);
        request.setAction(AINOTE.PUBLISHED);
        request.setPrescriptionId(1L);
        
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        Prescription prescription = mock(Prescription.class);
        
        when(transcriptionAppointmentNoteRepository.findById(request.getTranscriptionAppointmentNoteId()))
            .thenReturn(Optional.of(transcriptionAppointmentNote));
        when(transcriptionAppointmentNote.getTranscriptionDetail()).thenReturn(transcriptionDetail);
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);
        when(prescriptionService.findOne(request.getPrescriptionId())).thenReturn(prescription);
        when(prescription.getTreatingPractitionerId()).thenReturn(2L); // Different from userId

        assertThrows(TranscriptionAppointmentNotesException.class, () -> {
            transcriptionAppointmentNoteService.reviewNotes(request);
        });
    }

    @Test
    void testReviewNotes_PublishActionAllowedForSuperAdmin() throws TranscriptionAppointmentNotesException {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionAppointmentNoteId(1L);
        request.setAction(AINOTE.PUBLISHED);
        request.setPrescriptionId(1L);
        request.setSubject("Test Subject");
        request.setReviewedNoteString("Test note content");
        
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        
        when(transcriptionAppointmentNoteRepository.findById(request.getTranscriptionAppointmentNoteId()))
            .thenReturn(Optional.of(transcriptionAppointmentNote));
        when(transcriptionAppointmentNote.getTranscriptionDetail()).thenReturn(transcriptionDetail);
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(true);
        
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        savedNote.setId(123L);
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        String result = transcriptionAppointmentNoteService.reviewNotes(request);

        assertEquals("success", result);
        verify(transcriptionAppointmentNote).setSubject(request.getSubject());
        verify(transcriptionAppointmentNoteRepository).save(transcriptionAppointmentNote);
        verify(transcriptionDetail).setStatus(request.getAction());
        verify(transcriptionDetailRepository).save(transcriptionDetail);
    }

    @Test
    void testViewGeneratedNotes_NotFound() {
        Long transcriptionDetailsId = 1L;
        when(transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(transcriptionDetailsId))
            .thenReturn(null);

        assertThrows(TranscriptionAppointmentNotesException.class, () -> {
            transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);
        });
    }

    @Test
    void testViewGeneratedNotes_JsonProcessingError() throws JsonProcessingException {
        Long transcriptionDetailsId = 1L;
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        
        when(transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(transcriptionDetailsId))
            .thenReturn(transcriptionAppointmentNote);
        when(transcriptionAppointmentNote.getTranscriptionDetail()).thenReturn(transcriptionDetail);
        when(transcriptionAppointmentNote.getGeneratedConversation()).thenReturn("conversation");
        when(transcriptionAppointmentNote.getGeneratedClinicalNotes()).thenReturn("notes");
        
        // Mock the first fromJson call to succeed but second to fail
        when(transcriptionUtil.fromJson(anyString(), eq(ConversationContainer.class)))
            .thenReturn(new ConversationContainer());
        when(transcriptionUtil.fromJson(anyString(), eq(ClinicalDocumentationContainer.class)))
            .thenThrow(new JsonProcessingException("Error") {});

        assertThrows(TranscriptionAppointmentNotesException.class, () -> {
            transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);
        });
    }

    @Test
    void testLoadForeignKeys() {
        TranscriptionAppointmentNote note = new TranscriptionAppointmentNote();
        note.setNoteId(1L);
        note.setTranscriptionDetailId(2L);

        Note mockNote = mock(Note.class);
        TranscriptionDetail mockDetail = mock(TranscriptionDetail.class);

        when(noteService.findOne(1L)).thenReturn(mockNote);
        when(transcriptionDetailRepository.findById(2L)).thenReturn(Optional.of(mockDetail));

        transcriptionAppointmentNoteService.loadForeignKeys(note);

        assertEquals(mockNote, note.getNote());
        assertEquals(mockDetail, note.getTranscriptionDetail());
        verify(noteService).findOne(1L);
        verify(transcriptionDetailRepository).findById(2L);
    }

    @Test
    void testLoadForeignKeys_NullNote() {
        transcriptionAppointmentNoteService.loadForeignKeys(null);
        // Should not throw any exceptions
    }

    @Test
    void testLoadForeignKeys_NoNoteId() {
        TranscriptionAppointmentNote note = new TranscriptionAppointmentNote();
        note.setTranscriptionDetailId(2L);

        TranscriptionDetail mockDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetailRepository.findById(2L)).thenReturn(Optional.of(mockDetail));

        transcriptionAppointmentNoteService.loadForeignKeys(note);

        assertNull(note.getNote());
        assertEquals(mockDetail, note.getTranscriptionDetail());
        verify(noteService, never()).findOne(any());
        verify(transcriptionDetailRepository).findById(2L);
    }

    @Test
    void testViewGeneratedNotes_ReviewedNote() throws JsonProcessingException, TranscriptionAppointmentNotesException {
        Long transcriptionDetailsId = 1L;
        
        // Create mocks
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);
        Note mockNote = mock(Note.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        ConversationContainer conversationContainer = new ConversationContainer();
        ClinicalDocumentationContainer clinicalDocumentationContainer = new ClinicalDocumentationContainer();


        when(noteService.findOne(1L)).thenReturn(mockNote);
        when(mockNote.getNote()).thenReturn("Reviewed note content");
        when(mockNote.getSubject()).thenReturn("Note Subject");

        when(transcriptionDetail.getStatus()).thenReturn(AINOTE.DRAFT);
        when(transcriptionDetail.isArchived()).thenReturn(false);

        // Setup mock behavior
        when(transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(transcriptionDetailsId))
            .thenReturn(transcriptionAppointmentNote);
        when(transcriptionAppointmentNote.isReviewed()).thenReturn(true);
        when(transcriptionAppointmentNote.getNoteId()).thenReturn(1L);
        when(transcriptionAppointmentNote.getSubject()).thenReturn("Note Subject");
        when(transcriptionAppointmentNote.getGeneratedConversation()).thenReturn("conversation");
        when(transcriptionAppointmentNote.getGeneratedClinicalNotes()).thenReturn("notes");
        when(transcriptionAppointmentNote.getTranscriptionDetail()).thenReturn(transcriptionDetail);
        when(transcriptionAppointmentNote.getNote()).thenReturn(mockNote);
        

        
        when(transcriptionUtil.fromJson(anyString(), eq(ConversationContainer.class)))
            .thenReturn(conversationContainer);
        when(transcriptionUtil.fromJson(anyString(), eq(ClinicalDocumentationContainer.class)))
            .thenReturn(clinicalDocumentationContainer);

        // Execute test
        Optional<TranscriptionAppointmentNotesDto> result = transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);

        // Verify results
        assertTrue(result.isPresent());
        TranscriptionAppointmentNotesDto dto = result.get();
        assertEquals("Reviewed note content", dto.getReviewNoteString());
        assertEquals("Note Subject", dto.getSubject());
        assertEquals(AINOTE.DRAFT, dto.getStatus());
        assertFalse(dto.isArchived());
        
        // Verify interactions
        verify(transcriptionAppointmentNoteRepository).findByTranscriptionDetailId(transcriptionDetailsId);
        verify(noteService).findOne(1L);
        verify(transcriptionUtil).fromJson(anyString(), eq(ConversationContainer.class));
        verify(transcriptionUtil).fromJson(anyString(), eq(ClinicalDocumentationContainer.class));
    }

    @Test
    void testSaveTranscriptionChanges() {
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);

        transcriptionAppointmentNoteService.saveTranscriptionChanges(transcriptionDetail, transcriptionAppointmentNote);

        verify(transcriptionAppointmentNoteRepository).save(transcriptionAppointmentNote);
        verify(transcriptionDetailRepository).save(transcriptionDetail);
    }
}
