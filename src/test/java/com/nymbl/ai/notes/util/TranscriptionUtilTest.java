package com.nymbl.ai.notes.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.ai.notes.dto.TranscriptionDetailsDto;
import com.nymbl.ai.notes.dto.TranscriptionUploadRequest;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.UserDto;
import com.nymbl.master.repository.UserDtoRepository;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.dashboard.dto.AppointmentDto;
import com.nymbl.tenant.dashboard.repository.AppointmentDtoRepository;
import com.nymbl.tenant.model.Branch;
import com.nymbl.tenant.model.Patient;
import com.nymbl.tenant.repository.BranchRepository;
import com.nymbl.tenant.repository.PatientRepository;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockMultipartFile;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TranscriptionUtilTest {

    @Mock
    private BranchRepository branchRepository;

    @Mock
    private PatientRepository patientRepository;

    @Mock
    private AppointmentDtoRepository appointmentRepository;

    @Mock
    private UserDtoRepository userRepository;

    @Mock
    private StringUtil stringUtil;

    @InjectMocks
    private TranscriptionUtil transcriptionUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        TenantContext.setCurrentTenant("test-tenant");
    }

    @Test
    public void testReplacePathVariables_WithValidInput() {
        String pathTemplate = "/branch/{branch}/patient/{patientId}";
        Map<String, String> values = new HashMap<>();
        values.put("branch", "branch1");
        values.put("patientId", "123");

        String result = transcriptionUtil.replacePathVariables(pathTemplate, values);

        assertEquals("/branch/branch1/patient/123", result);
    }

    @Test
    public void testReplacePathVariables_WithMissingValues() {
        String pathTemplate = "/branch/{branch}/patient/{patientId}";
        Map<String, String> values = new HashMap<>();
        values.put("branch", "branch1");

        String result = transcriptionUtil.replacePathVariables(pathTemplate, values);

        assertEquals("/branch/branch1/patient/", result);
    }

    @Test
    public void testReplacePathVariables_WithEmptyTemplate() {
        String pathTemplate = "";
        Map<String, String> values = new HashMap<>();
        values.put("branch", "branch1");

        String result = transcriptionUtil.replacePathVariables(pathTemplate, values);

        assertEquals("", result);
    }

    @Test
    public void testConvertToMap_WithValidRequest() {
        TranscriptionUploadRequest request = new TranscriptionUploadRequest();
        request.setBranchId(1L);
        request.setPatientId(2L);
        request.setPractitionerId(3L);

        Branch branch = new Branch();
        branch.setName("BranchName");
        when(branchRepository.findById(1L)).thenReturn(Optional.of(branch));

        Map<String, String> result = transcriptionUtil.convertToMap(request);

        assertEquals("BranchName", result.get("branch"));
        assertEquals("2", result.get("patientId"));
        assertEquals("3", result.get("practitionerId"));
        assertEquals("test-tenant", result.get("tenant"));
    }

    @Test(expected = RuntimeException.class)
    public void testConvertToMap_WithInvalidBranchId() {
        TranscriptionUploadRequest request = new TranscriptionUploadRequest();
        request.setBranchId(999L);
        when(branchRepository.findById(999L)).thenReturn(Optional.empty());

        transcriptionUtil.convertToMap(request);
    }

    @Test
    public void testFromJson_WithValidJson() throws JsonProcessingException {
        String json = "{\"id\":1,\"name\":\"test\"}";
        TestObject result = transcriptionUtil.fromJson(json, TestObject.class);

        assertNotNull(result);
        assertEquals(1, result.getId());
        assertEquals("test", result.getName());
    }

    @Test(expected = JsonProcessingException.class)
    public void testFromJson_WithInvalidJson() throws JsonProcessingException {
        String invalidJson = "{invalid json}";
        when(stringUtil.fromJson(invalidJson, TestObject.class))
            .thenThrow(new JsonProcessingException("Invalid JSON") {});

        stringUtil.fromJson(invalidJson, TestObject.class);
    }

    @Test
    public void testMakeJobName_WithProdProfile() throws Exception {
        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setId(1L);
        
        // Use reflection to set the active profile
        java.lang.reflect.Field field = TranscriptionUtil.class.getDeclaredField("activeProfile");
        field.setAccessible(true);
        field.set(transcriptionUtil, "prod");

        String result = transcriptionUtil.makeJobName(transcriptionDetail);

        assertEquals("TStest-tenant-1", result);
    }

    @Test
    public void testMakeJobName_WithNonProdProfile() throws Exception {
        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setId(1L);
        
        // Use reflection to set the active profile
        java.lang.reflect.Field field = TranscriptionUtil.class.getDeclaredField("activeProfile");
        field.setAccessible(true);
        field.set(transcriptionUtil, "dev");

        String result = transcriptionUtil.makeJobName(transcriptionDetail);

        assertEquals("TStest-tenant-dev-1", result);
    }

    @Test
    public void testGetTranscriptionDetailsId_WithValidInput() {
        assertEquals(Long.valueOf(1), transcriptionUtil.getTranscriptionDetailsId("TStest-tenant_dev-1"));
        assertEquals(Long.valueOf(123), transcriptionUtil.getTranscriptionDetailsId("TStest-tenant_dev-123"));
        assertEquals(Long.valueOf(0), transcriptionUtil.getTranscriptionDetailsId("TStest-tenant_dev-0"));
    }

    @Test(expected = NumberFormatException.class)
    public void testGetTranscriptionDetailsId_WithInvalidInput() {
        transcriptionUtil.getTranscriptionDetailsId("TS-tenant_dev-invalid");
    }

    @Test
    public void testConvertToDto_WithCompleteData() {
        // Setup test data
        TranscriptionDetail details = createTestTranscriptionDetail();
        setupMockRepositories(details);

        TranscriptionDetailsDto result = transcriptionUtil.convertToDto(details);

        // Verify all fields are correctly mapped
        assertNotNull(result);
        assertEquals(details.getId(), result.getId());
        assertEquals(details.getStatus(), result.getStatus());
        assertEquals(details.getJobName(), result.getJobName());
        assertEquals(details.getBranchId(), result.getBranchId());
        assertEquals(details.getPrescriptionId(), result.getPrescriptionId());
        assertEquals(details.getStartTime(), result.getStartTime());
        assertEquals(details.getEndTime(), result.getEndTime());
        
        // Verify patient data
        assertNotNull(result.getPatient());
        assertEquals(details.getPatientId(), result.getPatient().getId());
        assertEquals("firstName", result.getPatient().getFirstName());
        assertEquals("lastName", result.getPatient().getLastName());
        
        // Verify practitioner data
        assertNotNull(result.getPractitioner());
        assertEquals(details.getPractitionerId(), result.getPractitioner().getId());
        
        // Verify appointment data
        assertNotNull(result.getAppointment());
        assertEquals(details.getAppointmentId(), result.getAppointment().getId());
    }

    @Test
    public void testConvertToDto_WithMissingPatient() {
        TranscriptionDetail details = createTestTranscriptionDetail();
        when(patientRepository.findById(details.getPatientId())).thenReturn(Optional.empty());
        
        // Set up other necessary mocks without using setupMockRepositories
        AppointmentDto appointmentDto = mock(AppointmentDto.class);
        when(appointmentDto.getId()).thenReturn(details.getAppointmentId());
        when(appointmentRepository.getAppointmentDtoById(details.getAppointmentId())).thenReturn(appointmentDto);

        UserDto practitioner = mock(UserDto.class);
        when(practitioner.getId()).thenReturn(details.getPractitionerId());
        when(userRepository.findUserDtoById(details.getPractitionerId())).thenReturn(practitioner);

        TranscriptionDetailsDto result = transcriptionUtil.convertToDto(details);

        assertNotNull(result);
        assertNull(result.getPatient());
    }

    @Test
    public void testGetFileExtension_FromFilename() {
        MockMultipartFile file = new MockMultipartFile(
            "test.mp3",
            "test.mp3",
            "audio/mpeg",
            "test content".getBytes()
        );

        String extension = transcriptionUtil.getFileExtension(file);

        assertEquals(".mp3", extension);
    }

    @Test
    public void testGetFileExtension_FromMimeType() {
        MockMultipartFile file = new MockMultipartFile(
            "test",
            "test",
            "audio/mp4",
            "test content".getBytes()
        );

        String extension = transcriptionUtil.getFileExtension(file);

        assertEquals(".m4a", extension);
    }

    @Test
    public void testFormatTime() {
        assertEquals("00:00:00", transcriptionUtil.formatTime(0));
        assertEquals("00:01:30", transcriptionUtil.formatTime(90));
        assertEquals("01:30:00", transcriptionUtil.formatTime(5400));
        assertEquals("02:30:45", transcriptionUtil.formatTime(9045));
    }

    @Test
    public void testToJson_WithValidObject() throws JsonProcessingException {
        TestObject testObject = new TestObject(1, "test");
        String result = transcriptionUtil.toJson(testObject);
        assertEquals("{\"id\":1,\"name\":\"test\"}", result);
    }

    @Test
    public void testToJson_WithNullObject() throws JsonProcessingException {
        String result = transcriptionUtil.toJson(null);
        assertEquals("null", result);
    }

    @Test
    public void testGetFileExtension_WithNoExtensionAndNoMimeType() {
        MockMultipartFile file = new MockMultipartFile(
            "test",
            "test",
            null,
            "test content".getBytes()
        );

        String extension = transcriptionUtil.getFileExtension(file);
        assertEquals("", extension);
    }

    @Test
    public void testGetFileExtension_WithWavMimeType() {
        MockMultipartFile file = new MockMultipartFile(
            "test",
            "test",
            "audio/wav",
            "test content".getBytes()
        );

        String extension = transcriptionUtil.getFileExtension(file);
        assertEquals(".wav", extension);
    }

    @Test
    public void testGetFileExtension_WithDefaultMimeType() {
        MockMultipartFile file = new MockMultipartFile(
            "test",
            "test",
            "audio/unknown",
            "test content".getBytes()
        );

        String extension = transcriptionUtil.getFileExtension(file);
        assertEquals(".mp3", extension);
    }

    @Test
    public void testGetTranscriptionDetailsId_WithNonProdProfile() throws Exception {
        // Use reflection to set the active profile
        java.lang.reflect.Field field = TranscriptionUtil.class.getDeclaredField("activeProfile");
        field.setAccessible(true);
        field.set(transcriptionUtil, "dev");

        Long result = transcriptionUtil.getTranscriptionDetailsId("TStest-tenant_dev-123");
        assertEquals(Long.valueOf(123), result);
    }

    @Test
    public void testConvertToDto_WithMissingAppointment() {
        TranscriptionDetail details = createTestTranscriptionDetail();
        when(patientRepository.findById(details.getPatientId())).thenReturn(Optional.of(new Patient()));
        when(appointmentRepository.getAppointmentDtoById(details.getAppointmentId())).thenReturn(null);
        when(userRepository.findUserDtoById(details.getPractitionerId())).thenReturn(mock(UserDto.class));

        TranscriptionDetailsDto result = transcriptionUtil.convertToDto(details);
        assertNotNull(result);
        assertNull(result.getAppointment());
    }

    @Test
    public void testConvertToDto_WithMissingPractitioner() {
        TranscriptionDetail details = createTestTranscriptionDetail();
        when(patientRepository.findById(details.getPatientId())).thenReturn(Optional.of(new Patient()));
        when(appointmentRepository.getAppointmentDtoById(details.getAppointmentId())).thenReturn(mock(AppointmentDto.class));
        when(userRepository.findUserDtoById(details.getPractitionerId())).thenReturn(null);

        TranscriptionDetailsDto result = transcriptionUtil.convertToDto(details);
        assertNotNull(result);
        assertNull(result.getPractitioner());
    }

    @Test
    public void testCalculateAudioLength_WithValidFile() {
        MockMultipartFile file = new MockMultipartFile(
            "test.mp3",
            "test.mp3",
            "audio/mpeg",
            "test content".getBytes()
        );

        double result = transcriptionUtil.calculateAudioLength(file);
        assertEquals(0.0, result, 0.001);
    }

    @Test
    public void testCalculateAudioLength_WithNullFile() {
        double result = transcriptionUtil.calculateAudioLength(null);
        assertEquals(0.0, result, 0.001);
    }

    private TranscriptionDetail createTestTranscriptionDetail() {
        TranscriptionDetail details = new TranscriptionDetail();
        details.setId(1L);
        details.setStatus(AINOTE.STARTED);
        details.setJobName("jobName");
        details.setBranchId(1L);
        details.setPrescriptionId(1L);
        details.setStartTime(new Timestamp(new Date().getTime()));
        details.setEndTime(new Timestamp(new Date().getTime()));
        details.setPatientId(2L);
        details.setPractitionerId(3L);
        details.setAppointmentId(1L);
        return details;
    }

    private void setupMockRepositories(TranscriptionDetail details) {
        Patient patient = new Patient();
        patient.setId(details.getPatientId());
        patient.setFirstName("firstName");
        patient.setLastName("lastName");
        when(patientRepository.findById(details.getPatientId())).thenReturn(Optional.of(patient));

        AppointmentDto appointmentDto = mock(AppointmentDto.class);
        when(appointmentDto.getId()).thenReturn(details.getAppointmentId());
        when(appointmentRepository.getAppointmentDtoById(details.getAppointmentId())).thenReturn(appointmentDto);

        UserDto practitioner = mock(UserDto.class);
        when(practitioner.getId()).thenReturn(details.getPractitionerId());
        when(userRepository.findUserDtoById(details.getPractitionerId())).thenReturn(practitioner);
    }

    private static class TestObject {
        private int id;
        private String name;

        public TestObject() {
        }

        public TestObject(int id, String name) {
            this.id = id;
            this.name = name;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            TestObject that = (TestObject) o;

            if (id != that.id) return false;
            return name != null ? name.equals(that.name) : that.name == null;
        }

        @Override
        public int hashCode() {
            int result = id;
            result = 31 * result + (name != null ? name.hashCode() : 0);
            return result;
        }
    }
}
