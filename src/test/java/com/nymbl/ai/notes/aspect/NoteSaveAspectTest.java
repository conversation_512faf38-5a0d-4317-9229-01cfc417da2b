package com.nymbl.ai.notes.aspect;

import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.ai.notes.model.TranscriptionAppointmentNote;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.service.TranscriptionAppointmentNoteService;
import com.nymbl.tenant.model.Note;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.sql.Timestamp;
import java.time.OffsetDateTime;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

class NoteSaveAspectTest {

    @Mock
    private TranscriptionAppointmentNoteService transcriptionAppointmentNoteService;

    @InjectMocks
    private NoteSaveAspect aspect;

    private Note note;
    private TranscriptionAppointmentNote tapNote;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        note = new Note();
        note.setId(1L);
        note.setSubject("Test");
        note.setTranscriptionDetailId(42L);

        tapNote = new TranscriptionAppointmentNote();
        tapNote.setSubject("Old Subject");
        TranscriptionDetail detail = new TranscriptionDetail();
        detail.setStatus(AINOTE.DRAFT);
        tapNote.setTranscriptionDetail(detail);
    }

    @Test
    void testAfterControllerSave_PublishedNote_TriggersSave() {
        note.setPublished(true);

        when(transcriptionAppointmentNoteService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(response);

        verify(transcriptionAppointmentNoteService, times(1)).saveTranscriptionChanges(any(), eq(tapNote));
        assertEquals("Test", tapNote.getSubject());
    }

    @Test
    void testAfterControllerSave_SignedNote_TriggersSave() {
        note.setUserSignedAt(Timestamp.valueOf(OffsetDateTime.now().toLocalDateTime()));
        note.setTranscriptionDetailId(42L);
        note.setPublished(false);
        
        // Set up the TAP note with DRAFT status
        TranscriptionDetail detail = tapNote.getTranscriptionDetail();
        detail.setStatus(AINOTE.DRAFT);
        
        when(transcriptionAppointmentNoteService.findByTranscriptionDetailId(any())).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(response);

        verify(transcriptionAppointmentNoteService, times(1)).saveTranscriptionChanges(any(), any());
        assertEquals(AINOTE.SIGNED, tapNote.getTranscriptionDetail().getStatus());
        assertEquals("Test", tapNote.getSubject());
    }

    @Test
    void testAfterControllerSave_UnsignedNote_TriggersSave() {
        // Set up the note as unsigned
        note.setUserSignedAt(null);
        note.setTranscriptionDetailId(42L);
        note.setPublished(false);
        
        // Set up the TAP note as previously signed
        TranscriptionDetail detail = tapNote.getTranscriptionDetail();
        detail.setStatus(AINOTE.SIGNED);
        
        when(transcriptionAppointmentNoteService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(response);

        verify(transcriptionAppointmentNoteService, times(1)).saveTranscriptionChanges(any(), eq(tapNote));
        assertEquals(AINOTE.DRAFT, tapNote.getTranscriptionDetail().getStatus());
        assertEquals("Test", tapNote.getSubject());
    }

    @Test
    void testAfterControllerSave_AlreadyPublished_NoAction() {
        TranscriptionDetail detail = tapNote.getTranscriptionDetail();
        detail.setStatus(AINOTE.PUBLISHED);
        note.setPublished(true);

        when(transcriptionAppointmentNoteService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(response);

        verify(transcriptionAppointmentNoteService, never()).saveTranscriptionChanges(any(), any());
    }

    @Test
    void testAfterControllerSave_InvalidBody_NoAction() {
        ResponseEntity<?> response = ResponseEntity.ok("Some unrelated response");

        aspect.afterControllerSave(response);

        verify(transcriptionAppointmentNoteService, never()).saveTranscriptionChanges(any(), any());
    }

    @Test
    void testAfterControllerSave_NullResponseOrNote_NoAction() {
        aspect.afterControllerSave(null);

        ResponseEntity<?> response = ResponseEntity.ok(null);
        aspect.afterControllerSave(response);

        verify(transcriptionAppointmentNoteService, never()).saveTranscriptionChanges(any(), any());
    }

}
