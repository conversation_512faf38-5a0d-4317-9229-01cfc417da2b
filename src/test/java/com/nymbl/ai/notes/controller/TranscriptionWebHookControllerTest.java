package com.nymbl.ai.notes.controller;

import com.nymbl.ai.notes.dto.AiNoteWebhookDto;
import com.nymbl.ai.notes.exception.TranscriptionAppointmentNotesException;
import com.nymbl.ai.notes.service.TranscriptionAppointmentNoteService;
import com.nymbl.tenant.TenantContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class TranscriptionWebHookControllerTest {

    @Mock
    private TranscriptionAppointmentNoteService transcriptionAppointmentNoteService;

    @InjectMocks
    private TranscriptionWebHookController transcriptionWebHookController;

    @Value("${healthscribe.authToken}")
    private String authToken = "testAuthToken"; // mock the value for testing

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Manually set the authToken value
        transcriptionWebHookController = new TranscriptionWebHookController(transcriptionAppointmentNoteService);
        transcriptionWebHookController.authToken = authToken;
    }

    @Test
    void testGeneratedNotesSuccess() throws TranscriptionAppointmentNotesException {
        // Prepare test data
        AiNoteWebhookDto aiNoteWebhookDto = new AiNoteWebhookDto();
        aiNoteWebhookDto.setAuthToken(authToken);
        aiNoteWebhookDto.setAwsEventName("ObjectCreated:Put");
        aiNoteWebhookDto.setTenant("tenant.name");

        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            // Call the controller method
            ResponseEntity<?> response = transcriptionWebHookController.generatedNotes(aiNoteWebhookDto);

            // Verify the interactions and response
            mockedTenantContext.verify(() -> TenantContext.setCurrentTenant("tenant_name"), times(1));
            mockedTenantContext.verify(TenantContext::clear, times(1));
            verify(transcriptionAppointmentNoteService, times(1)).writeCompletedNotesFromBucket(aiNoteWebhookDto);
            assertEquals(ResponseEntity.ok("success"), response);
        }
    }

    @Test
    void testGeneratedNotesInvalidEventName() throws TranscriptionAppointmentNotesException {
        // Prepare test data
        AiNoteWebhookDto aiNoteWebhookDto = new AiNoteWebhookDto();
        aiNoteWebhookDto.setAuthToken(authToken);
        aiNoteWebhookDto.setAwsEventName("ObjectDeleted:Delete");
        aiNoteWebhookDto.setTenant("tenant.name");

        // Call the controller method
        ResponseEntity<?> response = transcriptionWebHookController.generatedNotes(aiNoteWebhookDto);

        // Verify no interactions with the service and the response
        verify(transcriptionAppointmentNoteService, never()).writeCompletedNotesFromBucket(any());
        assertEquals(ResponseEntity.badRequest().body("Invalid Signature!!"), response);
    }

    @Test
    void testGeneratedNotesInvalidAuthToken() throws TranscriptionAppointmentNotesException {
        // Prepare test data
        AiNoteWebhookDto aiNoteWebhookDto = new AiNoteWebhookDto();
        aiNoteWebhookDto.setAuthToken("invalidToken");
        aiNoteWebhookDto.setAwsEventName("ObjectCreated:Put");
        aiNoteWebhookDto.setTenant("tenant.name");

        // Call the controller method
        ResponseEntity<?> response = transcriptionWebHookController.generatedNotes(aiNoteWebhookDto);

        // Verify no interactions with the service and the response
        verify(transcriptionAppointmentNoteService, never()).writeCompletedNotesFromBucket(any());
        assertEquals(ResponseEntity.badRequest().body("Invalid Signature!!"), response);
    }
}
