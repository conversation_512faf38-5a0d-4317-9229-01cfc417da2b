package com.nymbl.config;


/**
 * Created by <PERSON><PERSON> on 07/07/2021.
 * This is a repository to hold sql constants
 * for Internal GL reports ONLY.
 */
public class SQLConstantsReports {

    public static final String rxSalesSummary = "SELECT facility, branchName, patientBranchName, rxId, rxBranchName, rxDeliveredOn, rxDeliveryLocation, rxCategory, rxSubCategory, primaryClaimId, claimDateResolved, deviceName, deviceType, \n" +
            "               patientId, firstName, middleName, lastName,  \n" +
            "               practitionerFirstName, practitionerMiddleName, practitionerLastName, insuranceCompanyName, billerCode, dateOfService,  submissionDate, \n" +
            "               primaryCarePhysicianFirstName, primaryCarePhysicianMiddleName, primaryCarePhysicianLastName,  \n" +
            "               referringPhysicianId, referringPhysicianFirstName, referringPhysicianMiddleName, referringPhysicianLastName,  \n" +
            "               claimSubmitterFirstName, claimSubmitterMiddleName, claimSubmitterLastName,\n" +
            "               therapistFirstName, therapistMiddleName, therapistLastName,\n" +
            "               MAX(viewUserFirstName) AS viewUserFirstName, \n" +
            "               MAX(viewUserMiddleName) AS viewUserMiddleName, \n" +
            "               MAX(viewUserLastName) AS viewUserLastName, \n" +
            "               residentFirstName, residentMiddleName, residentLastName,  \n" +
            "               MAX(primaryDriver) AS primaryDriver, \n" +
            "               MAX(clericalUserFirstName) AS clericalUserFirstName, \n" +
            "               MAX(clericalUserMiddleName) AS clericalUserMiddleName, \n" +
            "               MAX(clericalUserLastName) AS clericalUserLastName, \n" +
            "               cogs_table.cogs as cogs, SUM(COALESCE(ivlcSalesTax, 0.00)) AS totalSalesTax, \n" +
            "               COALESCE(SUM(billable), 0.00) as billable , COALESCE(SUM(expectedContractual), 0.00) as expectedContractual, COALESCE(SUM(billable+expectedContractual), 0.00) as allowable FROM ( \n" +
            "               SELECT f.name AS facility, b.name as branchName, rxb.name as rxBranchName, rx.primary_driver AS primaryDriver, rx.category as rxCategory, rx.sub_category as rxSubCategory, rx.delivered_on as rxDeliveredOn, rx.delivery_location as rxDeliveryLocation, gl.prescription_id as rxId, gl.claim_id as primaryClaimId,  dt.name as deviceName, gl.device_type as deviceType,  \n" +
            "                   tp.first_name as practitionerFirstName, tp.middle_name as practitionerMiddleName, tp.last_name as practitionerLastName, \n" +
            "                   pc.first_name as primaryCarePhysicianFirstName, pc.middle_name as primaryCarePhysicianMiddleName, pc.last_name as primaryCarePhysicianLastName, \n" +
            "                   rp.id as referringPhysicianId, rp.first_name as referringPhysicianFirstName, rp.middle_name as referringPhysicianMiddleName, rp.last_name as referringPhysicianLastName, \n" +
            "                   res.first_name as residentFirstName, res.middle_name as residentMiddleName, res.last_name as residentLastName, \n" +
            "                   csu.first_name as claimSubmitterFirstName, csu.middle_name as claimSubmitterMiddleName, csu.last_name as claimSubmitterLastName, \n" +
            "                   gl.patient_id as patientId, c.date_resolved AS claimDateResolved, pt.first_name as firstName, pt.middle_name as middleName, pt.last_name as lastName, pb.name as patientBranchName,\n" +
            "                    vu.first_name AS viewUserFirstName, vu.middle_name AS viewUserMiddleName, vu.last_name AS viewUserLastName,\n" +
            "                   ic.name as insuranceCompanyName, ic.biller_code as billerCode, gl.date_of_service as dateOfService, gl.claim_submission_date as submissionDate, gl.prescription_l_code_id as plcId, \n" +
            "                   th.first_name AS therapistFirstName, th.middle_name AS therapistMiddleName, th.last_name AS therapistLastName, \n" +
            "                   clu.first_name AS clericalUserFirstName,\n" +
            "                   clu.middle_name AS clericalUserMiddleName,\n" +
            "                   clu.last_name AS clericalUserLastName,\n" +
            "                   CASE WHEN gl.sub_category = 'Billable' THEN SUM(COALESCE(gl.amount, 0.00)) * -1 ELSE 0  END AS billable, \n" +
            "                   CASE WHEN gl.sub_category = 'CO-45 Estimate' THEN SUM(COALESCE(gl.amount, 0.00)) * -1 ELSE 0  END AS expectedContractual, \n" +
            "                   SUM(COALESCE(ivlc.sales_tax, 0.00)) AS ivlcSalesTax \n" +
            "               FROM gl_view gl \n" +
            "               INNER JOIN patient pt ON gl.patient_id = pt.id \n" +
            "               INNER JOIN prescription rx ON gl.prescription_id = rx.id \n" +
            "               LEFT JOIN insurance_verification_l_code ivlc on ivlc.id = gl.insurance_verification_l_code_id and ivlc.prescription_l_code_id = gl.prescription_l_code_id AND (gl.category = 'Charge' AND gl.sub_category = 'Billable') \n" +
            "               LEFT JOIN ( \n" +
            "                               SELECT cs.claim_id as claimId1, cs.submitted_by_id as claimSubmitterId1, id1  \n" +
            "                               FROM claim_submission cs \n" +
            "                               LEFT JOIN ( \n" +
            "                                               SELECT cs1.claim_id,  MIN(cs1.id) as id1  \n" +
            "                                               FROM claim_submission cs1 GROUP BY cs1.claim_id)  \n" +
            "                                               as x ON cs.claim_id = x.claim_id \n" +
            "                                               WHERE cs.id = x.id1 \n" +
            "                               ) AS y ON gl.claim_id = y.claimId1 \n" +
            "               LEFT JOIN claim c ON gl.claim_id = c.id \n" +
            "               LEFT JOIN branch b ON gl.branch_id = b.id \n" +
            "               LEFT JOIN branch pb ON pt.primary_branch_id = pb.id \n" +
            "               LEFT JOIN branch rxb ON rx.branch_id = rxb.id \n" +
            "               LEFT JOIN device_type dt ON gl.device_type_id = dt.id \n" +
            "               LEFT JOIN insurance_company ic ON gl.insurance_company_id = ic.id  \n" +
            "               LEFT JOIN nymbl_master.user tp on tp.id = gl.treating_practitioner_id  \n" +
            "               LEFT JOIN nymbl_master.user res on res.id = rx.resident_id  \n" +
            "               LEFT JOIN nymbl_master.user csu on y.claimSubmitterId1 = csu.id  \n" +
            "               LEFT JOIN physician rp ON rp.id = gl.referring_physician_id  \n" +
            "               LEFT JOIN physician pc ON pc.id = gl.primary_care_physician_id  \n" +
            "               LEFT JOIN therapist th ON th.id = gl.therapist_id \n" +
            "               LEFT JOIN facility f ON gl.facility_id = f.id \n" +
            "               LEFT JOIN nymbl_master.user vu ON vu.id = rx.view_user_id \n" +
            "               LEFT JOIN nymbl_master.user clu ON rx.clerical_user_id = clu.id\n" +
            "               WHERE ((gl.category = 'Charge'  \n" +
            "                   AND gl.sub_category = 'Billable')  \n" +
            "                   OR (gl.category = 'Line Adjustments'  \n" +
            "                   AND gl.sub_category = 'CO-45 Estimate'))  \n" +
            "                   AND ( :branchId is null OR :branchId = 0 OR :branchId = gl.branch_id )  \n" +
            "                   AND (gl_year IS NOT NULL AND gl_period IS NOT NULL AND CONCAT(gl_year, LPAD(gl_period, 2, 0)) between :start and :end)  \n" +
            "                   AND gl.active = true \n" +
            "               GROUP BY branchName, rxBranchName, rxDeliveredOn, rxDeliveryLocation, rxId, rxCategory, rxSubCategory, primaryClaimId, claimDateResolved, deviceName, deviceType, practitionerFirstName, practitionerMiddleName, practitionerLastName, \n" +
            "                   primaryCarePhysicianFirstName, primaryCarePhysicianMiddleName, primaryCarePhysicianLastName, referringPhysicianId, referringPhysicianFirstName, referringPhysicianMiddleName, referringPhysicianLastName, \n" +
            "                   viewUserFirstName, viewUserMiddleName, viewUserLastName, facility, residentFirstName, residentMiddleName, residentLastName, claimSubmitterFirstName, claimSubmitterMiddleName, claimSubmitterLastName, \n" +
            "                   therapistFirstName, therapistMiddleName, therapistLastName, patientId, firstName, middleName, lastName, patientBranchName, insuranceCompanyName, billerCode, dateOfService, submissionDate, plcId, gl.sub_category  ) as x  \n" +
            "               LEFT JOIN (select sum(poi.total_cost) as cogs, prescription_id \n" +
            "                            from purchase_order_item poi \n" +
            "                            group by prescription_id) as cogs_table on cogs_table.prescription_id = rxId \n" +
            "            GROUP BY branchName, rxBranchName, rxDeliveredOn, rxDeliveryLocation, rxId, rxCategory, rxSubCategory, primaryClaimId, claimDateResolved, deviceName, deviceType, practitionerFirstName, practitionerMiddleName, practitionerLastName, \n" +
            "               primaryCarePhysicianFirstName, primaryCarePhysicianMiddleName, primaryCarePhysicianLastName, referringPhysicianId, referringPhysicianFirstName, referringPhysicianMiddleName, referringPhysicianLastName,\n" +
            "               viewUserFirstName, viewUserMiddleName, viewUserLastName, therapistFirstName, therapistMiddleName, therapistLastName, facility, \n" +
            "               residentFirstName, residentMiddleName, residentLastName, claimSubmitterFirstName, claimSubmitterMiddleName, claimSubmitterLastName, \n" +
            "               patientId, firstName, middleName, lastName, patientBranchName, insuranceCompanyName,billerCode, dateOfService, submissionDate, cogs_table.cogs";

    public static final String rxLCodeSalesSummary = "SELECT \n" +
            "    branchName,\n" +
            "    lCodeName as hcpc,\n" +
            "    lCodeId as hcpcId,\n" +
            "    friendlyDescription,\n" +
            "    quantity,\n" +
            "    modifier1,\n" +
            "    modifier2,\n" +
            "    modifier3,\n" +
            "    modifier4,\n" +
            "    rxId,\n" +
            "    rxBranchName,\n" +
            "    rxDeliveredOn,\n" +
            "    rxDeliveryLocation,\n" +
            "    primaryClaimId,\n" +
            "    claimStatus,\n" +
            "    deviceName,\n" +
            "    deviceType,\n" +
            "    practitionerFirstName,\n" +
            "    practitionerMiddleName,\n" +
            "    practitionerLastName,\n" +
            "    referringFirstName,\n" +
            "    referringMiddleName,\n" +
            "    referringLastName,\n" +
            "    residentFirstName,\n" +
            "    residentMiddleName,\n" +
            "    residentLastName,\n" +
            "    patientId,\n" +
            "    firstName,\n" +
            "    middleName,\n" +
            "    lastName,\n" +
            "    patientBranchName,\n" +
            "    insuranceCompanyName,\n" +
            "    billerCode, \n" +
            "    dateOfService,\n" +
            "    submissionDate,\n" +
            "    plcId,\n" +
            "    cogs,\n" +
            "    billable,\n" +
            "    expectedContractual,\n" +
            "    (billable + expectedContractual) AS allowable,\n" +
            "    salesTax\n" +
            "FROM\n" +
            "    (WITH rental AS (SELECT cl2.id AS claim_id, plc2.id AS plc_id, trans.item_physical_id AS item_phys_id, trans.id AS item_trans_id, \n" +
            "        CASE WHEN trans.depreciation_unit = 'month' THEN CASE \n" +
            "               WHEN TIMESTAMPDIFF(MONTH, trans.start_date, cl2.date_of_service) <= trans.depreciation_units_to_zero THEN (trans.initial_value/trans.depreciation_units_to_zero) \n" +
            "               ELSE 0 END \n" +
            "            WHEN trans.depreciation_unit = 'rental_cycle' THEN CASE \n" +
            "               WHEN trans.depreciation_unit <= trans.depreciation_units_to_zero THEN (trans.initial_value/trans.depreciation_units_to_zero) \n" +
            "               ELSE 0 END \n" +
            "            ELSE 0 END AS depreciation \n" +
            "        FROM item_transaction trans \n" +
            "        JOIN (SELECT item_physical_id AS phys_id, prescription_l_code_id AS plc_id, max(id) AS trans_id FROM item_transaction WHERE status = 'rented' GROUP BY item_physical_id, prescription_l_code_id) phys_trans ON trans.id = phys_trans.trans_id \n" +
            "        JOIN prescription_l_code plc2 ON trans.prescription_l_code_id = plc2.id JOIN claim cl2 ON plc2.prescription_id = cl2.prescription_id \n" +
            "        WHERE plc2.modifier1 = 'RR'), \n" +
            "      purch AS (SELECT trans1.prescription_l_code_id as plc_id, trans1.initial_value AS item_cost FROM item_transaction trans1 \n" +
            "            JOIN (SELECT trans2.item_physical_id AS phys_id, trans2.prescription_l_code_id AS plc_id, max(trans2.id) AS trans_id FROM item_transaction trans2 \n" +
            "                    JOIN prescription_l_code plc ON trans2.prescription_id = plc.prescription_id AND trans2.prescription_l_code_id = plc.id \n" +
            "                    WHERE (plc.modifier1 IS NULL OR plc.modifier1 <> 'RR') AND trans2.status = 'delivered' \n" +
            "                    GROUP BY trans2.item_physical_id, trans2.prescription_l_code_id) phys_trans \n" +
            "        ON trans1.id = phys_trans.trans_id) \n" +
            "    SELECT \n" +
            "        b.name AS branchName,\n" +
            "            lc.name AS lCodeName,\n" +
            "            lc.friendly_description AS friendlyDescription,\n" +
            "            lc.id as lCodeId,\n" +
            "            plc.quantity AS quantity,\n" +
            "            plc.modifier1 AS modifier1,\n" +
            "            plc.modifier2 AS modifier2,\n" +
            "            plc.modifier3 AS modifier3,\n" +
            "            plc.modifier4 AS modifier4,\n" +
            "            gl.prescription_id AS rxId,\n" +
            "            rxb.name AS rxBranchName,\n" +
            "            rx.delivered_on AS rxDeliveredOn,\n" +
            "            rx.delivery_location AS rxDeliveryLocation,\n" +
            "            gl.claim_id AS primaryClaimId,\n" +
            "            cs.name AS claimStatus,\n" +
            "            dt.name AS deviceName,\n" +
            "            gl.device_type AS deviceType,\n" +
            "            tp.first_name AS practitionerFirstName,\n" +
            "            tp.middle_name AS practitionerMiddleName,\n" +
            "            tp.last_name AS practitionerLastName,\n" +
            "            rp.first_name AS referringFirstName,\n" +
            "            rp.middle_name AS referringMiddleName,\n" +
            "            rp.last_name AS referringLastName,\n" +
            "            res.first_name AS residentFirstName,\n" +
            "            res.middle_name AS residentMiddleName,\n" +
            "            res.last_name AS residentLastName,\n" +
            "            gl.patient_id AS patientId,\n" +
            "            pt.first_name AS firstName,\n" +
            "            pt.middle_name AS middleName,\n" +
            "            pt.last_name AS lastName,\n" +
            "            pb.name AS patientBranchName,\n" +
            "            ic.name AS insuranceCompanyName,\n" +
            "            ic.biller_code as billerCode, \n" +
            "            gl.date_of_service AS dateOfService,\n" +
            "            gl.claim_submission_date AS submissionDate,\n" +
            "            gl.prescription_l_code_id AS plcId,\n" +
            "            (COALESCE(purch.item_cost, 0.00) + COALESCE(rental.depreciation, 0.00)) as cogs,\n" +
            "            COALESCE(gl_billable.total_amount, 0.00) * -1 as billable,\n" +
            "            COALESCE(gl_contractual.total_amount, 0.00) * -1 as expectedContractual,\n" +
            "        SUM(COALESCE(ivlc.sales_tax, 0)) as salesTax\n" +
            "    FROM\n" +
            "        gl_view gl\n" +
            "    JOIN prescription rx ON gl.prescription_id = rx.id\n" +
            "    JOIN patient pt ON rx.patient_id = pt.id\n" +
            "    JOIN prescription_l_code plc ON gl.prescription_l_code_id = plc.id\n" +
            "    JOIN l_code lc ON gl.l_code_id = lc.id\n" +
            "    JOIN claim c ON gl.claim_id = c.id\n" +
            "    LEFT JOIN rental ON plc.id = rental.plc_id AND c.id = rental.claim_id\n" +
            "    LEFT JOIN purch ON plc.id = purch.plc_id \n" +
            "    LEFT JOIN device_type dt ON gl.device_type_id = dt.id\n" +
            "    LEFT JOIN branch b ON gl.branch_id = b.id\n" +
            "    LEFT JOIN branch rxb ON rx.branch_id = rxb.id\n" +
            "    LEFT JOIN branch pb ON pt.primary_branch_id = pb.id\n" +
            "    LEFT JOIN insurance_company ic ON gl.insurance_company_id = ic.id\n" +
            "    LEFT JOIN nymbl_master.user tp ON gl.treating_practitioner_id = tp.id\n" +
            "    LEFT JOIN nymbl_master.user res ON rx.resident_id = res.id\n" +
            "    LEFT JOIN physician rp ON gl.referring_physician_id = rp.id\n" +
            "    LEFT JOIN nymbl_status cs ON c.nymbl_status_id = cs.id\n" +
            "    LEFT JOIN insurance_verification_l_code ivlc on ivlc.id = gl.insurance_verification_l_code_id and ivlc.prescription_l_code_id = gl.prescription_l_code_id\n" +
            "    LEFT JOIN (SELECT prescription_l_code_id, SUM(amount) total_amount FROM gl_view WHERE gl_view.category = 'Charge' AND gl_view.sub_category = 'Billable' AND prescription_l_code_id is not null GROUP BY prescription_l_code_id) AS gl_billable ON gl.prescription_l_code_id = gl_billable.prescription_l_code_id\n" +
            "    LEFT JOIN (SELECT prescription_l_code_id, SUM(amount) total_amount FROM gl_view WHERE gl_view.category = 'Line Adjustments' AND gl_view.sub_category = 'CO-45 Estimate' AND prescription_l_code_id is not null GROUP BY prescription_l_code_id) AS gl_contractual ON gl.prescription_l_code_id = gl_contractual.prescription_l_code_id\n" +
            "    WHERE\n" +
            "        (gl.category = 'Charge'\n" +
            "            AND gl.sub_category = 'Billable')\n" +
            "            AND (:branchId IS NULL OR :branchId = 0\n" +
            "            OR :branchId = gl.branch_id)\n" +
            "            AND (gl_year IS NOT NULL\n" +
            "            AND gl_period IS NOT NULL\n" +
            "            AND CONCAT(gl_year, LPAD(gl_period, 2, 0)) BETWEEN :start AND :end)\n" +
            "            AND gl.active = TRUE\n" +
            "    GROUP BY branchName , lCodeName , lCodeId, friendlyDescription , quantity , modifier1 , modifier2 , modifier3 , modifier4 , rxId , rxBranchName , rxDeliveredOn , rxDeliveryLocation , primaryClaimId , claimStatus , deviceName , deviceType , practitionerFirstName , practitionerMiddleName , practitionerLastName , referringFirstName , referringMiddleName , referringLastName , residentFirstName , residentMiddleName , residentLastName , patientId , firstName , middleName , lastName , patientBranchName , insuranceCompanyName , billerCode, dateOfService , submissionDate , plcId, gl.sub_category, rental.depreciation, purch.item_cost, gl_billable.total_amount, gl_contractual.total_amount) AS x\n" +
            "GROUP BY branchName , hcpc, hcpcId , friendlyDescription , quantity , modifier1 , modifier2 , modifier3 , modifier4 , rxId , rxBranchName , rxDeliveredOn , rxDeliveryLocation , primaryClaimId , claimStatus , deviceName , deviceType , practitionerFirstName , practitionerMiddleName , practitionerLastName , referringFirstName , referringMiddleName , referringLastName , residentFirstName , residentMiddleName , residentLastName , patientId, firstName, middleName, lastName, patientBranchName, insuranceCompanyName, billerCode, dateOfService, submissionDate, plcId, cogs, billable, expectedContractual, salesTax";


    public static final String hcpcDetailReport = "SELECT \n" +
            "                rxId,\n" +
            "                lcode AS hcpc,\n" +
            "                lcodeId AS hcpcId,\n" +
            "                description,\n" +
            "                quantity,\n" +
            "                modifier1,\n" +
            "                modifier2,\n" +
            "                modifier3,\n" +
            "                modifier4,\n" +
            "                MAX(primaryDiagnosisCode) AS primaryDiagnosisCode,\n" +
            "                MAX(primaryDiagnosisName) AS primaryDiagnosisName,\n" +
            "                MAX(primaryDiagnosisType) AS primaryDiagnosisType,\n" +
            "                SUM(CASE\n" +
            "                    WHEN `grouping` = 'billable' THEN (amount * - 1)\n" +
            "                    ELSE 0\n" +
            "                END) AS 'charge',\n" +
            "                SUM(CASE\n" +
            "                    WHEN `grouping` = 'co' THEN (amount * - 1)\n" +
            "                    ELSE 0\n" +
            "                END) AS 'co_45',\n" +
            "                SUM(CASE\n" +
            "                    WHEN `grouping` = 'payments' THEN (amount * - 1)\n" +
            "                    ELSE 0\n" +
            "                END) AS 'payments',\n" +
            "                SUM(CASE\n" +
            "                    WHEN `grouping` = 'lineAdjustments' THEN (amount * - 1)\n" +
            "                    ELSE 0\n" +
            "                END) AS 'line_adjustments',\n" +
            "                SUM(CASE\n" +
            "                    WHEN `grouping` = 'adjustments' THEN amount\n" +
            "                    ELSE 0\n" +
            "                END) AS 'adjustments',\n" +
            "                SUM(amount) * - 1 AS remainingBalance,\n" +
            "                SUM(paymentCount) AS paymentCount,\n" +
            "                SUM(adjustmentCount) AS adjustmentCount,\n" +
            "                patientId AS patientId,\n" +
            "                MAX(patientFirstName) AS patientFirstName,\n" +
            "                MAX(patientMiddleName) AS patientMiddleName,\n" +
            "                MAX(patientLastName) AS patientLastName,\n" +
            "                MIN(claim_id) AS primaryClaimId,\n" +
            "                MAX(dateOfService) AS dateOfService,\n" +
            "                practitionerId,\n" +
            "                MAX(practitionerFirstName) AS treatingPractitionerFirstName,\n" +
            "                MAX(practitionerMiddleName) AS treatingPractitionerMiddleName,\n" +
            "                MAX(practitionerLastName) AS treatingPractitionerLastName,\n" +
            "                primaryCare,\n" +
            "                MAX(primaryCarePhysicianFirstName) AS primaryCarePhysicianFirstName,\n" +
            "                MAX(primaryCarePhysicianMiddleName) AS primaryCarePhysicianMiddleName,\n" +
            "                MAX(primaryCarePhysicianLastName) AS primaryCarePhysicianLastName,\n" +
            "                therapist,\n" +
            "                MAX(therapistFirstName) AS therapistFirstName,\n" +
            "                MAX(therapistMiddleName) AS therapistMiddleName,\n" +
            "                MAX(therapistLastName) AS therapistLastName,\n" +
            "                referring,\n" +
            "                MAX(referringPhysicianFirstName) AS referringPhysicianFirstName,\n" +
            "                MAX(referringPhysicianMiddleName) AS referringPhysicianMiddleName,\n" +
            "                MAX(referringPhysicianLastName) AS referringPhysicianLastName,\n" +
            "                primaryDriver,\n" +
            "                deviceType,\n" +
            "                deviceName,\n" +
            "                (SELECT ic.name\n" +
            "                   FROM insurance_verification iv\n" +
            "                   JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id\n" +
            "                   JOIN insurance_company ic ON pin.insurance_company_id = ic.id\n" +
            "                   WHERE prescription_id = rxId\n" +
            "                   AND iv.carrier_type = 'primary' LIMIT 1) AS primaryInsurance,\n" +
            "                MIN(billerCode) AS billerCode,\n" +
            "                MIN(carrierType) AS carrierType,\n" +
            "                MIN(facilityName) AS facilityName,\n" +
            "                MAX(viewUserFirstName) AS viewUserFirstName,\n" +
            "                MAX(viewUserMiddleName) AS viewUserMiddleName,\n" +
            "                MAX(viewUserLastName) AS viewUserLastName,\n" +
            "                MAX(branchName) AS branchName,\n" +
            "                MAX(rxBranchName) AS rxBranchName,\n" +
            "                MAX(patientBranchName) AS patientBranchName,\n" +
            "                MAX(submissionDate) AS submissionDate,\n" +
            "                MAX(deliveryLocation) AS deliveryLocation,\n" +
            "                MAX(paymentFilterId) AS paymentFilterId,\n" +
            "                (SELECT \n" +
            "                        ic.name\n" +
            "                    FROM\n" +
            "                        insurance_verification iv\n" +
            "                            JOIN\n" +
            "                        patient_insurance pin ON iv.patient_insurance_id = pin.id\n" +
            "                            JOIN\n" +
            "                        insurance_company ic ON pin.insurance_company_id = ic.id\n" +
            "                    WHERE\n" +
            "                        prescription_id = rxId\n" +
            "                            AND iv.carrier_type = 'secondary' LIMIT 1) AS secondaryInsurance\n" +
            "            FROM\n" +
            "                ( SELECT \n" +
            "                    gl.prescription_id AS rxId,\n" +
            "                        lc.name AS lcode,\n" +
            "                        lc.id AS lcodeId,\n" +
            "                        plc.quantity AS quantity,\n" +
            "                        plc.modifier1 AS modifier1,\n" +
            "                        plc.modifier2 AS modifier2,\n" +
            "                        plc.modifier3 AS modifier3,\n" +
            "                        plc.modifier4 AS modifier4,\n" +
            "                        lc.friendly_description AS description,\n" +
            "                        CASE\n" +
            "                            WHEN gl.category = 'Charge' THEN 'billable'\n" +
            "                            WHEN gl.category = 'Payments' THEN 'payments'\n" +
            "                            WHEN gl.category = 'Adjustments' THEN 'adjustments'\n" +
            "                            WHEN\n" +
            "                                gl.category = 'Line Adjustments'\n" +
            "                                    AND gl.sub_category IN ('CO-45' , 'CO-45 Estimate', 'CO-45 Reversal')\n" +
            "                            THEN\n" +
            "                                'co'\n" +
            "                            WHEN\n" +
            "                                gl.category = 'Line Adjustments'\n" +
            "                                    AND gl.sub_category NOT IN ('CO-45' , 'CO-45 Estimate', 'CO-45 Reversal')\n" +
            "                            THEN\n" +
            "                                'lineAdjustments'\n" +
            "                            ELSE CONCAT(gl.category, gl.sub_category)\n" +
            "                        END AS `grouping`,\n" +
            "                        CASE\n" +
            "                            WHEN\n" +
            "                                gl.category = 'Payments'\n" +
            // "                                    AND amount <> 0.00\n" +
            "                            THEN\n" +
            "                                1\n" +
            "                            ELSE 0\n" +
            "                        END AS paymentCount,\n" +
            "                        CASE\n" +
            "                            WHEN\n" +
            "                                (gl.category NOT IN ('Charge' , 'Payments')\n" +
            "                                    AND gl.sub_category NOT IN ('CO-45 Estimate' , 'CO-45 Reversal'))\n" +
            // "                                    AND amount <> 0.00\n" +
            "                            THEN\n" +
            "                                1\n" +
            "                            ELSE 0\n" +
            "                        END AS adjustmentCount,\n" +
            "                        SUM(gl.amount) AS amount,\n" +
            "                        MIN(gl.claim_id) AS claim_id,\n" +
            "                        MAX(gl.date_of_service) AS dateOfService,\n" +
            "                        gl.treating_practitioner_id AS practitionerId,\n" +
            "                        MAX(tp.first_name) AS practitionerFirstName,\n" +
            "                        MAX(tp.middle_name) AS practitionerMiddleName,\n" +
            "                        MAX(tp.last_name) AS practitionerLastName,\n" +
            "                        gl.primary_care_physician_id AS primaryCare,\n" +
            "                        MAX(pcp.first_name) AS primaryCarePhysicianFirstName,\n" +
            "                        MAX(pcp.middle_name) AS primaryCarePhysicianMiddleName,\n" +
            "                        MAX(pcp.last_name) AS primaryCarePhysicianLastName,\n" +
            "                        gl.therapist_id AS therapist,\n" +
            "                        MAX(th.first_name) AS therapistFirstName,\n" +
            "                        MAX(th.middle_name) AS therapistMiddleName,\n" +
            "                        MAX(th.last_name) AS therapistLastName,\n" +
            "                        gl.referring_physician_id AS referring,\n" +
            "                        MAX(rp.first_name) AS referringPhysicianFirstName,\n" +
            "                        MAX(rp.middle_name) AS referringPhysicianMiddleName,\n" +
            "                        MAX(rp.last_name) AS referringPhysicianLastName,\n" +
            "                        rx.primary_driver AS primaryDriver,\n" +
            "                        MIN(gl.gl_date) AS glDate,\n" +
            "                        MIN(gl.gl_applied_date) AS glSubDate,\n" +
            "                        gl.applied_payment_id,\n" +
            "                        p.id AS patientId,\n" +
            "                        MAX(p.first_name) AS patientFirstName,\n" +
            "                        MAX(p.middle_name) AS patientMiddleName,\n" +
            "                        MAX(p.last_name) AS patientLastName,\n" +
            "                        dt.orthotic_or_prosthetic AS deviceType,\n" +
            "                        dt.name AS deviceName,\n" +
            "                        MIN(ic.name) AS primaryInsurance,\n" +
            "                        MIN(ic.biller_code) AS billerCode,\n" +
            "                        MIN(gl.carrier_type) AS carrierType,\n" +
            "                        MIN(gl.claim_submission_date) AS claimSubmissionDate,\n" +
            "                        MIN(c.created_at) AS sentToBillingDate,\n" +
            "                        MIN(f.name) AS facilityName,\n" +
            "                        vu.id AS viewUserId,\n" +
            "                        vu.first_name AS viewUserFirstName,\n" +
            "                        vu.middle_name AS viewUserMiddleName,\n" +
            "                        vu.last_name AS viewUserLastName,\n" +
            "                        b.name AS branchName,\n" +
            "                        rxb.name AS rxBranchName,\n" +
            "                        pb.name AS patientBranchName,\n" +
            "                        sub.submission_date AS submissionDate,\n" +
            "                        MAX(rx.delivery_location) AS deliveryLocation,\n" +
            "                        MAX(gl.applied_payment_id) AS paymentFilterId,\n" +
            "                        dc.code AS primaryDiagnosisCode,\n" +
            "                        dc.name AS primaryDiagnosisName,\n" +
            "                        dc.code_set AS primaryDiagnosisType\n" +
            "                FROM\n" +
            "                    gl_view gl\n" +
            "                JOIN l_code lc ON lc.id = gl.l_code_id\n" +
            "                JOIN prescription rx ON rx.id = gl.prescription_id\n" +
            "                JOIN prescription_l_code plc ON gl.prescription_id = plc.prescription_id AND gl.prescription_l_code_id = plc.id\n" +
            "                JOIN device_type dt ON dt.id = rx.device_type_id\n" +
            "                LEFT JOIN patient p ON rx.patient_id = p.id\n" +
            "                LEFT JOIN claim c ON gl.claim_id = c.id\n" +
            "                LEFT JOIN (SELECT cs.claim_id, MAX(cs.submission_date) submission_date FROM claim_submission cs GROUP BY cs.claim_id) sub ON sub.claim_id = c.id\n" +
            "                LEFT JOIN nymbl_master.user tp ON gl.treating_practitioner_id = tp.id\n" +
            "                LEFT JOIN physician rp ON gl.referring_physician_id = rp.id\n" +
            "                LEFT JOIN physician pcp ON gl.primary_care_physician_id = pcp.id\n" +
            "                LEFT JOIN therapist th ON gl.therapist_id = th.id\n" +
            "                LEFT JOIN insurance_company ic ON gl.insurance_company_id = ic.id\n" +
            "                LEFT JOIN facility f ON gl.facility_id = f.id\n" +
            "                LEFT JOIN nymbl_master.user vu ON rx.view_user_id = vu.id\n" +
            "                LEFT JOIN branch b ON gl.branch_id = b.id\n" +
            "                LEFT JOIN branch pb ON p.primary_branch_id = pb.id\n" +
            "                LEFT JOIN branch rxb ON rx.branch_id = rxb.id\n" +
            "                LEFT JOIN prescription_diagnosis_code pcd ON rx.id = pcd.prescription_id\n" +
            "                    AND pcd.is_primary = TRUE\n" +
            "                LEFT JOIN diagnosis_code dc ON pcd.diagnosis_code_id = dc.id\n" +
            "                WHERE\n" +
            "                    ((gl.category NOT IN ('Accounts Receivable' , 'Cash', 'Tax'))\n" +
            "                        AND (gl.sub_category <> 'Unapplied'))\n" +
            "                        AND (:branchId IS NULL OR :branchId = 0\n" +
            "                        OR gl.branch_id = :branchId)\n" +
            "                        AND (gl.gl_year IS NOT NULL\n" +
            "                        AND gl.gl_period IS NOT NULL\n" +
            "                        AND CONCAT(gl.gl_year, LPAD(gl.gl_period, 2, 0)) BETWEEN :start AND :end)\n" +
            "                GROUP BY gl.prescription_id , lc.name ,lCodeId, lc.friendly_description , plc.id , modifier1, modifier2, modifier3, modifier4, `grouping` , tp.id , tp.first_name , tp.last_name , gl.treating_practitioner_id , gl.therapist_id , referring , primaryCare ,ic.biller_code, gl.prescription_l_code_id , gl.applied_payment_id , paymentCount , adjustmentCount , patientId , deviceType , deviceName , primaryDiagnosisCode , branchName , patientBranchName , submissionDate, rxBranchName , primaryDiagnosisName , primaryDiagnosisType) AS x\n" +
            "            GROUP BY rxId , hcpc , hcpcId, quantity , description , modifier1, modifier2, modifier3, modifier4, practitionerId , primaryCare , billerCode,therapist , referring , primaryDriver , deviceType , deviceName , patientId , deliveryLocation\n" +
            "            ORDER BY rxId DESC";

    public static final String hcpcDetailTotals = "SELECT COALESCE(SUM(billable), 0.00) as billableTotal, COALESCE(SUM(expectedContractual), 0.00) as contractualTotal, COALESCE(SUM(allowable), 0.00) as allowableTotal FROM (" + SQLConstantsReports.hcpcDetailReport + ") as y";

    public static final String paymentsAdjustmentsReport = "SELECT\n" +
            "   facility, \n" +
            "   branchName, \n" +
            "   rxBranchName,\n" +
            "   rxId, \n" +
            "   rxCategory, \n" +
            "   rxSubCategory, \n" +
            "   primaryDriver, \n" +
            "   dateOfService, \n" +
            "   deviceType, \n" +
            "   deviceName, \n" +
            "   createdByFirstName, \n" +
            "   createdByMiddleName, \n" +
            "   createdByLastName, \n" +
            "   paymentDate, \n" +
            "   appliedId, \n" +
            "   appliedDate, \n" +
            "   appliedByFirstName,\n" +
            "   appliedByMiddleName,\n" +
            "   appliedByLastName,\n" +
            "   claimId, \n" +
            "   claimDateResolved, \n" +
            "   paymentId, \n" +
            "   paymentType, \n" +
            "   paymentDescription, \n" +
            "   depositDate, \n" +
            "   patientId, \n" +
            "   patientFirstName, \n" +
            "   patientMiddleName, \n" +
            "   patientLastName, \n" +
            "   patientBranchName, \n" +
            "   practitionerFirstName, \n" +
            "   practitionerMiddleName, \n" +
            "   practitionerLastName, \n" +
            "   therapistFirstName, \n" +
            "   therapistMiddleName, \n" +
            "   therapistLastName, \n" +
            "   primaryCarePhysicianFirstName, \n" +
            "   primaryCarePhysicianMiddleName, \n" +
            "   primaryCarePhysicianLastName,\n" +
            "   referringPhysicianFirstName, \n" +
            "   referringPhysicianMiddleName, \n" +
            "   referringPhysicianLastName, \n" +
            "   viewUserFirstName, \n" +
            "   viewUserMiddleName, \n" +
            "   viewUserLastName, \n" +
            "   payer, \n" +
            "   billerCode, \n" +
            "   CASE WHEN :isSuperAdmin THEN usesNymblRcm ELSE null END AS usesNymblRcm, \n" +
            "   COALESCE(paymentAmount, 0.00) as paymentAmount, \n" +
            "   COALESCE(appliedAmount, 0.00) AS appliedAmount, \n" +
            "   COALESCE(paymentAmount + appliedAmount, 0.00) AS unapplied \n" +
            " FROM ( \n" +
            "   SELECT f.name AS facility, gl.applied_payment_id as appliedId, gl.applied_date as appliedDate, gl.date_of_service as dateOfService, c.id as claimId, c.date_resolved AS claimDateResolved, c.nymbl_rcm AS usesNymblRcm, b.name AS branchName, rxb.name AS rxBranchName, gl.prescription_id AS rxId, rx.primary_driver AS primaryDriver, rx.category as rxCategory, rx.sub_category as rxSubCategory,  gl.device_type AS deviceType, dt.name AS deviceName, u.first_name AS createdByFirstName, u.middle_name AS createdByMiddleName, u.last_name AS createdByLastName, \n" +
            "   gl.payment_date AS paymentDate, gl.payment_id AS paymentId, p.payment_type AS paymentType, p.description as paymentDescription, gl.deposit_date AS depositDate, \n" +
            "   gl.patient_id AS patientId, pt.first_name AS patientFirstName, pt.middle_name AS patientMiddleName, pt.last_name AS patientLastName, pb.name AS patientBranchName, \n" +
            "   tp.first_name AS practitionerFirstName, tp.middle_name AS practitionerMiddleName, tp.last_name AS practitionerLastName, \n" +
            "   th.first_name AS therapistFirstName, th.middle_name AS therapistMiddleName, th.last_name AS therapistLastName, \n" +
            "   pu.first_name AS appliedByFirstName, pu.middle_name AS appliedByMiddleName, pu.last_name AS appliedByLastName, \n" +
            "   pcp.first_name AS primaryCarePhysicianFirstName, pcp.middle_name AS primaryCarePhysicianMiddleName, pcp.last_name AS primaryCarePhysicianLastName, \n" +
            "   rp.first_name AS referringPhysicianFirstName, rp.middle_name AS referringPhysicianMiddleName, rp.last_name AS referringPhysicianLastName, \n" +
            "   vu.first_name AS viewUserFirstName, vu.middle_name AS viewUserMiddleName, vu.last_name AS viewUserLastName, \n" +
            "   COALESCE(ic.name, 'Self Pay') AS payer, ic.biller_code as billerCode, gl.category, gl.sub_category, \n" +
            "   CASE WHEN (gl.category = 'Payments' AND gl.sub_category = 'Unapplied') THEN SUM(gl.amount) * -1  ELSE 0.00 END AS paymentAmount, \n" +
            "   CASE WHEN (gl.category = 'Payments' AND gl.sub_category = 'Applied') THEN SUM(gl.amount) * -1  ELSE 0.00 END AS appliedAmount  \n" +
            "   FROM gl_view gl \n" +
            "   JOIN payment p ON p.id = gl.payment_id \n" +
            "   LEFT JOIN claim c on gl.claim_id = c.id\n" +
            "   LEFT JOIN prescription rx ON rx.id = gl.prescription_id \n" +
            "   LEFT JOIN patient pt ON gl.patient_id = pt.id \n" +
            "   LEFT JOIN branch b ON gl.branch_id = b.id \n" +
            "   LEFT JOIN branch pb ON pt.primary_branch_id = pb.id \n" +
            "   LEFT JOIN branch rxb ON rx.branch_id = rxb.id\n" +
            "   LEFT JOIN device_type dt ON dt.id = gl.device_type_id \n" +
            "   LEFT JOIN applied_payment ap ON gl.applied_payment_id = ap.id\n" +
            "   LEFT JOIN insurance_company ic ON ic.id = p.insurance_company_id \n" +
            "   LEFT JOIN nymbl_master.user u ON u.id = p.created_by_id \n" +
            "   LEFT JOIN nymbl_master.user pu ON pu.id = ap.applied_by\n" +
            "   LEFT JOIN nymbl_master.user tp ON tp.id = gl.treating_practitioner_id \n" +
            "   LEFT JOIN nymbl_master.user vu ON vu.id = rx.view_user_id \n" +
            "   LEFT JOIN physician rp ON rp.id = gl.referring_physician_id \n" +
            "   LEFT JOIN physician pcp ON pcp.id = gl.primary_care_physician_id \n" +
            "   LEFT JOIN therapist th ON th.id = gl.therapist_id \n" +
            "   LEFT JOIN facility f ON gl.facility_id = f.id \n" +
            "   WHERE (gl.category = 'Payments') \n" +
            "   AND (:branchId is null OR :branchId = 0 OR :branchId = gl.branch_id ) \n" +
            "   AND (gl.gl_year IS NOT NULL AND gl.gl_period IS NOT NULL AND CONCAT(gl.gl_year, LPAD(gl.gl_period, 2, 0)) between :start and :end) \n" +
            "   AND (gl.active = true) \n" +
            "   GROUP BY facility, branchName, rxBranchName, rxId,  primaryDriver, rxCategory, rxSubCategory, deviceType, deviceName, createdByFirstName, createdByMiddleName, createdByLastName, \n" +
            "   paymentDate, paymentId, paymentType, gl.applied_payment_id, c.id, c.date_resolved, gl.date_of_service, depositDate, patientId, patientFirstName, patientMiddleName, patientLastName,  patientBranchName, \n" +
            "   practitionerFirstName, practitionerMiddleName, practitionerLastName, \n" +
            "   therapistFirstName, therapistMiddleName, therapistLastName, \n" +
            "   primaryCarePhysicianFirstName, primaryCarePhysicianMiddleName, primaryCarePhysicianLastName, \n" +
            "   referringPhysicianFirstName, referringPhysicianMiddleName, referringPhysicianLastName, \n" +
            "   viewUserFirstName, viewUserMiddleName, viewUserLastName, payer, billerCode, usesNymblRcm, gl.category, gl.sub_category, gl.applied_date) as y ";

    public static final String adjustmentsReport = "SELECT branchName, rxBranchName, rxId, deviceType, appliedDate, u.first_name as createdByFirstName, u.middle_name as createdByMiddleName, u.last_name as createdByLastName, paymentDate, paymentId, p.description as paymentDescription, paymentType, patientId, patientBranchName, patientFirstName, patientMiddleName, patientLastName, practitionerFirstName, practitionerMiddleName, practitionerLastName, ic.name as payer,ic.biller_code as billerCode, '0.00' as amount, COALESCE(SUM(applied), 0.00) as applied, '0.00' as unapplied from (  \n" +
            "               SELECT branchName, rxBranchName, MAX(paymentDate) as paymentDate, MAX(appliedDate) as appliedDate, rxId, deviceType, paymentId as paymentId, paymentType, patientId, patientBranchName, patientFirstName, patientMiddleName, patientLastName, practitionerFirstName, practitionerMiddleName, practitionerLastName, '0.00' as amount, COALESCE(SUM(amount), 0.00) as applied, '0.00' as unapplied FROM ( \n" +
            "                   SELECT gl.prescription_id as rxId, \n" +
            "                       gl.applied_date as appliedDate, \n" +
            "                       b.name as branchName, \n" +
            "                       rxb.name AS rxBranchName, \n" +
            "                       gl.device_type as deviceType, \n" +
            "                       gl.payment_date as paymentDate, \n" +
            "                       COALESCE(gl.payment_id,0) as paymentId, \n" +
            "                       gl.sub_category AS paymentType, \n" +
            "                       gl.patient_id as patientId, \n" +
            "                       pb.name AS patientBranchName,\n" +
            "                       pt.first_name as patientFirstName, \n" +
            "                       pt.middle_name as patientMiddleName, \n" +
            "                       pt.last_name as patientLastName, \n" +
            "                       tp.first_name as practitionerFirstName, \n" +
            "                       tp.middle_name as practitionerMiddleName, \n" +
            "                       tp.last_name as practitionerLastName, \n" +
            "                       gl.category, \n" +
            "                       gl.sub_category AS sub_category, \n" +
            "                       SUM(gl.amount) as amount, \n" +
            "                       gl.prescription_l_code_id \n" +
            "                   FROM gl_view gl \n" +
            "                   JOIN prescription rx ON gl.prescription_id = rx.id \n" +
            "                   LEFT JOIN patient pt ON gl.patient_id = pt.id \n" +
            "                   LEFT JOIN branch pb ON pt.primary_branch_id = pb.id \n" +
            "                   LEFT JOIN branch b ON gl.branch_id = b.id \n" +
            "                   LEFT JOIN branch rxb ON rx.branch_id = rxb.id \n" +
            "                   LEFT JOIN nymbl_master.user tp on tp.id = gl.treating_practitioner_id \n" +
            "                   WHERE ((gl.category = 'Line Adjustments'  AND gl.sub_category <> 'CO-45 Estimate') OR gl.category = 'Adjustments') \n" +
            "                       AND ( :branchId is null OR :branchId = 0 OR :branchId = gl.branch_id ) \n" +
            "                       AND (gl.gl_year IS NOT NULL AND gl.gl_period IS NOT NULL AND CONCAT(gl.gl_year, LPAD(gl.gl_period, 2, 0)) between :start and :end) AND amount <> 0.00 \n" +
            "                       AND gl.active = true \n" +
            "                   GROUP BY rxId, deviceType, branchName, rxBranchName, paymentId, paymentDate, appliedDate, paymentType, patientId, patientBranchName, patientFirstName, patientMiddleName, patientLastName, practitionerFirstName, practitionerMiddleName, practitionerLastName, gl.category, gl.sub_category, gl.prescription_l_code_id) as t \n" +
            "               GROUP BY branchName, rxBranchName, rxId, deviceType, paymentType, patientId, patientBranchName, patientFirstName, patientMiddleName, patientLastName, practitionerFirstName, practitionerMiddleName, practitionerLastName, paymentId) as x \n" +
            "            LEFT JOIN payment p ON x.paymentId = p.id \n" +
            "            LEFT JOIN nymbl_master.user u ON p.created_by_id = u.id \n" +
            "            LEFT JOIN insurance_company ic ON p.insurance_company_id = ic.id \n" +
            "            WHERE applied <> 0.00 \n" +
            "            GROUP BY branchName, rxBranchName, rxId, deviceType, createdByFirstName, createdByMiddleName, createdByLastName, paymentDate, paymentId, paymentType, patientId, patientBranchName, patientFirstName, patientMiddleName, patientLastName, practitionerFirstName, practitionerMiddleName, practitionerLastName, payer, billerCode";


    public static final String accountsReceivable = "SELECT \n" +
            "    MAX(branchName) AS branchName,\n" +
            "    rxBranchName,\n" +
            "    rxId,\n" +
            "    rxCategory,\n" +
            "    rxSubCategory,\n" +
            "    deviceType,\n" +
            "    CASE\n" +
            "        WHEN (MAX(claim) <> 0) THEN COALESCE(MAX(claim), NULL)\n" +
            "        ELSE NULL\n" +
            "    END AS claimId,\n" +
            "    deviceName,\n" +
            "    patientId,\n" +
            "    patientBranchName,\n" +
            "    patientFirstName,\n" +
            "    patientMiddleName,\n" +
            "    patientLastName,\n" +
            "    practitionerFirstName,\n" +
            "    practitionerMiddleName,\n" +
            "    practitionerLastName,\n" +
            "    insuranceCompanyName AS insuranceCompanyName,\n" +
            "    billerCode,\n" +
            "    MIN(dateOfService) AS dateOfService,\n" +
            "    CASE\n" +
            "        WHEN (MAX(submissionDate) <> '1990-01-01') THEN COALESCE(MAX(submissionDate), NULL)\n" +
            "        ELSE NULL\n" +
            "    END AS submissionDate,\n" +
            "    SUM(agingDays) AS agingDays,\n" +
            "    COALESCE(SUM(billable), 0.00) AS billable,\n" +
            "    COALESCE(SUM(payments), 0.00) AS payments,\n" +
            "    COALESCE(SUM(adjustments), 0.00) AS adjustments,\n" +
            "    COALESCE(SUM(billable + payments + adjustments),\n" +
            "            0.00) AS ar,\n" +
            "    COALESCE(activityTable.activity, 0.00) AS activity,\n" +
            "    COALESCE(activityTable.hasActivity, 0) AS hasActivity\n" +
            "FROM\n" +
            "    (SELECT \n" +
            "        (CASE\n" +
            "                WHEN\n" +
            "                    gl.category = 'Accounts Receivable'\n" +
            "                        AND gl.sub_category = 'Sales'\n" +
            "                THEN\n" +
            "                    (SELECT \n" +
            "                            name\n" +
            "                        FROM\n" +
            "                            branch\n" +
            "                        WHERE\n" +
            "                            id = gl.branch_id)\n" +
            "                ELSE ''\n" +
            "            END) AS branchName,\n" +
            "            rxb.name AS rxBranchName,\n" +
            "            gl.prescription_id AS rxId,\n" +
            "            rx.category AS rxCategory,\n" +
            "            rx.sub_category AS rxSubCategory,\n" +
            "            gl.device_type AS deviceType,\n" +
            "            gl.claim_id AS claim,\n" +
            "            dt.name AS deviceName,\n" +
            "            pb.name AS patientBranchName,\n" +
            "            pt.id AS patientId,\n" +
            "            pt.first_name AS patientFirstName,\n" +
            "            pt.middle_name AS patientMiddleName,\n" +
            "            pt.last_name AS patientLastName,\n" +
            "            tp.first_name AS practitionerFirstName,\n" +
            "            tp.middle_name AS practitionerMiddleName,\n" +
            "            tp.last_name AS practitionerLastName,\n" +
            "            ic.icName AS insuranceCompanyName,\n" +
            "            ic.biller_code AS billerCode,\n" +
            "            gl.date_of_service AS dateOfService,\n" +
            "            CASE\n" +
            "                WHEN gl.sub_category = 'Sales' THEN gl.claim_submission_date\n" +
            "                ELSE '1990-01-01'\n" +
            "            END AS submissionDate,\n" +
            "            CASE\n" +
            "                WHEN gl.sub_category = 'Sales' THEN DATEDIFF(CURRENT_DATE(), gl.claim_submission_date)\n" +
            "                ELSE 0\n" +
            "            END AS agingDays,\n" +
            "            CASE\n" +
            "                WHEN\n" +
            "                    gl.category = 'Accounts Receivable'\n" +
            "                        AND gl.sub_category = 'Sales'\n" +
            "                THEN\n" +
            "                    SUM(gl.amount)\n" +
            "                ELSE 0.00\n" +
            "            END AS billable,\n" +
            "            CASE\n" +
            "                WHEN\n" +
            "                    gl.category = 'Accounts Receivable'\n" +
            "                        AND gl.sub_category = 'Applied Payments'\n" +
            "                THEN\n" +
            "                    SUM(gl.amount)\n" +
            "                ELSE 0.00\n" +
            "            END AS payments,\n" +
            "            CASE\n" +
            "                WHEN\n" +
            "                    gl.category = 'Accounts Receivable'\n" +
            "                        AND (gl.sub_category = 'Line Adjustments'\n" +
            "                        OR gl.sub_category = 'Adjustments')\n" +
            "                THEN\n" +
            "                    SUM(gl.amount)\n" +
            "                ELSE 0.00\n" +
            "            END AS adjustments\n" +
            "    FROM\n" +
            "        gl_view gl\n" +
            "    JOIN prescription rx ON gl.prescription_id = rx.id\n" +
            "    LEFT JOIN device_type dt ON gl.device_type_id = dt.id\n" +
            "    LEFT JOIN patient pt ON pt.id = gl.patient_id\n" +
            "    LEFT JOIN branch b ON gl.branch_id = b.id\n" +
            "    LEFT JOIN branch pb ON pt.primary_branch_id = pb.id\n" +
            "    LEFT JOIN branch rxb ON rx.branch_id = rxb.id\n" +
            "    LEFT JOIN nymbl_master.user tp ON tp.id = gl.treating_practitioner_id\n" +
            "    LEFT JOIN (SELECT DISTINCT\n" +
            "        gl2.prescription_id, ic.name AS icName, ic.biller_code\n" +
            "    FROM\n" +
            "        gl_view gl2\n" +
            "    JOIN insurance_company ic ON gl2.insurance_company_id = ic.id\n" +
            "    WHERE\n" +
            "        gl2.category = 'Accounts Receivable'\n" +
            "            AND gl2.sub_category = 'Sales') AS ic ON gl.prescription_id = ic.prescription_id\n" +
            "    WHERE\n" +
            "        gl.category = 'Accounts Receivable'\n" +
            "            AND (:branchId IS NULL OR :branchId = 0\n" +
            "            OR :branchId = gl.branch_id)\n" +
            "            AND (gl.gl_year IS NOT NULL\n" +
            "            AND gl.gl_period IS NOT NULL\n" +
            "            AND CONCAT(gl.gl_year, LPAD(gl.gl_period, 2, 0)) <= :end)\n" +
            "            AND (gl.active = TRUE)\n" +
            "    GROUP BY branchName , rxBranchName , rxId , rxCategory , rxSubCategory , deviceType , claim , deviceName , patientBranchName , patientId , patientFirstName , patientMiddleName , patientLastName , practitionerFirstName , practitionerMiddleName , practitionerLastName , insuranceCompanyName , billerCode , gl.sub_category , agingDays , submissionDate , dateOfService) AS y \n" +
            "        LEFT JOIN\n" +
            "    (SELECT \n" +
            "        prescription_id,\n" +
            "            SUM(z.activity) AS activity,\n" +
            "            MAX(z.hasActivity) AS hasActivity\n" +
            "    FROM\n" +
            "        (SELECT \n" +
            "        gl1.prescription_id,\n" +
            "            COALESCE(gl1.amount, 0.00) AS activity,\n" +
            "            CASE\n" +
            "                WHEN gl1.amount <> 0.00 THEN 1\n" +
            "                ELSE 0\n" +
            "            END AS hasActivity\n" +
            "    FROM\n" +
            "        gl_view gl1\n" +
            "    WHERE\n" +
            "        gl1.category = 'Accounts Receivable'\n" +
            "            AND (:branchId IS NULL OR :branchId = 0\n" +
            "            OR :branchId = gl1.branch_id)\n" +
            "            AND (gl1.gl_year IS NOT NULL\n" +
            "            AND gl1.gl_period IS NOT NULL\n" +
            "            AND CONCAT(gl1.gl_year, LPAD(gl1.gl_period, 2, 0)) BETWEEN :end AND :end)) AS z\n" +
            "    WHERE\n" +
            "        activity <> 0.00 AND hasActivity <> 0\n" +
            "    GROUP BY prescription_id) AS activityTable ON rxId = activityTable.prescription_id\n" +
            "GROUP BY rxBranchName , rxId , rxCategory , rxSubCategory , deviceType , deviceName , patientBranchName , patientId , patientFirstName , patientMiddleName , patientLastName , practitionerFirstName , practitionerMiddleName , practitionerLastName , insuranceCompanyName , billerCode , activity , hasActivity ";

    public static final String accountsReceivableOld = "SELECT MAX(branchName) AS branchName, \n" +
            "   rxBranchName, \n" +
            "   rxId, \n" +
            "   rxCategory, \n" +
            "   rxSubCategory, \n" +
            "   deviceType, \n" +
            "   CASE WHEN (MAX(claim) <> 0) THEN COALESCE(MAX(claim), NULL) ELSE NULL END AS claimId, \n" +
            "   deviceName, \n" +
            "   patientId, \n" +
            "   patientBranchName, \n" +
            "   patientFirstName, \n" +
            "   patientMiddleName, \n" +
            "   patientLastName, \n" +
            "   practitionerFirstName, \n" +
            "   practitionerMiddleName, \n" +
            "   practitionerLastName, \n" +
            "   insuranceCompanyName as insuranceCompanyName, \n" +
            "   billerCode, \n" +
            "   MIN(dateOfService) as dateOfService, \n" +
            "   CASE WHEN (MAX(submissionDate) <> '1990-01-01') THEN COALESCE(MAX(submissionDate), NULL)  ELSE NULL END AS submissionDate, \n" +
            "   SUM(agingDays) as agingDays, COALESCE(SUM(billable),0.00) as billable, COALESCE(SUM(payments), 0.00) as payments, \n" +
            "   COALESCE(SUM(billable + payments), 0.00) as ar, \n" +
            "   COALESCE(activityTable.activity, 0.00) as activity, \n" +
            "   COALESCE(activityTable.hasActivity, 0) as hasActivity \n" +
            "FROM (SELECT (CASE WHEN gl.category = 'Accounts Receivable'  AND gl.sub_category = 'Sales'  THEN (SELECT name FROM branch WHERE id = gl.branch_id) ELSE '' END) AS branchName, rxb.name AS rxBranchName, gl.prescription_id as rxId,rx.category as rxCategory, rx.sub_category as rxSubCategory, gl.device_type as deviceType, gl.claim_id as claim, dt.name as deviceName, \n" +
            "       pb.name AS patientBranchName, pt.id as patientId, pt.first_name as patientFirstName, pt.middle_name as patientMiddleName, pt.last_name as patientLastName, \n" +
            "       tp.first_name as practitionerFirstName, tp.middle_name as practitionerMiddleName, tp.last_name as practitionerLastName, \n" +
            "       ic.icName as insuranceCompanyName, ic.biller_code AS billerCode, gl.date_of_service as dateOfService, \n" +
            "       CASE WHEN gl.sub_category = 'Sales' THEN gl.claim_submission_date ELSE '1990-01-01' END as submissionDate, \n" +
            "       CASE WHEN gl.sub_category = 'Sales' THEN DATEDIFF(current_date(), gl.claim_submission_date) ELSE 0 END as agingDays, \n" +
            "       CASE WHEN gl.category = 'Accounts Receivable'  AND gl.sub_category = 'Sales'  THEN SUM(gl.amount) ELSE 0.00 END as billable, \n" +
            "       CASE WHEN gl.category = 'Accounts Receivable'  AND gl.sub_category <> 'Sales'  THEN SUM(gl.amount) ELSE 0.00 END as payments, \n" +
            "       gl.sub_category \n" +
            "   FROM gl_view gl \n" +
            "   JOIN prescription rx ON gl.prescription_id = rx.id \n" +
            "   LEFT JOIN device_type dt ON gl.device_type_id = dt.id \n" +
            "   LEFT JOIN patient pt on pt.id = gl.patient_id \n" +
            "   LEFT JOIN branch b ON gl.branch_id = b.id \n" +
            "   LEFT JOIN branch pb ON pt.primary_branch_id = pb.id \n" +
            "   LEFT JOIN branch rxb ON rx.branch_id = rxb.id \n" +
            "   LEFT JOIN nymbl_master.user tp on tp.id = gl.treating_practitioner_id \n" +
            "   LEFT JOIN (SELECT DISTINCT gl2.prescription_id, ic.name as icName, ic.biller_code FROM gl_view gl2 \n" +
            "               JOIN insurance_company ic ON gl2.insurance_company_id = ic.id \n" +
            "               WHERE gl2.category = 'Accounts Receivable'  AND gl2.sub_category = 'Sales') as ic ON gl.prescription_id = ic.prescription_id \n" +
            "   WHERE gl.category = 'Accounts Receivable' \n" +
            "   AND ( :branchId is null OR :branchId = 0 OR :branchId = gl.branch_id ) \n" +
            "   AND (gl.gl_year IS NOT NULL AND gl.gl_period IS NOT NULL AND CONCAT(gl.gl_year, LPAD(gl.gl_period, 2, 0)) <= :end) \n" +
            "   AND (gl.active = true) \n" +
            "   GROUP BY branchName, rxBranchName, rxId, rxCategory, rxSubCategory, deviceType, claim, deviceName, patientBranchName, patientId, patientFirstName, patientMiddleName, patientLastName,  practitionerFirstName, practitionerMiddleName, practitionerLastName, insuranceCompanyName, billerCode, gl.sub_category, agingDays, submissionDate, dateOfService ) as y \n" +
            "LEFT JOIN (\n" +
            "   SELECT prescription_id, SUM(z.activity) as activity, MAX(z.hasActivity) as hasActivity FROM (\n" +
            "SELECT  gl1.prescription_id, COALESCE(gl1.amount,0.00) as activity, CASE WHEN gl1.amount <> 0.00 THEN 1 ELSE 0 END AS hasActivity FROM gl_view gl1 \n" +
            "           WHERE gl1.category = 'Accounts Receivable' \n" +
            "           AND ( :branchId is null OR :branchId = 0 OR :branchId = gl1.branch_id ) \n" +
            "           AND (gl1.gl_year IS NOT NULL AND gl1.gl_period IS NOT NULL AND CONCAT(gl1.gl_year, LPAD(gl1.gl_period, 2, 0)) between :end AND :end)\n" +
            "       ) AS z WHERE activity <> 0.00 AND hasActivity <> 0 GROUP BY prescription_id\n" +
            "   ) AS activityTable ON rxId = activityTable.prescription_id \n" +
            "GROUP BY rxBranchName, rxId, rxCategory, rxSubCategory, deviceType, deviceName, patientBranchName, patientId, patientFirstName, patientMiddleName, patientLastName, practitionerFirstName, practitionerMiddleName, practitionerLastName, insuranceCompanyName, billerCode, activity, hasActivity ";

    public static final String accountsReceivableTotals = "SELECT COALESCE(SUM(billable), 0.00) as totalBillable, COALESCE(SUM(payments), 0.00) as totalAmount, COALESCE(SUM(ar), 0.00) as totalAr, COALESCE(SUM(activity), 0.00) as totalActivity FROM (" + SQLConstantsReports.accountsReceivable + ") as y";

    public static final String outstandingBalancesReport = "SELECT \n" +
            "    c.id AS claimId,\n" +
            "    c.created_at AS claimCreatedDate, \n" +
            "    cs.name AS claimStatus, \n" +
            "     glar.insuranceCompanyName as primaryInsuranceName, \n" +
            "    secondaryData.submissionDate2 AS secondarySubmissionDate,\n" +
            "    secondaryData.secondaryInsurance AS secondaryInsuranceName,\n" +
            "    (SELECT \n" +
            "            MAX(cs.submission_date)\n" +
            "        FROM\n" +
            "            claim_submission cs\n" +
            "        WHERE\n" +
            "            cs.claim_id = claimId) AS mostRecentSubmissionDate, \n" +
            "    cu.first_name AS assignedFirstName, \n" +
            "    cu.middle_name AS assignedMiddleName, \n" +
            "    cu.last_name AS assignedLastName, \n" +
            "    glar.branchName AS billingBranchName,\n" +
            "    glar.rxId AS rxId,\n" +
            "    glar.rxCategory as rxCategory, \n" +
            "    glar.rxSubCategory as rxSubCategory, \n" +
            "    rxBranchName,\n" +
            "    glar.patientId AS patientId,\n" +
            "    iv.carrier_type AS carrierType,\n" +
            "    ic.biller_code AS billerCode,\n" +
            "    ic.name AS responsiblePayerName,\n" +
            "    CASE\n" +
            "        WHEN c.total_claim_balance <> 0.00 OR (c.total_claim_balance = 0 AND c.total_pt_responsibility_balance = 0) THEN 'payer'\n" +
            "        ELSE 'patient'\n" +
            "    END AS responsibleParty,\n" +
            "    c.total_claim_balance AS totalClaimBalance,\n" +
            "    c.total_pt_responsibility_balance AS totalPatientResponsibilityBalance,\n" +
            "    glar.billable AS billable,\n" +
            "    glar.payments AS payments,\n" +
            "    glar.adjustments AS adjustments,\n" +
            "    glar.ar AS outstandingBalance,\n" +
            "    SUM(COALESCE(ivlc.total_charge, 0.00)) AS billableTotal,\n" +
            "    SUM(COALESCE(ivlc.total_allowable, 0.00)) AS allowableTotal,\n" +
            "    glar.dateOfService AS dateOfService,\n" +
            "    glar.agingDays AS agingDays,\n" +
            "    deviceType,\n" +
            "    deviceName,\n" +
            "    patientBranchName,\n" +
            "    patientFirstName,\n" +
            "    patientMiddleName,\n" +
            "    patientLastName,\n" +
            "    submissionDate,\n" +
            "    latest_note.updated_at AS latestCommentDate,\n" +
            "    SUBSTRING(latest_note.note, 1, 32000) AS latestComment,\n" +
            "    latest_note.user_name AS latestCommentBy\n" +
            "FROM\n" +
            "    claim c\n" +
            "        JOIN\n" +
            "    ( " +
            accountsReceivable +
            " ) AS glar ON glar.rxId = c.prescription_id \n" +
            "        JOIN\n" +
            "    patient_insurance pi ON pi.id = c.responsible_patient_insurance_id\n" +
            "        JOIN\n" +
            "    insurance_company ic ON ic.id = pi.insurance_company_id\n" +
            "        JOIN\n" +
            "    insurance_verification iv ON iv.prescription_id = glar.rxId\n" +
            "        AND iv.patient_insurance_id = c.responsible_patient_insurance_id\n" +
            "        JOIN\n" +
            "    insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id \n" +
            "    LEFT JOIN nymbl_master.user cu on c.user_id = cu.id \n" +
            "    LEFT JOIN nymbl_status cs ON cs.id = c.nymbl_status_id \n" +
            "LEFT JOIN\n" +
            "    (SELECT n.*, CONCAT(nu.first_name, ' ', nu.last_name) AS user_name, c.id AS c_id FROM note n JOIN claim c ON c.id = n.claim_id OR c.prescription_id = n.prescription_id \n" +
            "    JOIN (SELECT latest1.c_id, max(latest1.note_id) AS note_id FROM \n" +
            "    (SELECT c.id AS c_id, max(n.updated_at) AS updated_at, n.id AS note_id FROM claim c JOIN note n ON c.id = n.claim_id OR c.prescription_id = n.prescription_id \n" +
            "    WHERE n.note_type = 'claim_comments' GROUP BY c.id, n.id) latest1 GROUP BY latest1.c_id) latest ON latest.c_id = c.id AND latest.note_id = n.id \n" +
            "    LEFT JOIN nymbl_master.user nu ON n.created_by_id = nu.id) latest_note ON c.id = latest_note.c_id \n" +
            "LEFT JOIN\n" +
            "    (SELECT \n" +
            "        iv2.prescription_id AS rxId2,\n" +
            "            c2.id AS primaryClaimId,\n" +
            "            ic2.name AS secondaryInsurance,\n" +
            "            ic2.timely_filing_days AS timelyFiling,\n" +
            "            ic2.biller_code AS billerCode,\n" +
            "            pin2.insurance_number AS insuranceNumber,\n" +
            "            iv2.benefits_payable AS insurancePercentage,\n" +
            "            iv2.patient_insurance_id,\n" +
            "            MIN(cs2.submission_date) AS submissionDate2\n" +
            "    FROM\n" +
            "        insurance_verification iv2\n" +
            "    JOIN patient_insurance pin2 ON iv2.patient_insurance_id = pin2.id\n" +
            "    JOIN insurance_company ic2 ON pin2.insurance_company_id = ic2.id\n" +
            "    JOIN claim c2 ON c2.prescription_id = iv2.prescription_id\n" +
            "    JOIN claim_submission cs2 ON cs2.claim_id = c2.id\n" +
            "        AND cs2.patient_insurance_id = iv2.patient_insurance_id\n" +
            "    WHERE\n" +
            "        iv2.carrier_type = 'secondary'\n" +
            "    GROUP BY iv2.prescription_id , c2.id , ic2.name , ic2.timely_filing_days , ic2.biller_code , pin2.insurance_number , iv2.benefits_payable , iv2.patient_insurance_id) AS secondaryData ON rxId = secondaryData.rxId2 \n" +
            "WHERE \n" +
            " glar.ar <> 0.00 AND (c.total_claim_balance <> 0.00 OR c.total_pt_responsibility_balance <> 0.00) \n" +
            "GROUP BY pi.id , iv.carrier_type , rxId , billingBranchName , patientId , ar , dateOfService , agingDays , submissionDate , c.id , billable , payments , adjustments, outstandingBalance , dateOfService , agingDays , submissionDate ,primaryInsuranceName, secondarySubmissionDate , secondaryInsuranceName ,deviceType , deviceName, claimId, claimStatus, latest_note.user_name, latest_note.note\n";
    
    public static final String practitionerAppointmentReport = "SELECT DISTINCT\n" +
            "    c.id AS id,\n" +
            "    COALESCE(c.first_name, '') AS firstName,\n" +
            "    COALESCE(c.middle_name, '') AS middleName,\n" +
            "    COALESCE(c.last_name, '') AS lastName,\n" +
            "    COALESCE(c.credentials, '') AS credentials,\n" +
            "    DATE_FORMAT(a.start_datetime, '%Y-%m-%dT%TZ') AS startDateTime,\n" +
            "    DATE_FORMAT(a.end_datetime, '%Y-%m-%dT%TZ') AS endDateTime,\n" +
            "    COALESCE(t.name, '') AS apptType,\n" +
            "    a.status AS apptStatus,\n" +
            "    COALESCE(p.first_name, '') AS patientFirstName,\n" +
            "    COALESCE(p.middle_name, '') AS patientMiddleName,\n" +
            "    COALESCE(p.last_name, '') AS patientLastName,\n" +
            "    COALESCE(p.cell_phone, '') AS patientCellPhone,\n" +
            "    COALESCE(p.home_phone, '') AS patientHomePhone,\n" +
            "    COALESCE(p.work_phone, '') AS patientWorkPhone,\n" +
            "    COALESCE(p.street_address, '') AS patientStreetAddress,\n" +
            "    COALESCE(p.street_address_line2, '') AS patientStreetAddressLine2,\n" +
            "    COALESCE(p.city, '') AS patientCity,\n" +
            "    COALESCE(p.state, '') AS patientState,\n" +
            "    COALESCE(p.zipcode, '') AS patientZipcode,\n" +
            "    COALESCE(p.dob, '') AS dob,\n" +
            "    CASE\n" +
            "        WHEN p.preferred_phone = 'work' THEN p.work_phone\n" +
            "        WHEN p.preferred_phone = 'home' THEN p.home_phone\n" +
            "        WHEN p.preferred_phone = 'cell' THEN p.cell_phone\n" +
            "        WHEN p.preferred_phone IS NULL AND p.cell_phone IS NOT NULL THEN p.cell_phone\n" +
            "        WHEN p.preferred_phone IS NULL AND p.cell_phone IS NULL THEN p.home_phone\n" +
            "    END AS patientPreferredPhone, \n" +
            "    CASE\n" +
            "        WHEN p.dob IS NOT NULL THEN DATE_FORMAT(NOW(), '%Y') - DATE_FORMAT(p.dob, '%Y') - (DATE_FORMAT(NOW(), '00-%m-%d') < DATE_FORMAT(p.dob, '00-%m-%d'))\n" +
            "        ELSE ''\n" +
            "    END AS age,\n" +
            "    COALESCE(p.gender, '') AS gender,\n" +
            "    COALESCE(dt.orthotic_or_prosthetic, '') AS deviceType,\n" +
            "    COALESCE(dt.name, '') AS deviceName,\n" +
            "    COALESCE(dc.name, '') AS primaryDiagnosisName,\n" +
            "    COALESCE(dc.code, '') AS primaryDiagnosisCode,\n" +
            "    COALESCE(a.notes, '') AS apptNotes,\n" +
            "    rx.category AS rxCategory,\n" +
            "    rx.sub_category AS rxSubCategory\n" +
            "FROM\n" +
            "    appointment a\n" +
            "        JOIN\n" +
            "    appointment_type t ON t.id = a.appointment_type_id\n" +
            "        LEFT JOIN\n" +
            "    patient p ON a.patient_id = p.id\n" +
            "        LEFT JOIN\n" +
            "    prescription rx ON a.prescription_id = rx.id\n" +
            "        OR a.prescription_two_id = rx.id\n" +
            "        LEFT JOIN\n" +
            "    device_type dt ON rx.device_type_id = dt.id\n" +
            "        LEFT JOIN\n" +
            "    prescription_diagnosis_code pcd ON rx.id = pcd.prescription_id\n" +
            "        AND pcd.is_primary = TRUE\n" +
            "        LEFT JOIN\n" +
            "    diagnosis_code dc ON pcd.diagnosis_code_id = dc.id\n" +
            "        JOIN\n" +
            "    nymbl_master.user c ON c.id = a.user_id\n" +
            "        JOIN\n" +
            "    user_roles ur ON a.user_id = ur.user_id\n" +
            "WHERE\n" +
            "    (CASE\n" +
            "        WHEN :userId IS NOT NULL AND :userId <> 0 THEN a.user_id = :userId\n" +
            "        ELSE a.user_id IS NOT NULL\n" +
            "    END)\n" +
            "        AND (CASE\n" +
            "        WHEN :branchId IS NOT NULL AND :branchId <> 0 THEN a.branch_id = :branchId\n" +
            "        ELSE a.branch_id IS NOT NULL\n" +
            "    END)\n" +
            "        AND a.patient_id IS NOT NULL\n" +
            "        AND (ur.role_id = 1 OR ur.role_id = 5)\n" +
            "        AND a.start_datetime BETWEEN :start AND :end \n" +
            "UNION ALL SELECT DISTINCT\n" +
            "    c.id AS id,\n" +
            "    COALESCE(c.first_name, '') AS firstName,\n" +
            "    COALESCE(c.middle_name, '') AS middleName,\n" +
            "    COALESCE(c.last_name, '') AS lastName,\n" +
            "    COALESCE(c.credentials, '') AS credentials,\n" +
            "    DATE_FORMAT(a.start_datetime, '%Y-%m-%dT%TZ') AS startDateTime,\n" +
            "    DATE_FORMAT(a.end_datetime, '%Y-%m-%dT%TZ') AS endDateTime,\n" +
            "    COALESCE(t.name, '') AS apptType,\n" +
            "    a.status AS apptStatus,\n" +
            "    COALESCE(p.first_name, '') AS patientFirstName,\n" +
            "    COALESCE(p.middle_name, '') AS patientMiddleName,\n" +
            "    COALESCE(p.last_name, '') AS patientLastName,\n" +
            "    COALESCE(p.cell_phone, '') AS patientCellPhone,\n" +
            "    COALESCE(p.home_phone, '') AS patientHomePhone,\n" +
            "    COALESCE(p.work_phone, '') AS patientWorkPhone,\n" +
            "    COALESCE(p.street_address, '') AS patientStreetAddress,\n" +
            "    COALESCE(p.street_address_line2, '') AS patientStreetAddressLine2,\n" +
            "    COALESCE(p.city, '') AS patientCity,\n" +
            "    COALESCE(p.state, '') AS patientState,\n" +
            "    COALESCE(p.zipcode, '') AS patientZipcode,\n" +
            "    COALESCE(p.dob, '') AS dob,\n" +
            "    CASE\n" +
            "        WHEN p.preferred_phone = 'work' THEN p.work_phone\n" +
            "        WHEN p.preferred_phone = 'home' THEN p.home_phone\n" +
            "        WHEN p.preferred_phone = 'cell' THEN p.cell_phone\n" +
            "        WHEN p.preferred_phone IS NULL AND p.cell_phone IS NOT NULL THEN p.cell_phone\n" +
            "        WHEN p.preferred_phone IS NULL AND p.cell_phone IS NULL THEN p.home_phone\n" +
            "    END AS patientPreferredPhone, \n" +
            "    CASE\n" +
            "        WHEN p.dob IS NOT NULL THEN DATE_FORMAT(NOW(), '%Y') - DATE_FORMAT(p.dob, '%Y') - (DATE_FORMAT(NOW(), '00-%m-%d') < DATE_FORMAT(p.dob, '00-%m-%d'))\n" +
            "        ELSE ''\n" +
            "    END AS age,\n" +
            "    COALESCE(p.gender, '') AS gender,\n" +
            "    COALESCE(dt.orthotic_or_prosthetic, '') AS deviceType,\n" +
            "    COALESCE(dt.name, '') AS deviceName,\n" +
            "    COALESCE(dc.name, '') AS primaryDiagnosisName,\n" +
            "    COALESCE(dc.code, '') AS primaryDiagnosisCode,\n" +
            "    COALESCE(a.notes, '') AS apptNotes,\n" +
            "    rx.category AS rxCategory,\n" +
            "    rx.sub_category AS rxSubCategory\n" +
            "FROM\n" +
            "    appointment a\n" +
            "        JOIN\n" +
            "    appointment_type t ON t.id = a.appointment_type_id\n" +
            "        LEFT JOIN\n" +
            "    patient p ON a.patient_id = p.id\n" +
            "        LEFT JOIN\n" +
            "    prescription rx ON a.prescription_id = rx.id\n" +
            "        OR a.prescription_two_id = rx.id\n" +
            "        LEFT JOIN\n" +
            "    device_type dt ON rx.device_type_id = dt.id\n" +
            "        LEFT JOIN\n" +
            "    prescription_diagnosis_code pcd ON rx.id = pcd.prescription_id\n" +
            "        AND pcd.is_primary = TRUE\n" +
            "        LEFT JOIN\n" +
            "    diagnosis_code dc ON pcd.diagnosis_code_id = dc.id\n" +
            "        JOIN\n" +
            "    nymbl_master.user c ON c.id = a.user_two_id\n" +
            "        JOIN\n" +
            "    user_roles ur ON a.user_two_id = ur.user_id\n" +
            "WHERE\n" +
            "    (CASE\n" +
            "        WHEN :userId IS NOT NULL AND :userId <> 0 THEN a.user_two_id = :userId\n" +
            "        ELSE a.user_two_id IS NOT NULL\n" +
            "    END)\n" +
            "        AND (CASE\n" +
            "        WHEN :branchId IS NOT NULL AND :branchId <> 0 THEN a.branch_id = :branchId\n" +
            "        ELSE a.branch_id IS NOT NULL\n" +
            "    END)\n" +
            "        AND a.patient_id IS NOT NULL\n" +
            "        AND (ur.role_id = 1 OR ur.role_id = 5)\n" +
            "        AND a.start_datetime BETWEEN :start AND :end \n" +
            "UNION ALL SELECT DISTINCT\n" +
            "    c.id AS id,\n" +
            "    COALESCE(c.first_name, '') AS firstName,\n" +
            "    COALESCE(c.middle_name, '') AS middleName,\n" +
            "    COALESCE(c.last_name, '') AS lastName,\n" +
            "    COALESCE(c.credentials, '') AS credentials,\n" +
            "    DATE_FORMAT(a.start_datetime, '%Y-%m-%dT%TZ') AS startDateTime,\n" +
            "    DATE_FORMAT(a.end_datetime, '%Y-%m-%dT%TZ') AS endDateTime,\n" +
            "    COALESCE(t.name, '') AS apptType,\n" +
            "    a.status AS apptStatus,\n" +
            "    COALESCE(p.first_name, '') AS patientFirstName,\n" +
            "    COALESCE(p.middle_name, '') AS patientMiddleName,\n" +
            "    COALESCE(p.last_name, '') AS patientLastName,\n" +
            "    COALESCE(p.cell_phone, '') AS patientCellPhone,\n" +
            "    COALESCE(p.home_phone, '') AS patientHomePhone,\n" +
            "    COALESCE(p.work_phone, '') AS patientWorkPhone,\n" +
            "    COALESCE(p.street_address, '') AS patientStreetAddress,\n" +
            "    COALESCE(p.street_address_line2, '') AS patientStreetAddressLine2,\n" +
            "    COALESCE(p.city, '') AS patientCity,\n" +
            "    COALESCE(p.state, '') AS patientState,\n" +
            "    COALESCE(p.zipcode, '') AS patientZipcode,\n" +
            "    COALESCE(p.dob, '') AS dob,\n" +
            "    CASE\n" +
            "        WHEN p.preferred_phone = 'work' THEN p.work_phone\n" +
            "        WHEN p.preferred_phone = 'home' THEN p.home_phone\n" +
            "        WHEN p.preferred_phone = 'cell' THEN p.cell_phone\n" +
            "        WHEN p.preferred_phone IS NULL AND p.cell_phone IS NOT NULL THEN p.cell_phone\n" +
            "        WHEN p.preferred_phone IS NULL AND p.cell_phone IS NULL THEN p.home_phone\n" +
            "    END AS patientPreferredPhone, \n" +
            "    CASE\n" +
            "        WHEN p.dob IS NOT NULL THEN DATE_FORMAT(NOW(), '%Y') - DATE_FORMAT(p.dob, '%Y') - (DATE_FORMAT(NOW(), '00-%m-%d') < DATE_FORMAT(p.dob, '00-%m-%d'))\n" +
            "        ELSE ''\n" +
            "    END AS age,\n" +
            "    COALESCE(p.gender, '') AS gender,\n" +
            "    COALESCE(dt.orthotic_or_prosthetic, '') AS deviceType,\n" +
            "    COALESCE(dt.name, '') AS deviceName,\n" +
            "    COALESCE(dc.name, '') AS primaryDiagnosisName,\n" +
            "    COALESCE(dc.code, '') AS primaryDiagnosisCode,\n" +
            "    COALESCE(a.notes, '') AS apptNotes,\n" +
            "    rx.category AS rxCategory,\n" +
            "    rx.sub_category AS rxSubCategory\n" +
            "FROM\n" +
            "    appointment a\n" +
            "        JOIN\n" +
            "    appointment_type t ON t.id = a.appointment_type_id\n" +
            "        LEFT JOIN\n" +
            "    patient p ON a.patient_id = p.id\n" +
            "        LEFT JOIN\n" +
            "    prescription rx ON a.prescription_id = rx.id\n" +
            "        OR a.prescription_two_id = rx.id\n" +
            "        LEFT JOIN\n" +
            "    device_type dt ON rx.device_type_id = dt.id\n" +
            "        LEFT JOIN\n" +
            "    prescription_diagnosis_code pcd ON rx.id = pcd.prescription_id\n" +
            "        AND pcd.is_primary = TRUE\n" +
            "        LEFT JOIN\n" +
            "    diagnosis_code dc ON pcd.diagnosis_code_id = dc.id\n" +
            "        JOIN\n" +
            "    nymbl_master.user c ON c.id = a.user_three_id\n" +
            "        JOIN\n" +
            "    user_roles ur ON a.user_three_id = ur.user_id\n" +
            "WHERE\n" +
            "    (CASE\n" +
            "        WHEN :userId IS NOT NULL AND :userId <> 0 THEN a.user_three_id = :userId\n" +
            "        ELSE a.user_three_id IS NOT NULL\n" +
            "    END)\n" +
            "        AND (CASE\n" +
            "        WHEN :branchId IS NOT NULL AND :branchId <> 0 THEN a.branch_id = :branchId\n" +
            "        ELSE a.branch_id IS NOT NULL\n" +
            "    END)\n" +
            "        AND a.patient_id IS NOT NULL\n" +
            "        AND (ur.role_id = 1 OR ur.role_id = 5)\n" +
            "        AND a.start_datetime BETWEEN :start AND :end";

    public static final String gLImportSQL = "INSERT INTO general_ledger ( \n" +
                    "`category_id`, `gl_date`, `gl_applied_date`,`gl_year`, `gl_period`,`gl_account`, `category`, `sub_category`, `amount`, \n" +
                    "`abs_amount`, `patient_id`, `prescription_id`, `claim_id`, `branch_id`, `patient_branch_id`, `prescription_branch_id`, `facility_id`, `prescription_l_code_id`, `l_code_id`, \n" +
                    "`insurance_verification_id`, `insurance_verification_l_code_id`, `payment_id`, `applied_payment_id`, \n" +
                    "`applied_payment_l_code_id`, `payer_type`, `payment_type`, `check_number`, `insurance_company_id`, `carrier_type`, `patient_insurance_id`, \n" +
                    "`device_type_id`, `device_type`, `treating_practitioner_id`, `primary_care_physician_id`, `therapist_id`, \n" +
                    "`referring_physician_id`, `claim_submission_date`, `date_of_service`, `prescription_date`, `payment_date`, `deposit_date`, \n" +
                    "`applied_date`, `rx_active`, `patient_active`) \n" +
                    "SELECT x.category_id, x.gl_date, x.gl_applied_date, \n" +
                    "CASE WHEN x.gl_applied_date IS NOT NULL AND (SELECT period.year FROM gl_period period WHERE unix_timestamp(x.gl_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(period.end_date) AND unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) > 0) \n" +
                    "THEN (SELECT period.year FROM gl_period period WHERE unix_timestamp(x.gl_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(period.end_date) AND unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) LIMIT 1 ) \n" +
                    "ELSE (SELECT period.year FROM gl_period period WHERE unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) ORDER BY (period.year * 100 + period.period) DESC LIMIT 1) \n" +
                    "END AS gl_year,\n" +
                    "CASE WHEN x.gl_applied_date IS NOT NULL AND (SELECT period.year FROM gl_period period WHERE unix_timestamp(x.gl_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(period.end_date) AND unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) > 0) \n" +
                    "THEN (SELECT period.period  FROM gl_period period WHERE unix_timestamp(x.gl_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(period.end_date) AND unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) LIMIT 1 ) \n" +
                    "ELSE (SELECT period.period  FROM gl_period period WHERE unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) ORDER BY (period.year * 100 + period.period) DESC LIMIT 1) \n" +
            "END AS gl_period,  \n" +
            "x.gl_account, x.category, x.sub_category, x.amount, x.abs_amount, x.patient_id, x.prescription_id, x.claim_id, x.billing_branch_id, x.patient_branch_id, x.prescription_branch_id, \n" +
            "x.facility_id, x.plc_id, x.l_code_id,x.insurance_verification_id, x.ivlc_id, x.payment_id, x.ap_id, x.aplc_id, \n" +
            "x.payer_type, x.payment_type, x.check_number, x.insurance_company_id, x.carrier_type, x.patient_insurance, x.device_type_id, x.device_type, \n" +
            "x.treating_practitioner_id, x.primary_care_physician_id,  x.therapist_id, x.referring_physician_id, x.claim_submission_date, \n" +
            "x.date_of_service, x.prescription_date, x.payment_date, x.deposit_date, x.applied_date, x.rx_active, x.pt_active \n" +
            "FROM ( \n" +
            " -- ***SALES / TOTAL CHARGES*** \n" +
            " -- Billable/Sales --  \n" +
            "SELECT DISTINCT 1 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'billable')  , '&b&', COALESCE(b.code, '000') ), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Charge' as category, 'Billable' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id, \n" +
            "primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type, \n" +
            "null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date, \n" +
            "c.date_of_service as date_of_service, rx.prescription_date, null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date, \n" +
            "COALESCE(ivlc.total_charge, 0.00) * - 1 as amount , ABS(COALESCE(ivlc.total_charge, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active \n" +
            "FROM insurance_verification_l_code ivlc \n" +
            "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
            "JOIN prescription rx ON rx.id = iv.prescription_id \n" +
            "JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id \n" +
            "JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id \n" +
            "AND pin.id = ( \n" +
            "SELECT iv1.patient_insurance_id \n" +
                    "FROM insurance_verification iv1 \n" +
            "WHERE iv1.prescription_id = rx.id \n" +
            "ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, \n" +
            "iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1) \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(submission_date) as subDate , MIN(c1.id) as primary_claim\n" +
            "FROM claim_submission cs1 \n" +
            "INNER JOIN claim c1 ON cs1.claim_id = c1.id \n" +
            "INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id) as x ON x.id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id\n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "UNION ALL \n" +

            "-- Sales Tax (Negative) \n" +
            "SELECT DISTINCT 2 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_tax')  , '&b&', COALESCE(b.code, '000') ), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Tax' as category, 'Sales Tax' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id, \n" +
            "primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type, \n" +
            "null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date, \n" +
            "c.date_of_service as date_of_service, rx.prescription_date, null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date, \n" +
            "COALESCE(ivlc.sales_tax, 0.00) * -1 as amount, ABS(COALESCE(ivlc.sales_tax, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active \n" +
            "FROM insurance_verification_l_code ivlc \n" +
            "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
            "JOIN prescription rx ON rx.id = iv.prescription_id \n" +
            "JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id \n" +
            "JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id \n" +
            "AND pin.id = ( \n" +
            "SELECT iv1.patient_insurance_id \n" +
                    "FROM insurance_verification iv1 \n" +
            "WHERE iv1.prescription_id = rx.id \n" +
            "ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, \n" +
            "iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1) \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim\n" +
            "FROM claim_submission cs1 \n" +
            "INNER JOIN claim c1 ON cs1.claim_id = c1.id \n" +
            "INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id) as x ON x.id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE ivlc.use_sales_tax = 1 and ivlc.sales_tax IS NOT NULL \n" +

            "UNION ALL \n" +
            "-- *** CASH *** \n" +
            "-- Payments \n" +
            "SELECT DISTINCT 3 as category_id, (SELECT x.account FROM gl_account x WHERE x.name = 'unapplied') as gl_account, \n" +
            "'Payments' as category, 'Unapplied' as sub_category, p.patient_id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id ,  null as facility_id, CASE WHEN p.prescription_id IS NULL THEN c.prescription_id ELSE p.prescription_id END as prescription_id, \n" +
            "p.claim_id as claim_id, null as plc_id, null as l_code_id, null as ivlc_id, p.id as payment_id, null as ap_id, null as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, null as insurance_company_id, null as carrier_type, null as patient_insurance, null as insurance_verification_id, null as device_type_id, null as device_type, \n" +
            "null as treating_practitioner_id, null as primary_care_physician_id, null as therapist_id, null as referring_physician_id, null as claim_submission_date, \n" +
            "null as date_of_service, null as prescription_date, p.date as payment_date, p.deposit_date as deposit_date, null as applied_date, p.date as gl_date, p.date as gl_applied_date, \n" +
            "COALESCE(p.amount, 0.00) * -1 as amount, ABS(COALESCE(p.amount, 0.00)) as abs_amount, null as rx_active, null as pt_active \n" +
            "FROM payment p \n" +
            "LEFT JOIN claim c ON p.claim_id = c.id \n" +
            "LEFT JOIN patient pt ON p.patient_id = pt.id \n" +
            "LEFT JOIN prescription rx ON c.prescription_id = rx.id\n" +
            "WHERE p.adjustment = 0.00 and p.adjustment_id IS NULL \n" +

            "UNION ALL \n" +

            "-- Payment Reversal  --  \n" +
            "SELECT DISTINCT 4 as category_id, (SELECT x.account FROM gl_account x WHERE x.name = 'unapplied') as gl_account, \n" +
            "'Payments' as category, 'Applied' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, \n" +
            "c.id as claim_id, plc.id as plc_id, plc.l_code_id, null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, \n" +
            "c.date_of_service as date_of_service, rx.prescription_date, p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.amount, 0.00) as amount, ABS(COALESCE(aplc.amount, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
                    "JOIN patient pt ON rx.patient_id = pt.id \n" +
                    "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
                    "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
                    "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
                    "WHERE p.adjustment = 0.00 and p.adjustment_id IS NULL \n" +

            "UNION ALL \n" +

            "SELECT DISTINCT 5 as category_id, \n" +
            "CASE WHEN c.billing_branch_id IS NOT NULL \n" +
            "THEN (REPLACE((SELECT x.account FROM gl_account x WHERE x.type = 'payment_type' AND x.name = p.payment_type), '&b&', COALESCE(b.code, '000'))) \n" +
            "WHEN c.billing_branch_id IS NULL AND pt.primary_branch_id IS NOT NULL \n" +
            "THEN (REPLACE((SELECT x.account FROM gl_account x WHERE x.type = 'payment_type' AND x.name = p.payment_type), '&b&', COALESCE(bp.code, '000'))) \n" +
            "ELSE REPLACE((SELECT x.account FROM gl_account x WHERE x.type = 'payment_type' AND x.name = p.payment_type), '&b&', '00') \n" +
            "END as gl_account , \n" +
            "'Cash' as category, p.payment_type as sub_category, p.patient_id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , null as facility_id, CASE WHEN p.prescription_id IS NULL THEN c.prescription_id ELSE p.prescription_id END as prescription_id, \n" +
            "p.claim_id as claim_id, null as plc_id, null as l_code_id, null as ivlc_id, p.id as payment_id, null as ap_id, null as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, null as insurance_company_id, null as carrier_type, null as patient_insurance, null as insurance_verification_id, null as device_type_id, null as device_type, \n" +
            "null as treating_practitioner_id, null as primary_care_physician_id, null as therapist_id, null as referring_physician_id, null as claim_submission_date, \n" +
            "null as date_of_service, null as prescription_date, p.date as payment_date, p.deposit_date as deposit_date, null as applied_date, p.date as gl_date, p.date as gl_applied_date, \n" +
            "COALESCE(p.amount, 0.00) as amount, ABS(COALESCE(p.amount, 0.00)) as abs_amount, null as rx_active, null as pt_active \n" +
            "FROM payment p \n" +
            "LEFT JOIN claim c ON p.claim_id = c.id \n" +
            "LEFT JOIN patient pt ON p.patient_id = pt.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "LEFT JOIN branch bp ON bp.id = pt.primary_branch_id\n" +
            "LEFT JOIN prescription rx ON c.prescription_id = rx.id\n" +
            "WHERE p.adjustment = 0.00 and p.adjustment_id IS NULL \n" +

            "UNION ALL \n" +

            "-- *** AR ADJUSTMENTS ***  \n" +

            "SELECT DISTINCT 6 as category_id, REPLACE(REPLACE((SELECT x.gl_account FROM adjustment x WHERE x.name = adj.name AND x.id = aplc.adjustment_type)  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Adjustments' as category, adj.name as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id,  dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "CASE WHEN adj.operation = '+' THEN COALESCE(aplc.adjustment, 0.00) * -1 ELSE COALESCE(aplc.adjustment, 0.00) END as amount, ABS(COALESCE(aplc.adjustment, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN adjustment adj ON aplc.adjustment_type = adj.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment <> 0.00 AND aplc.adjustment IS NOT NULL \n" +

            "UNION ALL \n" +

            "-- *** LINE ADJUSTMENTS *** -- \n" +
            "-- CO-45 ESTIMATION \n" +
            "SELECT DISTINCT 7 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Line Adjustments' as category, 'CO-45 Estimate' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id, \n" +
            "primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type, \n" +
            "null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date, \n" +
            "(COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) as amount, ABS((COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00))) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM insurance_verification_l_code ivlc \n" +
            "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
            "JOIN prescription rx ON rx.id = iv.prescription_id \n" +
            "JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id \n" +
            "JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id \n" +
            "AND pin.id = ( \n" +
                    "SELECT iv1.patient_insurance_id FROM insurance_verification iv1 \n" +
            "WHERE iv1.prescription_id = rx.id \n" +
            "ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, \n" +
            "iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1) \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim \n" +
            "FROM claim_submission cs1 \n" +
            "INNER JOIN claim c1 ON cs1.claim_id = c1.id \n" +
            "INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id) as x ON x.id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +

            "UNION ALL \n" +

            "-- CO-45 REVERSAL \n" +
            "SELECT DISTINCT 8 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Line Adjustments' as category, 'CO-45 Reversal' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, \n" +
            "primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, x.payment_id as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type, \n" +
            "x.check_number as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "subPayment_date as payment_date, null as deposit_date, subApplied_date as applied_date, subPayment_date as gl_date, subApplied_date as gl_applied_date, \n" +
            "(COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) * -1 as amount, ABS((COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00))) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM insurance_verification_l_code ivlc \n" +
            "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
            "JOIN prescription rx ON rx.id = iv.prescription_id \n" +
            "JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id \n" +
            "JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id \n" +
            "AND pin.id = ( \n" +
            "SELECT iv1.patient_insurance_id FROM insurance_verification iv1 \n" +
            "WHERE iv1.prescription_id = rx.id \n" +
            "ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, \n" +
            "iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1) \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(p.id) as payment_id, MIN(ap.applied_date) as subApplied_date, MIN(p.date) as subPayment_date, MAX(p.check_number) as check_number \n" +
            "FROM payment p \n" +
            "INNER JOIN applied_payment ap ON p.id = ap.payment_id \n" +
            "INNER JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id \n" +
            "INNER JOIN claim c ON ap.claim_id = c.id \n" +
            "INNER JOIN prescription rx1 ON c.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id  ) as x ON x.id = rx.id \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim \n" +
            "FROM claim_submission cs1 \n" +
            "INNER JOIN claim c1 ON cs1.claim_id = c1.id \n" +
            "INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id) as a ON a.id = rx.id\n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN applied_payment_l_code aplc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "UNION ALL \n" +

            "-- All Other Line Adjustments \n" +

            "SELECT DISTINCT 9 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Line Adjustments' as category,  aplc.adjustment_type1 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount1, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount1, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount1 <> 0.00 AND aplc.adjustment_amount1 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type1 NOT LIKE 'PR-%' \n" +

            "UNION ALL \n" +

            "SELECT DISTINCT 10 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Line Adjustments' as category,  aplc.adjustment_type2 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount2, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount2, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount2 <> 0.00 AND aplc.adjustment_amount2 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type2 NOT LIKE 'PR-%' \n" +

            "UNION ALL \n" +

            "SELECT DISTINCT 11 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Line Adjustments' as category,  aplc.adjustment_type3 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount3, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount3, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount3 <> 0.00 AND aplc.adjustment_amount3 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type3 NOT LIKE 'PR-%' \n" +

            "UNION ALL \n" +

            "SELECT DISTINCT 12 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Line Adjustments' as category,  aplc.adjustment_type4 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount4, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount4, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount4 <> 0.00 AND aplc.adjustment_amount4 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type4 NOT LIKE 'PR-%' \n" +

            "UNION ALL \n" +

            "SELECT DISTINCT 13 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account, \n" +
            "'Line Adjustments' as category,  aplc.adjustment_type5 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount5, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount5, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount5 <> 0.00 AND aplc.adjustment_amount5 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type5 NOT LIKE 'PR-%' \n" +

            "UNION ALL \n" +

            "-- ******************************************************************* ACCOUNTS RECIEVABLE*** **************************************************************** \n" +
            "-- SALES AR \n" +
            "SELECT DISTINCT 14 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000') ) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Sales' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id, \n" +
            "primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type, \n" +
            "null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date, \n" +
            "c.date_of_service as date_of_service, rx.prescription_date, null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date, \n" +
            "COALESCE(ivlc.total_charge, 0.00) as amount , ABS(COALESCE(ivlc.total_charge, 0.00)) as abs_amount , \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM insurance_verification_l_code ivlc \n" +
            "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
            "JOIN prescription rx ON rx.id = iv.prescription_id \n" +
            "JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id \n" +
            "JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id \n" +
            "AND pin.id = ( \n" +
                    "SELECT iv1.patient_insurance_id \n" +
                    "FROM insurance_verification iv1 \n" +
            "WHERE iv1.prescription_id = rx.id \n" +
            "ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, \n" +
            "iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1) \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim \n" +
            "FROM claim_submission cs1 \n" +
            "INNER JOIN claim c1 ON cs1.claim_id = c1.id \n" +
            "INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id) as x ON x.id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +

            "UNION ALL \n" +

            "-- Sales AR \n" +
            "SELECT DISTINCT 15 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000') ) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Sales' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id, \n" +
            "primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type, \n" +
            "null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date, \n" +
            "c.date_of_service as date_of_service, rx.prescription_date, null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date, \n" +
            "COALESCE(ivlc.sales_tax, 0.00) as amount, ABS(COALESCE(ivlc.sales_tax, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM insurance_verification_l_code ivlc \n" +
            "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
            "JOIN prescription rx ON rx.id = iv.prescription_id \n" +
            "JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id \n" +
            "JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id \n" +
            "AND pin.id = ( \n" +
                    "SELECT iv1.patient_insurance_id \n" +
                    "FROM insurance_verification iv1 \n" +
            "WHERE iv1.prescription_id = rx.id \n" +
            "ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, \n" +
            "iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1) \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim \n" +
            "FROM claim_submission cs1 \n" +
            "INNER JOIN claim c1 ON cs1.claim_id = c1.id \n" +
            "INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id) as x ON x.id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE ivlc.use_sales_tax = 1 and ivlc.sales_tax IS NOT NULL \n" +

            "UNION ALL \n" +

            "-- Applied Payments AR --- \n" +
            "SELECT DISTINCT 16 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'applied_payments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Applied Payments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id,  rx.id as prescription_id, \n" +
            "c.id as claim_id, plc.id as plc_id, plc.l_code_id, null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, \n" +
            "c.date_of_service as date_of_service, rx.prescription_date, p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.amount, 0.00) * -1 as amount, ABS(COALESCE(aplc.amount, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
                    "JOIN prescription rx ON c.prescription_id = rx.id \n" +
                    "JOIN patient pt ON rx.patient_id = pt.id \n" +
                    "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id \n" +
            "WHERE aplc.amount <> 0.00 AND p.date IS NOT NULL \n" +

            "UNION ALL \n" +

            "-- *** AR ADJUSTMENTS AR*** \n" +

            "SELECT DISTINCT 17 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "CASE WHEN adj.operation = '+' THEN COALESCE(aplc.adjustment, 0.00) ELSE COALESCE(aplc.adjustment, 0.00) * -1 END as amount, ABS(COALESCE(aplc.adjustment, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN adjustment adj ON aplc.adjustment_type = adj.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment <> 0.00 AND aplc.adjustment IS NOT NULL \n" +
            "AND p.date IS NOT NULL \n" +

            "UNION ALL \n" +

            "-- CO-45 ESTIMATION \n" +
            "SELECT DISTINCT 18 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, \n" +
            "primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type, \n" +
            "null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date, \n" +
            "(COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) * -1 as amount, ABS(COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM insurance_verification_l_code ivlc \n" +
            "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
            "JOIN prescription rx ON rx.id = iv.prescription_id \n" +
            "JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id \n" +
            "JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id \n" +
            "AND pin.id = ( \n" +
                    "SELECT iv1.patient_insurance_id FROM insurance_verification iv1 \n" +
            "WHERE iv1.prescription_id = rx.id \n" +
            "ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, \n" +
            "iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1) \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim \n" +
            "FROM claim_submission cs1 \n" +
            "INNER JOIN claim c1 ON cs1.claim_id = c1.id \n" +
            "INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id) as x ON x.id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +

            "UNION ALL \n" +

            "-- CO-45 REVERSAL \n" +
            "SELECT DISTINCT 19 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, \n" +
            "primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, x.payment_id as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type, \n" +
            "x.check_number as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "subPayment_date as payment_date, null as deposit_date, subApplied_date as applied_date, subPayment_date as gl_date, subApplied_date as gl_applied_date, \n" +
            "(COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) as amount, ABS((COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00))) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM insurance_verification_l_code ivlc \n" +
            "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
            "JOIN prescription rx ON rx.id = iv.prescription_id \n" +
            "JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id \n" +
            "JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id \n" +
            "AND pin.id = ( \n" +
            "SELECT iv1.patient_insurance_id FROM insurance_verification iv1 \n" +
            "WHERE iv1.prescription_id = rx.id \n" +
            "ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, \n" +
            "iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1) \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(p.id) as payment_id, MIN(ap.applied_date) as subApplied_date, MIN(p.date) as subPayment_date, MAX(p.check_number) as check_number  \n" +
            "FROM payment p \n" +
            "INNER JOIN applied_payment ap ON p.id = ap.payment_id \n" +
            "INNER JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id \n" +
            "INNER JOIN claim c ON ap.claim_id = c.id \n" +
            "INNER JOIN prescription rx1 ON c.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id  ) as x ON x.id = rx.id \n" +
            "LEFT JOIN( \n" +
            "SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim \n" +
            "FROM claim_submission cs1 \n" +
            "INNER JOIN claim c1 ON cs1.claim_id = c1.id \n" +
            "INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id \n" +
            "GROUP BY rx1.id) as a ON a.id = rx.id\n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN applied_payment_l_code aplc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +

            "UNION ALL \n" +
            "-- **** LINE ADJUSTMENTS **** \n" +

            "SELECT DISTINCT 20 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount1, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount1, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount1 <> 0.00 AND aplc.adjustment_amount1 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type1 NOT LIKE 'PR-%' AND p.date IS NOT NULL \n" +

            "UNION ALL \n" +

            "SELECT DISTINCT 21 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount2, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount2, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount2 <> 0.00 AND aplc.adjustment_amount2 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type2 NOT LIKE 'PR-%' AND p.date IS NOT NULL \n" +

            "UNION ALL \n" +

            "SELECT DISTINCT 22 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount3, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount3, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount3 <> 0.00 AND aplc.adjustment_amount3 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type3 NOT LIKE 'PR-%'  AND p.date IS NOT NULL \n" +

            "UNION ALL \n" +

            "SELECT DISTINCT 23 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, \n" +
            "p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount4, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount4, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount4 <> 0.00 AND aplc.adjustment_amount4 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type4 NOT LIKE 'PR-%' AND p.date IS NOT NULL \n" +

            "UNION ALL \n" +

            "SELECT DISTINCT 24 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account, \n" +
            "'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id, \n" +
            "null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type, \n" +
            "p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type, \n" +
            "rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date, \n" +
            "p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, p.date as gl_date, ap.applied_date as gl_applied_date, \n" +
            "COALESCE(aplc.adjustment_amount5, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount5, 0.00)) as abs_amount, \n" +
            "rx.active as rx_active, pt.active as pt_active \n" +
            "FROM applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id = ap.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN claim c ON ap.claim_id = c.id \n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN patient_insurance pin ON c.patient_insurance_id = pin.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id \n" +
            "LEFT JOIN branch b ON c.billing_branch_id = b.id\n" +
            "WHERE aplc.adjustment_amount5 <> 0.00 AND aplc.adjustment_amount5 IS NOT NULL \n" +
            "AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date) \n" +
            "OR (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0 \n" +
            "AND aplc.adjustment_type5 NOT LIKE 'PR-%' AND p.date IS NOT NULL  \n" +
            ") as x";

    public static final String multitenantPreparedStatment = "SELECT gl.category_id as categoryId, gl.gl_date as glDate, gl.gl_applied_date as glAppliedDate, gl.gl_year as year, gl.gl_period as period, \n" +
            "            gl.gl_account as glAccount, gl.category as category, gl.sub_category as subCategory, gl.amount as amount, gl.abs_amount as absAmount, gl.patient_id as patientId, \n" +
            "            pt.first_name as ptFirstName, pt.middle_name as ptMiddleName, pt.last_name as ptLastName, gl.prescription_id as prescriptionId, \n" +
            "            gl.claim_id as claimId, gl.branch_id as branchId, b.name as branch, gl.patient_branch_id as patientBranchId, pb.name as patientBranch, gl.prescription_branch_id as prescriptionBranchId, rxb.name as prescriptionBranch, gl.facility_id as facilityId, f.name as facility, gl.prescription_l_code_id as prescriptionLCodeId, \n" +
            "            gl.l_code_id as lCodeId, lc.name as lCode, gl.insurance_verification_id as insuranceVerificationId, gl.insurance_verification_l_code_id as insuranceVerificationLCodeId, gl.payment_id as paymentId, \n" +
            "            gl.applied_payment_id as appliedPaymentId, gl.applied_payment_l_code_id as appliedPaymentLCodeId, gl.payer_type as payerType, gl.payment_type as paymentType, p.check_number as checkNumber, gl.insurance_company_id as insuranceCompanyId, \n" +
            "            ic.name as insuranceCompany, gl.carrier_type as carrierType, gl.patient_insurance_id as patientInsuranceId, gl.device_type_id as deviceTypeId, \n" +
            "            gl.device_type as deviceTypeCategory, dt.name as deviceType, gl.treating_practitioner_id  as treatingPractitionerId, \n" +
            "            tp.first_name as tpFirstName, tp.middle_name as tpMiddleName, tp.last_name as tpLastName, tp.credentials as tpCredentials, \n" +
            "            gl.primary_care_physician_id as primaryCarePhysicianId, pc.first_name as pcFirstName, pc.middle_name as pcMiddleName, pc.last_name as pcLastName, pc.credentials as pcCredentials, \n" +
            "            gl.therapist_id as therapistId, th.first_name as thFirstName, th.middle_name as thMiddleName, th.last_name as thLastName, th.credentials as thCredentials, \n" +
            "            gl.referring_physician_id as referringPhysicianId, rp.first_name as rpFirstName, rp.middle_name as rpMiddleName, rp.last_name as rpLastName, rp.credentials as rpCredentials, \n" +
            "            gl.claim_submission_date as claimSubmissionDate, gl.date_of_service as dateOfService, gl.prescription_date as prescriptionDate, gl.payment_date as paymentDate, gl.deposit_date as depositDate, gl.applied_date as appliedDate, \n" +
            "            CASE WHEN (gl.rx_active = 1) THEN 'true' ELSE 'false' END as rxActive, \n" +
            "            CASE WHEN (gl.patient_active = 1) THEN 'true' ELSE 'false' END as ptActive, gl.last_updated as lastUpdated \n" +
            "            FROM general_ledger gl \n" +
            "            LEFT JOIN patient pt ON gl.patient_id = pt.id \n" +
            "            LEFT JOIN branch b ON gl.branch_id = b.id \n" +
            "            LEFT JOIN branch pb ON gl.patient_branch_id = pb.id \n" +
            "            LEFT JOIN branch rxb ON gl.prescription_branch_id = rxb.id \n" +
            "            LEFT JOIN facility f ON gl.facility_id = f.id \n" +
            "            LEFT JOIN insurance_company ic ON gl.insurance_company_id = ic.id \n" +
            "            LEFT JOIN device_type dt ON gl.device_type_id = dt.id \n" +
            "            LEFT JOIN nymbl_master.user tp ON gl.treating_practitioner_id = tp.id \n" +
            "            LEFT JOIN physician pc ON gl.primary_care_physician_id = pc.id \n" +
            "            LEFT JOIN therapist th ON gl.therapist_id = th.id \n" +
            "            LEFT JOIN physician rp ON gl.referring_physician_id = rp.id \n" +
            "            LEFT JOIN l_code lc ON gl.l_code_id = lc.id\n" +
            "            LEFT JOIN gl_period glp ON gl.gl_year = glp.year AND gl.gl_period = glp.period\n" +
            "            LEFT JOIN payment p ON gl.payment_id = p.id\n" +
            "            WHERE ((glp.status = 'open') OR (ISNULL(gl.gl_year) AND ISNULL(gl.gl_period)))\n" +
            "            \n" +
            "            UNION ALL\n" +
            "            \n" +
            "            SELECT gls.category_id as categoryId, gls.gl_date as glDate, gls.gl_applied_date as glAppliedDate, gls.gl_year as year, gls.gl_period as period, \n" +
            "            gls.gl_account as glAccount, gls.category as category, gls.sub_category as subCategory, gls.amount as amount, gls.abs_amount as absAmount, gls.patient_id as patientId, \n" +
            "            pt.first_name as ptFirstName, pt.middle_name as ptMiddleName, pt.last_name as ptLastName, gls.prescription_id as prescriptionId, \n" +
            "            gls.claim_id as claimId, gls.branch_id as branchId, b.name as branch, gls.patient_branch_id as patientBranchId, pb.name as patientBranch, gls.prescription_branch_id as prescriptionBranchId, rxb.name as prescriptionBranch, gls.facility_id as facilityId, f.name as facility, gls.prescription_l_code_id as prescriptionLCodeId, \n" +
            "            gls.l_code_id as lCodeId, lc.name as lCode, gls.insurance_verification_id as insuranceVerificationId, gls.insurance_verification_l_code_id as insuranceVerificationLCodeId, gls.payment_id as paymentId, \n" +
            "            gls.applied_payment_id as appliedPaymentId, gls.applied_payment_l_code_id as appliedPaymentLCodeId, gls.payer_type as payerType, gls.payment_type as paymentType, p.check_number as checkNumber, gls.insurance_company_id as insuranceCompanyId, \n" +
            "            ic.name as insuranceCompany, gls.carrier_type as carrierType, gls.patient_insurance_id as patientInsuranceId, gls.device_type_id as deviceTypeId, \n" +
            "            gls.device_type as deviceTypeCategory, dt.name as deviceType, gls.treating_practitioner_id  as treatingPractitionerId, \n" +
            "            tp.first_name as tpFirstName, tp.middle_name as tpMiddleName, tp.last_name as tpLastName, tp.credentials as tpCredentials, \n" +
            "            gls.primary_care_physician_id as primaryCarePhysicianId, pc.first_name as pcFirstName, pc.middle_name as pcMiddleName, pc.last_name as pcLastName, pc.credentials as pcCredentials, \n" +
            "            gls.therapist_id as therapistId, th.first_name as thFirstName, th.middle_name as thMiddleName, th.last_name as thLastName, th.credentials as thCredentials, \n" +
            "            gls.referring_physician_id as referringPhysicianId, rp.first_name as rpFirstName, rp.middle_name as rpMiddleName, rp.last_name as rpLastName, rp.credentials as rpCredentials, \n" +
            "            gls.claim_submission_date as claimSubmissionDate, gls.date_of_service as dateOfService, gls.prescription_date as prescriptionDate, gls.payment_date as paymentDate, gls.deposit_date as depositDate, gls.applied_date as appliedDate, \n" +
            "            CASE WHEN (gls.rx_active = 1) THEN 'true' ELSE 'false' END as rxActive, \n" +
            "            CASE WHEN (gls.patient_active = 1) THEN 'true' ELSE 'false' END as ptActive, gls.last_updated as lastUpdated \n" +
            "            FROM general_ledger_static gls\n" +
            "            LEFT JOIN patient pt ON gls.patient_id = pt.id \n" +
            "            LEFT JOIN branch b ON gls.branch_id = b.id \n" +
            "            LEFT JOIN branch pb ON gls.patient_branch_id = pb.id \n" +
            "            LEFT JOIN branch rxb ON gls.prescription_branch_id = rxb.id \n" +
            "            LEFT JOIN facility f ON gls.facility_id = f.id \n" +
            "            LEFT JOIN insurance_company ic ON gls.insurance_company_id = ic.id \n" +
            "            LEFT JOIN device_type dt ON gls.device_type_id = dt.id \n" +
            "            LEFT JOIN nymbl_master.user tp ON gls.treating_practitioner_id = tp.id \n" +
            "            LEFT JOIN physician pc ON gls.primary_care_physician_id = pc.id \n" +
            "            LEFT JOIN therapist th ON gls.therapist_id = th.id \n" +
            "            LEFT JOIN physician rp ON gls.referring_physician_id = rp.id \n" +
            "            LEFT JOIN l_code lc ON gls.l_code_id = lc.id\n" +
            "            LEFT JOIN payment p ON gls.payment_id = p.id\n" +
            "            WHERE gls.active = 1";


    public static final String populateStaticGeneralLedgerByPeriod = "INSERT IGNORE INTO general_ledger_static \n" +
            "(category_id, gl_date, gl_applied_date, gl_year, gl_period, gl_account, category, sub_category, amount, abs_amount, \n" +
            "patient_id, prescription_id, claim_id, branch_id, patient_branch_id, prescription_branch_id, facility_id, prescription_l_code_id, l_code_id, insurance_verification_id, \n" +
            "insurance_verification_l_code_id, payment_id, applied_payment_id, applied_payment_l_code_id, payer_type, \n" +
            "payment_type, check_number, insurance_company_id, carrier_type, patient_insurance_id, device_type_id, device_type, treating_practitioner_id, \n" +
            "primary_care_physician_id, therapist_id, referring_physician_id, claim_submission_date, date_of_service, prescription_date, \n" +
            "payment_date, deposit_date, applied_date, rx_active, patient_active, last_updated, active) \n" +
            "SELECT \n" +
            "gl.category_id, gl.gl_date, gl.gl_applied_date, gl.gl_year, gl.gl_period, gl.gl_account, gl.category, gl.sub_category, \n" +
            "gl.amount, gl.abs_amount, gl.patient_id, gl.prescription_id, gl.claim_id, gl.branch_id, gl.patient_branch_id, gl.prescription_branch_id, gl.facility_id, gl.prescription_l_code_id, \n" +
            "gl.l_code_id, gl.insurance_verification_id, gl.insurance_verification_l_code_id, gl.payment_id, gl.applied_payment_id, \n" +
            "gl.applied_payment_l_code_id, gl.payer_type, gl.payment_type, gl.check_number, gl.insurance_company_id, gl.carrier_type, gl.patient_insurance_id, \n" +
            "gl.device_type_id, gl.device_type, gl.treating_practitioner_id, gl.primary_care_physician_id, gl.therapist_id, gl.referring_physician_id, \n" +
            "gl.claim_submission_date, gl.date_of_service, gl.prescription_date, gl.payment_date, gl.deposit_date, gl.applied_date, \n" +
            "gl.rx_active, gl.patient_active, gl.last_updated, true \n" +
            "FROM general_ledger gl \n" +
            "WHERE gl.gl_year = :year \n" +
            "AND gl.gl_period = :period";

    public static final String setAllStaticGeneralLedgerTransactionInactiveByPeriod = "UPDATE general_ledger_static \n" +
            "SET active = false \n" +
            "WHERE gl_year = :year \n" +
            "AND gl_period = :period";
    
    public static final String zeroDollarPayments = "SELECT \n" +
            "    p.id AS paymentId,\n" +
            "    p.date AS paymentDate,\n" +
            "    p.payment_type AS paymentType,\n" +
            "    p.description AS paymentDescription,\n" +
            "    p.amount AS paymentAmount,\n" +
            "    p.unapplied_amount as paymentUnappliedAmount,\n" +
            "    p.check_number AS checkNumber,\n" +
            "    p.created_at AS createdAt,\n" +
            "    p.deposit_date AS paymentDepositDate,\n" +
            "    dt.orthotic_or_prosthetic AS deviceType,\n" +
            "    dt.name AS deviceName,\n" +
            "    cbu.first_name AS createdByFirstName,\n" +
            "    cbu.middle_name AS createdByMiddleName,\n" +
            "    cbu.last_name AS createdByLastName,\n" +
            "    c.id AS claimId,\n" +
            "    c.date_of_service AS dateOfService,\n" +
            "    cs.submission_date AS submissionDate,\n" +
            "    c.date_resolved AS claimResolvedDate,\n" +
            "    cns.name AS claimStatus,\n" +
            "    pt.id AS patientId,\n" +
            "    pt.first_name AS patientFirstName,\n" +
            "    pt.middle_name AS patientMiddleName,\n" +
            "    pt.last_name AS patientLastName,\n" +
            "    pb.name AS patientBranchName,\n" +
            "    rx.id AS rxId,\n" +
            "    ap.amount_applied AS appliedAmount,\n" +
            "    ap.applied_date as appliedDate,\n" +
            "    ic.name AS insuranceCompanyName,\n" +
            "    (SELECT \n" +
            "            iv1.carrier_type\n" +
            "        FROM\n" +
            "            insurance_verification iv1\n" +
            "                JOIN\n" +
            "            patient_insurance pin1 ON pin1.id = iv1.patient_insurance_id\n" +
            "                JOIN\n" +
            "            insurance_company ic1 ON ic1.id = pin1.insurance_company_id\n" +
            "        WHERE\n" +
            "            ic1.id = ic.id\n" +
            "                AND iv1.prescription_id = rx.id\n" +
            "        LIMIT 1) AS carrierType,\n" +
            "    COALESCE(ic.name, 'Self Pay') AS payer,\n" +
            "    rx.primary_driver AS primaryDriver,\n" +
            "    tp.first_name AS treatingPractitionerFirstName,\n" +
            "    tp.middle_name AS treatingPractitionerMiddleName,\n" +
            "    tp.last_name AS treatingPractitionerLastName,\n" +
            "    th.first_name AS therapistFirstName,\n" +
            "    th.middle_name AS therapistMiddleName,\n" +
            "    th.last_name AS therapistLastName,\n" +
            "    pu.first_name AS appliedByFirstName,\n" +
            "    pu.middle_name AS appliedByMiddleName,\n" +
            "    pu.last_name AS appliedByLastName,\n" +
            "    pcp.first_name AS primaryCarePhysicianFirstName,\n" +
            "    pcp.middle_name AS primaryCarePhysicianMiddleName,\n" +
            "    pcp.last_name AS primaryCarePhysicianLastName,\n" +
            "    rp.first_name AS referringPhysicianFirstName,\n" +
            "    rp.middle_name AS referringPhysicianMiddleName,\n" +
            "    rp.last_name AS referringPhysicianLastName,\n" +
            "    vu.first_name AS viewUserFirstName,\n" +
            "    vu.middle_name AS viewUserMiddleName,\n" +
            "    vu.last_name AS viewUserLastName,\n" +
            "    f.name AS facility\n" +
            "FROM\n" +
            "    payment p\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user cbu ON p.created_by_id = cbu.id\n" +
            "        JOIN\n" +
            "    patient pt ON p.patient_id = pt.id\n" +
            "        LEFT JOIN\n" +
            "    applied_payment ap ON p.id = ap.payment_id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user abu ON p.created_by_id = abu.id\n" +
            "        LEFT JOIN\n" +
            "    branch pb ON pt.primary_branch_id = pb.id\n" +
            "        LEFT JOIN\n" +
            "    prescription rx ON rx.id = p.prescription_id\n" +
            "        LEFT JOIN\n" +
            "    branch rxb ON rx.branch_id = rxb.id\n" +
            "        LEFT JOIN\n" +
            "    claim c ON c.id = ap.claim_id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_status cns ON cns.id = c.nymbl_status_id\n" +
            "        LEFT JOIN\n" +
            "    claim_submission cs ON cs.claim_id = c.id\n" +
            "        LEFT JOIN\n" +
            "    device_type dt ON dt.id = rx.device_type_id\n" +
            "        LEFT JOIN\n" +
            "    insurance_company ic ON ic.id = p.insurance_company_id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user u ON u.id = p.created_by_id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user pu ON pu.id = ap.applied_by\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user tp ON tp.id = rx.treating_practitioner_id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user vu ON vu.id = rx.view_user_id\n" +
            "        LEFT JOIN\n" +
            "    physician rp ON rp.id = rx.referring_physician_id\n" +
            "        LEFT JOIN\n" +
            "    physician pcp ON pcp.id = rx.primary_care_physician_id\n" +
            "        LEFT JOIN\n" +
            "    therapist th ON th.id = rx.therapist_id\n" +
            "        LEFT JOIN\n" +
            "    facility f ON rx.facility_id = f.id\n" +
            "WHERE\n" +
            "    p.amount = 0.00 AND p.adjustment = 0.00\n" +
            "        AND p.created_at BETWEEN :startDate AND :endDate\n" +
            "        AND (:branchId IS NULL OR :branchId = 0\n" +
            "        OR pt.primary_branch_id = :branchId)\n" +
            "ORDER BY p.created_at";

  public static final String claimsActivityReport =
          "WITH LastComments AS (\n" +
                  "    -- Compute the latest comment per claim, matching the original subquery logic\n" +
                  "    SELECT nt.claim_id, \n" +
                  "           nt.note AS lastComment, \n" +
                  "           nt.created_at AS lastCommentDate, \n" +
                  "           CONCAT(nu.first_name, ' ', nu.last_name) AS lastCommenter,\n" +
                  "           ROW_NUMBER() OVER (PARTITION BY nt.claim_id ORDER BY nt.created_at DESC) AS rn\n" +
                  "    FROM note nt\n" +
                  "    LEFT JOIN nymbl_master.user nu ON nu.id = nt.created_by_id\n" +
                  "    WHERE nt.note_type = 'claim_comments'\n" +
                  "),\n" +
                  "LastPayments AS (\n" +
                  "    -- Compute the latest payment per claim, matching the original subquery logic\n" +
                  "    SELECT ap.claim_id, \n" +
                  "           ap.applied_date AS lastPayment,\n" +
                  "           CASE \n" +
                  "               WHEN com.name IS NULL AND c.total_pt_responsibility_paid > 0 THEN 'Patient'\n" +
                  "               ELSE com.name \n" +
                  "           END AS lastPayerName,\n" +
                  "           ROW_NUMBER() OVER (PARTITION BY ap.claim_id ORDER BY ap.id DESC) AS rn\n" +
                  "    FROM applied_payment ap\n" +
                  "    JOIN payment p ON ap.payment_id = p.id\n" +
                  "    JOIN claim c ON ap.claim_id = c.id\n" +
                  "    LEFT JOIN insurance_company com ON p.insurance_company_id = com.id\n" +
                  "    WHERE ap.amount_applied > 0\n" +
                  ")\n" +
                  "SELECT DISTINCT\n" +
                  "    rx.id AS prescriptionId,\n" +
                  "    rxBranch.name AS prescriptionBranch,\n" +
                  "    cb.name AS billingBranch,\n" +
                  "    ptBranch.name AS patientBranch,\n" +
                  "    pt.id AS patientId,\n" +
                  "    pt.first_name AS patientFirstName,\n" +
                  "    pt.last_name AS patientLastName,\n" +
                  "    COALESCE(pt.state, '') AS state,\n" +
                  "    pt.dob AS dateOfBirth,\n" +
                  "    creator.first_name AS createdByFirstName,\n" +
                  "    creator.last_name AS createdByLastName,\n" +
                  "    rx.created_at AS createdDate,\n" +
                  "    assigned.first_name AS assignedToPrescriptionFirstName,\n" +
                  "    assigned.last_name AS assignedToPrescriptionLastName,\n" +
                  "    cu.first_name AS assignedFirstName,\n" +
                  "    cu.last_name AS assignedLastName,\n" +
                  "    COALESCE(tp.first_name, '') AS practitionerFirstName,\n" +
                  "    COALESCE(tp.middle_name, '') AS practitionerMiddleName,\n" +
                  "    COALESCE(tp.last_name, '') AS practitionerLastName,\n" +
                  "    c.id AS claimId,\n" +
                  "    cns.name AS claimStatus,\n" +
                  "    c.date_of_service AS dateOfService,\n" +
                  "    dt.orthotic_or_prosthetic AS deviceType,\n" +
                  "    dt.name AS deviceName,\n" +
                  "    COALESCE(cd.lcName, '') AS firstHcpc,\n" +
                  "    COALESCE(cd.dcCode, '') AS firstDiagnosisCode,\n" +
                  "    t.sales AS primaryBillableAmount,\n" +
                  "    t.allowable AS primaryAllowableAmount,\n" +
                  "    CASE \n" +
                  "        WHEN t.balance > 0 AND c.total_pt_responsibility_balance = 0 \n" +
                  "        THEN t.balance \n" +
                  "        ELSE t.balance - c.total_pt_responsibility_balance \n" +
                  "    END AS insuranceBalance,\n" +
                  "    CASE \n" +
                  "        WHEN c.total_pt_responsibility_balance <> 0 \n" +
                  "        THEN c.total_pt_responsibility_balance \n" +
                  "        ELSE 0.00 \n" +
                  "    END AS patientBalance,\n" +
                  "    t.balance AS totalBalance,\n" +
                  "    icResponse.name AS responsibleInsurance,\n" +
                  "    CASE \n" +
                  "        WHEN t.balance <> 0 \n" +
                  "        THEN DATEDIFF(CURRENT_DATE, primaryData.submissionDate) \n" +
                  "        ELSE 0 \n" +
                  "    END AS aging,\n" +
                  "    primaryData.primaryInsurance,\n" +
                  "    COALESCE(primaryData.timelyFiling, 0) AS primaryTimelyFiling,\n" +
                  "    primaryData.insuranceNumber AS primaryInsuranceNumber,\n" +
                  "    primaryData.insurancePercentage AS primaryInsurancePercentage,\n" +
                  "    primaryData.submissionDate AS primarySubmissionDate,\n" +
                  "    primaryData.billerCode AS primaryBillerCode,\n" +
                  "    secondaryData.secondaryInsurance,\n" +
                  "    COALESCE(secondaryData.timelyFiling, 0) AS secondaryTimelyFiling,\n" +
                  "    secondaryData.insuranceNumber AS secondaryInsuranceNumber,\n" +
                  "    secondaryData.insurancePercentage AS secondaryInsurancePercentage,\n" +
                  "    secondaryData.submissionDate AS secondarySubmissionDate,\n" +
                  "    secondaryData.billerCode AS secondaryBillerCode,\n" +
                  "    tertData.tertInsurance AS tertiaryInsurance,\n" +
                  "    COALESCE(tertData.timelyFiling, 0) AS tertiaryTimelyFiling,\n" +
                  "    tertData.insuranceNumber AS tertiaryInsuranceNumber,\n" +
                  "    tertData.insurancePercentage AS tertiaryInsurancePercentage,\n" +
                  "    tertData.submissionDate AS tertSubmissionDate,\n" +
                  "    tertData.billerCode AS tertBillerCode,\n" +
                  "    t.primaryApplied AS primaryAppliedPayments,\n" +
                  "    t.primaryAdjustment AS primaryAdjustments,\n" +
                  "    t.secondaryApplied AS secondaryAppliedPayments,\n" +
                  "    t.secondaryAdjustment AS secondaryAdjustments,\n" +
                  "    t.tertiaryApplied AS tertiaryAppliedPayments,\n" +
                  "    t.tertiaryAdjustment AS tertiaryAdjustments,\n" +
                  "    t.otherApplied AS otherAppliedPayments,\n" +
                  "    t.otherAdjustment AS otherAdjustments,\n" +
                  "    t.patientPaid AS totalPatientPaid,\n" +
                  "    t.insurancePaid AS totalInsurancePaid,\n" +
                  "    t.adjustment AS totalAdjustments,\n" +
                  "    n.unAppliedPayment AS unAppliedPayment,\n" +
                  "    n.unAppliedAdjustment AS unAppliedAdjustment,\n" +
                  "    (t.insurancePaid + t.patientPaid) AS totalPaymentAmount,\n" +
                  "    lc.lastComment,\n" +
                  "    lc.lastCommentDate,\n" +
                  "    lc.lastCommenter,\n" +
                  "    lp.lastPayment,\n" +
                  "    lp.lastPayerName\n" +
                  "FROM prescription rx\n" +
                  "JOIN patient pt ON pt.id = rx.patient_id\n" +
                  "LEFT JOIN (\n" +
                  "    SELECT iv1.prescription_id AS rxId1, \n" +
                  "           c1.id AS primaryClaimId, \n" +
                  "           ic1.name AS primaryInsurance, \n" +
                  "           ic1.timely_filing_days AS timelyFiling, \n" +
                  "           ic1.biller_code AS billerCode, \n" +
                  "           pin1.insurance_number AS insuranceNumber, \n" +
                  "           iv1.benefits_payable AS insurancePercentage, \n" +
                  "           iv1.patient_insurance_id, \n" +
                  "           MIN(cs.submission_date) AS submissionDate\n" +
                  "    FROM insurance_verification iv1\n" +
                  "    JOIN patient_insurance pin1 ON iv1.patient_insurance_id = pin1.id\n" +
                  "    JOIN insurance_company ic1 ON pin1.insurance_company_id = ic1.id\n" +
                  "    JOIN claim c1 ON c1.prescription_id = iv1.prescription_id\n" +
                  "    JOIN claim_submission cs ON cs.claim_id = c1.id \n" +
                  "    AND cs.patient_insurance_id = iv1.patient_insurance_id\n" +
                  "    WHERE iv1.carrier_type = 'primary'\n" +
                  "    GROUP BY iv1.prescription_id, c1.id, ic1.name, ic1.timely_filing_days, ic1.biller_code, pin1.insurance_number, iv1.benefits_payable, iv1.patient_insurance_id\n" +
                  ") AS primaryData ON rx.id = primaryData.rxId1\n" +
                  "LEFT JOIN (\n" +
                  "    SELECT iv2.prescription_id AS rxId2, \n" +
                  "           c2.id AS primaryClaimId, \n" +
                  "           ic2.name AS secondaryInsurance, \n" +
                  "           ic2.timely_filing_days AS timelyFiling, \n" +
                  "           ic2.biller_code AS billerCode, \n" +
                  "           pin2.insurance_number AS insuranceNumber, \n" +
                  "           iv2.benefits_payable AS insurancePercentage, \n" +
                  "           iv2.patient_insurance_id, \n" +
                  "           MIN(cs2.submission_date) AS submissionDate\n" +
                  "    FROM insurance_verification iv2\n" +
                  "    JOIN patient_insurance pin2 ON iv2.patient_insurance_id = pin2.id\n" +
                  "    JOIN insurance_company ic2 ON pin2.insurance_company_id = ic2.id\n" +
                  "    JOIN claim c2 ON c2.prescription_id = iv2.prescription_id\n" +
                  "    JOIN claim_submission cs2 ON cs2.claim_id = c2.id \n" +
                  "    AND cs2.patient_insurance_id = iv2.patient_insurance_id\n" +
                  "    WHERE iv2.carrier_type = 'secondary'\n" +
                  "    GROUP BY iv2.prescription_id, c2.id, ic2.name, ic2.timely_filing_days, ic2.biller_code, pin2.insurance_number, iv2.benefits_payable, iv2.patient_insurance_id\n" +
                  ") AS secondaryData ON rx.id = secondaryData.rxId2\n" +
                  "LEFT JOIN (\n" +
                  "    SELECT iv3.prescription_id AS rxId3, \n" +
                  "           c3.id AS primaryClaimId, \n" +
                  "           ic3.name AS tertInsurance, \n" +
                  "           ic3.timely_filing_days AS timelyFiling, \n" +
                  "           ic3.biller_code AS billerCode, \n" +
                  "           pin3.insurance_number AS insuranceNumber, \n" +
                  "           iv3.benefits_payable AS insurancePercentage, \n" +
                  "           iv3.patient_insurance_id, \n" +
                  "           MIN(cs3.submission_date) AS submissionDate\n" +
                  "    FROM insurance_verification iv3\n" +
                  "    JOIN patient_insurance pin3 ON iv3.patient_insurance_id = pin3.id\n" +
                  "    JOIN insurance_company ic3 ON pin3.insurance_company_id = ic3.id\n" +
                  "    JOIN claim c3 ON c3.prescription_id = iv3.prescription_id\n" +
                  "    JOIN claim_submission cs3 ON cs3.claim_id = c3.id \n" +
                  "    AND cs3.patient_insurance_id = iv3.patient_insurance_id\n" +
                  "    WHERE iv3.carrier_type = 'tertiary'\n" +
                  "    GROUP BY iv3.prescription_id, c3.id, ic3.name, ic3.timely_filing_days, ic3.biller_code, pin3.insurance_number, iv3.benefits_payable, iv3.patient_insurance_id\n" +
                  ") AS tertData ON rx.id = tertData.rxId3\n" +
                  "JOIN claim c ON primaryData.primaryClaimId = c.id\n" +
                  "LEFT JOIN (\n" +
                  "    SELECT plc.prescription_id, \n" +
                  "           lc.name AS lcName, \n" +
                  "           dc.code AS dcCode\n" +
                  "    FROM prescription_l_code plc\n" +
                  "    JOIN l_code lc ON plc.l_code_id = lc.id\n" +
                  "    JOIN diagnosis_code dc ON plc.diagnosis_code1id = dc.id\n" +
                  "    WHERE plc.order_num = 1\n" +
                  ") AS cd ON rx.id = cd.prescription_id\n" +
                  "LEFT JOIN branch cb ON c.billing_branch_id = cb.id\n" +
                  "LEFT JOIN branch rxBranch ON rx.branch_id = rxBranch.id\n" +
                  "LEFT JOIN branch ptBranch ON pt.primary_branch_id = ptBranch.id\n" +
                  "LEFT JOIN nymbl_master.user creator ON rx.created_by_id = creator.id\n" +
                  "LEFT JOIN nymbl_master.user assigned ON rx.clerical_user_id = assigned.id\n" +
                  "LEFT JOIN nymbl_master.user tp ON rx.treating_practitioner_id = tp.id\n" +
                  "LEFT JOIN nymbl_master.user cu ON c.user_id = cu.id\n" +
                  "LEFT JOIN nymbl_status cns ON cns.id = c.nymbl_status_id\n" +
                  "LEFT JOIN device_type dt ON rx.device_type_id = dt.id\n" +
                  "LEFT JOIN patient_insurance pinResponse ON c.responsible_patient_insurance_id = pinResponse.id\n" +
                  "LEFT JOIN insurance_company icResponse ON pinResponse.insurance_company_id = icResponse.id\n" +
                  "JOIN (\n" +
                  "    SELECT z.prescription_id,\n" +
                  "           SUM(z.charge) AS sales,\n" +
                  "           SUM(z.charge) + SUM(z.allowable) AS allowable,\n" +
                  "           SUM(z.primaryApplied) AS primaryApplied,\n" +
                  "           SUM(z.primaryAdjustment) AS primaryAdjustment,\n" +
                  "           SUM(z.secondaryApplied) AS secondaryApplied,\n" +
                  "           SUM(z.secondaryAdjustment) AS secondaryAdjustment,\n" +
                  "           SUM(z.tertiaryApplied) AS tertiaryApplied,\n" +
                  "           SUM(z.tertiaryAdjustment) AS tertiaryAdjustment,\n" +
                  "           SUM(z.otherApplied) AS otherApplied,\n" +
                  "           SUM(z.otherAdjustment) AS otherAdjustment,\n" +
                  "           SUM(z.adjustment) AS adjustment,\n" +
                  "           SUM(z.amount) AS balance,\n" +
                  "           SUM(z.patientPaid) AS patientPaid,\n" +
                  "           SUM(z.insurancePaid) AS insurancePaid\n" +
                  "    FROM (\n" +
                  "        SELECT gl1.prescription_id, \n" +
                  "               gl1.category, \n" +
                  "               gl1.sub_category, \n" +
                  "               gl1.payer_type, \n" +
                  "               ivv1.carrier_type,\n" +
                  "               CASE WHEN gl1.category = 'Accounts Receivable' THEN gl1.amount ELSE 0.00 END AS amount,\n" +
                  "               CASE WHEN gl1.sub_category = 'Sales' THEN gl1.amount ELSE 0.00 END AS charge,\n" +
                  "               CASE WHEN gl1.category = 'Line Adjustments' THEN gl1.amount * -1 ELSE 0.00 END AS allowable,\n" +
                  "               CASE WHEN gl1.sub_category = 'Applied Payments' AND ivv1.carrier_type = 'primary' THEN gl1.amount ELSE 0.00 END AS primaryApplied,\n" +
                  "               CASE WHEN gl1.sub_category = 'Applied Payments' AND ivv1.carrier_type = 'secondary' THEN gl1.amount ELSE 0.00 END AS secondaryApplied,\n" +
                  "               CASE WHEN gl1.sub_category = 'Applied Payments' AND ivv1.carrier_type = 'tertiary' THEN gl1.amount ELSE 0.00 END AS tertiaryApplied,\n" +
                  "               CASE WHEN gl1.sub_category = 'Applied Payments' AND (ivv1.carrier_type NOT IN ('primary', 'secondary', 'tertiary') OR ivv1.carrier_type IS NULL) THEN gl1.amount ELSE 0.00 END AS otherApplied,\n" +
                  "               CASE WHEN gl1.sub_category = 'Line Adjustments' AND ivv1.carrier_type = 'primary' THEN gl1.amount ELSE 0.00 END AS primaryAdjustment,\n" +
                  "               CASE WHEN gl1.sub_category = 'Line Adjustments' AND ivv1.carrier_type = 'secondary' THEN gl1.amount ELSE 0.00 END AS secondaryAdjustment,\n" +
                  "               CASE WHEN gl1.sub_category = 'Line Adjustments' AND ivv1.carrier_type = 'tertiary' THEN gl1.amount ELSE 0.00 END AS tertiaryAdjustment,\n" +
                  "               CASE WHEN gl1.sub_category = 'Line Adjustments' AND (ivv1.carrier_type NOT IN ('primary', 'secondary', 'tertiary') OR ivv1.carrier_type IS NULL) THEN gl1.amount ELSE 0.00 END AS otherAdjustment,\n" +
                  "               CASE WHEN gl1.category = 'Accounts Receivable' AND (gl1.sub_category = 'Line Adjustments' OR gl1.sub_category = 'Adjustments') THEN gl1.amount ELSE 0.00 END AS adjustment,\n" +
                  "               CASE WHEN gl1.category = 'Accounts Receivable' AND gl1.sub_category <> 'Sales' AND gl1.payer_type = 'patient' THEN gl1.amount ELSE 0.00 END AS patientPaid,\n" +
                  "               CASE WHEN gl1.category = 'Accounts Receivable' AND gl1.sub_category = 'Applied Payments' AND gl1.payer_type = 'insurance_company' THEN gl1.amount ELSE 0.00 END AS insurancePaid\n" +
                  "        FROM gl_view gl1\n" +
                  "        LEFT JOIN (\n" +
                  "            SELECT aplc.id AS aplc_id, \n" +
                  "                   iv.carrier_type, \n" +
                  "                   iv.patient_insurance_id, \n" +
                  "                   c.prescription_id\n" +
                  "            FROM applied_payment_l_code aplc\n" +
                  "            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id\n" +
                  "            JOIN payment p ON ap.payment_id = p.id\n" +
                  "            JOIN insurance_verification_l_code ivlc ON ivlc.prescription_l_code_id = aplc.prescription_l_code_id\n" +
                  "            JOIN insurance_verification iv ON ivlc.insurance_verification_id = iv.id\n" +
                  "            JOIN claim c ON ap.claim_id = c.id \n" +
                  "            AND (c.patient_insurance_id = iv.patient_insurance_id \n" +
                  "                 OR c.other_patient_insurance_id = iv.patient_insurance_id \n" +
                  "                 OR c.responsible_patient_insurance_id = iv.patient_insurance_id)\n" +
                  "            JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id \n" +
                  "            AND pin.active = 1\n" +
                  "            JOIN insurance_company com ON pin.insurance_company_id = com.id \n" +
                  "            AND (pin.insurance_company_id = p.insurance_company_id \n" +
                  "                 OR (com.name = 'Self Pay' AND p.insurance_company_id IS NULL))\n" +
                  "        ) ivv1 ON gl1.applied_payment_l_code_id = ivv1.aplc_id\n" +
                  "        WHERE ((CONCAT(gl1.gl_year, LPAD(gl1.gl_period, 2, 0)) BETWEEN :start AND :end)\n" +
                  "               AND (gl1.category = 'Accounts Receivable'\n" +
                  "                    OR (gl1.category = 'Line Adjustments' \n" +
                  "                        AND gl1.sub_category IN ('CO-45 Estimate', 'CO-45 Reversal', 'CO-45')))\n" +
                  "              )\n" +
                  "              OR (gl1.prescription_id IN (\n" +
                  "                      SELECT DISTINCT prescription_id \n" +
                  "                      FROM gl_view \n" +
                  "                      WHERE CONCAT(gl_year, LPAD(gl_period, 2, 0)) BETWEEN :start AND :end\n" +
                  "                  ) \n" +
                  "                  AND CONCAT(gl1.gl_year, LPAD(gl1.gl_period, 2, 0)) < :start \n" +
                  "                  AND (gl1.category = 'Accounts Receivable'\n" +
                  "                       OR (gl1.category = 'Line Adjustments' \n" +
                  "                           AND gl1.sub_category IN ('CO-45 Estimate', 'CO-45 Reversal', 'CO-45', 'Sales')))\n" +
                  "              )\n" +
                  "    ) AS z\n" +
                  "    GROUP BY z.prescription_id\n" +
                  ") AS t ON rx.id = t.prescription_id\n" +
                  "LEFT JOIN (\n" +
                  "    SELECT pmt.prescription_id, \n" +
                  "           pmt.claim_id, \n" +
                  "           pmt.patient_id, \n" +
                  "           SUM(pmt.unapplied_amount) AS unAppliedPayment,\n" +
                  "           SUM(pmt.unapplied_adjustment) AS unAppliedAdjustment,\n" +
                  "           pmt.insurance_company_id AS insuranceId\n" +
                  "    FROM payment pmt\n" +
                  "    WHERE pmt.unapplied_adjustment <> 0 \n" +
                  "       OR pmt.unapplied_amount <> 0\n" +
                  "    GROUP BY pmt.prescription_id, pmt.claim_id, pmt.patient_id, pmt.insurance_company_id\n" +
                  ") AS n ON c.id = n.claim_id\n" +
                  "LEFT JOIN LastComments lc ON c.id = lc.claim_id AND lc.rn = 1\n" +
                  "LEFT JOIN LastPayments lp ON c.id = lp.claim_id AND lp.rn = 1\n" +
                  "WHERE (:branchId IS NULL OR :branchId = 0 OR :branchId = c.billing_branch_id)\n";



    public static final String rentalPrescriptionReportQuery = "SELECT \n" +
            "    CASE WHEN c.id is not null THEN 1 ELSE 0 END AS billed,\n" +
            "    cb.name AS claimBranch,\n" +
            "    c.id AS claimId,\n" +
            "    cns.name AS claimStatus,\n" +
            "    claimSub.submissionDate AS claimSubmissionDate,\n" +
            "    c.date_of_service AS dateOfService,\n" +
            "    dt.name AS deviceTypeName,\n" +
            "    cd.lcName AS firstHCPCOnClaim,\n" +
            "    cd.dcCode AS firstDiagnosisCodeOnClaim,\n" +
            "    ic.name AS insurance,\n" +
            "    (SELECT \n" +
            "            nt.note\n" +
            "        FROM\n" +
            "            note nt\n" +
            "        WHERE\n" +
            "            nt.prescription_id = rx.id\n" +
            "                AND nt.note_type = 'patient_summary'\n" +
            "        ORDER BY nt.created_at DESC\n" +
            "        LIMIT 1) AS latestComment,\n" +
            "    (SELECT \n" +
            "            nt.created_at\n" +
            "        FROM\n" +
            "            note nt\n" +
            "        WHERE\n" +
            "            nt.prescription_id = rx.id\n" +
            "                AND nt.note_type = 'patient_summary'\n" +
            "        ORDER BY nt.created_at DESC\n" +
            "        LIMIT 1) AS latestCommentDate,\n" +
            "    (SELECT \n" +
            "            CONCAT(nu.first_name, ' ', nu.last_name)\n" +
            "        FROM\n" +
            "            note nt\n" +
            "                LEFT JOIN\n" +
            "            nymbl_master.user nu ON nu.id = nt.created_by_id\n" +
            "        WHERE\n" +
            "            nt.prescription_id = rx.id\n" +
            "                AND nt.note_type = 'patient_summary'\n" +
            "        ORDER BY nt.created_at DESC\n" +
            "        LIMIT 1) AS latestCommentBy,\n" +
            "    rx.next_prescription_date AS nextBillDate,\n" +
            "    rx.rental_billing_periods AS numRentalBillingPeriods,\n" +
            "    parent.id AS parentId,\n" +
            "    ptb.name AS patientBranch,\n" +
            "    pt.id AS patientId,\n" +
            "    CONCAT(pt.first_name, ' ', pt.last_name) AS patientName,\n" +
            "    rx.is_rental_paused AS paused,\n" +
            "    CONCAT(tp.first_name, ' ', tp.last_name) AS practitionerName,\n" +
            "    rxb.name AS prescriptionBranch,\n" +
            "    rx.prescription_date AS prescriptionDate,\n" +
            "    rx.id AS prescriptionId,\n" +
            "    SUM(COALESCE(ivlc.total_allowable, 0.00)) AS primaryAllowable,\n" +
            "    SUM(COALESCE(ivlc.total_charge, 0.00)) AS primaryBillable,\n" +

            "    rx.rental_start_period AS rentalClaimNumber,\n" +

            "    rx.rental_start_period AS rentalStartPeriod,\n" +
            "    plc.rental_status AS rentalStatus,\n" +
            "    c.date_resolved AS resolvedDate,\n" +
            "    rxns.name AS rxStatus\n" +
            "FROM\n" +
            "    prescription rx\n" +
            "        LEFT JOIN\n" +
            "    prescription parent ON parent.id = rx.parent_id\n" +
            "        LEFT JOIN\n" +
            "    (SELECT \n" +
            "        COUNT(rx2.id) AS periods, rx1.id AS id\n" +
            "    FROM\n" +
            "        prescription rx1\n" +
            "    LEFT JOIN prescription rx2 ON rx2.parent_id = rx1.id\n" +
            "    GROUP BY rx1.id) subRx ON rx.parent_id = subRx.id\n" +
            "        LEFT JOIN\n" +
            "    (SELECT \n" +
            "        COUNT(id) AS periods, parent_id\n" +
            "    FROM\n" +
            "        prescription rx\n" +
            "    GROUP BY parent_id) parentRx ON rx.id = parentRx.parent_id\n" +
            "        JOIN\n" +
            "    patient pt ON pt.id = rx.patient_id\n" +
            "        JOIN\n" +
            "    claim c ON c.prescription_id = rx.id\n" +
            "        AND c.patient_insurance_id = rx.patient_insurance_id\n" +
            "        LEFT JOIN\n" +
            "    (SELECT \n" +
            "        MAX(submission_date) AS submissionDate, claim_id\n" +
            "    FROM\n" +
            "        claim_submission\n" +
            "    GROUP BY claim_id) AS claimSub ON claimSub.claim_id = c.id\n" +
            "        JOIN\n" +
            "    device_type dt ON rx.device_type_id = dt.id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_status rxns ON rx.nymbl_status_id = rxns.id\n" +
            "        LEFT JOIN\n" +

            "    nymbl_status cns ON c.nymbl_status_id = cns.id\n" +

            "        LEFT JOIN\n" +
            "    branch ptb ON pt.primary_branch_id = ptb.id\n" +
            "        LEFT JOIN\n" +
            "    branch rxb ON rx.branch_id = rxb.id\n" +
            "        LEFT JOIN\n" +
            "    branch cb ON c.billing_branch_id = cb.id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user tp ON rx.treating_practitioner_id = tp.id\n" +
            "        LEFT JOIN\n" +
            "    patient_insurance pi ON rx.patient_insurance_id = pi.id\n" +
            "        AND pi.patient_id = pt.id\n" +
            "        LEFT JOIN\n" +
            "    insurance_verification iv ON rx.id = iv.prescription_id\n" +
            "        AND iv.patient_insurance_id = pi.id\n" +
            "        AND iv.carrier_type = 'primary'\n" +
            "        LEFT JOIN\n" +
            "    insurance_company ic ON pi.insurance_company_id = ic.id\n" +
            "        LEFT JOIN\n" +
            "    (SELECT \n" +
            "        plc.prescription_id,\n" +
            "            lc.name AS lcName,\n" +
            "            dc.code AS dcCode,\n" +
            "            lc.id AS lCodeId\n" +
            "    FROM\n" +
            "        prescription_l_code plc\n" +

            "    LEFT JOIN l_code lc ON plc.l_code_id = lc.id\n" +
            "    LEFT JOIN diagnosis_code dc ON plc.diagnosis_code1id = dc.id\n" +

            "    WHERE\n" +
            "        order_num = 1) AS cd ON rx.id = cd.prescription_id\n" +
            "        LEFT JOIN\n" +
            "    prescription_l_code plc ON plc.prescription_id = rx.id\n" +
            "        AND cd.lCodeId = plc.l_code_id\n" +
            "        LEFT JOIN\n" +
            "    insurance_verification_l_code ivlc ON ivlc.insurance_verification_id = iv.id\n" +
            "WHERE\n" +
            "    (rx.rental_billing_periods IS NOT NULL\n" +
            "        OR parent.rental_billing_periods IS NOT NULL)\n" +
            "        AND (:branchId IS NULL OR :branchId = 0\n" +
            "        OR :branchId = pt.primary_branch_id)\n" +
            "        AND (:prescriptionStatusId IS NULL\n" +
            "        OR (:prescriptionStatusId = rx.nymbl_status_id\n" +
            "        OR :prescriptionStatusId = parent.nymbl_status_id and rx.parent_id = parent.id))\n" +
            "        AND (:isCanceled = 0\n" +
            "        OR (:isCanceled = 1\n" +
            "        AND ((rx.next_prescription_date IS NULL\n" +
            "        AND rx.parent_id IS NULL)\n" +
            "        OR parent.next_prescription_date IS NULL and rx.parent_id = parent.id)))\n" +
            "        AND (:isCompleted = 0\n" +
            "        OR (:isCompleted = 1\n" +
            "        AND (rx.parent_id IS NULL\n" +

            "        AND rx.rental_start_period + parentRx.periods = rx.rental_billing_periods)\n" +
            "        OR parent.rental_start_period + subRx.periods = parent.rental_billing_periods))\n" +
            "        AND (:isPaused = 0\n" +
            "        OR (:isPaused = 1\n" +
            "        AND (rx.is_rental_paused = 1\n" +
            "        OR parent.is_rental_paused = 1)))\n" +
            "GROUP BY prescriptionId , parentId , prescriptionBranch , deviceTypeName , rxStatus , prescriptionDate , patientName , patientId , patientBranch , claimId , claimStatus , claimSubmissionDate , dateOfService , claimBranch , resolvedDate , numRentalBillingPeriods , paused , practitionerName , firstHCPCOnClaim , firstDiagnosisCodeOnClaim , insurance , latestComment , latestCommentDate , latestCommentBy , plc.rental_status , nextBillDate, rentalClaimNumber";

    public static final String resupplyReportSQL = "SELECT \n" +
            "rx.patient_id patientId,\n" +
            "CONCAT(p.first_name, ' ' , p.last_name) patientName,\n" +
            "p.home_phone homePhone,\n" +
            "p.cell_phone cellPhone,\n" +
            "p.work_phone workPhone,\n" +
            "p.email,\n" +
            "b.name prescriptionBranch,\n" +
            "dt.orthotic_or_prosthetic deviceType,\n" +
            "dt.name device,\n" +
            "CONCAT(py.first_name, ' ' , py.last_name) referringPhysician,\n" +
            "rx.id prescriptionId,\n" +
            "c.id claimId,\n" +
            "ic.name insurance,\n" +
            "MAX(cs.submission_date) submissionDate,\n" +
            "DATEDIFF(now(), c.date_of_service) daysSinceDos,\n" +
            "CASE WHEN eh.eligibility_date IS NULL THEN 'No' ELSE 'Yes' END isEligible,\n" +
            "eh.verified_time lastEligibilityDate\n" +
            "FROM patient p\n" +
            "JOIN prescription rx on p.id = rx.patient_id\n" +
            "LEFT JOIN device_type dt on rx.device_type_id = dt.id\n" +
            "LEFT JOIN branch b on rx.branch_id = b.id\n" +
            "LEFT JOIN physician py on rx.referring_physician_id = py.id\n" +
            "JOIN claim c on rx.id = c.prescription_id\n" +
            "JOIN patient_insurance pi on c.patient_insurance_id = pi.id\n" +
            "LEFT JOIN eligibility_history eh on eh.id = (SELECT id FROM eligibility_history where patient_insurance_id = pi.id ORDER BY id DESC LIMIT 1)\n" +
            "JOIN insurance_company ic on pi.insurance_company_id = ic.id\n" +
            "JOIN claim_submission cs on c.id = cs.claim_id\n" +
            "WHERE\n" +
            "(:daysSinceDos IS NULL OR :daysSinceDos = 0 OR DATEDIFF(NOW(), c.date_of_service) = :daysSinceDos)\n" +
            "AND \n" +
            "(:branchId IS NULL OR :branchId = 0 OR rx.branch_id = :branchId)\n" +
            "GROUP BY rx.patient_id, p.first_name, p.last_name, p.home_phone, p.cell_phone, p.work_phone, p.email, b.name, dt.orthotic_or_prosthetic, dt.name, py.first_name,  py.last_name, rx.id, c.id, ic.name, c.date_of_service, eh.verified_time, eh.eligibility_date";
}
