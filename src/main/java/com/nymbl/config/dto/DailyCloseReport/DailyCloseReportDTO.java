package com.nymbl.config.dto.DailyCloseReport;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class DailyCloseReportDTO {

    public List<DailyCloseBranchDTO> branchCloseDtos = new ArrayList<>();
    public BigDecimal reportCash = BigDecimal.ZERO;
    public BigDecimal reportCheck = BigDecimal.ZERO;
    public BigDecimal reportCredit = BigDecimal.ZERO;
    public BigDecimal reportAdjustment = BigDecimal.ZERO;
    public BigDecimal reportElectronic = BigDecimal.ZERO;
    public BigDecimal reportAch = BigDecimal.ZERO;
    public BigDecimal reportEra = BigDecimal.ZERO;
    public BigDecimal reportApplied = BigDecimal.ZERO;
    public BigDecimal reportUnApplied = BigDecimal.ZERO;
    public BigDecimal reportTotalDeposited = BigDecimal.ZERO;

}
