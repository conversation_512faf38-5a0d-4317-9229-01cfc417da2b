package com.nymbl.config.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nymbl.config.utils.JsonDateTimeDeserializer;
import com.nymbl.config.utils.JsonDateTimeSerializer;
import com.nymbl.tenant.model.Note;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSeeAlso;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@XmlSeeAlso(ArrayList.class)
@XmlAccessorType(XmlAccessType.FIELD)


public class NoteDTO {

    private Long id;
    private String subject;
    private String note;
    private String noteType;
    private Long version = 0L;
    private Long patientId;
    private Long prescriptionId;

    private Long claimId;
    private Long userId;
    private Long appointmentId;
    private Boolean published;
    private Boolean cosigned;
    private String childType;
    private String action;
    private Long transcriptionDetailId;


    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    private Timestamp createdAt;

    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    private Timestamp updateAt = new Timestamp(Calendar.getInstance().getTimeInMillis());

    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    private Timestamp publishedAt;

    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    private Timestamp userSignedAt;

    private Long parentId;
    private Long treatingPractitionerId;
    private Long residentId;

    public NoteDTO() {}

    public NoteDTO(Note n) {
        BeanUtils.copyProperties(n, this);
    }

    public Note _getNoteEntity(){
        // should use BeanUtils.copyProperties(this, n); like others
        Note n = new Note();
        n.setId(id);
        n.setVersion(version);
        n.setSubject(subject);
        n.setNote(note);
        n.setNoteType(noteType);
        n.setPatientId(patientId);
        n.setPrescriptionId(prescriptionId);
        n.setClaimId(claimId);
        n.setUserId(userId);
        n.setAppointmentId(appointmentId);
        n.setPublished(published);
        n.setCosigned(cosigned);
        n.setUpdatedById(userId);
        n.setUpdatedAt(updateAt);
        n.setPublishedAt(publishedAt);
        n.setUserSignedAt(userSignedAt);
        n.setCreatedById(userId);
        n.setCreatedAt(id == null ? updateAt : createdAt);
        n.setParentId(parentId);
        n.setTreatingPractitionerId(treatingPractitionerId);
        n.setResidentId(residentId);
        n.setChildType(childType);
        n.setTranscriptionDetailId(transcriptionDetailId);
        return n;
    }
}
