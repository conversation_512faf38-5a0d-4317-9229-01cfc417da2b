package com.nymbl.config.cron;

import com.nymbl.config.clearingHouse.ClearingHouseSSH;
import com.nymbl.config.clearingHouse.ClearingHouseSSHv2;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.Company;
import com.nymbl.master.model.User;
import com.nymbl.master.repository.CompanyRepository;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.service.SystemSettingService;
import com.nymbl.tenant.service.X12FileService;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by Bradley Moore on 12/13/2017.
 * <p>
 * Waystar sends batch requests to providers at 9AM, 12PM, 3PM, and 7PM EST
 */
@Configuration
@Component
@Profile(value = {"prod", "cron"})
@Conditional(value = {ProfileCondition.class})
@Slf4j
public class ClearingHouseJob {

    private final ClearingHouseSSH clearingHouseSSH;
    private final ClearingHouseSSHv2 clearingHouseSSHv2;
    private final X12FileService x12FileService;
    private final CompanyRepository companyRepository;
    private final UserService userService;
    private final SystemSettingService systemSettingService;

    @Autowired
    public ClearingHouseJob(ClearingHouseSSH clearingHouseSSH,
                            ClearingHouseSSHv2 clearingHouseSSHv2,
                            X12FileService x12FileService,
                            CompanyRepository companyRepository,
                            UserService userService,
                            SystemSettingService systemSettingService) {
        this.clearingHouseSSH = clearingHouseSSH;
        this.clearingHouseSSHv2 = clearingHouseSSHv2;
        this.x12FileService = x12FileService;
        this.companyRepository = companyRepository;
        this.userService = userService;
        this.systemSettingService = systemSettingService;
    }

    @Scheduled(cron = "${nymbl.send.claims}")
    public void cronClaimsToClearingHouse() {
        List<Company> companies = companyRepository.findAllByActiveTrue();
        log.info("Submit Claims To Waystar - Start Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Submit Claims To Waystar - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        User u = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(u, null, u.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            try {
                String dch = systemSettingService.findBySectionAndField("billing", "default_clearing_house").getValue();
                if ("1".equals(dch)) { // 1 = Waystar
                    clearingHouseSSH.submitClaimsToClearingHouse();
                } else {
                    clearingHouseSSHv2.submitClaimsToClearingHouse();
                }
            } catch (Exception e) {
                log.error(StringUtil.getExceptionAsString(e));
                Sentry.captureException(e);
            }
            TenantContext.clear();
        }
        log.info("Submit Claims To Waystar - End Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Submit Claims To Waystar - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

//    @Timed
//    @Scheduled(cron = "${nymbl.era.download}")
//    public void cronDownloadEraFiles() {
//        List<Company> companies = companyRepository.findAllByActive();
//        log.info("Download X12 Files - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
//        for (Company company : companies) {
//            TenantContext.setCurrentTenant(company.getKey());
//            clearingHouseSSH.downloadX12Files();
//            TenantContext.clear();
//        }
//        log.info("Download X12 Files - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
//    }

//    @Timed
//    @Scheduled(cron = "${nymbl.era.process}")
//    public void cronProcessEraFiles() {
//        List<Company> companies = companyRepository.findAllByActive();
//        log.info("Process ERA Files - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
//        for (Company company : companies) {
//            TenantContext.setCurrentTenant(company.getKey());
//            x12FileService.processX12Files();
//            TenantContext.clear();
//        }
//        log.info("Process ERA Files - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
//    }

    @Scheduled(cron = "${nymbl.era.download}")
    public void cronDownloadAndProcess() {
        List<Company> companies = companyRepository.findAllByActiveTrue();
        log.info("Download and Process X12 Files - Start Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Download and Process X12 Files - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        User user = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            String dch = systemSettingService.findBySectionAndField("billing", "default_clearing_house").getValue();
            if ("1".equals(dch)) { // 1 = Waystar
                clearingHouseSSH.downloadX12Files();
            } else {
                clearingHouseSSHv2.downloadX12Files();
            }
            x12FileService.processX12Files();
            TenantContext.clear();
        }
        log.info("Download and Process X12 Files - End Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Download and Process X12 Files - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

}
