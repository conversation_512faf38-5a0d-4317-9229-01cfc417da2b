package com.nymbl.config.security;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;


import com.nymbl.tenant.service.SystemSettingService;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 * Default implementation of PasswordHistoryService that uses Hibernate Envers
 * to check password history from audit tables.
 */
@Service
public class DefaultPasswordHistoryService implements PasswordHistoryService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultPasswordHistoryService.class);

    @PersistenceContext(name = "masterEntityManager", unitName = "master")
    @Qualifier("masterEntityManager")
    private EntityManager entityManager;

    private final PasswordEncoder passwordEncoder;
    private final SystemSettingService systemSettingService;

    @Value("${password.history.depth:6}")
    private int historyDepth;

    @Value("${password.history.enabled:true}")
    private boolean enabled;

    @Value("${password.reuse.days:365}")
    private int passwordReuseDays;

    @Autowired
    public DefaultPasswordHistoryService(PasswordEncoder passwordEncoder,
                                         SystemSettingService systemSettingService) {
        this.passwordEncoder = passwordEncoder;
        this.systemSettingService = systemSettingService;
    }

    // Constructor for backward compatibility
    public DefaultPasswordHistoryService(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
        this.systemSettingService = null;
    }

    @Override
    public boolean isPasswordInHistory(Long userId, String newPass) {
        if (!enabled) {
            return false;
        }

        int depth = getHistoryDepth();
        int reuseDays = getPasswordReuseDays();

        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(reuseDays);
        Timestamp cutoffTimestamp = Timestamp.valueOf(cutoffDate);

        // First, check if the password is in the time-based history (within reuseDays)
        // PERFORMANCE OPTIMIZATION: Use DISTINCT to get only unique password hashes
        // This dramatically reduces BCrypt comparisons for users with many duplicate password changes
        String timeSql = """
            SELECT ua.password, ar.rev_timestamp
            FROM user_audit ua
            JOIN audit_revision ar ON ua.revision_id = ar.revision_id
            WHERE ua.id = :userId
            AND ar.rev_timestamp >= :cutoffTimestamp
            AND ua.password IS NOT NULL
            ORDER BY ar.rev_timestamp DESC
            """;

        try {
            // Check all unique passwords within the time window
            Query timeQuery = entityManager.createNativeQuery(timeSql);
            timeQuery.setParameter("userId", userId);
            timeQuery.setParameter("cutoffTimestamp", cutoffTimestamp);

            @SuppressWarnings("unchecked")
            List<String> uniqueTimePasswords = (List<String>) timeQuery.getResultList();

            // Check each unique password hash with BCrypt
            boolean isInTimeHistory = uniqueTimePasswords.stream()
                .anyMatch(oldPassHash -> passwordEncoder.matches(newPass, oldPassHash));

            if (isInTimeHistory) {
                logger.debug("Password found in time-based history (within {} days)", reuseDays);
                return true;
            }

            // If not in time history, also check the depth-based history
            // This is a fallback in case the time-based query missed something
            // PERFORMANCE OPTIMIZATION: Use DISTINCT to get only unique password hashes
            String depthSql = """
                SELECT DISTINCT ua.password
                FROM user_audit ua
                JOIN audit_revision ar ON ua.revision_id = ar.revision_id
                WHERE ua.id = :userId
                AND ua.password IS NOT NULL
                """;

            Query depthQuery = entityManager.createNativeQuery(depthSql);
            depthQuery.setParameter("userId", userId);
            // Note: setMaxResults with DISTINCT will limit unique passwords, not total records
            depthQuery.setMaxResults(depth);

            @SuppressWarnings("unchecked")
            List<String> uniqueDepthPasswords = (List<String>) depthQuery.getResultList();

            // Check each unique password hash with BCrypt
            boolean isInDepthHistory = uniqueDepthPasswords.stream()
                .anyMatch(oldPassHash -> passwordEncoder.matches(newPass, oldPassHash));

            if (isInDepthHistory) {
                logger.debug("Password found in depth-based history (last {} passwords)", depth);
                return true;
            }

            return false;
        } catch (Exception e) {
            logger.warn("Error checking password history: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getHistoryDepth() {
        try {
            var setting = systemSettingService.findBySectionAndField("password", "password_history_depth");
            if (setting != null) {
                // Try to access the value using reflection as a fallback for IDE issues
                String value;
                try {
                    // First try the standard getter
                    value = setting.getValue();
                } catch (Exception ex) {
                    try {
                        // If that fails, try direct field access via reflection
                        java.lang.reflect.Field valueField = setting.getClass().getDeclaredField("value");
                        valueField.setAccessible(true);
                        value = (String) valueField.get(setting);
                    } catch (Exception e) {
                        // If all else fails, use the default value
                        logger.warn("Failed to access value field via reflection: {}", e.getMessage());
                        return historyDepth;
                    }
                }
                int parsedValue = Integer.parseInt(value);
                return parsedValue > 0 ? parsedValue : historyDepth;
            }
            return historyDepth;
        } catch (Exception e) {
            logger.warn("Failed to get password history depth from settings, using default: {}", historyDepth, e);
            return historyDepth;
        }
    }

    @Override
    public int getPasswordReuseDays() {
        try {
            var setting = systemSettingService.findBySectionAndField("password", "password_reuse_days");
            if (setting != null) {
                // Try to access the value using reflection as a fallback for IDE issues
                String value;
                try {
                    // First try the standard getter
                    value = setting.getValue();
                } catch (Exception ex) {
                    try {
                        // If that fails, try direct field access via reflection
                        java.lang.reflect.Field valueField = setting.getClass().getDeclaredField("value");
                        valueField.setAccessible(true);
                        value = (String) valueField.get(setting);
                    } catch (Exception e) {
                        // If all else fails, use the default value
                        logger.warn("Failed to access value field via reflection: {}", e.getMessage());
                        return passwordReuseDays;
                    }
                }
                int parsedValue = Integer.parseInt(value);
                return parsedValue > 0 ? parsedValue : passwordReuseDays;
            }
            return passwordReuseDays;
        } catch (Exception e) {
            logger.warn("Failed to get password reuse days from settings, using default: {}", passwordReuseDays, e);
            return passwordReuseDays;
        }
    }
}
