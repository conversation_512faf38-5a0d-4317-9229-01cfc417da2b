package com.nymbl.config.security;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;


import com.nymbl.tenant.service.SystemSettingService;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 * Default implementation of PasswordHistoryService that uses Hibernate Envers
 * to check password history from audit tables.
 */
@Service
public class DefaultPasswordHistoryService implements PasswordHistoryService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultPasswordHistoryService.class);

    @PersistenceContext(name = "masterEntityManager", unitName = "master")
    @Qualifier("masterEntityManager")
    private EntityManager entityManager;

    private final PasswordEncoder passwordEncoder;
    private final SystemSettingService systemSettingService;

    @Value("${password.history.depth:6}")
    private int historyDepth;

    @Value("${password.history.enabled:true}")
    private boolean enabled;

    @Value("${password.reuse.days:365}")
    private int passwordReuseDays;

    @Autowired
    public DefaultPasswordHistoryService(PasswordEncoder passwordEncoder,
                                         SystemSettingService systemSettingService) {
        this.passwordEncoder = passwordEncoder;
        this.systemSettingService = systemSettingService;
    }

    // Constructor for backward compatibility
    public DefaultPasswordHistoryService(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
        this.systemSettingService = null;
    }

    @Override
    public boolean isPasswordInHistory(Long userId, String newPass) {
        if (!enabled) {
            return false;
        }

        int depth = getHistoryDepth();
        int reuseDays = getPasswordReuseDays();

        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(reuseDays);
        Timestamp cutoffTimestamp = Timestamp.valueOf(cutoffDate);

        // First, check if the password is in the time-based history (within reuseDays)
        String timeSql = """
            SELECT DISTINCT ua.password, ar.rev_timestamp
            FROM user_audit ua
            JOIN audit_revision ar ON ua.revision_id = ar.revision_id
            WHERE ua.id = :userId
            AND ar.rev_timestamp >= :cutoffTimestamp
            AND ua.password IS NOT NULL
            ORDER BY ar.rev_timestamp DESC
            """;

        try {
            // Check all passwords within the time window, regardless of depth
            Query timeQuery = entityManager.createNativeQuery(timeSql);
            timeQuery.setParameter("userId", userId);
            timeQuery.setParameter("cutoffTimestamp", cutoffTimestamp);

            @SuppressWarnings("unchecked")
            List<Object[]> timeResults = (List<Object[]>) timeQuery.getResultList();

            // PERFORMANCE OPTIMIZATION: Hash the new password once and compare hashes directly
            // This avoids expensive BCrypt.matches() calls for every historical password
            String newPasswordHash = passwordEncoder.encode(newPass);

            // First try direct hash comparison (fast)
            boolean isInTimeHistory = timeResults.stream()
                .map(row -> (String) row[0]) // Extract password from first column
                .anyMatch(oldPassHash -> oldPassHash.equals(newPasswordHash));

            // If direct hash comparison didn't find a match, fall back to BCrypt.matches()
            // This handles edge cases where the same password might have different hashes
            if (!isInTimeHistory) {
                logger.debug("Direct hash comparison found no matches, falling back to BCrypt validation for {} historical passwords", timeResults.size());
                isInTimeHistory = timeResults.stream()
                    .map(row -> (String) row[0]) // Extract password from first column
                    .anyMatch(oldPass -> passwordEncoder.matches(newPass, oldPass));
            } else {
                logger.debug("Direct hash comparison found match - password validation completed quickly");
            }

            if (isInTimeHistory) {
                logger.debug("Password found in time-based history (within {} days)", reuseDays);
                return true;
            }

            // If not in time history, also check the depth-based history
            // This is a fallback in case the time-based query missed something
            String depthSql = """
                SELECT ua.password
                FROM user_audit ua
                JOIN audit_revision ar ON ua.revision_id = ar.revision_id
                WHERE ua.id = :userId
                AND ua.password IS NOT NULL
                ORDER BY ar.rev_timestamp DESC
                """;

            Query depthQuery = entityManager.createNativeQuery(depthSql);
            depthQuery.setParameter("userId", userId);
            depthQuery.setMaxResults(depth);

            @SuppressWarnings("unchecked")
            List<String> depthResults = (List<String>) depthQuery.getResultList();

            // Apply same optimization to depth-based check
            boolean isInDepthHistory = depthResults.stream()
                .anyMatch(oldPassHash -> oldPassHash.equals(newPasswordHash));

            // Fall back to BCrypt.matches() if direct comparison didn't find a match
            if (!isInDepthHistory) {
                logger.debug("Direct hash comparison found no matches in depth history, falling back to BCrypt validation for {} historical passwords", depthResults.size());
                isInDepthHistory = depthResults.stream()
                    .anyMatch(oldPass -> passwordEncoder.matches(newPass, oldPass));
            } else {
                logger.debug("Direct hash comparison found match in depth history - password validation completed quickly");
            }

            if (isInDepthHistory) {
                logger.debug("Password found in depth-based history (last {} passwords)", depth);
                return true;
            }

            return false;
        } catch (Exception e) {
            logger.warn("Error checking password history: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getHistoryDepth() {
        try {
            var setting = systemSettingService.findBySectionAndField("password", "password_history_depth");
            if (setting != null) {
                // Try to access the value using reflection as a fallback for IDE issues
                String value;
                try {
                    // First try the standard getter
                    value = setting.getValue();
                } catch (Exception ex) {
                    try {
                        // If that fails, try direct field access via reflection
                        java.lang.reflect.Field valueField = setting.getClass().getDeclaredField("value");
                        valueField.setAccessible(true);
                        value = (String) valueField.get(setting);
                    } catch (Exception e) {
                        // If all else fails, use the default value
                        logger.warn("Failed to access value field via reflection: {}", e.getMessage());
                        return historyDepth;
                    }
                }
                int parsedValue = Integer.parseInt(value);
                return parsedValue > 0 ? parsedValue : historyDepth;
            }
            return historyDepth;
        } catch (Exception e) {
            logger.warn("Failed to get password history depth from settings, using default: {}", historyDepth, e);
            return historyDepth;
        }
    }

    @Override
    public int getPasswordReuseDays() {
        try {
            var setting = systemSettingService.findBySectionAndField("password", "password_reuse_days");
            if (setting != null) {
                // Try to access the value using reflection as a fallback for IDE issues
                String value;
                try {
                    // First try the standard getter
                    value = setting.getValue();
                } catch (Exception ex) {
                    try {
                        // If that fails, try direct field access via reflection
                        java.lang.reflect.Field valueField = setting.getClass().getDeclaredField("value");
                        valueField.setAccessible(true);
                        value = (String) valueField.get(setting);
                    } catch (Exception e) {
                        // If all else fails, use the default value
                        logger.warn("Failed to access value field via reflection: {}", e.getMessage());
                        return passwordReuseDays;
                    }
                }
                int parsedValue = Integer.parseInt(value);
                return parsedValue > 0 ? parsedValue : passwordReuseDays;
            }
            return passwordReuseDays;
        } catch (Exception e) {
            logger.warn("Failed to get password reuse days from settings, using default: {}", passwordReuseDays, e);
            return passwordReuseDays;
        }
    }
}
