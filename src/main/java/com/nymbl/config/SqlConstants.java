package com.nymbl.config;

import com.nymbl.tenant.repository.ClaimSQL;

/**
 * Created by <PERSON> on 11/19/2020.
 */
public class SqlConstants {

    private static final String adjustments = "select distinct aplc.id as aplc_id, aplc.prescription_l_code_id, ap.claim_id, dt.orthotic_or_prosthetic, " +
            "cast(concat(a.operation, coalesce(aplc.adjustment, 0.00)) as decimal(19,2)) adjustment\n" +
            "from applied_payment_l_code aplc\n" +
            "JOIN applied_payment ap on ap.id = aplc.applied_payment_id\n" +
            "JOIN payment p on p.id = ap.payment_id\n" +
            "JOIN claim c on c.id = ap.claim_id\n" +
            "JOIN prescription rx on rx.id = c.prescription_id\n" +
            "JOIN patient pt on pt.id = rx.patient_id\n" +
            "JOIN device_type dt on dt.id = rx.device_type_id\n" +
            "JOIN adjustment a on a.id = p.adjustment_id\n" +
            "JOIN claim_submission cs on c.id = cs.claim_id\n" +
            "where ((:dateOption = 'payment' and p.date between :startDate and :endDate) or (:dateOption != 'payment' and p.deposit_date between :startDate and :endDate))\n" +
            "and (:branchId is null OR :branchId = 0 OR c.billing_branch_id = :branchId) \n" +
            "and rx.id <> 0 and rx.active = 1 and pt.id <> 0 and pt.active = 1 and dt.orthotic_or_prosthetic is not null\n" +
            "and a.withdraw = 0\n" +
            "and (select min(submission_date) from claim_submission cs1 where cs1.claim_id = c.id) <= :endDate\n" +
            "group by aplc.id, ap.claim_id, dt.orthotic_or_prosthetic";

    public static final String salesDetailArAdjustmentsHeader = "aplc_id, prescription_l_code_id, claim_id, orthotic_or_prosthetic, adjustment";
    public static final String salesDetailArAdjustmentsQuery = "select " + salesDetailArAdjustmentsHeader + " from (\n"
            + adjustments
            + ") as a\n";

    private static final String secondaryWithoutPrimaryAdjustments = "select distinct aplc.id as aplc_id, ap.claim_id, dt.orthotic_or_prosthetic, " +
            "cast(concat(a.operation, coalesce(aplc.adjustment, 0.00)) as decimal(19,2)) adjustment\n" +
            "from applied_payment_l_code aplc\n" +
            "JOIN applied_payment ap on ap.id = aplc.applied_payment_id\n" +
            "JOIN payment p on p.id = ap.payment_id\n" +
            "JOIN claim c on c.id = ap.claim_id\n" +
            "JOIN prescription rx on rx.id = c.prescription_id\n" +
            "JOIN patient pt on pt.id = rx.patient_id\n" +
            "JOIN device_type dt on dt.id = rx.device_type_id\n" +
            "JOIN adjustment a on a.id = p.adjustment_id\n" +
            "JOIN claim_submission cs on c.id = cs.claim_id\n" +
            "JOIN (\n" +
            ClaimSQL.getRowNumberOrderedInfoActivePrescriptionsWithClaimsWithClaimSubmissionOnly +
            ") as b on b.claim_id=c.id and b.prescription_id=c.prescription_id and b.claim_submission_date=cs.submission_date and b.billing_branch_id = c.billing_branch_id \n" +
            "where ((:dateOption = 'payment' and p.date between :startDate and :endDate) or (:dateOption != 'payment' and p.deposit_date between :startDate and :endDate))\n" +
            "and (:branchId is null OR :branchId = 0 OR c.billing_branch_id = :branchId) \n" +
            "and rx.id <> 0 and rx.active = 1 and pt.id <> 0 and pt.active = 1 and dt.orthotic_or_prosthetic is not null\n" +
            "and a.withdraw = 0\n" +
            "and b.claim_submission_date <= :endDate\n" +
            "group by aplc.id, ap.claim_id, dt.orthotic_or_prosthetic";

    public static final String getSalesSummaryArAdjustmentsHeader = "orthotic_or_prosthetic, adjustment";
    public static final String salesSummaryArAdjustmentsQuery =
            "select " + getSalesSummaryArAdjustmentsHeader + " from (\n" +
                    "select b.orthotic_or_prosthetic, sum(b.adjustment) adjustment from (\n" +
                    adjustments + ") as b\n" +
                    "group by b.orthotic_or_prosthetic\n" +
                    ") as a";

    public static final String arAgingArAdjustmentsQuery =
            "select b.claim_id, sum(b.adjustment) adjustment from (\n" +
                    secondaryWithoutPrimaryAdjustments + ") as b\n" +
                    "group by b.claim_id;";

    public static final String totalDailyClose = "select " +
            "(sum(aplc.amount) - SUM(CASE when a.withdraw = 1 and a.operation = '+' THEN aplc.adjustment when a.withdraw = 1 and a.operation = '-' THEN aplc.adjustment * -1 else 0.00 END)) * -1 \n" +
            "from applied_payment_l_code aplc\n" +
            "JOIN applied_payment ap ON ap.id = aplc.applied_payment_id\n" +
            "JOIN payment p on ap.payment_id = p.id\n" +
            "LEFT JOIN adjustment a on a.id = p.adjustment_id\n" +
            "JOIN claim c on c.id = ap.claim_id\n" +
            "JOIN prescription rx ON rx.id = c.prescription_id\n" +
            "JOIN patient pt on pt.id = rx.patient_id\n" +
            "where ((:dateOption = 'payment' and p.date <= :asOfDate) OR (:dateOption != 'payment' and p.deposit_date <= :asOfDate))\n" +
            "and (:branchId is null OR :branchId = 0 OR c.billing_branch_id = :branchId)\n" +
            "and rx.id <> 0 and rx.active = 1 and pt.id <> 0 and pt.active = 1;";

    public static final String getInsuranceVerificationByPrescriptionWaterfallWithoutParameters =
            "SELECT iv.* FROM insurance_verification iv \n" +
                    "WHERE iv.prescription_id = :prescriptionId \n" +
                    "ORDER BY  iv.carrier_type = 'primary'  DESC, iv.carrier_type = 'secondary'  DESC, iv.carrier_type = 'tertiary'  DESC, iv.carrier_type = 'quaternary'  DESC, iv.carrier_type = 'quinary'  DESC, iv.carrier_type = 'senary'  DESC, iv.carrier_type = 'septenary'  DESC, iv.carrier_type = 'octonary'  DESC, iv.carrier_type = 'nonary'  DESC, iv.carrier_type = 'denary'  DESC, " +
                    "iv.carrier_type = 'other'  DESC, iv.carrier_type = 'inactive'  DESC LIMIT 1 ";


    public static final String findProperPatientInsuranceIdByPrescription =
            "SELECT iv.patient_insurance_id as patientInsuranceId FROM insurance_verification iv \n" +
                    "WHERE iv.prescription_id = :prescriptionId \n" +
                    "ORDER BY  iv.carrier_type = 'primary'  DESC, iv.carrier_type = 'secondary'  DESC, iv.carrier_type = 'tertiary'  DESC, iv.carrier_type = 'quaternary'  DESC, iv.carrier_type = 'quinary'  DESC, iv.carrier_type = 'senary'  DESC, iv.carrier_type = 'septenary'  DESC, iv.carrier_type = 'octonary'  DESC, iv.carrier_type = 'nonary'  DESC, iv.carrier_type = 'denary'  DESC, " +
                    "iv.carrier_type = 'other'  DESC, iv.carrier_type = 'inactive'  DESC LIMIT 1 ";

    public static final String findProperSalesNumbersByPrescriptionId =
            "SELECT iv.prescription_id, iv.patient_insurance_id, SUM(COALESCE(ivlc.total_charge,0.00)) as billable, SUM(COALESCE(ivlc.total_allowable, 0.00)) as allowable, \n" +
                    "(SUM(COALESCE(ivlc.total_charge,0.00)) - SUM(COALESCE(ivlc.total_allowable, 0.00))) * -1 AS writeOff, SUM(COALESCE(ivlc.sales_tax, 0.00)) as salesTax \n" +
                    "FROM insurance_verification_l_code ivlc \n" +
                    "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
                    "JOIN (" + findProperPatientInsuranceIdByPrescription + ") as a \n" +
                    "ON iv.patient_insurance_id = a.patientInsuranceId \n" +
                    "WHERE iv.prescription_id = :prescriptionId \n" +
                    "GROUP BY iv.prescription_id, iv.patient_insurance_id ";

    public static final String findPrimaryIvlcForPrescriptionByPrescriptionId =
            "SELECT ivlc.* \n" +
                    "FROM insurance_verification_l_code ivlc \n" +
                    "JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id \n" +
                    "JOIN ( \n" +
                    "SELECT iv.patient_insurance_id as patientInsuranceId FROM insurance_verification iv \n" +
                    "WHERE iv.prescription_id = :prescriptionId \n" +
                    "ORDER BY  iv.carrier_type = 'primary'  DESC, iv.carrier_type = 'secondary'  DESC, iv.carrier_type = 'tertiary'  DESC, iv.carrier_type = 'quaternary'  DESC, iv.carrier_type = 'quinary'  DESC, iv.carrier_type = 'senary'  DESC, iv.carrier_type = 'septenary'  DESC, iv.carrier_type = 'octonary'  DESC, iv.carrier_type = 'nonary'  DESC, iv.carrier_type = 'denary'  DESC, \n" +
                    "iv.carrier_type = 'other'  DESC, iv.carrier_type = 'inactive'  DESC LIMIT 1 ) as a \n" +
                    "ON iv.patient_insurance_id = a.patientInsuranceId \n" +
                    "WHERE iv.prescription_id = :prescriptionId ";

    public static final String findClaimsByPrescriptionIdWithoutClaimSubmissionAndAppliedPayments =
            "SELECT c.* FROM claim c \n" +
                    "INNER JOIN prescription rx ON c.prescription_id = rx.id \n" +
                    "LEFT JOIN applied_payment ap ON c.id = ap.claim_id \n" +
                    "LEFT JOIN claim_submission cs ON c.id = cs.claim_id \n" +
                    "WHERE rx.id = :prescriptionId \n" +
                    "AND cs.id is NULL \n" +
                    "AND ap.id is NULL; ";

    public static final String findClaimsToUpdateWithoutAnyClaimSubmissionOrAppliedPaymentsOnThePrescription =
            "SELECT c.* FROM claim c \n" +
                    "INNER JOIN prescription rx ON c.prescription_id = rx.id \n" +
                    "WHERE c.prescription_id = :prescriptionId \n" +
                    "AND c.prescription_id NOT IN ( \n" +
                    "SELECT c1.prescription_id FROM claim c1 \n" +
                    "INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id \n" +
                    "LEFT JOIN applied_payment ap1 ON c1.id = ap1.claim_id \n" +
                    "LEFT JOIN claim_submission cs1 ON c1.id = cs1.claim_id \n" +
                    "WHERE rx1.id = :prescriptionId  \n" +
                    "AND (cs1.claim_id IS NOT NULL \n" +
                    "OR ap1.id IS NOT NULL));";


    public static final String populatePrescriptionSummaryV2_SELECT = "SELECT \n" +
            "    rx.id AS rxId,\n" +
            "    rx.projected_delivery_date AS projectedDeliveryDate,\n" +
            "    CASE WHEN rx.signed_date IS NOT NULL THEN rx.signed_date\n" +
            "         WHEN rx.manual_pod_signed_date IS NOT NULL THEN rx.manual_pod_signed_date\n" +
            "         WHEN rx.user_signed_date IS NOT NULL THEN rx.user_signed_date\n" +
            "         ELSE NULL END AS podSignedDate,\n" +
            "    rx.surgery_date AS surgeryDate,\n" +
            "    rx.patient_id AS patientId,\n" +
            "    rx.hold_until_date AS holdUntil,\n" +
            "    rx.created_at AS createdAt,\n" +
            "    DATEDIFF(CURRENT_DATE(), rx.created_at) AS rxAging,\n" +
            "    rx.category as rxCategory, \n" +
            "    rx.sub_category as rxSubCategory, \n" +
            "    c.id AS claimId,\n" +
            "    createdBy.first_name as createdByFirstName,\n" +
            "    createdBy.last_name as createdByLastName,\n" +
            "    p.first_name AS patientFirstName,\n" +
            "    p.middle_name AS patientMiddleName,\n" +
            "    p.last_name AS patientLastName,\n" +
            "    pb.name AS patientPrimaryBranchName,\n" +
            "    pb.id AS patientPrimaryBranchId,\n" +
            "    dt.orthotic_or_prosthetic AS deviceType,\n" +
            "    dt.id AS deviceTypeId,\n" +
            "    dt.name AS deviceName,\n" +
            "    rx.prescription_date AS rxDate,\n" +
            "    b.name AS rxBranchName,\n" +
            "    rx.branch_id AS rxBranchId,\n" +
            "    ns.name AS rxStatus,\n" +
            "    nsh.updated_at as rxStatusUpdatedDate, \n" +
            "    DATEDIFF(current_date(),nsh.updated_at) as daysInRxStatus, \n" +
            "    pds.name AS physicianDocumentationStatus,\n" +
            "    tp.first_name AS treatingPractitionerFirstName,\n" +
            "    tp.middle_name AS treatingPractitionerMiddleName,\n" +
            "    tp.last_name AS treatingPractitionerLastName,\n" +
            "    referring.first_name AS referringPhysicianFirstname,\n" +
            "    referring.middle_name AS referringPhysicianMiddlename,\n" +
            "    referring.last_name AS referringPhysicianLastName,\n" +
            "    referring.credentials AS referringPhysicianCredentials,\n" +
            "    res.first_name as residentFirstName, \n" +
            "    res.middle_name as residentMiddleName, \n" +
            "    res.last_name as residentLastName, \n" +
            "    primep.first_name AS primaryPractitionerFirstName,\n" +
            "    primep.middle_name AS primaryPractitionerMiddleName,\n" +
            "    primep.last_name AS primaryPractitionerLastName,\n" +
            "    pcdr.first_name AS primaryCarePhysicianFirstName,\n" +
            "    pcdr.middle_name AS primaryCarePhysicianMiddleName,\n" +
            "    pcdr.last_name AS primaryCarePhysicianLastName,\n" +
            "    rx.delivered_on AS deliveredOn,\n" +
            "    clu.first_name AS clericalUserFirstName,\n" +
            "    clu.middle_name AS clericalUserMiddleName,\n" +
            "    clu.last_name AS clericalUserLastName,\n" +
            "    th.first_name AS therapistFirstName,\n" +
            "    th.middle_name AS therapistMiddleName,\n" +
            "    th.last_name AS therapistLastName,\n" +
            "    f.name AS facilityName,\n" +
            "    CASE WHEN (c.id is not null) THEN true ELSE false END AS billed,\n" +
            "    rx.active AS active, \n" +
            "    rx.archived AS archived, \n" +
            " 	 IF(latest_unauth_after_auth.prescription_id IS NULL\n" +
            "            AND latest_auth.prescription_id IS NULL,\n" +
            "        1,\n" +
            "        IF(latest_unauth_after_auth.prescription_id IS NOT NULL,\n" +
            "            0,\n" +
            "            1)) AS hasHCPCSAuthorization, \n" +
            "    rx.delivery_location AS deliveryLocation,\n" +
            "    CASE \n" +
            "        WHEN rx.delivery_location = 'primary_branch' THEN CONCAT('Primary Branch: ', COALESCE(pb.name, ''))\n" +
            "        WHEN rx.delivery_location = 'prescription_branch' THEN CONCAT('Prescription Branch: ', COALESCE(b.name, ''))\n" +
            "        WHEN rx.delivery_location = 'patient_address' THEN CONCAT('Patient Address: ', COALESCE(p.address_label, ''))\n" +
            "        WHEN rx.delivery_location = 'patient_alternate_address' THEN CONCAT('Patient Alternate Address: ', COALESCE(p.address_label2, ''))\n" +
            "        WHEN rx.delivery_location = 'other' THEN UPPER(rx.delivery_location)\n" +
            "        ELSE COALESCE(dl.name, '') \n" +
            "    END AS deliveryLocationName,\n" +                                                                           
            "    CASE \n" +
            "        WHEN rx.delivery_location = 'primary_branch' THEN REGEXP_REPLACE(CONCAT(COALESCE(pb.street_address, ''), ', ', COALESCE(pb.city, ''), ', ', COALESCE(pb.state, ''), ' ', COALESCE(pb.zipcode, '')), ', ,', '')\n" +
            "        WHEN rx.delivery_location = 'prescription_branch' THEN REGEXP_REPLACE(CONCAT(COALESCE(b.street_address, ''), ', ', COALESCE(b.city, ''), ', ', COALESCE(b.state, ''), ' ', COALESCE(b.zipcode, '')), ', ,', '')\n" +
            "        WHEN rx.delivery_location = 'patient_address' THEN REGEXP_REPLACE(CONCAT(COALESCE(p.street_address, ''), ', ', COALESCE(p.city, ''), ', ', COALESCE(p.state, ''), ' ', COALESCE(p.zipcode, '')), ', ,', '')\n" +
            "        WHEN rx.delivery_location = 'patient_alternate_address' THEN REGEXP_REPLACE(CONCAT(COALESCE(p.street_address2, ''), ', ', COALESCE(p.city2, ''), ', ', COALESCE(p.state2, ''), ' ', COALESCE(p.zipcode2, '')), ', ,', '')\n" +
            "        WHEN rx.delivery_location = 'other' THEN COALESCE(rx.delivery_location_address, '')\n" +
            "        ELSE REGEXP_REPLACE(CONCAT(COALESCE(dl.street_address, ''), ', ', COALESCE(dl.city, ''), ', ', COALESCE(dl.state, ''), ' ', COALESCE(dl.zipcode, '')), ', ,', ''                   )\n" +
            "        END AS deliveryLocationAddress\n" +
            "FROM\n" +
            "    prescription rx\n" +
            "        JOIN\n" +
            "    patient p ON rx.patient_id = p.id\n" +
            "        JOIN\n" +
            "    device_type dt ON rx.device_type_id = dt.id\n" +
            "        LEFT JOIN\n" +
            "    claim c ON c.prescription_id = rx.id\n" +
            "        LEFT JOIN\n" +
            "    branch b ON rx.branch_id = b.id\n" +
            "        LEFT JOIN\n" +
            "    branch pb ON p.primary_branch_id = pb.id\n" +
            "        LEFT JOIN\n" +
            "    delivery_location dl ON rx.delivery_location = dl.id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_status ns ON rx.nymbl_status_id = ns.id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_status pds ON rx.physician_documentation_status_id = pds.id\n" +
            "         LEFT JOIN" +
            "    nymbl_master.user res on res.id = rx.resident_id  \n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user primep ON p.primary_practitioner_id = primep.id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user tp ON rx.treating_practitioner_id = tp.id\n" +
            "        LEFT JOIN\n" +
            "    physician referring ON rx.referring_physician_id = referring.id\n" +
            "        LEFT JOIN\n" +
            "    physician pcdr ON rx.primary_care_physician_id = pcdr.id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user createdBy ON rx.created_by_id = createdBy.id \n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user clu ON rx.clerical_user_id = clu.id\n" +
            "        LEFT JOIN\n" +
            "   therapist th ON rx.therapist_id = th.id\n" +
            "        LEFT JOIN \n" +
            "    facility f ON rx.facility_id = f.id\n" +
            " LEFT JOIN\n" +
            "    (SELECT \n" +
            "        hs.prescription_id, MAX(hs.created_at) AS latest_auth_time\n" +
            "    FROM\n" +
            "        hcpcs_selection hs\n" +
            "    WHERE\n" +
            "        hs.authorized = 1\n" +
            "    GROUP BY hs.prescription_id) AS latest_auth ON rx.id = latest_auth.prescription_id\n" +
            "        LEFT JOIN\n" +
            "    (SELECT \n" +
            "        hs1.prescription_id,\n" +
            "            MAX(hs1.created_at) AS latest_unauth_time\n" +
            "    FROM\n" +
            "        hcpcs_selection hs1\n" +
            "    WHERE\n" +
            "        hs1.initial_edit = 0\n" +
            "            AND hs1.authorized = 0\n" +
            "    GROUP BY hs1.prescription_id) AS latest_unauth_after_auth ON rx.id = latest_unauth_after_auth.prescription_id\n" +
            "        AND (latest_auth.latest_auth_time IS NULL\n" +
            "        OR latest_unauth_after_auth.latest_unauth_time > latest_auth.latest_auth_time) \n" +
            "   LEFT JOIN (SELECT \n" +
            "        MAX(nsh.updated_at) AS updated_at,\n" +
            "            nsh.prescription_id,\n" +
            "            nsh.nymbl_status_id\n" +
            "    FROM\n" +
            "        nymbl_status_history nsh\n" +
            "    GROUP BY nsh.prescription_id , nsh.nymbl_status_id) AS nsh ON rx.id = nsh.prescription_id\n" +
            "        AND nsh.nymbl_status_id = rx.nymbl_status_id \n";

    public static final String populatePrescriptionSummaryV2_WHERE = "WHERE\n" +
            "    rx.active = :isActive\n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR rx.branch_id = :branchId) \n" +
            "AND (:practitionerId IS NULL OR rx.treating_practitioner_id = :practitionerId) \n" +
            "AND (:assignedToId IS NULL OR rx.clerical_user_id = :assignedToId) \n" +
            "AND (:statusId IS NULL OR rx.nymbl_status_id = :statusId) \n" +
            "AND (:practitionerId Is NULL or rx.treating_practitioner_id = :practitionerId) \n" +
            "AND (:careExtenderId IS NULL or rx.resident_id = :careExtenderId) \n" +
            "AND (:physicianDocumentationStatusId IS NULL OR rx.physician_documentation_status_id = :physicianDocumentationStatusId) \n" +
            "ORDER BY rxId DESC";

    public static final String populatePrescriptionSummaryV2 = populatePrescriptionSummaryV2_SELECT + populatePrescriptionSummaryV2_WHERE;

    public static final String populateAllPrescriptionSummaryV2 = populatePrescriptionSummaryV2_SELECT + "ORDER BY rxId DESC";

    public static final String populateMultiTenantPrescriptionSummaryV2 = "INSERT INTO multitenant.prescription_summary (\n" +
            "    tenant,\n" +
            "    rx_id,\n" +
            "    projected_delivery_date,\n" +
            "    pod_signed_date,\n" +
            "    surgery_date,\n" +
            "    patient_id,\n" +
            "    patient_name,\n" +
            "    hold_until,\n" +
            "    created_at,\n" +
            "    rx_aging,\n" +
            "    rx_category,\n" +
            "    rx_sub_category,\n" +
            "    claim_id,\n" +
            "    created_by_Name,\n" +
            "    device_type,\n" +
            "    device_type_id,\n" +
            "    device_name,\n" +
            "    rx_date,\n" +
            "    rx_branch_name,\n" +
            "    rx_branch_id,\n" +
            "    rx_status,\n" +
            "    rx_status_updated_date,\n" +
            "    physician_documentation_status,\n" +
            "    treating_practitioner_name,\n" +
            "    referring_physician_name,\n" +
            "    resident_name,\n" +
            "    primary_care_physician_name,\n" +
            "    delivered_on,\n" +
            "    clerical_user_name,\n" +
            "    therapist_name,\n" +
            "    facility_name,\n" +
            "    billed,\n" +
            "    active,\n" +
            "    cost_of_goods,\n" +
            "    allowable_total,\n" +
            "    billable_total,\n" +
            "    rx_l_codes,\n" +
            "    insurance,\n" +
            "    fabrication_steps,\n" +
            "    incomplete_sections,\n" +
            "    incomplete_tasks,\n" +
            "    missing_documents,\n" +
            "    first_appointment_date_time,\n" +
            "    last_appointment_date_time,\n" +
            "    next_appointment_date_time,\n" +
            "    latest_comment,\n" +
            "    latest_comment_date_time,\n" +
            "    latest_comment_by," +
            "    delivery_location_address) values (\n" +

            "    :tenant,\n" +
            "    :rxId,\n" +
            "    :projectedDeliveryDate,\n" +
            "    :podSignedDate,\n" +
            "    :surgeryDate,\n" +
            "    :patientId,\n" +
            "    :patientName,\n" +
            "    :holdUntil,\n" +
            "    :createdAt,\n" +
            "    :rxAging,\n" +
            "    :rxCategory,\n" +
            "    :rxSubCategory,\n" +
            "    :claimId,\n" +
            "    :createdByName,\n" +
            "    :deviceType,\n" +
            "    :deviceTypeId,\n" +
            "    :deviceName,\n" +
            "    :rxDate,\n" +
            "    :rxBranchName,\n" +
            "    :rxBranchId,\n" +
            "    :rxStatus,\n" +
            "    :rxStatusUpdatedDate,\n" +
            "    :physicianDocumentationStatus,\n" +
            "    :treatingPractitionerName,\n" +
            "    :referringPhysicianName,\n" +
            "    :residentName,\n" +
            "    :primaryCarePhysicianName,\n" +
            "    :deliveredOn,\n" +
            "    :clericalUserName,\n" +
            "    :therapistName,\n" +
            "    :facilityName,\n" +
            "    :billed,\n" +
            "    :active,\n" +
            "    :cogs,\n" +
            "    :allowableTotal,\n" +
            "    :billableTotal,\n" +
            "    :rxLCodes,\n" +
            "    :insuranceVerifications,\n" +
            "    :fabricationSteps,\n" +
            "    :incompleteSections,\n" +
            "    :incompleteTasks,\n" +
            "    :missingDocuments,\n" +
            "    :firstAppointmentDateTime,\n" +
            "    :lastAppointmentDateTime,\n" +
            "    :nextAppointmentDateTime,\n" +
            "    :latestComment,\n" +
            "    :latestCommentDateTime,\n" +
            "    :latestCommentBy," +
            "    :deliveryLocationAddress)\n";

    public static final String populateMultiTenantPurchasingHistory = "INSERT INTO multitenant.purchasing_history (\n" +
            "    tenant,\n" +
            "    branch_name,\n" +
            "    branch_address,\n" +
            "    branch_state,\n" +
            "    branch_city,\n" +
            "    branch_zip,\n" +
            "    delivery_location,\n" +
            "    ordered_by_user,\n" +
            "    po_number,\n" +
            "    po_status,\n" +
            "    po_date_created,\n" +
            "    po_date_ordered,\n" +
            "    item_number,\n" +
            "    item_name,\n" +
            "    item_description,\n" +
            "    part_number,\n" +
            "    sku_number,\n" +
            "    category,\n" +
            "    vendor,\n" +
            "    manufacturer,\n" +
            "    item_cost,\n" +
            "    quantity,\n" +
            "    total_cogs,\n" +
            "    sales_tax,\n" +
            "    shipping_charges,\n" +
            "    add_charges,\n" +
            "    discount,\n" +
            "    po_grand_total,\n" +
            "    rx_id,\n" +
            "    device_type,\n" +
            "    device_type_category,\n" +
            "    patient_id,\n" +
            "    patient_name,\n" +
            "    treating_practioner_name) values (\n" +

            "    :tenant,\n" +
            "    :branchName,\n" +
            "    :branchAddress,\n" +
            "    :branchState,\n" +
            "    :branchCity,\n" +
            "    :branchZip,\n" +
            "    :deliveryLocation,\n" +
            "    :orderedByUser,\n" +
            "    :poNumber,\n" +
            "    :poStatus,\n" +
            "    :poDateOrdered,\n" +
            "    :poDateCreated,\n" +
            "    :itemNumber,\n" +
            "    :itemName,\n" +
            "    :itemDescription,\n" +
            "    :partNumber,\n" +
            "    :skuNumber,\n" +
            "    :category,\n" +
            "    :vendor,\n" +
            "    :manufacturer,\n" +
            "    :itemCost,\n" +
            "    :quantity,\n" +
            "    :totalCogs,\n" +
            "    :salesTax,\n" +
            "    :shippingCharges,\n" +
            "    :addCharges,\n" +
            "    :discount,\n" +
            "    :poGrandTotal,\n" +
            "    :rxId,\n" +
            "    :deviceType,\n" +
            "    :deviceTypeCategory,\n" +
            "    :patientId,\n" +
            "    :patientName,\n" +
            "    :treatingPractionerName)\n";

    public static final String populateMultiTenantGeneralLedger = """
                        INSERT INTO multitenant.general_ledger(
                        tenant,
                        category_id,
                        gl_date,
                        gl_applied_date,
                        gl_year,
                        gl_period,
                        gl_account,
                        category,
                        sub_category,
                        amount,
                        abs_amount,
                        patient_id,
                        patient,
                        prescription_id,
                        claim_id,
                        branch_id,
                        branch,
                        patient_branch_id,
                        patient_branch,
                        prescription_branch_id,
                        prescription_branch,
                        facility_id,
                        facility,
                        prescription_l_code_id,
                        l_code_id,
                        l_code,
                        insurance_verification_id,
                        insurance_verification_l_code_id,
                        payment_id,
                        applied_payment_id,
                        applied_payment_l_code_id,
                        payer_type,
                        payment_type,
                        check_number,
                        insurance_company_id,
                        insurance_company,
                        carrier_type,
                        patient_insurance_id,
                        device_type_id,
                        device_type_category,
                        device_type,
                        treating_practitioner_id,
                        treating_practitioner,
                        primary_care_physician_id,
                        primary_care_physician,
                        therapist_id,
                        therapist,
                        referring_physician_id,
                        referring_physician,
                        claim_submission_date,
                        date_of_service,
                        prescription_date,
                        payment_date,
                        deposit_date,
                        applied_date,
                        rx_active,
                        patient_active,
                        last_updated) VALUES (
                        :tenant,
                        :categoryId,
                        :glDate,
                        :glAppliedDate,
                        :glYear,
                        :glPeriod,
                        :glAccount,
                        :category,
                        :subCategory,
                        :amount,
                        :absAmount,
                        :patientId,
                        :patient,
                        :prescriptionId,
                        :claimId,
                        :branchId,
                        :branch,
                        :patientBranchId,
                        :patientBranch,
                        :prescriptionBranchId,
                        :prescriptionBranch,
                        :facilityId,
                        :facility,
                        :prescriptionLCodeId,
                        :lCodeId,
                        :lCode,
                        :insuranceVerificationId,
                        :insuranceVerificationLCodeId,
                        :paymentId,
                        :appliedPaymentId,
                        :appliedPaymentLCodeId,
                        :payerType,
                        :paymentType,
                        :checkNumber,
                        :insuranceCompanyId,
                        :insuranceCompany,
                        :carrierType,
                        :patientInsuranceId,
                        :deviceTypeId,
                        :deviceTypeCategory,
                        :deviceType,
                        :treatingPractitionerId,
                        :treatingPractitioner,
                        :primaryCarePhysicianId,
                        :primaryCarePhysician,
                        :therapistId,
                        :therapist,
                        :referringPhysicianId,
                        :referringPhysician,
                        :claimSubmissionDate,
                        :dateOfService,
                        :prescriptionDate,
                        :paymentDate,
                        :depositDate,
                        :appliedDate,
                        :rxActive,
                        :patientActive,
                        :lastUpdated)
            """;

    public static final String rxSummaryNotes = "SELECT rx1.id AS rxId, n1.note AS latestComment, n1.created_at AS latestCommentDate, CONCAT(nu.first_name, ' ', nu.last_name) AS latestCommentBy  \n" +
			"FROM note n1\n" +
			"JOIN ( SELECT MAX(n2.id) as noteId, rx2.id AS rxId\n" +
			"			FROM note n2\n" +
			"			JOIN prescription rx2 ON n2.prescription_id = rx2.id\n" +
			"       	WHERE n2.note_type = 'patient_summary'\n" +
			"			GROUP BY rxId ) as mxNote ON mxNote.noteId = n1.id\n" +
			"JOIN prescription rx1 ON n1.prescription_id = rx1.id\n" +
			"LEFT JOIN claim c ON c.prescription_id = rx1.id\n" +
			"LEFT JOIN nymbl_master.user nu ON n1.created_by_id = nu.id\n" +
			"WHERE rx1.active = :isActive\n" +
			"	AND (:includeSentToBilling = 0\n" +
			"	AND c.id IS NULL\n" +
			"	OR :includeSentToBilling = 1)\n" +
			"	AND (:branchId = 0 OR :branchId IS NULL\n" +
			"	OR rx1.branch_id = :branchId)";

    public static final String rxSummaryTaskCount = "SELECT \n" +
            "    COUNT(t.id) as taskCount, rx.id AS rxId\n" +
            "FROM\n" +
            "    prescription rx\n" +
            "         JOIN\n" +
            "    task t ON t.prescription_id = rx.id\n" +
            "        LEFT JOIN\n" +
            "    claim c ON rx.id = c.prescription_id\n" +
            "WHERE\n" +
            "    t.completed = 0 AND \n" +
            "    rx.active = :isActive\n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR rx.branch_id = :branchId)\n" +
            "GROUP BY rx.id";

    public static final String rxSummarySections = "SELECT \n" +
            "    rx.id AS rxId,\n" +
            "    ps.section AS section,\n" +
            "    ps.locked AS locked,\n" +
            "    ps.updated_at AS updatedAt\n" +
            "FROM\n" +
            "    prescription_section ps\n" +
            "        JOIN\n" +
            "    prescription rx ON ps.prescription_id = rx.id\n" +
            "        LEFT JOIN\n" +
            "    claim c ON c.prescription_id = rx.id\n" +
            "WHERE\n" +
            "    rx.active = :isActive\n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR rx.branch_id = :branchId) ";

    public static final String rxSummaryFabSteps = "SELECT \n" +
            "    rx.id as rxId, cls.name, cls.status, cls.updated_at AS updatedAt\n" +
            "FROM\n" +
            "    prescription rx\n" +
            "        JOIN\n" +
            "    checklist_step cls ON cls.prescription_id = rx.id\n" +
            "        AND cls.type = 'wip_fabrication'\n" +
            "        LEFT JOIN\n" +
            "    claim c ON c.prescription_id = rx.id\n" +
            "WHERE\n" +
            "    rx.active = :isActive\n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR rx.branch_id = :branchId) ";

    public static final String rxSummaryInsuranceVerificationHCPC = "SELECT \n" +
            "    rx.id AS rxId,\n" +
            "    iv.carrier_type AS carrierType,\n" +
            "    ic.name AS insuranceCompanyName,\n" +
            "    iv.referral_number AS referralNumber,\n" +
            "    iv.auth_initiation_date AS authInitiationDate,\n" +
            "    iv.auth_approval_date AS authApprovalDate,\n" +
            "    iv.expiration_date AS expirationDate,\n" +
            "    SUM(COALESCE(ivlc.total_allowable, 0.00)) AS allowableTotal,\n" +
            "    SUM(COALESCE(ivlc.total_charge, 0.00)) AS billableTotal,\n" +
            "    ic.timely_filing_days AS timelyFilingDays \n" +
            "FROM\n" +
            "    prescription rx\n" +
            "        JOIN\n" +
            "    patient_insurance pi ON pi.id = rx.patient_insurance_id\n" +
            "        JOIN\n" +
            "    insurance_company ic ON pi.insurance_company_id = ic.id\n" +
            "        JOIN\n" +
            "    insurance_verification iv ON rx.id = iv.prescription_id\n" +
            "        AND iv.patient_insurance_id = pi.id\n" +
            "        JOIN\n" +
            "    insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id\n" +
            "    LEFT JOIN claim c on rx.id = c.prescription_id\n" +
            "WHERE\n" +
            "         rx.active = :isActive\n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR rx.branch_id = :branchId)\n" +
            "GROUP BY rxId , carrierType , insuranceCompanyName , referralNumber , authApprovalDate , expirationDate , authInitiationDate";

    public static final String rxSummaryInsuranceVerification = "SELECT \n" +
            "    rx.id AS rxId,\n" +
            "    iv.carrier_type AS carrierType,\n" +
            "    ic.name AS insuranceCompanyName,\n" +
            "    iv.referral_number AS referralNumber, \n" +
            "    ic.timely_filing_days AS timelyFilingDays \n" +
            "FROM\n" +
            "    prescription rx\n" +
            "        JOIN\n" +
            "    insurance_verification iv ON rx.id = iv.prescription_id\n" +
            "        JOIN\n" +
            "    patient_insurance pi ON iv.patient_insurance_id = pi.id\n" +
            "        JOIN\n" +
            "    insurance_company ic ON pi.insurance_company_id = ic.id\n" +
            "    LEFT JOIN claim c on rx.id = c.prescription_id\n" +
            "WHERE\n" +
            "   rx.active = :isActive\n" +
            "        AND pi.active = true \n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR rx.branch_id = :branchId)";

    public static final String rxSummaryRxDocs = "SELECT \n" +
            "    f.file_type_id as fileTypeId, f.id as fileId, rx.id AS rxId\n" +
            "FROM\n" +
            "    `file` f\n" +
            "        JOIN\n" +
            "    prescription rx ON rx.id = f.prescription_id\n" +
            "        LEFT JOIN\n" +
            "    claim c ON rx.id = c.prescription_id\n" +
            "WHERE\n" +
            "    rx.active = :isActive\n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR rx.branch_id = :branchId) ";

    public static final String rxSummaryRequiredDeviceDocs = "SELECT rx.id AS rxId,\n" +
            "dtft.device_type_id AS deviceTypeId, \n" +
            "ft.id as fileTypeId, \n" +
            "ft.key as fileTypeKey, \n" +
            "ft.name as fileTypeName, \n" +
            "ft.color as fileTypeColor, \n" +
            "ft.not_editable AS notEditable, \n" +
            "ft.is_physician_documentation AS isPhysicianDocumentation \n" +
            "FROM prescription rx\n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id\n" +
            "JOIN device_type_file_type dtft ON dtft.device_type_id = dt.id\n" +
            "JOIN file_type ft ON dtft.file_type_id = ft.id\n" +
            "LEFT JOIN claim c ON rx.id = c.prescription_id\n" +
            "WHERE\n" +
            "rx.active = :isActive\n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR rx.branch_id = :branchId)";

    public static final String rxSummaryPLCodes = "SELECT \n" +
            "    rx.id AS rxId,\n" +
            "    plc.id AS plcId,\n" +
            "    lc.name,\n" +
            "    lc.friendly_description AS friendlyDescription,\n" +
            "    plc.quantity\n" +
            "FROM\n" +
            "    prescription_l_code plc\n" +
            "        JOIN\n" +
            "    prescription rx ON plc.prescription_id = rx.id\n" +
            "        JOIN\n" +
            "    l_code lc ON plc.l_code_id = lc.id\n" +
            "        LEFT JOIN\n" +
            "    claim c ON rx.id = c.prescription_id\n" +
            "WHERE\n" +
            "    rx.active = :isActive\n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR rx.branch_id = :branchId) ";

    public static final String rxSummaryAppointments = "SELECT \n" +
            "    rx.id AS rxId,\n" +
            "    pt.id AS patientId,\n" +
            "    appt.id AS appointmentId,\n" +
            "    DATE_FORMAT(appt.start_datetime, '%Y-%m-%dT%TZ') as startDateTime,\n" +
            "    appt.`status` AS `status`,\n" +
            "    apptType.id as apptTypeId,\n" +
            "    apptType.name AS apptType,\n" +
            "    apptType.color as apptTypeColor,\n" +
            "    apptType.appointment_type_status_id as apptTypeStatusId,\n" +
            "    appt.prescription_id AS apptRxId,\n" +
            "    appt.prescription_two_id AS apptRxTwoId,\n" +
            "    appt.branch_id as branchId\n" +
            "FROM\n" +
            "    appointment appt\n" +
            "        JOIN\n" +
            "    appointment_type apptType ON apptType.id = appt.appointment_type_id\n" +
            "        JOIN\n" +
            "    patient pt ON appt.patient_id = pt.id\n" +
            "        JOIN\n" +
            "    prescription rx ON pt.id = rx.patient_id\n" +
            "        AND (appt.prescription_id = rx.id\n" +
            "        OR rx.id = appt.prescription_two_id)\n" +
            "        LEFT JOIN\n" +
            "    claim c ON c.prescription_id = rx.id\n" +
            "WHERE\n" +
            "    appt.status NOT IN ('cancelled' , 'no_show', 'rejected', 'rescheduled')\n" +
            "        AND rx.active = :isActive\n" +
            "        AND (:includeSentToBilling = 0\n" +
            "        AND c.id IS NULL\n" +
            "        OR :includeSentToBilling = 1)\n" +
            "        AND (:branchId = 0 OR :branchId IS NULL\n" +
            "        OR appt.branch_id = :branchId)";

    public static final String rxSummaryPOTotalCostOfGoods =
            "SELECT rx.id AS rxId, SUM(poi.total_cost) AS cogs FROM prescription AS rx JOIN purchase_order_item AS poi WHERE rx.id = poi.prescription_id AND (:branchId = 0 OR :branchId IS NULL OR rx.branch_id = :branchId) GROUP BY rx.id";

    public static final String dailyCloseV2 = "SELECT DISTINCT\n" +
            "    pb.name AS patientBranchName,\n" +
            "    rxb.name AS rxBranchName,\n" +
            "    bb.name AS billingBranchName,\n" +
//            "    NULL AS apptBranchName,\n" +
            "    pt.id AS patientId,\n" +
            "    pt.first_name AS patientFirstName,\n" +
            "    pt.last_name AS patientLastName,\n" +
            "    tp.first_name AS treatingPractitionerFirstName,\n" +
            "    tp.last_name AS treatingPractitionerLastName,\n" +
            "    CASE\n" +
            "        WHEN\n" +
            "            (p.payer_type = 'adjustment'\n" +
            "                OR p.payer_type = 'insurance_company')\n" +
            "        THEN\n" +
            "            ic.name\n" +
            "        ELSE 'Patient'\n" +
            "    END AS payerName,\n" +
            "    p.payment_type AS payerType,\n" +
            "    u.first_name AS createdByFirstName,\n" +
            "    u.last_name AS createdByLastName,\n" +
            "    p.created_at AS createdAt,\n" +
            "    p.date AS paymentDate,\n" +
            "    p.id AS paymentId,\n" +
            "    p.payment_type AS paymentType,\n" +
            "    p.description AS paymentDescription,\n" +
            "    p.check_number AS checkNumber,\n" +
            "    p.deposit_date AS paymentDepositDate,\n" +
            "    x.paymentAmount AS paymentAmount,\n" +
            "    x.adjustmentAmount AS adjustmentAmount,\n" +
            "    apb.first_name AS appliedByFirstName,\n" +
            "    apb.last_name AS appliedByLastName,\n" +
            "    a.operation AS operation,\n" +
            "    a.withdraw AS withdraw,\n" +
//            "    0 AS paymentTypeOrigin,\n" +
            "    COALESCE(x.paymentAmount, 0.00) AS amount,\n" +
            "    COALESCE(x.adjustmentAmount, 0.00) AS adjustment,\n" +
            "    CASE\n" +
            "        WHEN p.payment_type = 'cash' THEN COALESCE(x.paymentAmount, 0.00)\n" +
            "        ELSE 0.00\n" +
            "    END AS cashAmount,\n" +
            "    CASE\n" +
            "        WHEN\n" +
            "            (p.payment_type = 'patient_check'\n" +
            "                OR p.payment_type = 'insurance_payment_check')\n" +
            "        THEN\n" +
            "            COALESCE(x.paymentAmount, 0.00)\n" +
            "        ELSE 0.00\n" +
            "    END AS checkAmount,\n" +
            "    CASE\n" +
            "        WHEN\n" +
            "            (p.payment_type = 'patient_credit_card'\n" +
            "                OR p.payment_type = 'insurance_payment_credit_card'\n" +
            "                OR p.payment_type = 'stripe')\n" +
            "        THEN\n" +
            "            COALESCE(x.paymentAmount, 0.00)\n" +
            "        ELSE 0.00\n" +
            "    END AS creditAmount,\n" +
            "    CASE\n" +
            "        WHEN p.payment_type = 'autopost' THEN COALESCE(x.paymentAmount, 0.00)\n" +
            "        ELSE 0.00\n" +
            "    END AS eraAmount,\n" +
            "    CASE\n" +
            "        WHEN p.payment_type = 'patient_ach' THEN COALESCE(x.paymentAmount, 0.00)\n" +
            "        ELSE 0.00\n" +
            "    END AS achAmount,\n" +
            "    CASE\n" +
            "        WHEN\n" +
            "            (p.payment_type = 'insurance_payment_electronic'\n" +
            "                OR p.payment_type = 'patient_electronic')\n" +
            "        THEN\n" +
            "            COALESCE(x.paymentAmount, 0.00)\n" +
            "        ELSE 0.00\n" +
            "    END AS electronicAmount,\n" +
            "    x.amountApplied AS paymentApplied,\n" +
            "    x.adjustmentApplied AS adjustmentApplied,\n" +
            "    ap2.applied_date AS appliedDate,\n" +
            "    x.paymentAmount - x.amountApplied AS paymentUnapplied,\n" +
            "    x.adjustmentAmount - x.adjustmentApplied AS adjustmentUnapplied\n" +
            "FROM\n" +
            "    payment p\n" +
            "        JOIN\n" +
            "    (SELECT DISTINCT\n" +
            "        p.id AS paymentId,\n" +
            "            CASE\n" +
            "                WHEN adj.withdraw = 1 THEN MAX(aplc.adjustment_type)\n" +
            "                ELSE NULL\n" +
            "            END AS adjustmentType,\n" +
            "            CASE\n" +
            "                WHEN adj.withdraw = 1 THEN MAX(p.adjustment)\n" +
            "                ELSE 0.00\n" +
            "            END AS adjustmentAmount,\n" +
            "            CASE\n" +
            "                WHEN adj.withdraw = 1 THEN SUM(aplc.adjustment)\n" +
            "                ELSE 0.00\n" +
            "            END AS adjustmentApplied,\n" +
            "            MAX(p.amount) AS paymentAmount,\n" +
            "            SUM(aplc.amount) AS amountApplied\n" +
            "    FROM\n" +
//            "        payment p\n" +
            "        (SELECT p.* FROM payment p LEFT JOIN applied_payment ap ON p.id = ap.payment_id\n" +
            "             LEFT JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id\n" +
            "             LEFT JOIN adjustment adj on aplc.adjustment_type = adj.id\n" +
            "             WHERE aplc.adjustment_type IS NULL OR (aplc.adjustment_type IS NOT NULL AND adj.withdraw IS TRUE)\n" +
            "             GROUP by p.id) As p\n" +
            "    LEFT JOIN applied_payment ap ON p.id = ap.payment_id\n" +
            "    LEFT JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id\n" +
            "    LEFT JOIN adjustment adj ON p.adjustment_id = adj.id\n" +
            "    WHERE\n" +
            "        ((:dateOption = 'payment'\n" +
            "            AND p.date BETWEEN :startDate AND :endDate)\n" +
            "            OR (:dateOption <> 'payment'\n" +
            "            AND p.deposit_date BETWEEN :startDate AND :endDate))\n" +
            "            AND (('%' = :payerType\n" +
            "            AND p.payer_type LIKE :payerType)\n" +
            "            OR ('patient%' = :payerType\n" +
            "            AND p.payer_type LIKE :payerType)\n" +
            "            OR ('insurance%' = :payerType\n" +
            "            AND p.payer_type LIKE :payerType))\n" +
            "    GROUP BY p.id) AS x ON x.paymentId = p.id\n" +
            "        LEFT JOIN\n" +
            "    (SELECT x.* FROM applied_payment x JOIN (SELECT payment_id, MAX(id) ap_id FROM applied_payment GROUP BY payment_id) y\n" +
            "     ON x.payment_id = y.payment_id AND x.id = y.ap_id) ap2 ON p.id = ap2.payment_id\n" +
            "        LEFT JOIN\n" +
            "    claim c ON p.claim_id = c.id\n" +
            "        LEFT JOIN\n" +
            "    patient pt ON p.patient_id = pt.id\n" +
            "        LEFT JOIN\n" +
            "    (SELECT rx.id rx_id, rx.branch_id, rx.treating_practitioner_id, p.id payment_id FROM prescription rx \n" +
            "           JOIN claim c ON c.prescription_id = rx.id JOIN payment p ON c.id = p.claim_id WHERE c.id <> 0 UNION \n" +
            "     SELECT rx.id rx_id, rx.branch_id, rx.treating_practitioner_id, p.id payment_id FROM prescription rx \n" +
            "           JOIN payment p ON rx.id = p.prescription_id) rx ON p.id = rx.payment_id \n" +
            "        LEFT JOIN\n" +
            "    branch pb ON pt.primary_branch_id = pb.id\n" +
            "        LEFT JOIN\n" +
            "    branch rxb ON rx.branch_id = rxb.id\n" +
            "        LEFT JOIN\n" +
            "    branch bb ON c.billing_branch_id = bb.id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user tp ON rx.treating_practitioner_id = tp.id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user u ON p.created_by_id = u.id\n" +
            "        LEFT JOIN\n" +
            "    nymbl_master.user apb ON ap2.applied_by = apb.id\n" +
            "        LEFT JOIN\n" +
            "    insurance_company ic ON p.insurance_company_id = ic.id\n" +
            "        LEFT JOIN\n" +
            "    adjustment a ON p.adjustment_id = a.id \n" +
            " Where (:branchId = 0 OR :branchId IS NULL " +
            " OR pt.primary_branch_id = :branchId)";

    public static final String dailyCloseV2PatientClaimSubQuery = "SELECT pb.name AS patientBranchName, rxb.name AS rxBranchName, bb.name AS billingBranchName,\n" +
            "    ap.payment_id AS paymentId, c.id AS claimId, rx.id AS rxId, pi.patient_id AS patientId,\n" +
            "    apx.applied_amount AS paymentApplied, pt.first_name AS patientFirstName, pt.last_name AS patientLastName,\n" +
            "    tp.first_name AS treatingPractitionerFirstName, tp.last_name AS treatingPractitionerLastName,\n" +
            "    apusr.first_name AS appliedByFirstName, apusr.last_name AS appliedByLastName, ap.applied_date AS appliedDate\n" +
            "    FROM (SELECT aplc.applied_payment_id, sum(aplc.amount) AS applied_amount FROM applied_payment_l_code aplc GROUP BY applied_payment_id) apx\n" +
            "    JOIN applied_payment ap ON apx.applied_payment_id = ap.id\n" +
            "    JOIN claim c ON ap.claim_id = c.id JOIN patient_insurance pi ON c.patient_insurance_id = pi.id\n" +
            "    LEFT JOIN patient pt ON pi.patient_id = pt.id\n" +
            "    LEFT JOIN prescription rx ON c.prescription_id = rx.id\n" +
            "    LEFT JOIN branch pb ON pt.primary_branch_id = pb.id\n" +
            "    LEFT JOIN branch rxb ON rx.branch_id = rxb.id\n" +
            "    LEFT JOIN branch bb ON c.billing_branch_id = bb.id\n" +
            "    LEFT JOIN nymbl_master.user tp ON rx.treating_practitioner_id = tp.id\n" +
            "    LEFT JOIN nymbl_master.user apusr ON ap.applied_by = apusr.id\n" +
            "    ORDER BY paymentId, claimId\n";

    public static final String loadAutoPostPatients = "SELECT \n" +
            "`app`.`id` AS `id`,\n" +
            "`app`.`auto_post_id` AS `autoPostId`,\n" +
            "`app`.`patient_id` AS `patientId`,\n" +
            "`p`.`first_name` AS `patientFirstName`,\n" +
            "`p`.`last_name` AS `patientLastName`,\n" +
            "`b`.`name` AS `patientPrimaryBranchName`,\n" +
            "`app`.`icn` AS `icn`,\n" +
            "`app`.`claim_id` AS `claimId`,\n" +
            "`c`.`prescription_id` AS `claimPrescriptionId`,\n" +
            "`c`.`patient_insurance_id` AS `claimPatientInsuranceId`,\n" +
            "`app`.`claim_billed` AS `claimBilled`,\n" +
            "`app`.`claim_paid` AS `claimPaid`,\n" +
            "`app`.`adjustment` AS `adjustment`,\n" +
            "`app`.`transaction_message` AS `transactionMessage`,\n" +
            "`app`.`status` AS `status`,\n" +
            "CASE WHEN `ic`.`self_pay` IS NULL THEN 0 ELSE `ic`.`self_pay` END AS `selfPay`,\n" +
            "`app`.`claim_status_code` AS `claimStatusCode`,\n" +
            "`c`.`total_claim_balance` AS `totalClaimBalance`,\n" +
            "`c`.`total_pt_responsibility_balance` AS `totalPtResponsibilityBalance`,\n" +
            "CASE WHEN COUNT(`applc`.`id`) > 0 THEN 1 ELSE 0 END AS `missingPlc`,\n" +
            "CASE WHEN COUNT(`apy`.`id`) > 0 THEN 1 ELSE 0 END AS `isApplied`\n" +
            "FROM `auto_post_patient` `app`\n" +
            "LEFT JOIN `applied_payment` `apy` ON `app`.`id` = `apy`.`autopost_patient_id`\n" +
            "LEFT JOIN `auto_post_patient_l_code` `applc` ON `applc`.`auto_post_patient_id` = `app`.`id` AND `applc`.`prescription_l_code_id` IS NULL\n" +
            "LEFT JOIN `auto_post` `ap` ON `app`.`auto_post_id` = `ap`.`id`\n" +
            "LEFT JOIN `claim` `c` ON `app`.`claim_id` = `c`.`id`\n" +
            "LEFT JOIN `patient_insurance` `pi` ON `c`.`patient_insurance_id` = `pi`.`id`\n" +
            "LEFT JOIN `insurance_company` `ic` ON `pi`.`insurance_company_id` = `ic`.`id`\n" +
            "LEFT JOIN `patient` `p` ON `app`.`patient_id` = `p`.`id`\n" +
            "LEFT JOIN `branch` `b` ON `p`.`primary_branch_id` = `b`.`id`\n" +
            "WHERE `app`.`auto_post_id` = :autoPostId\n" +
            "GROUP BY `app`.`id`,`app`.`auto_post_id`,`app`.`patient_id`,`p`.`first_name`,`p`.`last_name`,`b`.`name`,`app`.`icn`,`app`.`claim_id`,`c`.`prescription_id`,`c`.`patient_insurance_id`,`app`.`claim_billed`,`app`.`claim_paid`,`app`.`adjustment`,`app`.`transaction_message`,`app`.`status`,`ic`.`self_pay`";
}
