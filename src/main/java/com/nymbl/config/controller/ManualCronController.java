package com.nymbl.config.controller;

import com.nymbl.config.clearingHouse.ClearingHouseSSH;
import com.nymbl.config.clearingHouse.ClearingHouseSSHv2;
import com.nymbl.config.service.QuicksightExportService;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.interfaces.service.PrescriptionSummaryService;
import com.nymbl.tenant.service.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/api/manual-cron")
public class ManualCronController {

    private final GeneralLedgerService generalLedgerService;
    private final PhysicianService physicianService;
    private final PrescriptionService prescriptionService;
    private final ClearingHouseSSH clearingHouseSSH;
    private final ClearingHouseSSHv2 clearingHouseSSHv2;
    private final QuicksightExportService quicksightExportService;
    private final SystemSettingService systemSettingService;
    private final FeatureFlagService featureFlagService;
    private final PrescriptionSummaryService rxSummaryService;
    private final ClinicalOperationsService clinicalOperationsService;

    @GetMapping(value = "/daily/generalLedger")
    public void runDailyGeneralLedger(HttpServletRequest request) {
        generalLedgerService.runDailyGeneralLedgerHistoryPopulation();
        generalLedgerService.executeStaticGeneralLedgerProcess();
    }

    @GetMapping(value = "/pecos")
    public void verifyPecosForTenant(HttpServletRequest request) {
        physicianService.pecosVerificationUpdate();
    }

    @GetMapping(value = "/daily/clearingHouse")
    public void dailyClearingHouse(HttpServletRequest request) {
        prescriptionService.checkEligibility(DateTime.now().plusDays(1).toDate());
    }

    @GetMapping(value = "/daily/user-permissions")
    public void userInfoByRegion(HttpServletRequest request) {
        quicksightExportService.uploadUserPermissionsCsvS3Quicksight();
    }

    /**
     * This is a duplicate in UploadController.java
     *
     * @param request
     */
    @GetMapping(value = "/daily/submit-claims")
    public void submitClaims(HttpServletRequest request) {
        String dch = systemSettingService.findBySectionAndField("billing", "default_clearing_house").getValue();
        if ("1".equals(dch)) { // 1 = Waystar
            clearingHouseSSH.submitClaimsToClearingHouse();
        } else {
            clearingHouseSSHv2.submitClaimsToClearingHouse();
        }
    }

    @GetMapping(value = "/quicksight/clinical-operations")
    public void quicksightClinicalOperations(HttpServletRequest request) {
        quicksightExportService.uploadClinicalOperationsCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/general-ledger")
    public void quicksightGeneralLedger(HttpServletRequest request) {
        quicksightExportService.uploadGeneralLedgerCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/prescription-summary")
    public void quicksightPrescriptionSummary(HttpServletRequest request) {
        quicksightExportService.uploadPrescriptionSummaryCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/purchasing-history")
    public void quicksightPurchasingHistory(HttpServletRequest request) {
        quicksightExportService.uploadPurchasingHistoryCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/nymbl-status-history")
    public void quicksightNymblStatusHistory(HttpServletRequest request) {
        quicksightExportService.uploadNymblStatusHistoryCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/ai-notes-usage")
    public void quicksightAiNotesUsage(HttpServletRequest request) {
        quicksightExportService.uploadAiNotesUsageCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/asset-user-permissions")
    public void quicksightUserPermissions(HttpServletRequest request) {
        quicksightExportService.uploadAssetUserPermissionsCsvS3Quicksight();
    }

    @GetMapping(value = "/customer/sftp/prescription-summary")
    public ResponseEntity<?> customerSftpRxSummary() {
        String tenant = TenantContext.getCurrentTenant();
        if (featureFlagService.findFeatureFlagByFeature("upload_rx_summary_aws_s3") != null) {
            rxSummaryService.importPrescriptionSummary(tenant);
            rxSummaryService.uploadRxSummaryToAws();
            return ResponseEntity.ok().build();
        } else {
            Map<String, String> response = new HashMap<>();
            response.put("message", "Feature Flag `upload_rx_summary_aws_s3` is not set for " + tenant);
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping(value = "/customer/sftp/clinical-operations")
    public ResponseEntity<?> customerSftpClinicalOperations() {
        String tenant = TenantContext.getCurrentTenant();
        if (featureFlagService.findFeatureFlagByFeature("upload_clinical_operations_aws_s3") != null) {
            clinicalOperationsService.clinicalOperationsPopulate();
            clinicalOperationsService.uploadClinicalOpsToAws();
            return ResponseEntity.ok().build();
        } else {
            Map<String, String> response = new HashMap<>();
            response.put("message", "Feature Flag `upload_clinical_operations_aws_s3` is not set for " + tenant);
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping(value = "/customer/sftp/general-ledger")
    public ResponseEntity<?> customerSftpGeneralLedger() {
        String tenant = TenantContext.getCurrentTenant();
        if (featureFlagService.findFeatureFlagByFeature("upload_general_ledger_aws_s3") != null) {
            generalLedgerService.importGeneralLedgerHistoryToMultitenant();
            generalLedgerService.triggerGLToAws();
            return ResponseEntity.ok().build();
        } else {
            Map<String, String> response = new HashMap<>();
            response.put("message", "Feature Flag `upload_general_ledger_aws_s3` is not set for " + tenant);
            return ResponseEntity.badRequest().body(response);
        }
    }
}
