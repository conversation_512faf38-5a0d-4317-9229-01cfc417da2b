package com.nymbl.config;

/**
 * Created by <PERSON> on 6/12/24.
 */
public class QuicksightCsvSQL {

    public static final String clinicalOperationsSelect = """
            tenant,
            id,
            patientId,
            ptFirstName,
            ptMiddleName,
            ptLastName,
            patientDob,
            patientCreatedAt,
            patientCreatedById,
            patientCreatedBy,
            patientPrimaryBranchId,
            patientPrimaryBranch,
            dateOfSurgery,
            prescriptionId,
            prescriptionBranchId,
            prescriptionBranch,
            prescriptionCreatedAt,
            prescriptionDuplicatedFromId,
            prescriptionCreatedById,
            prescriptionCreatedBy,
            prescriptionAssignedTo,
            prescriptionDate,
            prescriptionProjectedDeliveryDate,
            prescriptionDeliveredOn,
            appointmentDeliveredOn,
            prescriptionDeliveryLocation,
            prescriptionNymblStatusId,
            prescriptionNymblStatus,
            prescriptionLCodeId,
            lCodeId,
            lCode,
            quantity,
            modifier1,
            modifier2,
            modifier3,
            modifier4,
            lCodeDateOfService,
            lCodePlaceOfService,
            deviceTypeId,
            deviceTypeCategory,
            deviceName,
            claimId,
            claimDateOfService,
            sentToBillingDate,
            dateResolved,
            sentToBillingUser,
            claimNymblStatusId,
            claimNymblStatus,
            claimSubmissionDate,
            claimBillingBranchId,
            claimBillingBranch,
            claimBranch,
            claimAssignedFirstName,
            claimAssignedMiddleName,
            claimAssignedLastName,
            totalCost,
            totalAppliedPayment,
            insuranceCompanyId,
            insuranceCompany,
            allowableFee,
            billingFee,
            covered,
            bill,
            totalAllowable,
            totalCharge,
            verifiedById,
            verifiedBy,
            verifiedOn,
            treatingPractitionerId,
            tpFirstName,
            tpMiddleName,
            tpLastName,
            primaryCarePhysicianId,
            pcFirstName,
            pcMiddleName,
            pcLastName,
            referringPhysicianId,
            rpFirstName,
            rpMiddleName,
            rpLastName,
            therapistId,
            thFirstName,
            thMiddleName,
            thLastName,
            facilityId,
            facilityName,
            firstEvalDate,
            evalCreatedBy,
            appointmentFirstEvalDate,
            lastSubmissionBy,
            daysInCurrentClaimStatus,
            daysInCurrentRxStatus,
            firstPaymentDate
            """;


    public static final String clinicalOperations = """
    		with lastSub as (
    			SELECT `cs1`.`claim_id` AS `claimId`, `cs1`.`submission_date` AS `submissionDate`, `csu`.`username` AS `username`,  `cs1`.`submitted_by_id`
    			FROM `claim_submission` `cs1`
    			LEFT JOIN `nymbl_master`.`user` `csu` ON `cs1`.`submitted_by_id` = `csu`.`id`
    			ORDER BY `cs1`.`claim_id` ASC, `cs1`.`submission_date` DESC, `cs1`.`id` DESC
    		)
            SELECT DISTINCT
            :tenant AS `tenant`,
            `plc`.`id` AS `id`,
            `rx`.`patient_id` AS `patientId`,
            COALESCE(`pt`.`first_name`, NULL) AS `ptFirstName`,
            COALESCE(`pt`.`middle_name`, NULL) AS `ptMiddleName`,
            COALESCE(`pt`.`last_name`, NULL) AS `ptLastName`,
            `pt`.`dob` AS `patientDob`,
            `pt`.`created_at` AS `patientCreatedAt`,
            `pt`.`created_by_id` AS `patientCreatedById`,
            COALESCE(`patu`.`username`, NULL) AS `patientCreatedBy`,
            `pt`.`primary_branch_id` AS `patientPrimaryBranchId`,
            COALESCE(`pb`.`name`, NULL) AS `patientPrimaryBranch`,
            `rx`.`surgery_date` AS `dateOfSurgery`,
            `rx`.`id` AS `prescriptionId`,
            `rx`.`branch_id` AS `prescriptionBranchId`,
            COALESCE(`rxb`.`name`, NULL) AS `prescriptionBranch`,
            `rx`.`created_at` AS `prescriptionCreatedAt`,
            `rx`.`duplicated_from_id` AS `prescriptionDuplicatedFromId`,
            `rx`.`created_by_id` AS `prescriptionCreatedById`,
            COALESCE(`rxu`.`username`, NULL) AS `prescriptionCreatedBy`,
            COALESCE(`clerical`.`username`, NULL) AS `prescriptionAssignedTo`,
            `rx`.`prescription_date` AS `prescriptionDate`,
            `rx`.`projected_delivery_date` AS `prescriptionProjectedDeliveryDate`,
            `rx`.`delivered_on` AS `prescriptionDeliveredOn`,
            COALESCE(MAX(`delivery_apt`.`start_datetime`), null) AS `appointmentDeliveredOn`,
            COALESCE(`rx`.`delivery_location`, NULL) AS `prescriptionDeliveryLocation`,
            `rxns`.`id` AS `prescriptionNymblStatusId`,
            COALESCE(`rxns`.`name`, NULL) AS `prescriptionNymblStatus`,
            `plc`.`id` AS `prescriptionLCodeId`,
            `lc`.`id` AS `lCodeId`,
            COALESCE(`lc`.`name`, NULL) AS `lCode`,
            `plc`.`quantity` AS `quantity`,
            COALESCE(`plc`.`modifier1`, NULL) AS `modifier1`,
            COALESCE(`plc`.`modifier2`, NULL) AS `modifier2`,
            COALESCE(`plc`.`modifier3`, NULL) AS `modifier3`,
            COALESCE(`plc`.`modifier4`, NULL) AS `modifier4`,
            `plc`.`date_of_service` AS `lCodeDateOfService`,
            COALESCE(`plc`.`pos`, NULL) AS `lCodePlaceOfService`,
            `dt`.`id` AS `deviceTypeId`,
            `dt`.`orthotic_or_prosthetic` AS `deviceTypeCategory`,
            `dt`.`name` AS `deviceName`,
            `c`.`id` AS `claimId`,
            `c`.`date_of_service` AS `claimDateOfService`,
            `c`.`created_at` AS `sentToBillingDate`,
            `c`.`date_resolved` AS `dateResolved`,
            `sb`.`username` AS `sentToBillingUser`,
            `c`.`nymbl_status_id` AS `claimNymblStatusId`,
            `cns`.`name` AS `claimNymblStatus`,
            COALESCE(MIN(`cs`.`submission_date`), null) AS `claimSubmissionDate`,
            `c`.`billing_branch_id` AS `claimBillingBranchId`,
            COALESCE(`cb`.`name`, '') AS `claimBillingBranch`,
            COALESCE(`cb`.`name`, '') AS `claimBranch`,
            `c_user`.`first_name` AS `claimAssignedFirstName`,
            `c_user`.`middle_name` AS `claimAssignedMiddleName`,
            `c_user`.`last_name` AS `claimAssignedLastName`,
            `poi_sum`.`total_cost` AS `totalCost`,
            `aplc_sum`.`total_applied_payment` AS `totalAppliedPayment`,
            `ic`.`id` AS `insuranceCompanyId`,
            `ic`.`name` AS `insuranceCompany`,
            `ivlc`.`allowable_fee` AS `allowableFee`,
            `ivlc`.`billing_fee` AS `billingFee`,
            `ivlc`.`covered` AS `covered`,
            `ivlc`.`bill` AS `bill`,
            `ivlc`.`total_allowable` AS `totalAllowable`,
            `ivlc`.`total_charge` AS `totalCharge`,
            `iv`.`verified_by_id` AS `verifiedById`,
            `ivu`.`username` AS `verifiedBy`,
            `iv`.`verified_on` AS `verifiedOn`,
            `tp`.`id` AS `treatingPractitionerId`,
            `tp`.`first_name` AS `tpFirstName`,
            `tp`.`middle_name` AS `tpMiddleName`,
            `tp`.`last_name` AS `tpLastName`,
            `pc`.`id` AS `primaryCarePhysicianId`,
            `pc`.`first_name` AS `pcFirstName`,
            `pc`.`middle_name` AS `pcMiddleName`,
            `pc`.`last_name` AS `pcLastName`,
            `rp`.`id` AS `referringPhysicianId`,
            `rp`.`first_name` AS `rpFirstName`,
            `rp`.`middle_name` AS `rpMiddleName`,
            `rp`.`last_name` AS `rpLastName`,
            `th`.`id` AS `therapistId`,
            `th`.`first_name` AS `thFirstName`,
            `th`.`middle_name` AS `thMiddleName`,
            `th`.`last_name` AS `thLastName`,
            `fac`.`id` AS `facilityId`,
            `fac`.`name` AS `facilityName`,
            MIN(COALESCE(`eval_audit`.`created_at`, NULL)) AS `firstEvalDate`,
            MAX(`eval_audit`.`user`) AS `evalCreatedBy`,
            MIN(`init_eval_apt`.`start_datetime`) AS `appointmentFirstEvalDate`,
            (SELECT username FROM lastSub WHERE lastSub.claimId = c.id LIMIT 1) AS lastSubmissionBy,
            DATEDIFF(FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), FROM_UNIXTIME(MIN(`c_audit_rev`.`rev_timestamp`) / 1000, '%Y-%m-%d')) AS `daysInCurrentClaimStatus`,
            DATEDIFF(FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), FROM_UNIXTIME(MIN(`rx_audit_rev`.`rev_timestamp`) / 1000, '%Y-%m-%d')) AS `daysInCurrentRxStatus`,
            MIN(`ap`.`applied_date`) AS `firstPaymentDate`
            FROM `prescription_l_code` `plc`
            JOIN `prescription` `rx` ON `plc`.`prescription_id` = `rx`.`id`
            JOIN `patient` `pt` ON `rx`.`patient_id` = `pt`.`id`
            LEFT JOIN `prescription_audit` `rx_audit` ON `rx`.`id` = `rx_audit`.`id` AND `rx`.`nymbl_status_id` = `rx_audit`.`nymbl_status_id`
            LEFT JOIN `audit_revision` `rx_audit_rev` ON `rx_audit`.`revision_id` = `rx_audit_rev`.`revision_id`
            LEFT JOIN `insurance_verification` `iv` ON `rx`.`id` = `iv`.`prescription_id` AND `iv`.`carrier_type` = 'primary'
            LEFT JOIN `insurance_verification_l_code` `ivlc` ON `plc`.`id` = `ivlc`.`prescription_l_code_id` AND `iv`.`id` = `ivlc`.`insurance_verification_id`
            LEFT JOIN `patient_insurance` `pi` ON `iv`.`patient_insurance_id` = `pi`.`id`
            LEFT JOIN `insurance_company` `ic` ON `pi`.`insurance_company_id` = `ic`.`id`
            LEFT JOIN (
               SELECT `prescription_l_code_id`, SUM(COALESCE(`total_cost`, 0.00)) `total_cost`
               FROM `purchase_order_item`
               WHERE `prescription_l_code_id` IS NOT NULL GROUP BY `prescription_l_code_id`
            ) AS `poi_sum` ON `plc`.`id` = `poi_sum`.`prescription_l_code_id`
            LEFT JOIN `claim` `c` ON `rx`.`id` = `c`.`prescription_id` AND `iv`.`patient_insurance_id` = `c`.`patient_insurance_id`
            LEFT JOIN `claim_audit` `c_audit` ON `c`.`id` = `c_audit`.`id` AND `c`.`nymbl_status_id` = `c_audit`.`nymbl_status_id`
            LEFT JOIN `audit_revision` `c_audit_rev` ON `c_audit`.`revision_id` = `c_audit_rev`.`revision_id`
            LEFT JOIN `applied_payment` `ap` ON `ap`.`claim_id` = `c`.`id`
            LEFT JOIN (
               SELECT `prescription_l_code_id`, SUM(COALESCE(`amount`, 0.00)) AS `total_applied_payment`
               FROM `applied_payment_l_code` GROUP BY `prescription_l_code_id`
            ) AS `aplc_sum` ON `plc`.`id` = `aplc_sum`.`prescription_l_code_id`
            LEFT JOIN `claim_submission` `cs` ON `cs`.`claim_id` = `c`.`id`
            LEFT JOIN `l_code` `lc` ON `lc`.`id` = `plc`.`l_code_id`
            LEFT JOIN `device_type` `dt` ON `dt`.`id` = `rx`.`device_type_id`
            LEFT JOIN `branch` `pb` ON `pt`.`primary_branch_id` = `pb`.`id`
            LEFT JOIN `branch` `rxb` ON `rx`.`branch_id` = `rxb`.`id`
            LEFT JOIN `branch` `cb` ON `c`.`billing_branch_id` = `cb`.`id`
            LEFT JOIN `nymbl_master`.`user` `ivu` ON `iv`.`verified_by_id` = `ivu`.`id`
            LEFT JOIN `nymbl_master`.`user` `patu` ON `pt`.`created_by_id` = `patu`.`id`
            LEFT JOIN `nymbl_master`.`user` `rxu` ON `rx`.`created_by_id` = `rxu`.`id`
            LEFT JOIN `nymbl_master`.`user` `clerical` ON `rx`.`clerical_user_id` = `clerical`.`id`
            LEFT JOIN `nymbl_master`.`user` `sb` ON `c`.`created_by_id` = `sb`.`id`
            LEFT JOIN `nymbl_master`.`user` `pc` ON `pt`.`primary_practitioner_id` = `pc`.`id`
            LEFT JOIN `nymbl_master`.`user` `tp` ON `rx`.`treating_practitioner_id` = `tp`.`id`
            LEFT JOIN `physician` `rp` ON `rx`.`referring_physician_id` = `rp`.`id`
            LEFT JOIN `therapist` `th` ON `rx`.`therapist_id` = `th`.`id`
            LEFT JOIN `facility` `fac` ON `rx`.`facility_id` = `fac`.`id`
            LEFT JOIN `nymbl_status` `rxns` ON `rx`.`nymbl_status_id` = `rxns`.`id`
            LEFT JOIN `nymbl_status` `cns` ON `c`.`nymbl_status_id` = `cns`.`id`
            LEFT JOIN `nymbl_master`.`user` `c_user` ON `c`.`user_id` = `c_user`.`id`
            LEFT JOIN (
               SELECT `efa`.`prescription_id`, MIN(`efa`.`created_at`) `created_at`, MIN(`ar`.`user`) `user`
               FROM `evaluation_form_audit` `efa`
               LEFT JOIN `audit_revision` `ar` ON `efa`.`revision_id` = `ar`.`revision_id`
               WHERE `efa`.`prescription_id` IS NOT NULL
               GROUP BY `efa`.`prescription_id`
            ) AS `eval_audit` ON `rx`.`id` = `eval_audit`.`prescription_id`
            LEFT JOIN (
               SELECT `apt`.`prescription_id`,
               MAX(`apt`.`start_datetime`) AS `start_datetime`
               FROM `appointment` `apt`
               LEFT JOIN `appointment_type` `apt_type` ON `apt_type`.`id` = `apt`.`appointment_type_id`
               LEFT JOIN `appointment_type_status`  `apt_type_status` ON `apt_type_status`.`id` = `apt_type`.`appointment_type_status_id`
               WHERE `apt`.`prescription_id` IS NOT NULL AND `apt_type_status`.`key` = 'delivery'
               GROUP BY  `apt`.`prescription_id`
            ) AS `delivery_apt` ON `rx`.`id` = `delivery_apt`.`prescription_id`
            LEFT JOIN (
               SELECT `apt`.`prescription_id`,
               MAX(`apt`.`start_datetime`) AS `start_datetime`
               FROM `appointment` `apt`
               LEFT JOIN `appointment_type` `apt_type` ON `apt_type`.`id` = `apt`.`appointment_type_id`
               LEFT JOIN `appointment_type_status`  `apt_type_status` ON `apt_type_status`.`id` = `apt_type`.`appointment_type_status_id`
               WHERE `apt`.`prescription_id` IS NOT NULL AND `apt_type_status`.`key` = 'initial_eval'
               GROUP BY  `apt`.`prescription_id`
            ) AS `init_eval_apt` ON `rx`.`id` = `init_eval_apt`.`prescription_id`
            WHERE `rx`.`active` = 1 AND `pt`.`active` = 1
            GROUP BY `rx`.`patient_id`, `c`.`id`, `plc`.`id`, `ivlc`.`allowable_fee`, `ivlc`.`billing_fee`, `ivlc`.`covered`, `ivlc`.`bill`, `ivlc`.`total_allowable`, `ivlc`.`total_charge`, `iv`.`verified_by_id`, `iv`.`verified_on`, `ic`.`id`, `poi_sum`.`total_cost`, `aplc_sum`.`total_applied_payment`
            """;

    public static final String generalLedgerSelect = """
            tenant,
            glId,
            categoryId,
            glDate,
            glAppliedDate,
            year,
            period,
            glAccount,
            category,
            subCategory,
            amount,
            absAmount,
            patientId,
            ptFirstName,
            ptMiddleName,
            ptLastName,
            prescriptionId,
            claimId,
            branchId,
            branch,
            patientBranchId,
            patientBranch,
            prescriptionBranchId,
            prescriptionBranch,
            facilityId,
            facility,
            prescriptionLCodeId,
            lCodeId,
            lCode,
            insuranceVerificationId,
            insuranceVerificationLCodeId,
            paymentId,
            appliedPaymentId,
            appliedPaymentLCodeId,
            payerType,
            paymentType,
            checkNumber,
            insuranceCompanyId,
            insuranceCompany,
            carrierType,
            patientInsuranceId,
            deviceTypeId,
            deviceTypeCategory,
            deviceType,
            treatingPractitionerId,
            tpFirstName,
            tpMiddleName,
            tpLastName,
            tpCredentials,
            primaryCarePhysicianId,
            pcFirstName,
            pcMiddleName,
            pcLastName,
            pcCredentials,
            therapistId,
            thFirstName,
            thMiddleName,
            thLastName,
            thCredentials,
            referringPhysicianId,
            rpFirstName,
            rpMiddleName,
            rpLastName,
            rpCredentials,
            claimSubmissionDate,
            dateOfService,
            prescriptionDate,
            paymentDate,
            depositDate,
            appliedDate,
            rxActive,
            ptActive,
            lastUpdated
            """;

    public static final String generalLedger = """
            SELECT :tenant AS `tenant`,
            `gl`.`id` AS `glId`,
            `gl`.`category_id` AS `categoryId`,
            `gl`.`gl_date` AS `glDate`,
            `gl`.`gl_applied_date` AS `glAppliedDate`,
            `gl`.`gl_year` AS `year`,
            `gl`.`gl_period` AS `period`,
            `gl`.`gl_account` AS `glAccount`,
            `gl`.`category` AS `category`,
            `gl`.`sub_category` AS `subCategory`,
            `gl`.`amount` AS `amount`,
            `gl`.`abs_amount` AS `absAmount`,
            `gl`.`patient_id` AS `patientId`,
            `pt`.`first_name` AS `ptFirstName`,
            `pt`.`middle_name` AS `ptMiddleName`,
            `pt`.`last_name` AS `ptLastName`,
            `gl`.`prescription_id` AS `prescriptionId`,
            `gl`.`claim_id` AS `claimId`,
            `gl`.`branch_id` AS `branchId`,
            `b`.`name` AS `branch`,
            `gl`.`patient_branch_id` AS `patientBranchId`,
            `pb`.`name` AS `patientBranch`,
            `gl`.`prescription_branch_id` AS `prescriptionBranchId`,
            `rxb`.`name` AS `prescriptionBranch`,
            `gl`.`facility_id` AS `facilityId`,
            `f`.`name` AS `facility`,
            `gl`.`prescription_l_code_id` AS `prescriptionLCodeId`,
            `gl`.`l_code_id` AS `lCodeId`,
            `lc`.`name` AS `lCode`,
            `gl`.`insurance_verification_id` AS `insuranceVerificationId`,
            `gl`.`insurance_verification_l_code_id` AS `insuranceVerificationLCodeId`,
            `gl`.`payment_id` AS `paymentId`,
            `gl`.`applied_payment_id` AS `appliedPaymentId`,
            `gl`.`applied_payment_l_code_id` AS `appliedPaymentLCodeId`,
            `gl`.`payer_type` AS `payerType`,
            `gl`.`payment_type` AS `paymentType`,
            `p`.`check_number` AS `checkNumber`,
            `gl`.`insurance_company_id` AS `insuranceCompanyId`,
            `ic`.`name` AS `insuranceCompany`,
            `gl`.`carrier_type` AS `carrierType`,
            `gl`.`patient_insurance_id` AS `patientInsuranceId`,
            `gl`.`device_type_id` AS `deviceTypeId`,
            `gl`.`device_type` AS `deviceTypeCategory`,
            `dt`.`name` AS `deviceType`,
            `gl`.`treating_practitioner_id`  AS `treatingPractitionerId`,
            `tp`.`first_name` AS `tpFirstName`,
            `tp`.`middle_name` AS `tpMiddleName`,
            `tp`.`last_name` AS `tpLastName`,
            `tp`.`credentials` AS `tpCredentials`,
            `gl`.`primary_care_physician_id` AS `primaryCarePhysicianId`,
            `pc`.`first_name` AS `pcFirstName`,
            `pc`.`middle_name` AS `pcMiddleName`,
            `pc`.`last_name` AS `pcLastName`,
            `pc`.`credentials` AS `pcCredentials`,
            `gl`.`therapist_id` AS `therapistId`,
            `th`.`first_name` AS `thFirstName`,
            `th`.`middle_name` AS `thMiddleName`,
            `th`.`last_name` AS `thLastName`,
            `th`.`credentials` AS `thCredentials`,
            `gl`.`referring_physician_id` AS `referringPhysicianId`,
            `rp`.`first_name` AS `rpFirstName`,
            `rp`.`middle_name` AS `rpMiddleName`,
            `rp`.`last_name` AS `rpLastName`,
            `rp`.`credentials` AS `rpCredentials`,
            `gl`.`claim_submission_date` AS `claimSubmissionDate`,
            `gl`.`date_of_service` AS `dateOfService`,
            `gl`.`prescription_date` AS `prescriptionDate`,
            `gl`.`payment_date` AS `paymentDate`,
            `gl`.`deposit_date` AS `depositDate`,
            `gl`.`applied_date` AS `appliedDate`,
            CASE WHEN (`gl`.`rx_active` = 1) THEN TRUE ELSE FALSE END AS `rxActive`,
            CASE WHEN (`gl`.`patient_active` = 1) THEN TRUE ELSE FALSE END AS `ptActive`,
            `gl`.`last_updated` AS `lastUpdated`
            FROM `general_ledger` `gl`
            LEFT JOIN `patient` `pt` ON `gl`.`patient_id` = `pt`.`id`
            LEFT JOIN `branch` `b` ON `gl`.`branch_id` = `b`.`id`
            LEFT JOIN `branch` `pb` ON `gl`.`patient_branch_id` = `pb`.`id`
            LEFT JOIN `branch` `rxb` ON `gl`.`prescription_branch_id` = `rxb`.`id`
            LEFT JOIN `facility` `f` ON `gl`.`facility_id` = `f`.`id`
            LEFT JOIN `insurance_company` `ic` ON `gl`.`insurance_company_id` = `ic`.`id`
            LEFT JOIN `device_type` `dt` ON `gl`.`device_type_id` = `dt`.`id`
            LEFT JOIN `nymbl_master`.`user` `tp` ON `gl`.`treating_practitioner_id` = `tp`.`id`
            LEFT JOIN `physician` `pc` ON `gl`.`primary_care_physician_id` = `pc`.`id`
            LEFT JOIN `therapist` `th` ON `gl`.`therapist_id` = `th`.`id`
            LEFT JOIN `physician` `rp` ON `gl`.`referring_physician_id` = `rp`.`id`
            LEFT JOIN `l_code` `lc` ON `gl`.`l_code_id` = `lc`.`id`
            LEFT JOIN `payment` `p` ON `gl`.`payment_id` = `p`.`id`
            LEFT JOIN `gl_period` `glp` ON `gl`.`gl_year` = `glp`.`year` AND `gl`.`gl_period` = `glp`.`period`
            WHERE ((`glp`.`status` = 'open') OR (ISNULL(`gl`.`gl_year`) AND ISNULL(`gl`.`gl_period`)))
            """;

    public static final String generalLedgerStatic = """
            SELECT :tenant AS `tenant`,
            `gl`.`id` AS `glId`,
            `gl`.`category_id` AS `categoryId`,
            `gl`.`gl_date` AS `glDate`,
            `gl`.`gl_applied_date` AS `glAppliedDate`,
            `gl`.`gl_year` AS `year`,
            `gl`.`gl_period` AS `period`,
            `gl`.`gl_account` AS `glAccount`,
            `gl`.`category` AS `category`,
            `gl`.`sub_category` AS `subCategory`,
            `gl`.`amount` AS `amount`,
            `gl`.`abs_amount` AS `absAmount`,
            `gl`.`patient_id` AS `patientId`,
            `pt`.`first_name` AS `ptFirstName`,
            `pt`.`middle_name` AS `ptMiddleName`,
            `pt`.`last_name` AS `ptLastName`,
            `gl`.`prescription_id` AS `prescriptionId`,
            `gl`.`claim_id` AS `claimId`,
            `gl`.`branch_id` AS `branchId`,
            `b`.`name` AS `branch`,
            `gl`.`patient_branch_id` AS `patientBranchId`,
            `pb`.`name` AS `patientBranch`,
            `gl`.`prescription_branch_id` AS `prescriptionBranchId`,
            `rxb`.`name` AS `prescriptionBranch`,
            `gl`.`facility_id` AS `facilityId`,
            `f`.`name` AS `facility`,
            `gl`.`prescription_l_code_id` AS `prescriptionLCodeId`,
            `gl`.`l_code_id` AS `lCodeId`,
            `lc`.`name` AS `lCode`,
            `gl`.`insurance_verification_id` AS `insuranceVerificationId`,
            `gl`.`insurance_verification_l_code_id` AS `insuranceVerificationLCodeId`,
            `gl`.`payment_id` AS `paymentId`,
            `gl`.`applied_payment_id` AS `appliedPaymentId`,
            `gl`.`applied_payment_l_code_id` AS `appliedPaymentLCodeId`,
            `gl`.`payer_type` AS `payerType`,
            `gl`.`payment_type` AS `paymentType`,
            `p`.`check_number` AS `checkNumber`,
            `gl`.`insurance_company_id` AS `insuranceCompanyId`,
            `ic`.`name` AS `insuranceCompany`,
            `gl`.`carrier_type` AS `carrierType`,
            `gl`.`patient_insurance_id` AS `patientInsuranceId`,
            `gl`.`device_type_id` AS `deviceTypeId`,
            `gl`.`device_type` AS `deviceTypeCategory`,
            `dt`.`name` AS `deviceType`,
            `gl`.`treating_practitioner_id`  AS `treatingPractitionerId`,
            `tp`.`first_name` AS `tpFirstName`,
            `tp`.`middle_name` AS `tpMiddleName`,
            `tp`.`last_name` AS `tpLastName`,
            `tp`.`credentials` AS `tpCredentials`,
            `gl`.`primary_care_physician_id` AS `primaryCarePhysicianId`,
            `pc`.`first_name` AS `pcFirstName`,
            `pc`.`middle_name` AS `pcMiddleName`,
            `pc`.`last_name` AS `pcLastName`,
            `pc`.`credentials` AS `pcCredentials`,
            `gl`.`therapist_id` AS `therapistId`,
            `th`.`first_name` AS `thFirstName`,
            `th`.`middle_name` AS `thMiddleName`,
            `th`.`last_name` AS `thLastName`,
            `th`.`credentials` AS `thCredentials`,
            `gl`.`referring_physician_id` AS `referringPhysicianId`,
            `rp`.`first_name` AS `rpFirstName`,
            `rp`.`middle_name` AS `rpMiddleName`,
            `rp`.`last_name` AS `rpLastName`,
            `rp`.`credentials` AS `rpCredentials`,
            `gl`.`claim_submission_date` AS `claimSubmissionDate`,
            `gl`.`date_of_service` AS `dateOfService`,
            `gl`.`prescription_date` AS `prescriptionDate`,
            `gl`.`payment_date` AS `paymentDate`,
            `gl`.`deposit_date` AS `depositDate`,
            `gl`.`applied_date` AS `appliedDate`,
            CASE WHEN (`gl`.`rx_active` IS NULL) THEN FALSE ELSE `gl`.`rx_active` END AS `rxActive`,
            CASE WHEN (`gl`.`patient_active` IS NULL) THEN FALSE ELSE `gl`.`patient_active` END AS `ptActive`,
            `gl`.`last_updated` AS `lastUpdated`
            FROM `general_ledger_static` `gl`
            LEFT JOIN `patient` `pt` ON `gl`.`patient_id` = `pt`.`id`
            LEFT JOIN `branch` `b` ON `gl`.`branch_id` = `b`.`id`
            LEFT JOIN `branch` `pb` ON `gl`.`patient_branch_id` = `pb`.`id`
            LEFT JOIN `branch` `rxb` ON `gl`.`prescription_branch_id` = `rxb`.`id`
            LEFT JOIN `facility` `f` ON `gl`.`facility_id` = `f`.`id`
            LEFT JOIN `insurance_company` `ic` ON `gl`.`insurance_company_id` = `ic`.`id`
            LEFT JOIN `device_type` `dt` ON `gl`.`device_type_id` = `dt`.`id`
            LEFT JOIN `nymbl_master`.`user` `tp` ON `gl`.`treating_practitioner_id` = `tp`.`id`
            LEFT JOIN `physician` `pc` ON `gl`.`primary_care_physician_id` = `pc`.`id`
            LEFT JOIN `therapist` `th` ON `gl`.`therapist_id` = `th`.`id`
            LEFT JOIN `physician` `rp` ON `gl`.`referring_physician_id` = `rp`.`id`
            LEFT JOIN `l_code` `lc` ON `gl`.`l_code_id` = lc.id
            LEFT JOIN `payment` `p` ON `gl`.`payment_id` = `p`.`id`
            WHERE `gl`.`active` = 1
            """;

    public static final String prescriptionSummarySelect = """
            tenant,
            prescriptionId,
            projectedDeliveryDate,
            podSignedDate,
            surgeryDate,
            patientId,
            patientName,
            holdUntil,
            createdAt,
            rxAging,
            rxCategory,
            rxSubCategory,
            claimId,
            createdByName,
            deviceType,
            deviceTypeId,
            deviceName,
            rxDate,
            rxBranchName,
            rxBranchId,
            rxStatus,
            rxStatusUpdatedDate,
            physicianDocumentationStatus,
            treatingPractitionerName,
            referringPhysicianName,
            residentName,
            primaryCarePhysicianName,
            deliveredOn,
            deliveryLocationAddress,
            deliveryLocationName,
            clericalUserName,
            therapistName,
            facilityName,
            billed,
            active,
            costOfGoods,
            allowableTotal,
            billableTotal,
            rxHcpcs,
            insurance,
            fabricationSteps,
            incompleteSections,
            incompleteTasks,
            missingDocuments,
            firstAppointmentDateTime,
            lastAppointmentDateTime,
            nextAppointmentDateTime,
            latestComment,
            latestCommentDateTime,
            latestCommentBy
            """;

    public static final String prescriptionSummary = """
            SELECT DISTINCT :tenant AS `tenant`,
            `rx`.`id` AS `prescriptionId`,
            `rx`.`projected_delivery_date` AS `projectedDeliveryDate`,
            CASE WHEN `rx`.`signed_date` IS NOT NULL THEN `rx`.`signed_date`
                 WHEN `rx`.`manual_pod_signed_date` IS NOT NULL THEN `rx`.`manual_pod_signed_date`
                 WHEN `rx`.`user_signed_date` IS NOT NULL THEN `rx`.`user_signed_date`
                 ELSE NULL END AS `podSignedDate`,
            `rx`.`surgery_date` AS `surgeryDate`,
            `rx`.`patient_id` AS `patientId`,
            CONCAT(`p`.`first_name`, ' ', `p`.`last_name`) AS `patientName`,
            `rx`.`hold_until_date` AS `holdUntil`,
            `rx`.`created_at` AS `createdAt`,
            DATEDIFF(CURRENT_DATE(), `rx`.`created_at`) AS `rxAging`,
            `rx`.`category` AS `rxCategory`,
            `rx`.`sub_category` AS `rxSubCategory`,
            MAX(`c`.`id`) AS `claimId`,
            CONCAT(`createdBy`.`first_name`, ' ', `createdBy`.`last_name`) AS `createdByName`,
            `dt`.`orthotic_or_prosthetic` AS `deviceType`,
            `dt`.`id` AS `deviceTypeId`,
            `dt`.`name` AS `deviceName`,
            `rx`.`prescription_date` AS `rxDate`,
            `b`.`name` AS `rxBranchName`,
            `rx`.`branch_id` AS `rxBranchId`,
            `ns`.`name` AS `rxStatus`,
            MAX(`nsh`.`updated_at`) AS `rxStatusUpdatedDate`,
            `pds`.`name` AS `physicianDocumentationStatus`,
            CONCAT(`tp`.`first_name`, ' ', `tp`.`last_name`) AS `treatingPractitionerName`,
            CONCAT(`referring`.`first_name`, ' ', `referring`.`last_name`) AS `referringPhysicianName`,
            CONCAT(`res`.`first_name`, ' ', `res`.`last_name`) AS `residentName`,
            CONCAT(`pcdr`.`first_name`, ' ', `pcdr`.`last_name`) AS `primaryCarePhysicianName`,
            `rx`.`delivered_on` AS `deliveredOn`,
            CASE 
                WHEN `rx`.`delivery_location` = 'primary_branch' THEN CONCAT('Primary Branch: ', COALESCE(`pb`.`name`, ''))
                WHEN `rx`.`delivery_location` = 'prescription_branch' THEN CONCAT('Prescription Branch: ', COALESCE(`b`.`name`, ''))
                WHEN `rx`.`delivery_location` = 'patient_address' THEN CONCAT('Patient Address: ', COALESCE(`p`.`address_label`, ''))
                WHEN `rx`.`delivery_location` = 'patient_alternate_address' THEN CONCAT('Patient Alternate Address: ', COALESCE(`p`.`address_label2`, ''))
                WHEN `rx`.`delivery_location` = 'other' THEN UPPER(`rx`.`delivery_location`)
                ELSE COALESCE(`dl`.`name`, '')
            END AS `deliveryLocationName`,
            CASE 
                WHEN `rx`.`delivery_location` = 'primary_branch' THEN REGEXP_REPLACE(CONCAT(COALESCE(`pb`.`street_address`, ''), ', ', COALESCE(`pb`.`city`, ''), ', ', COALESCE(`pb`.`state`, ''), ' ', COALESCE(`pb`.`zipcode`, '')), ', ,', '')
                WHEN `rx`.`delivery_location` = 'prescription_branch' THEN REGEXP_REPLACE(CONCAT(COALESCE(`b`.`street_address`, ''), ', ', COALESCE(`b`.`city`, ''), ', ', COALESCE(`b`.`state`, ''), ' ', COALESCE(`b`.`zipcode`, '')), ', ,', '')
                WHEN `rx`.`delivery_location` = 'patient_address' THEN REGEXP_REPLACE(CONCAT(COALESCE(`p`.`street_address`, ''), ', ', COALESCE(`p`.`city`, ''), ', ', COALESCE(`p`.`state`, ''), ' ', COALESCE(`p`.`zipcode`, '')), ', ,', '')
                WHEN `rx`.`delivery_location` = 'patient_alternate_address' THEN REGEXP_REPLACE(CONCAT(COALESCE(`p`.`street_address2`, ''), ', ', COALESCE(`p`.`city2`, ''), ', ', COALESCE(`p`.`state2`, ''), ' ', COALESCE(`p`.`zipcode2`, '')), ', ,', '')
                WHEN `rx`.`delivery_location` = 'other' THEN COALESCE(`rx`.`delivery_location_address`, '')
                ELSE REGEXP_REPLACE(CONCAT(COALESCE(`dl`.`street_address`, ''), ', ', COALESCE(`dl`.`city`, ''), ', ', COALESCE(`dl`.`state`, ''), ' ', COALESCE(`dl`.`zipcode`, '')), ', ,', '')
            END AS `deliveryLocationAddress`,
            CONCAT(`clu`.`first_name`, ' ', `clu`.`last_name`) AS `clericalUserName`,
            CONCAT(`th`.`first_name`, ' ', `th`.`last_name`) AS `therapistName`,
            `f`.`name` AS `facilityName`,
            IF(MAX(`c`.`id`) IS NOT NULL, 1, 0) AS `billed`,
            `rx`.`active` AS `active`,
            `cogs`.`total_amount` AS `costOfGoods`,
            AVG(`ivlc`.`allowable_total`) AS `allowableTotal`,
            AVG(`ivlc`.`billable_total`) AS `billableTotal`,
            `plc`.`hcpcs_names` AS `rxHcpcs`,
            `iv`.`insurance_company` AS `insurance`,
            `fab`.`steps` AS `fabricationSteps`,
            `sec`.`sections` AS `incompleteSections`,
            `tsk`.`total` AS `incompleteTasks`,
            `missingDoc`.`names` AS `missingDocuments`,
            `firstAppt`.`visit_date` AS `firstAppointmentDateTime`,
            `lastAppt`.`visit_date` AS `lastAppointmentDateTime`,
            `nextAppt`.`visit_date` AS `nextAppointmentDateTime`,
            `nte`.`latest_comment` AS `latestComment`,
            `nte`.`latest_comment_date` AS `latestCommentDateTime`,
            `nte`.`latest_comment_by` AS `latestCommentBy`
            FROM `prescription` `rx`
            JOIN `patient` `p` ON `rx`.`patient_id` = `p`.`id`
            JOIN `device_type` `dt` ON `rx`.`device_type_id` = `dt`.`id`
            LEFT JOIN `therapist` `th` ON `rx`.`therapist_id` = `th`.`id`
            LEFT JOIN `claim` `c` ON `c`.`prescription_id` = `rx`.`id`
            LEFT JOIN `branch` `b` ON `rx`.`branch_id` = `b`.`id`
            LEFT JOIN `branch` `pb` ON `p`.`primary_branch_id` = `pb`.`id`
            LEFT JOIN `nymbl_status` `ns` ON `rx`.`nymbl_status_id` = `ns`.`id`
            LEFT JOIN `nymbl_status` `pds` ON `rx`.`physician_documentation_status_id` = `pds`.`id`
            LEFT JOIN `nymbl_master`.`user` `res` ON `res`.`id` = `rx`.`resident_id`
            LEFT JOIN `nymbl_master`.`user` `tp` ON `rx`.`treating_practitioner_id` = `tp`.`id`
            LEFT JOIN `physician` `referring` ON `rx`.`referring_physician_id` = `referring`.`id`
            LEFT JOIN `physician` `pcdr` ON `rx`.`primary_care_physician_id` = `pcdr`.`id`
            LEFT JOIN `nymbl_master`.`user` `createdBy` ON `rx`.`created_by_id` = `createdBy`.`id`
            LEFT JOIN `nymbl_master`.`user` `clu` ON `rx`.`clerical_user_id` = `clu`.`id`
            LEFT JOIN `facility` `f` ON `rx`.`facility_id` = `f`.`id`
            LEFT JOIN `delivery_location` `dl` ON `rx`.`delivery_location` = `dl`.`id`
            LEFT JOIN (
                SELECT `a`.`rxId`,
                IF(`a`.`latest_comment` IS NULL, NULL, IF(INSTR(`a`.`latest_comment`, '$!$') = 0, `a`.`latest_comment`, SUBSTRING(`a`.`latest_comment`, 1, INSTR(`a`.`latest_comment`, '$!$') - 1))) AS `latest_comment`,
                IF(`a`.`latest_comment_date` IS NULL, NULL, IF(INSTR(`a`.`latest_comment_date`, '$!$') = 0, `a`.`latest_comment_date`, SUBSTRING(`a`.`latest_comment_date`, 1, INSTR(`a`.`latest_comment_date`, '$!$') - 1))) AS `latest_comment_date`,
                IF(`a`.`latest_comment_by` IS NULL, NULL, IF(INSTR(`a`.`latest_comment_by`, '$!$') = 0, `a`.`latest_comment_by`, SUBSTRING(`a`.`latest_comment_by`, 1, INSTR(`a`.`latest_comment_by`, '$!$') - 1))) AS `latest_comment_by`
                FROM (
                    SELECT `a1`.`rxId` AS `rxId`,
                    GROUP_CONCAT(`a1`.`latest_comment` ORDER BY `a1`.`updated_at` DESC SEPARATOR '$!$') AS `latest_comment`,
                    GROUP_CONCAT(`a1`.`latest_comment_date` ORDER BY `a1`.`updated_at` DESC SEPARATOR '$!$') `latest_comment_date`,
                    GROUP_CONCAT(`a1`.`latest_comment_by` ORDER BY `a1`.`updated_at` DESC SEPARATOR '$!$') AS `latest_comment_by`
                    FROM (
                        SELECT `n`.`prescription_id` AS `rxId`, `n`.`updated_at` AS `updated_at`,
                        COALESCE(`n`.`note`, '') AS `latest_comment`,
                        COALESCE(`n`.`created_at`, '') AS `latest_comment_date`,
                        CONCAT(COALESCE(`nu`.`first_name`, ''), ' ', COALESCE(`nu`.`last_name`, '')) AS `latest_comment_by`
                        FROM `note` `n`
                        JOIN  `prescription` `rx` ON `n`.`prescription_id` = `rx`.`id`
                        LEFT JOIN `nymbl_master`.`user` `nu` on `nu`.`id` = `n`.`created_by_id`
                        WHERE `rx`.`active` = 1 AND `n`.`note_type` = 'patient_summary'
                        ORDER BY `rxId`, `n`.`updated_at`
                    ) AS `a1` GROUP BY `a1`.`rxId`
                ) AS `a`
            ) AS `nte` ON `nte`.`rxId` =`rx`.`id`
            LEFT JOIN (
                SELECT `nsh`.`prescription_id` AS `rxId`, `nsh`.`nymbl_status_id` AS `nymblStatusId`, MAX(`nsh`.`updated_at`) AS `updated_at`
                FROM `nymbl_status_history` `nsh`
                GROUP BY `nsh`.`prescription_id` , `nsh`.`nymbl_status_id`
            ) AS `nsh` ON `nsh`.`rxId` = `rx`.`id` AND `nsh`.`nymblStatusId` = `rx`.`nymbl_status_id`
            LEFT JOIN (
                SELECT `rx`.`id` AS `rxId`, GROUP_CONCAT(`ic`.`name` SEPARATOR ' - ') AS `insurance_company`
                FROM `insurance_verification` `iv`
                JOIN `prescription` `rx` ON rx.id = `iv`.`prescription_id`
                JOIN `patient_insurance` `pi` ON `iv`.`patient_insurance_id` = `pi`.`id`
                JOIN `insurance_company` `ic` ON `pi`.`insurance_company_id` = `ic`.`id`
                WHERE `rx`.`active` = 1 AND `pi`.`active` = 1 GROUP BY `rx`.`id`
            ) AS `iv` ON `iv`.`rxId` = `rx`.`id`
            LEFT JOIN (
               SELECT `a`.`rxId` AS `rxId`, GROUP_CONCAT(`a`.`steps` SEPARATOR ' - ') AS `steps`
               FROM (
                   SELECT `cls`.`prescription_id` AS `rxId`, CONCAT(`cls`.`name`, ': ', `cls`.`status`) AS `steps`
                   FROM `checklist_step` `cls`
                   JOIN `prescription` `rx` ON `cls`.`prescription_id` = `rx`.`id` AND `cls`.`type` = 'wip_fabrication'
                   WHERE `rx`.`active` = 1
               ) AS `a` GROUP BY `a`.`rxId`
            ) AS `fab` ON `fab`.`rxId` = `rx`.`id`
            LEFT JOIN (
               SELECT `rx`.`id` AS `rxId`, COUNT(`t`.`id`) AS `total`
               FROM `task` `t`
               JOIN `prescription` `rx` ON `t`.`prescription_id` = `rx`.`id`
               WHERE `rx`.`active` = 1 AND `t`.`completed` = 0 GROUP BY `rx`.`id`
            ) `tsk` ON `tsk`.`rxId` = `rx`.`id`
            LEFT JOIN (
                SELECT `rx`.`id` AS `rxId`, GROUP_CONCAT(`ps`.`section` SEPARATOR ' - ') AS `sections`
                FROM `prescription_section` `ps`
                JOIN `prescription` `rx` ON `ps`.`prescription_id` = `rx`.`id`
                WHERE `rx`.`active` = 1 and `ps`.`locked` = 0 and `ps`.`section` != 'evaluation_sections' GROUP BY `rx`.`id`
            ) AS `sec` ON `sec`.`rxId` = `rx`.`id`
            LEFT JOIN (
                SELECT `rx`.`id` AS rxId, SUM(COALESCE(`ivlc`.`total_allowable`, 0.00)) AS `allowable_total`, SUM(COALESCE(`ivlc`.`total_charge`, 0.00)) AS `billable_total`
                FROM `prescription` `rx`
                JOIN `insurance_verification` `iv` ON `rx`.`id` = `iv`.`prescription_id` AND `iv`.`patient_insurance_id` = `rx`.`patient_insurance_id`
                JOIN `insurance_verification_l_code` `ivlc` ON `iv`.`id` = `ivlc`.`insurance_verification_id`
                LEFT JOIN `claim` `c` on `rx`.`id` = `c`.`prescription_id`
                WHERE `rx`.`active` = 1 GROUP BY `rx`.`id`
            ) AS `ivlc` ON `ivlc`.`rxId` = `rx`.`id`
            LEFT JOIN (
                SELECT `rx`.`id` AS `rxId`, GROUP_CONCAT(`lc`.`name` SEPARATOR ' - ') AS `hcpcs_names`
                FROM `prescription` `rx`
                JOIN `prescription_l_code` `plc` ON `plc`.`prescription_id` = `rx`.`id`
                JOIN `l_code` `lc` ON `plc`.`l_code_id` = `lc`.`id`
                WHERE `rx`.`active` = 1 GROUP BY `rx`.`id`
            ) `plc` ON `plc`.`rxId` = `rx`.`id`
            LEFT JOIN (
                SELECT `rx`.`id` AS rxId, SUM(`poi`.`total_cost`) AS `total_amount`
                FROM `purchase_order_item` AS `poi`
                JOIN `prescription` AS `rx` ON `poi`.`prescription_id` = `rx`.`id`
                WHERE `rx`.`active` = 1 GROUP BY `rx`.`id`
            ) AS `cogs` ON `cogs`.`rxId` = `rx`.`id`
            LEFT JOIN (
                SELECT `a`.`rxId`, MIN(`a`.`visit_date`) `visit_date` FROM (
                   SELECT `rx`.`id` AS `rxId`, MIN(`appt`.`start_datetime`) AS `visit_date`
                   FROM `appointment` `appt`
                   JOIN `prescription` rx ON `appt`.`prescription_id` = `rx`.`id`
                   WHERE `rx`.`active` = 1 AND `appt`.`status` NOT IN ('cancelled' , 'no_show', 'rejected', 'rescheduled') GROUP BY `rx`.`id`
                   UNION
                   SELECT `rx`.`id` AS `rxId`, MIN(`appt`.`start_datetime`) AS `visit_date`
                   FROM `appointment` `appt`
                   JOIN `prescription` `rx` ON `appt`.`prescription_two_id` = `rx`.`id`
                   WHERE `rx`.`active` = 1 AND `appt`.`status` NOT IN ('cancelled' , 'no_show', 'rejected', 'rescheduled') GROUP BY `rx`.`id`
                ) AS `a` GROUP BY `a`.`rxId`
            ) AS `firstAppt` ON `firstAppt`.`rxId` = `rx`.`id`
            LEFT JOIN (
               SELECT `a`.`rxId`, MAX(`a`.`visit_date`) `visit_date` FROM (
                   SELECT `rx`.`id` AS `rxId`, MAX(`appt`.`start_datetime`) AS `visit_date`
                   FROM `appointment` `appt`
                   JOIN `prescription` `rx` ON `appt`.`prescription_id` = `rx`.`id`
                   WHERE `appt`.`status` NOT IN ('cancelled' , 'no_show', 'rejected', 'rescheduled') AND `appt`.`start_datetime` < NOW() AND `rx`.`active` = 1 GROUP BY `rx`.`id`
                   UNION
                   SELECT `rx`.`id` AS `rxId`, MAX(`appt`.`start_datetime`) AS `visit_date`
                   FROM `appointment` `appt`
                   JOIN `prescription` `rx` ON `appt`.`prescription_two_id` = `rx`.`id`
                   WHERE  `appt`.`status` NOT IN ('cancelled' , 'no_show', 'rejected', 'rescheduled') AND `appt`.`start_datetime` < NOW() AND `rx`.`active` = 1 GROUP BY `rx`.`id`
               ) AS `a` GROUP BY `a`.`rxId`
            ) AS `lastAppt` ON `lastAppt`.`rxId` = `rx`.`id`
            LEFT JOIN (
                SELECT `a`.`rxId`, MAX(`a`.`visit_date`) AS `visit_date` FROM (
                   SELECT `rx`.`id` AS `rxId`, MIN(`appt`.`start_datetime`) AS `visit_date`
                   FROM `appointment` `appt`
                   JOIN `prescription` `rx` ON `appt`.`prescription_id` = `rx`.`id`
                   WHERE `appt`.`status` NOT IN ('cancelled' , 'no_show', 'rejected', 'rescheduled') AND `appt`.`start_datetime` >= NOW() AND `rx`.`active` = 1 GROUP BY `rx`.`id`
                   UNION
                   SELECT `rx`.`id` AS `rxId`, MIN(`appt`.`start_datetime`) AS `visit_date`
                   FROM `appointment` `appt`
                   JOIN `prescription` `rx` ON `appt`.`prescription_two_id` = `rx`.`id`
                   WHERE  `appt`.`status` NOT IN ('cancelled' , 'no_show', 'rejected', 'rescheduled') AND `appt`.`start_datetime` >= NOW() AND `rx`.`active` = 1 GROUP BY `rx`.`id`
                ) AS `a` GROUP BY `a`.`rxId`
            ) AS `nextAppt` ON `nextAppt`.`rxId` = `rx`.`id`
            LEFT JOIN (
               SELECT `a`.`rxId` AS `rxId`, GROUP_CONCAT(`a`.`names` SEPARATOR ' - ') AS `names`
               FROM (
                   SELECT DISTINCT `rx`.`id` AS `rxId`,
                   CASE WHEN (`ft`.`id` = 19 AND `ivp`.`referral_number` IS NOT NULL AND `f`.`id` IS NOT NULL) OR
                              (`ft`.`id` = 20 AND `ivs`.`referral_number` IS NOT NULL AND `f`.`id` IS NOT NULL)
                              THEN ''
                              ELSE `ft`.`name`
                              END AS `names`
                   FROM `prescription` `rx`
                   LEFT JOIN `device_type` `dt` ON `dt`.`id` = `rx`.`device_type_id`
                   LEFT JOIN `device_type_file_type` `dtft` ON `dtft`.`device_type_id` = `dt`.`id`
                   LEFT JOIN `file_type` `ft` ON `ft`.`id` = `dtft`.`file_type_id`
                   LEFT JOIN `file` `f` ON `f`.`prescription_id` = `rx`.`id` and `f`.`file_type_id` = `ft`.`id`
                   LEFT JOIN `insurance_verification` `ivp` ON `ivp`.`prescription_id` = `rx`.`id` AND `ivp`.`carrier_type` = 'primary'
                   LEFT JOIN `insurance_verification` `ivs` ON `ivs`.`prescription_id` = `rx`.`id` AND `ivs`.`carrier_type` = 'secondary'
                   WHERE `rx`.`active` = 1
               ) AS `a` GROUP BY `a`.`rxId`
            ) AS `missingDoc` on `missingDoc`.`rxId` = `rx`.`id`
            GROUP BY `rx`.`id`, `fab`.`steps`, `nte`.`latest_comment`, `nte`.`latest_comment_date`, `nte`.`latest_comment_by`
            """;

    public static final String purchasingHistorySelect = """
            tenant,
            id,
            poNumber,
            branchName,
            branchAddress,
            branchCity,
            branchState,
            branchZip,
            vendorName,
            vendorId,
            manufacturerName,
            manufacturerId,
            partNumber,
            skuNumber,
            itemName,
            itemId,
            description,
            lCodeCategoryName,
            purchaseOrderItemType,
            lCodeCategoryId,
            quantity,
            orderStatus,
            patientId,
            patientName,
            deviceTypeName,
            deviceTypeCategory,
            itemCost,
            totalCost,
            addCharges,
            discount,
            shippingCharges,
            salesTax,
            grandTotal,
            orderedByUser,
            dateCreated,
            dateOrdered,
            deliveryLocation,
            rxId,
            practitionerId,
            practitionerName
            """;

    public static final String purchasingHistory = """
            SELECT :tenant AS `tenant`,
            `poi`.`id` AS `id`,
            /*IF(`poi_b`.`po_prefix` IS NOT NULL, CONCAT(`poi_b`.`po_prefix`, ' ', `po`.`id`), CONCAT(`po`.`id`, ' ')) AS `poNumber`,*/
            `po`.`id` AS `poNumber`,
            COALESCE(`poi_b`.`name`, '') AS `branchName`,
            COALESCE(`po`.`street_address`, '') AS `branchAddress`,
            COALESCE(`po`.`city`, '') AS `branchCity`,
            COALESCE(`po`.`state`, '') AS `branchState`,
            COALESCE(`po`.`zipcode`, '') AS `branchZip`,
            COALESCE(`v`.`name`, '') AS `vendorName`,
            `v`.`id` AS `vendorId`,
            COALESCE(`m`.`name`, '') AS `manufacturerName`,
            `m`.`id` AS `manufacturerId`,
            COALESCE(`i`.`part_number`, '') AS `partNumber`,
            COALESCE(`i`.`sku`, '') AS `skuNumber`,
            COALESCE(`i`.`name`, '') AS `itemName`,
            `i`.`id` AS `itemId`,
            COALESCE(`i`.`description`, '') AS `description`,
            COALESCE(`lcc`.`category`, '') AS `lCodeCategoryName`,
            COALESCE(`poi`.`type`, '') AS `purchaseOrderItemType`,
            COALESCE(`i`.`l_code_category_id`, '') AS `lCodeCategoryId`,
            `poi`.`quantity` AS `quantity`,
            COALESCE(`po`.`status`, '') AS `orderStatus`,
            CASE WHEN `poi`.`patient_id` IS NOT NULL THEN `poi`.`patient_id`
                 WHEN `poi`.`prescription_id` IS NOT NULL THEN `rxPatient`.`id`
                 ELSE NULL
            END AS `patientId`,
            CASE WHEN `poi`.`patient_id` IS NOT NULL THEN CONCAT(`p`.`first_name`, ' ', `p`.`last_name`)
                 WHEN `poi`.`prescription_id` IS NOT NULL THEN CONCAT(`rxPatient`.`first_name`, ' ', `rxPatient`.`last_name`)
                 ELSE NULL
            END AS `patientName`,
            COALESCE(`dt`.`name`, '') AS `deviceTypeName`,
            COALESCE(`dt`.`orthotic_or_prosthetic`, '') AS `deviceTypeCategory`,
            `poi`.`item_cost` AS `itemCost`,
            `poi`.`item_cost` * `poi`.`quantity` AS `totalCost`,
            `po`.`additional_charges` AS `addCharges`,
            `po`.`discount` AS `discount`,
            `po`.`shipping_charges` AS `shippingCharges`,
            `po`.`sales_tax` AS `salesTax`,
            `po`.`total_cost` AS `grandTotal`,
            IF(`uo`.`id` IS NOT NULL, CONCAT(`uo`.`first_name`, ' ', `uo`.`last_name`), NULL) AS `orderedByUser`,
            `po`.`created_at` AS `dateCreated`,
            `po`.`ordered_at` AS `dateOrdered`,
            COALESCE(`po`.`location_id`, '') AS `deliveryLocation`,
            `poi`.`prescription_id` AS `rxId`,
            `poi`.`practitioner_id` AS `practitionerId`,
            IF(`pt`.`id` IS NOT NULL, CONCAT(`pt`.`first_name`, ' ', `pt`.`last_name`), NULL) AS `practitionerName`
            FROM `purchase_order_item` `poi`
            LEFT JOIN `item` `i` ON `poi`.`item_id` = `i`.`id`
            LEFT JOIN (
                SELECT DISTINCT `il`.`item_id` AS `item_id`, GROUP_CONCAT(DISTINCT `lc`.`name`) AS `l_code_names`
                FROM `items_lcodes` `il`
                LEFT JOIN `l_code` `lc` ON `il`.`l_code_id` = `lc`.`id`
                GROUP BY `il`.`item_id`
            ) `lc` ON `lc`.`item_id` = `i`.`id`
            LEFT JOIN `nymbl_master`.`user` `pt` ON `poi`.`practitioner_id` = `pt`.`id`
            LEFT JOIN `patient` `p` ON `poi`.`patient_id` = `p`.`id`
            LEFT JOIN `vendor` `v` ON `poi`.`vendor_id` = `v`.`id`
            LEFT JOIN `vendor` `m` ON `i`.`manufacturer_id` = `m`.`id`
            LEFT JOIN `purchase_order` `po` ON `poi`.`purchase_order_id` = `po`.`id`
            LEFT JOIN `l_code_category` lcc ON `i`.`l_code_category_id` = `lcc`.`id`
            LEFT JOIN `branch` `poi_b` ON `poi`.`branch_id` = `poi_b`.`id`
            LEFT JOIN `prescription` `rx` ON `poi`.`prescription_id` = `rx`.`id`
            LEFT JOIN `patient` `rxPatient` ON `rx`.`patient_id` = `rxPatient`.`id`
            LEFT JOIN `device_type` `dt` ON `rx`.`device_type_id` = `dt`.`id`
            LEFT JOIN `patient_insurance` `pi` ON `rx`.`patient_insurance_id` = `pi`.`id`
            LEFT JOIN `insurance_company` `ic` ON `pi`.`insurance_company_id` = `ic`.`id`
            LEFT JOIN `nymbl_master`.`user` `uo` ON `po`.`ordered_by_id` = `uo`.`id`
            WHERE `po`.`status` NOT LIKE '%inventory%'
            """;

    public static final String taskSelect = """
            id,
            tenant,
            name,
            description,
            priority,
            due_date,
            patient_id,
            patient_first_name,
            patient_last_name,
            patient_branch,
            prescription_id,
            prescription,
            claim_id,
            insurance,
            user_first_name,
            user_last_name,
            completed_by_first_name,
            completed_by_last_name,
            date_completed,
            created_at,
            created_by_first_name,
            created_by_last_name
            """;

    public static final String task = """
            SELECT
            `t`.`id` AS `id`,
            :tenant AS `tenant`,
            `t`.`name` AS `name`,
            `t`.`description` AS `description`,
            `t`.`priority` AS `priority`,
            `t`.`due_date` AS `due_date`,
            `t`.`patient_id` AS `patient_id`,
            `p`.`first_name` AS `patient_first_name`,
            `p`.`last_name` AS `patient_last_name`,
            `b`.`name` AS `patient_branch`,
            `t`.`prescription_id` AS `prescription_id`,
            `dt`.`name` AS `prescription`,
            `t`.`claim_id` AS `claim_id`,
            `ic`.`name` AS `insurance`,
            `tu`.`first_name` AS `user_first_name`,
            `tu`.`last_name` AS `user_last_name`,
            `cou`.`first_name` AS `completed_by_first_name`,
            `cou`.`last_name` AS `completed_by_last_name`,
            `t`.`date_completed` AS `date_completed`,
            `t`.`created_at` AS `created_at`,
            `cru`.`first_name` AS `created_by_first_name`,
            `cru`.`last_name` AS `created_by_last_name`
            FROM `task` `t`
            LEFT JOIN `patient` `p` ON `t`.`patient_id` = `p`.`id`
            LEFT JOIN `branch` `b` ON `p`.`primary_branch_id` = `b`.`id`
            LEFT JOIN `prescription` `rx` ON `t`.`prescription_id` = `rx`.`id`
            LEFT JOIN `device_type` `dt` ON `rx`.`device_type_id` = `dt`.`id`
            LEFT JOIN `patient_insurance` `pi` ON `t`.`patient_insurance_id` = `pi`.`id`
            LEFT JOIN `insurance_company` `ic` ON `pi`.`insurance_company_id` = `ic`.`id`
            LEFT JOIN `nymbl_master`.`user` `tu` ON `t`.`user_id` = `tu`.`id`
            LEFT JOIN `nymbl_master`.`user` `cru` ON `t`.`created_by_id` = `cru`.`id`
            LEFT JOIN `nymbl_master`.`user` `cou` ON `t`.`completed_by_id` = `cou`.`id`
            WHERE `t`.`created_at` > DATE_SUB(now(), INTERVAL 6 MONTH)
            """;

    public static final String nymblStatusHistorySelect = """
            tenant, id, prescription_id, claim_id, nymbl_status, type, note, updated_at, first_name, last_name,
            prescription_created_at, prescription_created_by_first_name, prescription_created_by_last_name,
            claim_created_at, claim_created_by_first_name, claim_created_by_last_name, claim_submitted_by_first_name,
            claim_submitted_by_last_name, practitioner_first_name, practitioner_last_name, device_type
            """;

    public static final String nymblStatusHistory = """
    		WITH `sub` AS (SELECT `sub1`.`claim_id`, `sub1`.`submitted_by_id` FROM `claim_submission` `sub1` JOIN
                             (SELECT `claim_id`, MAX(`submission_date`) AS `submission_date`, MAX(`id`) AS `submission_id` FROM `claim_submission`
                             GROUP BY `claim_id`) `sub2` ON `sub1`.`id` = `sub2`.`submission_id` AND `sub1`.`submission_date` = `sub2`.`submission_date`)
            SELECT
            :tenant AS `tenant`,
            `nsh`.`id` AS `id`,
            `nsh`.`prescription_id` AS `prescription_id`,
            `c`.`id` AS `claim_id`,
            `ns`.`name` AS `nymbl_status`,
            `nsh`.`type` AS `type`,
            `nsh`.`note` AS `note`,
            `nsh`.`updated_at`,
            `u`.`first_name`,
            `u`.`last_name`,
            `rx`.`created_at` AS `prescription_created_at`,
            `rxu`.`first_name` AS `prescription_created_by_first_name`,
            `rxu`.`last_name` AS `prescription_created_by_last_name`,
            `c`.`created_at` AS `claim_created_at`,
            `cu`.`first_name` AS `claim_created_by_first_name`,
            `cu`.`last_name` AS `claim_created_by_last_name`,
            `subu`.`first_name` AS `claim_submitted_by_first_name`,
            `subu`.`last_name` AS `claim_submitted_by_last_name`,
            `tpu`.`first_name` AS `practitioner_first_name`,
            `tpu`.`last_name` AS `practitioner_last_name`,
            `dt`.`orthotic_or_prosthetic` AS `device_type`
            FROM `nymbl_status_history` `nsh`
            JOIN `nymbl_status` `ns` ON `nsh`.`nymbl_status_id` = `ns`.`id`
            JOIN `prescription` `rx` ON `nsh`.`prescription_id` = `rx`.`id`
            LEFT JOIN `claim` `c` ON `rx`.`id` = `c`.`prescription_id`
            LEFT JOIN `sub` ON `c`.`id` = `sub`.`claim_id`
            LEFT JOIN `nymbl_master`.`user` `u` ON `nsh`.`updated_by_id` = `u`.`id`
            LEFT JOIN `nymbl_master`.`user` `rxu` ON `rx`.`created_by_id` = `rxu`.`id`
            LEFT JOIN `nymbl_master`.`user` `cu` ON `c`.`created_by_id` = `cu`.`id`
            LEFT JOIN `nymbl_master`.`user` `subu` ON `sub`.`submitted_by_id` = `subu`.`id`
            LEFT JOIN `nymbl_master`.`user` `tpu` ON `rx`.`treating_practitioner_id` = `tpu`.`id`
            LEFT JOIN `device_type` `dt` ON `rx`.`device_type_id` = `dt`.`id`
            """;

    public static final String aiNotesUsageSelect = """
            tenant, id, first_name, last_name, appointment_type, status, start_time, end_time, audio_time, audio_length, created_at
            """;

    public static final String aiNotesUsage = """
            SELECT  
            :tenant AS `tenant`,
            `transcription_detail`.`id`,
            `user`.`first_name`,
            `user`.`last_name`, 
            `appointment_type`.`name` AS `appointment_type`,
            `transcription_detail`.`status`,
            `transcription_detail`.`start_time`,
            `transcription_detail`.`end_time`,
            `transcription_detail`.`audio_time`,
            `transcription_detail`.`audio_length`,
            `transcription_detail`.`created_at`
            FROM `transcription_detail`
            JOIN `appointment` ON `transcription_detail`.`appointment_id` = `appointment`.`id`
            JOIN `appointment_type` ON `appointment`.`appointment_type_id` = `appointment_type`.`id`
            JOIN `nymbl_master`.`user` ON `user`.`id` = `transcription_detail`.`practitioner_id`
            """;

    public static final String lincareAlacritiStatementSelect = """
            tenant, partner_key, customer_prefix, claim_id, patient_id, first_name, last_name, street_address, 
            street_address_line2, cell_phone, city, dob, email, work_phone, state, zipcode, patient_balance, 
            customer_eligibility_indicator, claim_submission_date
            """;

    public static final String lincareAlacritiStatement = """
    		SELECT :tenant AS `tenant`,
    		`cp`.`properties`->>'$.alacriti_partner_key' AS `partner_key`,
    		`cp`.`properties`->>'$.alacriti_customer_prefix' AS `customer_prefix`,
    		`c`.`id` AS `claim_id`, 
    		`p`.`id` AS `patient_id`, 
    		`p`.`first_name`, 
    		`p`.`last_name`,
    		`p`.`street_address`, 
    		`p`.`street_address_line2`, 
    		`p`.`cell_phone`, 
    		`p`.`city`, 
    		`p`.`dob`, 
    		COALESCE(`p`.`email`,'') AS `email`, 
    		COALESCE(`p`.`work_phone`,'') AS `work_phone`, 
    		`p`.`state`, 
    		`p`.`zipcode`,
    		`c`.`total_pt_responsibility_balance` AS `patient_balance`, 
    		'E' AS `customer_eligibility_indicator`,
    		MAX(`sub`.`submission_date`) AS `claim_submission_date`
    		FROM `claim` `c` 
    		LEFT JOIN `claim_submission` `sub` ON `c`.`id` = `sub`.`claim_id`
    		JOIN `prescription` `rx` ON `c`.`prescription_id` = `rx`.`id` 
    		JOIN `patient` `p` ON `rx`.`patient_id` = `p`.`id` 
    		JOIN `nymbl_master`.`company` `cp` ON `cp`.`key` = :tenant
    		WHERE `c`.`total_pt_responsibility_balance` <> 0
    		GROUP BY `partner_key`, `customer_prefix`, `c`.`id`
            """;

}
