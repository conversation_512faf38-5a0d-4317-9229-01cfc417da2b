package com.nymbl.tenant.controller;

import com.nymbl.config.Constants;
import com.nymbl.config.dto.*;
import com.nymbl.config.dto.DailyCloseReport.DailyCloseReportDTO;
import com.nymbl.config.dto.arReports.ArAgingReportDTO;
import com.nymbl.config.dto.arReports.SalesDetailDTO;
import com.nymbl.config.dto.outstandingBalanceReport.OutstandingBalanceReportDTO;
import com.nymbl.config.dto.reports.CashRow;
import com.nymbl.config.dto.reports.PurchasingHistoryReport;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.Timed;
import com.nymbl.tenant.dashboard.service.DailySalesOutstandingDtoService;
import com.nymbl.tenant.interfaces.service.ClericalReportsService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.service.*;
import io.sentry.Sentry;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.time.*;
import java.util.*;

@RestController
@RequestMapping("/api/report/ad-hoc")
public class AdHocReportController {

    private final PatientService patientService;
    private final PaymentService paymentService;
    private final DiagnosisCodeService diagnosisCodeService;
    private final AppointmentService appointmentService;
    private final PhysicianService physicianService;
    private final PrescriptionService prescriptionService;
    private final ClaimService claimService;
    private final NoteService noteService;
    private final Insurance_L_CodeAlertService insuranceLCodeAlertService;
    private final ClaimSubmissionService claimSubmissionService;
    private final AppliedPaymentService appliedPaymentService;
    private final CriticalMessageService criticalMessageService;
    private final InventoryItemService inventoryItemService;
    private final SurveyService surveyService;
    private final InsuranceVerificationService insuranceVerificationService;
    private final UserService userService;
    private final ChecklistService checklistService;
    private final AdHocReportService adHocReportService;
    private final PatientStatementService patientStatementService;
    private final DailySalesOutstandingDtoService dailySalesOutstandingDtoService;

    private final ClericalReportsService clericalReportsService;


    @Autowired
    public AdHocReportController(PatientService patientService,
                                 PaymentService paymentService,
                                 DiagnosisCodeService diagnosisCodeService,
                                 AppointmentService appointmentService,
                                 PhysicianService physicianService,
                                 PrescriptionService prescriptionService,
                                 ClaimService claimService,
                                 NoteService noteService,
                                 Insurance_L_CodeAlertService insuranceLCodeAlertService,
                                 ClaimSubmissionService claimSubmissionService,
                                 AppliedPaymentService appliedPaymentService,
                                 CriticalMessageService criticalMessageService,
                                 InventoryItemService inventoryItemService,
                                 SurveyService surveyService,
                                 InsuranceVerificationService insuranceVerificationService,
                                 UserService userService,
                                 ChecklistService checklistService,
                                 AdHocReportService adHocReportService,
                                 PatientStatementService patientStatementService, DailySalesOutstandingDtoService dailySalesOutstandingDtoService, ClericalReportsService clericalReportsService) {
        super();
        this.patientService = patientService;
        this.paymentService = paymentService;
        this.diagnosisCodeService = diagnosisCodeService;
        this.appointmentService = appointmentService;
        this.physicianService = physicianService;
        this.prescriptionService = prescriptionService;
        this.claimService = claimService;
        this.noteService = noteService;
        this.insuranceLCodeAlertService = insuranceLCodeAlertService;
        this.claimSubmissionService = claimSubmissionService;
        this.appliedPaymentService = appliedPaymentService;
        this.criticalMessageService = criticalMessageService;
        this.inventoryItemService = inventoryItemService;
        this.surveyService = surveyService;
        this.insuranceVerificationService = insuranceVerificationService;
        this.userService = userService;
        this.checklistService = checklistService;
        this.adHocReportService = adHocReportService;
        this.patientStatementService = patientStatementService;
        this.dailySalesOutstandingDtoService = dailySalesOutstandingDtoService;
        this.clericalReportsService = clericalReportsService;
    }

    /**
     * Year by Year Billings vs Collected
     */
    @Timed
    @GetMapping(value = "/yearly-billings-collected/chart")
    public ResponseEntity<?> yearlyBillingsCollected(@RequestParam(name = "branchId", required = false) Long branchId,
                                                     @RequestParam(name = "startYear") Long startYear,
                                                     @RequestParam(name = "endYear") Long endYear) {

        Map<String, List<Object>> results = paymentService.billingCollectionsByYear(branchId, startYear, endYear);
        return ResponseEntity.ok(results);
    }

    /**
     * Monthly Receivables/Collections - Month by Month Billings vs Collected
     */
    @Timed
    @GetMapping(value = "/monthly-billings-collected/chart")
    public ResponseEntity<?> monthlyBillingsCollected(@RequestParam(name = "branchId", required = false) Long branchId,
                                                      @RequestParam(name = "numberMonths") Long numberMonths) {

        Map<String, List<Object>> results = paymentService.billingCollectionsByMonth(branchId, numberMonths);
        return ResponseEntity.ok(results);
    }

    /**
     * **** Is this really needed???
     * <p>
     * Percent Collections By Month - Month by Month value of Collected/Billings
     */
    @Timed
    @GetMapping(value = "/monthly-collected-percentage/chart")
    public ResponseEntity<?> monthlyCollectedPercentage(@RequestParam(name = "branchId", required = false) Long branchId,
                                                        @RequestParam(name = "numberMonths") Long numberMonths) {

        Map<String, List<Object>> results = paymentService.percentCollectedByMonth(branchId, numberMonths);
        return ResponseEntity.ok(results);
    }

    /**
     * List report of all billings within a number of months, grouped and totaled by Practitioner
     */
    @Timed
    @GetMapping(value = "/monthly-billings-by-practitioner/list")
    public ResponseEntity<?> monthlyBillingsByPractitioner(@RequestParam(name = "branchId", required = false) Long branchId,
                                                           @RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date startDate,
                                                           @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date endDate,
                                                           @RequestParam(name = "lCodeDisplay") String lCodeDisplay,
                                                           @RequestParam(name = "usePatientBranch") String usePatientBranch) {

        List<PractitionerBillingResultsDTO> results = paymentService.monthlyBillingsByPractitioner(branchId, startDate, endDate, lCodeDisplay, usePatientBranch);
        return ResponseEntity.ok(results);
    }

    /**
     * List report of all billings within a number of months, grouped and totaled by Practitioner
     */
    @Timed
    @GetMapping(value = "/monthly-billings-by-representative/list")
    public ResponseEntity<?> monthlyBillingsByRepresentative(@RequestParam(name = "branchId", required = false) Long branchId,
                                                       @RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date startDate,
                                                       @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date endDate,
                                                       @RequestParam(name = "lCodeDisplay") String lCodeDisplay,
                                                       @RequestParam(name = "usePatientBranch") String usePatientBranch) {

        List<PractitionerBillingResultsDTO> results = paymentService.monthlyBillingsByRepresentative(branchId, startDate, endDate, lCodeDisplay, usePatientBranch);
        return ResponseEntity.ok(results);
    }

    /**
     * WIP and Billables Outstanding - List Report of all 'incomplete' RX, aging, estimated values
     */
    @Timed
    @GetMapping(value = "/wip-billings-collected/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> wipBillingsCollected(@RequestParam(name = "branchId", required = false) Long branchId) {

        WipBillingsReportDTO results = prescriptionService.wipBillingsCollected(branchId);
        return ResponseEntity.ok(results);
    }

    /**
     * Un-claimed Prescriptions Report - List all Prescriptions with no claims.
     */
    @Timed
    @GetMapping(value = "/un-claimed-prescriptions/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> unClaimedPrescriptions(@RequestParam(name = "branchId", required = false) Long branchId,
                                                    @RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                    @RequestParam(name = "completeStatus", required = false) String completeStatus) {

        List<PrescriptionReportDTO> results = prescriptionService.getUnClaimedPrescriptionsForReport(branchId, completeStatus, startDate);
        return ResponseEntity.ok(results);
    }

    /**
     * Delivered Prescriptions Without Survey Txt Report  - List all Delivered Prescriptions Without Survey Text Sent.
     */
    @Timed
    @GetMapping(value = "/survey/for-appointment-types", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> surveyTextMessagesForAppointmentTypes(@RequestParam(name = "branchId", required = false) Long branchId,
                                                                   @RequestParam(name = "startDateTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDateTime,
                                                                   @RequestParam(name = "endDateTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDateTime,
                                                                   @RequestParam(name = "appointmentTypeId", required = false) Long appointmentTypeId,
                                                                   @RequestParam(name = "appointmentStatusName", required = false) String appointmentStatusName,
                                                                   @RequestParam(name = "surveySent") String surveySent,
                                                                   @RequestParam(name = "startSurveySentDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startSurveySentDate,
                                                                   @RequestParam(name = "endSurveySentDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endSurveySentDate) {

        List<SurveyTextMessagesForAppointmentTypesDTO> results = appointmentService.surveyTextMessagesForAppointmentTypes(branchId, startDateTime, endDateTime, appointmentTypeId, appointmentStatusName, surveySent, startSurveySentDate, endSurveySentDate);

        return ResponseEntity.ok(results);
    }

    /**
     * Report of phone number SMS compliance.
     */
    @Timed
    @GetMapping(value = "/check-sms-compliance-user", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> checkSmsComplianceUser(@RequestParam(name = "userId", required = false) Long userId, @RequestParam(name = "phoneNumber", required = false) String phoneNumber) {
        List<User> results = userService.findAllBySmsCompliantCellIsFalse(userId, phoneNumber, TenantContext.getCurrentTenant());

        return ResponseEntity.ok(results);
    }

    /**
     * Report of phone number SMS compliance.
     */
    @Timed
    @GetMapping(value = "/check-sms-compliance-patient", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> checkSmsCompliancePatient(@RequestParam(name = "branchId", required = false) Long branchId, @RequestParam(name = "patientId", required = false) Long patientId, @RequestParam(name = "phoneNumber", required = false) String phoneNumber) {
        List<Patient> results = patientService.findAllByPrimaryBranchIdAndSmsCompliantCell(branchId, patientId, phoneNumber);
        return ResponseEntity.ok(results);
    }

    /**
     * Details of sent survey text messages.
     */
    @Timed
    @GetMapping(value = "/survey/sent-text-message-details", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> sentTextMessageDetails(@RequestParam(name = "branchId", required = false) Long branchId,
                                                    @RequestParam(name = "appointmentTypeId", required = false) Long appointmentTypeId,
                                                    @RequestParam(name = "patientId", required = false) Long patientId,
                                                    @RequestParam(name = "phoneNumber", required = false) String phoneNumber,
                                                    @RequestParam(name = "startConfirmationRequestSentDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startConfirmationRequestSentDate,
                                                    @RequestParam(name = "endConfirmationRequestSentDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endConfirmationRequestSentDate) {

        List<SurveyTextMessagesForAppointmentTypesDTO> results = appointmentService.sentTextMessageDetails(branchId, appointmentTypeId, patientId, phoneNumber, startConfirmationRequestSentDate, endConfirmationRequestSentDate);

        return ResponseEntity.ok(results);
    }

    /**
     * Delivered Prescriptions Without Survey Txt Report  - List all Delivered Prescriptions Without Survey Text Sent.
     */
    @Timed
    @GetMapping(value = "/survey/no-text-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> prescriptionsWithoutSurveyText(@RequestParam(name = "branchId", required = false) Long branchId,
                                                            @RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                            @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        List<PrescriptionWithoutSurveyTextDTO> results = prescriptionService.prescriptionsWithoutSurveyText(branchId, startDate, endDate);
        return ResponseEntity.ok(results);
    }
    /**
     * Prescriptions On Hold Report
     */
    @Timed
    @GetMapping(value = "/prescription/on-hold", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> prescriptionsOnHold(@RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                 @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        List<Prescription> results = prescriptionService.prescriptionsOnHold(startDate, endDate);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/survey-response/list")
    public ResponseEntity<?> surveyResponse(@RequestParam(name = "branchId", required = false) Long branchId,
                                            @RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                            @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        List<Survey> result = surveyService.findAllByBranchIdAndCreatedAtBetween(branchId, startDate, endDate);
        return ResponseEntity.ok(result);
    }

    /**
     * New Patients by Month Report - Graph report of X number of months prior
     */
    @Timed
    @GetMapping(value = "/new-prescriptions-by-month/chart")
    public ResponseEntity<?> newPrescriptionsByMonth(@RequestParam(name = "numberMonths") Long numberMonths) {

        Map<String, List<Object>> results = prescriptionService.countNewPrescriptionsByMonth(numberMonths);
        return ResponseEntity.ok(results);
    }

    /**
     * Get patients by date of birth month
     */
    @Timed
    @GetMapping(value = "/patient/birthday/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> patientBirthdayList(@RequestParam(name = "branchId", required = false) Long branchId,
                                                 @RequestParam(name = "dobMonth") String dobMonth) {

        List<Patient> results = patientService.patientListByDOBMonth(branchId, DateUtil.getMonth(dobMonth));
        return ResponseEntity.ok(results);
    }

    /**
     * Get patients by branchId
     */
    @Timed
    @GetMapping(value = "/patient/branch/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> patientList(@RequestParam(name = "branchId", required = false) Long branchId) {

        List<Patient> results = patientService.getPatients("", null, branchId);
        return ResponseEntity.ok(results);
    }


    /**
     * Get diagnosis codes for report
     */
    @Timed
    @GetMapping(value = "/diagnosis-code/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> diagnosisCodeList(@RequestParam(name = "active", required = false) Boolean active,
                                               @RequestParam(name = "codeSet", required = false) String codeSet) {

        List<DiagnosisCode> results = diagnosisCodeService.search("", codeSet, active);
        return ResponseEntity.ok(results);
    }

    /**
     * Billing Totals by Referring Physician
     */
    @Timed
    @GetMapping(value = "/billings-referring-physician/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> billingsByReferringPhysician(@RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                          @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                          @RequestParam(name = "branchId", required = false) Long branchId,
                                                          @RequestParam(name = "usePatientBranch") String usePatientBranch) {

        List<ReferringPhysicianBillingTotalDTO> results =
                adHocReportService.billingsByReferringPhysician(new java.sql.Date(startDate.getTime()), new java.sql.Date(endDate.getTime()), branchId, usePatientBranch);
        return ResponseEntity.ok(results);
    }

    /**
     * Physician Monthly Visit Count Report
     */
    @Timed
    @GetMapping(value = "/appointment/physician-monthly-count/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> monthlyPhysicianVisitCount(@RequestParam(name = "startDateTime", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDateTime) {
        List<PhysicianVisitCountDTO> results = appointmentService.getMonthlyPhysicianCountDTO(startDateTime);
        return ResponseEntity.ok(results);
    }

    /**
     * Get appointments by date range
     */
    @Timed
    @GetMapping(value = "/appointment/appointmentDateRange/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> appointmentList(@RequestParam(name = "branchId", required = false) Long[] branchId,
                                             @RequestParam(name = "appointmentTypeId", required = false) Long appointmentTypeId,
                                             @RequestParam(name = "startDateTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDateTime,
                                             @RequestParam(name = "endDateTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDateTime,
                                             @PageableDefault(size = 10000, sort = {"startDateTime", "patient.lastName"}, direction = Sort.Direction.DESC) Pageable pageable) {// value = 10000 and size = 10000 didn't override the 2000 limit for page, had to create a page request in AppointmentService.

        List<Appointment> results = appointmentService.search(branchId, appointmentTypeId, null, null, null, startDateTime, endDateTime, pageable);
        return ResponseEntity.ok(results);
    }

    /**
     * Get canceled appointments that need to be rescheduled.
     */
    @Timed
    @GetMapping(value = "/appointment/{status}/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> appointmentsByStatusWithNoReschedule(@PathVariable String status,
                                                                  @RequestParam(name = "startDateTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDateTime,
                                                                  @RequestParam(name = "endDateTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDateTime,
                                                                  @RequestParam(name = "branchId", required = false) Long branchId) {
        List<Appointment> results = appointmentService.getAllAppointmentsByDateRangeByStatusWithNoReschedule(startDateTime, endDateTime, status, branchId);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/physician/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> physicianList() {

        List<Physician> results = physicianService.search(null, null, null).getContent();
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/patient/referral-source/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> patientByReferralSource(@RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                     @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                     @RequestParam(name = "branchId", required = false) Long branchId) {

        List<Prescription> results = prescriptionService.getPatientByReferralSource(startDate, endDate, branchId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/quick-sight/dashboards", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> dashBoardsByQuickSight() {
        List<String> embedUrlList = userService.populateQuickSightDashboardEmbedUrlList();
        return ResponseEntity.ok(embedUrlList);
    }

    /**
     * List all Claims with total patient responsibility balance greater than 0.
     */
    @Timed
    @GetMapping(value = "/outstanding-patient-responsibility-balance/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> outstandingPatientResponsibilityBalance(@RequestParam(name = "branchId", required = false) Long branchId,
                                                                     @PageableDefault(value = 10000) Pageable pageable) {

//        List<Claim> claimList = claimService.search(null, null, branchId, null, null, null, null, null, pageable);
//        List<Claim> results = new ArrayList<>(claimList);
//        results.removeIf((claim) ->
//                claim.getTotalPtResponsibilityBalance() == null || claimService.compare(claim.getTotalPtResponsibilityBalance(), "<=", BigDecimal.ZERO)
//        );

        List<Claim> claimList = claimService.getOutstandingPatientResponsibilityBalanceClaims(branchId);

        return ResponseEntity.ok(claimList);
    }

    /**
     * Unapproved Notes - List all Notes that have not been approved
     */
    @Timed
    @GetMapping(value = "/unapproved-notes/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> unapprovedNotes(@RequestParam(name = "userId", required = false) Long userId,
                                             @RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                             @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                             @RequestParam(name = "branchId", required = false) Long branchId) {

        ZoneId userZoneId = ZoneId.of(TenantContext.getUserTimezoneId());
        LocalDateTime endOfDayForEndDate = endDate.toInstant()
                .atZone(userZoneId)
                .toLocalDate()
                .atTime(LocalTime.MAX);
        Date convertedEndDate = Date.from(endOfDayForEndDate.atZone(userZoneId).toInstant());
        List<Note> results = noteService.getByUserIdAndCosignedIsNullAndPrescriptionIdIsNotNullAndCreatedAtBetween(userId, startDate, convertedEndDate, branchId);
        return ResponseEntity.ok(results);
    }

    /**
     * Loop insurance_company, for each insurance_company loop claim based on report parameters and
     * if claim.prescription.patient_insurance.insurance_company.id == insurance_company.id
     * creating sum(claim.total_claim_balance) grouped by the day ranges of claim_submission.submission_date (unique by claim_id)
     * grouped by insurance_company
     * <p>
     * Then sums each bucket to return its totals
     */
    @Timed
    @GetMapping(value = "/outstanding-insurance-balances/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> outstandingInsuranceBalances(@RequestParam(name = "branchId", required = false) Long branchId,
                                                          @RequestParam(name = "useSubmissionDate") boolean useSubmissionDate,
                                                          @RequestParam(name = "hideNegativeBalance") boolean hideNegativeBalance,
                                                          @RequestParam(name = "useBillable") boolean useBillable) throws CloneNotSupportedException {

        OutstandingBalanceReportDTO result = adHocReportService.getOutstandingBalanceReportDTO(branchId, useSubmissionDate, hideNegativeBalance, useBillable);
        return ResponseEntity.ok(result);
    }


    /**
     * List all Claims with total patient responsibility balance less than 0.
     */
    @Timed
    @GetMapping(value = "/patients-owed-refund/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> patientRefunds(@RequestParam(name = "branchId", required = false) Long branchId) {
        List<Claim> results = claimService.getPatientRefundClaims(branchId);
        return ResponseEntity.ok(results);
    }

    /**
     * Billings By L-Code - List report of all LCodes with billings and the billing totals of each
     */
    @Timed
    @GetMapping(value = "/lcode-billings/list")
    public ResponseEntity<?> lCodeBilledCharges(@RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        List<L_CodeBillingDTO> results = paymentService.billingsByLCode(startDate, endDate);
        return ResponseEntity.ok(results);
    }

    /**
     * @return count(distinct ( claim.claim_id)),
     * sum(distinct (insurance_verification_l_code.total_allowable))
     * sum(distinct (claim.total_allowable))
     */
    @Timed
    @GetMapping(value = "/total-billed/list")
    public ResponseEntity<?> totalBilled(@RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                         @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        ClaimTotalsReportDTO result = claimService.getClaimTotalsReportDTOList(startDate, endDate);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/patient/critical-messages/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> patientsWithCriticalMessages(@RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                          @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                          @RequestParam(name = "branchId", required = false) Long branchId) {

        List<CriticalMessage> results = criticalMessageService.findAllByDateBetween(startDate, endDate, branchId);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/claim/additional-comment/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> claimsWithAdditionalComment() {

        List<Claim> results = claimService.findByAdditionalCommentIsNotNull();
        return ResponseEntity.ok(results);
    }

    /**
     * <pre>
     * Get all applied_payment where applied_date between :startDate and :endDate groups by applied_by and
     * Total Billed = sum of applied_payment.claim.total_claim_amount (for each unique prescription_id in claims)
     * Total Amount Applied = sum of applied_payment.amount_applied
     * Total Adjustment Applied = sum of applied_payment.adjustment_applied
     * While looping applied_payment collect list of claims for current applied_payment (sub list of applied_payment User sum line)
     * </pre>
     */
    @Timed
    @GetMapping(value = "general-batch-summary/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> generalBatchSummaryList(@RequestParam(name = "startDate") String startDate,
                                                     @RequestParam(name = "endDate") String endDate) {

        List<Map<String, Object>> results =
                appliedPaymentService.generalBatchSummaryList(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD), DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD));
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "claims-summary/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> claimsSummary(@RequestParam(name = "startDate") String startDate,
                                           @RequestParam(name = "endDate") String endDate,
                                           @RequestParam(name = "branchId", required = false) Long branchId,
                                           @RequestParam(name = "dateType") String dateType) {

        Map<String, Object> results = claimService.claimsSummary(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD)
                , DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId, dateType);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "uncollected/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> uncollected(@RequestParam(name = "branchId", required = false) Long branchId,
                                         @RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                         @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
//        List<Claim> results = claimService.uncollected(branchId, startDate, endDate);
        List<PatientStatement> results = patientStatementService.findAllPatientStatementsForClaimsSentToCollectionsReport(branchId, "sent_to_collections", startDate, endDate);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "claims-summary/pivotTableExport", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> claimsSummaryPivotTableExport(@RequestParam(name = "startDate") String startDate,
                                                           @RequestParam(name = "endDate") String endDate,
                                                           @RequestParam(name = "branchId", required = false) Long branchId,
                                                           @RequestParam(name = "dateType") String dateType) {

        String results = claimService.claimsSummaryPivotTableExport(DateUtil.getDate(startDate,
                        Constants.DF_YYYY_MM_DD)
                , DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId, dateType);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/delivered-prescriptions/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> deliveredPrescriptionsList(@RequestParam(name = "branchId", required = false) Long branchId,
                                                        @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDeliveredOn,
                                                        @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDeliveredOn,
                                                        @PageableDefault(value = 1000) Pageable pageable) {

        List<Map<String, Object>> results = prescriptionService.deliveredPrescriptionsList(branchId, startDeliveredOn, endDeliveredOn);
        return ResponseEntity.ok(results);
    }

    /**
     * Loop all insurance companies, for each insurance loop all claim submissions and
     * sum the claim_submission.claim.total_claim_amount (by unique claim.prescription_id) grouped by insurance company
     */
    @Timed
    @GetMapping(value = "/insurance-companies-billed/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> insuranceCompaniesBilled(@RequestParam(name = "branchId", required = false) Long branchId,
                                                      @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                      @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                      @PageableDefault(value = 1000) Pageable pageable) {

        List<InsuranceCompanyBilledReportDTO> results =
                claimSubmissionService.insuranceCompaniesBilled(branchId, new java.sql.Date(startDate.getTime()), new java.sql.Date(endDate.getTime()), pageable);
        return ResponseEntity.ok(results);

    }

    @Timed
    @GetMapping(value = "/low-inventory-stock/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> lowInventoryStockList(@RequestParam(name = "branchId", required = false) Long branchId) {
        List<InventoryItem> results = inventoryItemService.getLowInventoryStock(branchId);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/note/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> noteList(@RequestParam(name = "userId", required = false) Long userId,
                                      @RequestParam(name = "startDate", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                      @RequestParam(name = "endDate", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                      @RequestParam(name = "branchId", required = false) Long branchId,
                                      @RequestParam(name = "noteType", required = false) String noteType) {

        List<Note> results = noteService.getByUserIdAndCreatedAtBetweenAndBranchIdAndNoteType(userId, startDate, endDate, branchId, noteType);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/l-code-alerts/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> lCodeAlertsList(@RequestParam(name = "insuranceCompanyId", required = false) Long insuranceCompanyId) {

        List<Insurance_L_CodeAlert> results = insuranceLCodeAlertService.findLCodeAlerts(insuranceCompanyId);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/patient/device-category/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getPatientsByCategory(@RequestParam(name = "branchId", required = false) Long branchId,
                                                   @RequestParam(name = "category") String category,
                                                   @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                   @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        List<Map<String, Object>> results = prescriptionService.findByCategory(branchId, category, new java.sql.Date(startDate.getTime()), new java.sql.Date(endDate.getTime()));
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/device-type-prescription", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> averageDaysToDeliverByDeviceType(
            @RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(name = "branchId", required = false) Long branchId) {
        List<PrescriptionDeviceDTO> results = prescriptionService.getAverageDaysToDeliver(new java.sql.Date(startDate.getTime()), new java.sql.Date(endDate.getTime()), branchId);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/fabrications-past-due-date", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getFabricationsPastDueDateForReport(@RequestParam(name = "branchId", required = false) Long branchId,
                                                                 @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate) {
        List<FabricationPastDueDateDTO> results = checklistService.getFabricationsPastDueDateForReport(branchId, startDate);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/user-batch-summary/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> userBatchSummaryList(@RequestParam(name = "userId") Long userId,
                                                  @RequestParam(name = "startDate") String startDate,
                                                  @RequestParam(name = "endDate") String endDate) {

        List<Map<String, Object>> results =
                appliedPaymentService.userBatchSummaryList(userId, DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD), DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD));
        return ResponseEntity.ok(results);
    }

    /**
     * Get all claim where submission_date etween <start> and <end>
     * Loops all applied_payment returning the most recent applied_date fot
     * Loops all prescription_l_code returning (prescription_l_code.total_allowable * prescription_l_code.quantity)
     */
    @Timed
    @GetMapping(value = "/billings-by-date-of-service/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> billingsByDateOfService(@RequestParam(name = "branchId", required = false) Long branchId,
                                                     @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                     @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                     @RequestParam(name = "usePatientBranch") String usePatientBranch) {

        Map<String, List<ClaimBillingsByDeliveryDateDTO>> results = adHocReportService.billingsByDateOfService(branchId, new java.sql.Date(startDate.getTime()), new java.sql.Date(endDate.getTime()), usePatientBranch);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/ar-aging/chart", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> arAgingReportChart(@RequestParam(name = "startDate") String startDate,
                                                @RequestParam(name = "branchId", required = false) Long branchId,
                                                @RequestParam(name = "dateOption") String dateOption) {
        // refuse to run AR aging and log the user
        ArAgingReportDTO result = new ArAgingReportDTO(); // adHocReportService.arAgingReport(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD), branchId, dateOption);
        Sentry.captureMessage("AR Aging report was attempted by: " + userService.getCurrentUser().getUsername());
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    @Timed
    @GetMapping(value = "/billing-summary/{startDate}/{endDate}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> billingSummary(@PathVariable String startDate,
                                            @PathVariable String endDate,
                                            @RequestParam(name = "branchId", required = false) Long branchId,
                                            @RequestParam(name = "deviceType", required = false) String deviceType) {

        BillingSummaryDTO dto = adHocReportService.billingSummary(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD),
                DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId, deviceType);
        return new ResponseEntity<>(dto, HttpStatus.CREATED);
    }

    @Timed
    @GetMapping(value = "/payments-by-payer/{startDate}/{endDate}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> paymentsByPayer(@PathVariable String startDate,
                                             @PathVariable String endDate,
                                             @RequestParam(name = "branchId", required = false) Long branchId,
                                             @RequestParam(name = "dateOption", required = false) String dateOption) {

        List<PaymentsByPayerWithTotalsDTO> dto = adHocReportService.paymentsByPayer(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD),
                DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId, dateOption);
        return new ResponseEntity<>(dto, HttpStatus.CREATED);
    }

    @Timed
    @GetMapping(value = "/cash-summary/{startDate}/{endDate}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> cashSummary(@PathVariable String startDate,
                                         @PathVariable String endDate,
                                         @RequestParam(name = "branchId", required = false) Long branchId,
                                         @RequestParam(name = "deviceType", required = false) String deviceType,
                                         @RequestParam(name = "dateOption", required = true) String dateOption) {

        BillingSummaryDTO dto = adHocReportService.cashSummary(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD),
                DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId, deviceType, dateOption);
        return new ResponseEntity<>(dto, HttpStatus.CREATED);
    }

    @Timed
    @GetMapping(value = "/sales-summary/{startDate}/{endDate}/{dateOption}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> salesSummary(@PathVariable String startDate,
                                          @PathVariable String endDate,
                                          @PathVariable String dateOption,
                                          @RequestParam(name = "branchId", required = false) Long branchId,
                                          @RequestParam(name = "usePatientBranch") String usePatientBranch) {

        Map<String, BillingSummaryDTO> salesSummaryMap = adHocReportService.salesSummary(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD),
                DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId, dateOption, usePatientBranch);
        return new ResponseEntity<>(salesSummaryMap, HttpStatus.CREATED);
    }

    @Timed
    @GetMapping(value = "/sales-detail/{startDate}/{endDate}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> salesDetail(@PathVariable String startDate,
                                         @PathVariable String endDate,
                                         @RequestParam(name = "branchId", required = false) Long branchId,
                                         @RequestParam(name = "usePatientBranch") String usePatientBranch) {

        Map<String, SalesDetailDTO> salesDetailList = adHocReportService.salesDetail(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD),
                DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId, usePatientBranch);
        return new ResponseEntity<>(salesDetailList, HttpStatus.CREATED);
    }

    @Timed
    @GetMapping(value = "/sales-tax-report/{startDate}/{endDate}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> salesTaxReport(@PathVariable String startDate,
                                            @PathVariable String endDate) {
        SalesTaxReportResultsDTO result = adHocReportService.salesTaxReport(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD),
                DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD));
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    @Timed
    @GetMapping(value = "/insurance-verification/expiring-pre-auth", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<InsuranceVerification> expiringPreAuthList(@RequestParam(name = "startDate") String startDate,
                                                           @RequestParam(name = "endDate") String endDate,
                                                           @RequestParam(name = "branchId", required = false) Long branchId) {

        List<InsuranceVerification> results = insuranceVerificationService.findByExpirationDateWithInTwoWeeksAndBranchId(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD), DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId);

        return results;
    }

    @Deprecated
    @Timed
    @GetMapping(value = "/daily-close/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> dailyClose(@RequestParam(name = "payeeType", required = false) String payeeType,
                                        @RequestParam(name = "dateOption", required = false) String dateOption,
                                        @RequestParam(name = "startDate", required = false) String startDate,
                                        @RequestParam(name = "endDate", required = false) String endDate,
                                        @RequestParam(name = "branchId", required = false) Long branchId) {
        DailyCloseReportDTO result = paymentService.dailyClose(payeeType, dateOption, DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD), DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId);

        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/daily-close/list/new", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> dailyCloseNew(@RequestParam(name = "payeeType", required = false) String payeeType,
                                        @RequestParam(name = "dateOption", required = false) String dateOption,
                                        @RequestParam(name = "startDate", required = false) String startDate,
                                        @RequestParam(name = "endDate", required = false) String endDate,
                                        @RequestParam(name = "branchId", required = false) Long branchId) {
        DailyCloseReportDTO result = paymentService.dailyCloseNew(payeeType, dateOption, DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD), DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId);

        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/daily-close/v2/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> dailyCloseV2(@RequestParam(name = "payeeType", required = false) String payeeType,
                                          @RequestParam(name = "dateOption", required = false) String dateOption,
                                          @RequestParam(name = "startDate", required = false) String startDate,
                                          @RequestParam(name = "endDate", required = false) String endDate,
                                          @RequestParam(name = "branchId", required = false) Long branchId) {
        List<CashRow> result = paymentService.getDailyCloseV2(payeeType, dateOption, DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD), DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/practitioner-commissions/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getPractitionerCommissionReport(@RequestParam(name = "startDate", required = false) String startDate,
                                                             @RequestParam(name = "endDate", required = false) String endDate,
                                                             @RequestParam(name = "branchId", required = false) Long branchId,
                                                             @RequestParam(name = "dateOption", required = false) String dateOption,
                                                             @RequestParam(name = "deviceType", required = false) String deviceType) {
        List<PractitionerCommissionsDTO> result = paymentService.getPractitionerCommissions(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD),
                DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId, dateOption, deviceType);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/representative-commissions/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getUserCommissionReport(@RequestParam(name = "startDate", required = false) String startDate,
                                                     @RequestParam(name = "endDate", required = false) String endDate,
                                                     @RequestParam(name = "branchId", required = false) Long branchId,
                                                     @RequestParam(name = "dateOption", required = false) String dateOption,
                                                     @RequestParam(name = "deviceType", required = false) String deviceType) {
        List<PractitionerCommissionsDTO> result = paymentService.getRepresentativeCommissions(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD),
                DateUtil.getDate(endDate, Constants.DF_YYYY_MM_DD), branchId, dateOption, deviceType);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/user/clerical-productivity/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getClericalProductivityReport(@RequestParam(name = "branchId", required = false) Long branchId,
                                                           @RequestParam(name = "startDate", required = false) java.sql.Date startDate,
                                                           @RequestParam(name = "endDate", required = false) java.sql.Date endDate,
                                                           @RequestParam(name = "userIds", required = false) List<Long> userIds,
                                                           @RequestParam(name = "timeZone", required = false) TimeZone timeZone) {
        if(timeZone == null)
            timeZone = TimeZone.getTimeZone(ZoneId.systemDefault());
        ZonedDateTime zonedStartDate = ZonedDateTime.of(startDate.toLocalDate().atTime(0,0), timeZone.toZoneId());
        ZonedDateTime zonedEndRangeEODDate = ZonedDateTime.of(endDate.toLocalDate().atTime(23,59), timeZone.toZoneId());

        List<ClericalProductivityReportDTO> dtos = userService.getClericalProductivityByUserIdBetween(branchId, zonedStartDate, zonedEndRangeEODDate, userIds);

        return ResponseEntity.ok(dtos);
    }

    @Timed
    @GetMapping(value = "/export/{className}")
    public void createExcelReportToExport(@PathVariable String className,
                                          @RequestParam(name = "includeFields") ArrayList<String> includedFields,
                                          @RequestParam(name = "includeKeys", required = false) ArrayList<String> includedKeys,
                                          HttpServletRequest request, HttpServletResponse response) throws Exception {

        ArrayList<String> emptyKeys = new ArrayList<String>(); // SCRUM-5304: never export additional tables...
        ByteArrayOutputStream outByteStream = adHocReportService.getServerGeneratedExportFile(className, includedFields, emptyKeys);//includedKeys);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setContentLength(outByteStream.toByteArray().length);
        response.setHeader("Expires", "0"); // eliminates browser caching
        response.setHeader("Content-Disposition", "attachment; filename=download.xls");
        OutputStream outStream = response.getOutputStream();
        outStream.write(outByteStream.toByteArray());
        outStream.flush();
        outStream.close();
    }

    @Timed
    @GetMapping(value = "/widget/daily-sales-outstanding", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> dailySalesOutstandingWidget(@RequestParam(name = "startDate") String startDate,
                                                         @RequestParam(name = "branchIds", required = false) List<Long> branchIds) {
        // SCRUM-4433 (later...more disabled)
        //List<DailySalesOutstandingWidgetDTO> results = dailySalesOutstandingDtoService.getDailySalesOutstandingDto(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD), branchIds);
        //List<DailySalesOutstandingWidgetDTO> results = adHocReportService.dailySalesOutstandingWidget(DateUtil.getDate(startDate, Constants.DF_YYYY_MM_DD), branchIds);

        Sentry.captureMessage("Daily Sales Outstanding widget was attempted by: " + userService.getCurrentUser().getUsername());

        return new ResponseEntity<>(null, HttpStatus.CREATED);
    }

    @GetMapping(value = "/table/listings")
    public ResponseEntity<?> tableListing() {
        return ResponseEntity.ok(adHocReportService.tableListing());
    }

    @Timed
    @GetMapping(value = "/table/metadata/{className}")
    public ResponseEntity<?> tableMetadata(@PathVariable String className) throws Exception {
        return ResponseEntity.ok(adHocReportService.getClassMetaData(className));
    }

    @Timed
    @GetMapping(value = "/daily-close-export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity<?> dailyCloseExport() {
        try {
            String export = adHocReportService.dailyCloseExport();
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"daily-close-export.csv\"")
                    .body(export);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Timed
    @GetMapping(value = "/sales-detail-export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity<?> salesDetailExport() {
        try {
            String export = adHocReportService.salesDetailExport();
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"sales-detail-export.csv\"")
                    .body(export);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Timed
    @GetMapping(value = "/purchasing-history", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> purchasingHistory(@RequestParam(name = "branchId", required = false) Long branchId,
                                               @RequestParam(name = "vendorId", required = false) Long vendorId,
                                               @RequestParam(name = "itemId", required = false) Long itemId,
                                               @RequestParam(name = "partNumber", required = false) String partNumber,
                                               @RequestParam(name = "startDate", required = false) java.sql.Date startDate,
                                               @RequestParam(name = "endDate", required = false) java.sql.Date endDate) {
        PurchasingHistoryReport result = adHocReportService.getPurchasingHistoryReport(branchId, vendorId, itemId, partNumber, startDate, endDate);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/clerical/userId/{userId}/", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> clericalReports_V2(@RequestParam(name = "branchId", required = false) Long branchId,
                                                @PathVariable(name = "userId") Long userId,
                                                @RequestParam(name = "startDate") java.sql.Date startDate,
                                                @RequestParam(name = "endDate") java.sql.Date endDate,
                                                @RequestParam(name = "entityName") String entityName,
                                                @RequestParam(name = "timeZone", required = false) TimeZone timeZone) {
        if(timeZone == null)
            timeZone = TimeZone.getTimeZone(ZoneId.systemDefault());
        ZonedDateTime zonedStartDate = ZonedDateTime.of(startDate.toLocalDate().atTime(0,0), timeZone.toZoneId());
        ZonedDateTime zonedEndRangeEODDate = ZonedDateTime.of(endDate.toLocalDate().atTime(23,59), timeZone.toZoneId());


        return ResponseEntity.ok(clericalReportsService.getUserLists(branchId, userId, zonedStartDate, zonedEndRangeEODDate, entityName));
    }

}
