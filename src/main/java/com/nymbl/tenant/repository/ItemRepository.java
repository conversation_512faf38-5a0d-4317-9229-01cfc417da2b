package com.nymbl.tenant.repository;

import com.nymbl.tenant.model.Item;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.List;

public interface ItemRepository extends JpaRepository<Item, Long>, JpaSpecificationExecutor<Item> {

    String selectClause = "SELECT ibv.*\n";
    String fromClause = "FROM `item_by_vendor` ibv INNER JOIN `item_by_manufacturer` ibm on ibm.`id` = ibv.`item_by_manufacturer_id`\n";
    String whereClause = "WHERE (:showInactive = 1 OR ibv.`active` = 1)\n" +
            "AND (:oneTimePurchase IS NULL OR :oneTimePurchase = 0 OR ibv.`one_time_purchase` = 1)\n" +
            "AND (CONCAT(:childCategoryIds) IS NULL OR ibm.`l_code_category_id` IN (:childCategoryIds))\n" +
            "AND (:vendorId IS NULL OR ibv.`vendor_id` = :vendorId)\n" +
            "AND (((:sku IS NULL OR :sku = '') AND (:keywords IS NULL OR :keywords = ''))\n" +
            "  OR (:sku IS NOT NULL AND :sku <> '' AND (ibm.`part_number` LIKE :sku OR ibm.`description` LIKE :sku" +
            "    OR ibm.`name` LIKE :sku OR ibm.`keywords` LIKE :sku OR ibv.`sku` LIKE :sku))\n" +
            "  OR (:keywords IS NOT NULL AND :keywords <> '' AND MATCH(ibm.`part_number`, ibm.`description`, ibm.`name`, ibm.`keywords`)\n" +
            "    AGAINST (:keywords IN BOOLEAN MODE)))\n";
            //"ORDER BY MATCH(ibm.`name`, ibm.`part_number`, ibm.`description`, ibm.`keywords`) AGAINST (:keywords IN NATURAL LANGUAGE MODE)\n";
    String findByLCodeCategoryIdInSql = selectClause + fromClause + "WHERE ibm.`l_code_category_id` in (:lCodeCategoryId)";
    String findByLCodeIdQuery = selectClause + fromClause +
            "INNER JOIN `items_by_manufacturer_lcodes` iml ON iml.`item_by_manufacturer_id` = ibm.`id`\n" +
            "WHERE iml.`l_code_id` = :lCodeId";
    String findByPartNumberSql = selectClause + fromClause + "WHERE ibm.`part_number` = :partNumber";
    String findByPartNumberPriceAndVendorIdSql = selectClause + fromClause + "WHERE ibv.`active` = 1 AND ibm.`part_number` = :partNumber AND ibv.`price` = :price AND ibv.`vendor_id` = :vendorId ORDER BY ibv.`id` LIMIT 1";
    String findByPartNumberAndVendorIdSql = selectClause + fromClause + "WHERE ibm.`part_number` = :partNumber AND ibv.`vendor_id` = :vendorId";
    String findAllByNameAndSkuSql = selectClause + fromClause + "WHERE ibm.`name` = :name AND ibv.`sku` = :sku";
    String getItemsQuery = selectClause + fromClause + whereClause;
    String getItemsCountQuery = "SELECT COUNT(*)" + fromClause + whereClause;

    void deleteById(Long itemId);

    @Query(value = findByLCodeCategoryIdInSql, nativeQuery = true)
    List<Item> findByLCodeCategoryIdIn(List<Long> lCodeCategoryId);

    @Query(value = findByLCodeIdQuery, nativeQuery = true)
    List<Item> findByLCodeId(Long lCodeId);

    @Query(value = findByPartNumberSql, nativeQuery = true)
    List<Item> findByPartNumber(String partNumber);

    @Query(value = findByPartNumberPriceAndVendorIdSql, nativeQuery = true)
    Item findByPartNumberPriceAndVendorId(String partNumber, BigDecimal price, Long vendorId);

    @Query(value = findByPartNumberAndVendorIdSql, nativeQuery = true)
    Item findByPartNumberAndVendorId(String partNumber, Long vendorId);

    @Query(value = findAllByNameAndSkuSql, nativeQuery = true)
    List<Item> findAllByNameAndSku(String name, String sku);

    @Query(value = getItemsQuery, countQuery = getItemsCountQuery, nativeQuery = true)
    Page<Item> getItems(String keywords, List<Long> childCategoryIds, Boolean oneTimePurchase, Pageable pageable,
                        Boolean showInactive, String sku, Long vendorId);

    @Query(value = "SELECT * FROM item_by_vendor WHERE item_by_manufacturer_id in (:itemByManufacturerIds)", nativeQuery = true)
    List<Item> findAllByItemByManufacturerIdIn(List<Long> itemByManufacturerIds);

    @Query(value = "SELECT * FROM item_by_vendor WHERE item_by_manufacturer_id = :itemByManufacturerId and price = :price and vendor_id = :vendorId", nativeQuery = true)
    List<Item> findDuplicates(Long itemByManufacturerId, BigDecimal price, Long vendorId);

    @Query(value = "SELECT * FROM item_by_vendor_audit where id = :itemId", nativeQuery = true)
    public List<Item> getAudit(Long itemId);

    @Query(value = getItemsQuery + " ORDER BY ibm.`name` LIMIT 1000", nativeQuery = true)
    public List<Item> search(String keywords, List<Long> childCategoryIds, Boolean oneTimePurchase, Boolean showInactive, String sku, Long vendorId);

    /**
     * This query uses a UNION ALL (includes duplicates) to combine a list of item_by_vendor records
     * in both purchase_order_item and shopping_cart tables
     * <p>
     * This can and should return duplicate items if duplicates exists
     *  </p>
     *
     * @param prescriptionLCodeId
     * @return {@link List<Item>}
     */
    @Query(value = """
            SELECT i.* FROM item_by_vendor i JOIN purchase_order_item poi ON i.id = poi.item_id WHERE poi.prescription_l_code_id = :prescriptionLCodeId
            UNION ALL
            SELECT i.* FROM item_by_vendor i JOIN shopping_cart sc ON i.id = sc.item_id WHERE sc.prescription_l_code_id = :prescriptionLCodeId
            """, nativeQuery = true)
    List<Item> findByPrescriptionLCodeId(@Param(value = "prescriptionLCodeId") Long prescriptionLCodeId);
}
