package com.nymbl.tenant.repository;

import com.nymbl.config.dto.DailyCloseReport.DCPayment;
import com.nymbl.tenant.model.AppliedPayment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Collection;
import java.util.List;

/**
 * Created by <PERSON> on 06/15/2017.
 */
public interface AppliedPaymentRepository extends JpaRepository<AppliedPayment, Long>, JpaSpecificationExecutor<AppliedPayment> {

    List<AppliedPayment> getByPaymentId(Long paymentId);

    @Query(value = "SELECT * from applied_payment ap WHERE ap.id IN (:ids)", nativeQuery = true)
    List<AppliedPayment> findByIdIn(@Param(value = "ids") List<Long> ids);

    @Query("SELECT ap from AppliedPayment ap inner join ap.claim c where c.prescription.patientId = :patientId")
    List<AppliedPayment> getByPatientId(@Param("patientId") Long patientId);

    List<AppliedPayment> getDistinctByPaymentId(Long paymentId);

    List<AppliedPayment> findByClaimId(Long claimId);

    List<AppliedPayment> findByClaimIdIn(Collection<Long> claimIds);

    @Query("SELECT ap FROM AppliedPayment ap INNER JOIN ap.claim c WHERE c.prescriptionId = :prescriptionId")
    List<AppliedPayment> findByPrescriptionId(@Param("prescriptionId") Long prescriptionId);

    List<AppliedPayment> findByAppliedDateBetweenOrderByAppliedByAscClaimIdAsc(Date startDate, Date endDate);

    List<AppliedPayment> findByAppliedDateBetweenAndAppliedByOrderByAppliedByAscClaimIdAsc(Date startDate, Date endDate, Long appliedBy);

//    List<AppliedPayment> findByPaymentId(Long paymentId);

//    @Query(value = "SELECT id, adjustment_applied, amount_applied, applied_by, applied_date, auto_post_message, claim_id, autopost_patient_id, icn, next_responsibility, payment_id, task_notes FROM applied_payment WHERE payment_id = :paymentId", nativeQuery = true)
//    List<AppliedPayment> findByPaymentId(@Param("paymentId") Long paymentId);

    @Query(value = "SELECT MAX(applied_date) FROM applied_payment WHERE claim_id = :claimId", nativeQuery = true)
    Date findLastAppliedDateByClaimId(@Param("claimId") Long claimId);

    @Query(value = "SELECT ap FROM AppliedPayment ap WHERE ap.paymentId = :paymentId")
    List<AppliedPayment> findByPaymentId(@Param("paymentId") Long paymentId);

    @Query("SELECT ap FROM AppliedPayment ap " +
            "INNER JOIN ap.payment p " +
            "INNER JOIN ap.claim c " +
            "INNER JOIN c.prescription rx " +
            "INNER JOIN rx.patient pt " +
            "WHERE pt.primaryBranchId = :branchId " +
            "AND p.patientId IS NULL AND p.adjustmentId IS NULL " +
            "AND p.date BETWEEN :startDate AND :endDate")
    List<AppliedPayment> getBranchBulkPaymentAppliedPaymentsByPaymentDate(@Param("branchId") Long branchId,
                                                                          @Param("startDate") Date startDate,
                                                                          @Param("endDate") Date endDate);

    @Query("SELECT ap FROM AppliedPayment ap " +
            "INNER JOIN ap.payment p " +
            "INNER JOIN ap.claim c " +
            "INNER JOIN c.prescription rx " +
            "INNER JOIN rx.patient pt " +
            "WHERE pt.primaryBranchId = :branchId " +
            "AND p.patientId IS NULL AND p.adjustmentId IS NULL " +
            "AND p.depositDate BETWEEN :startDate AND :endDate")
    List<AppliedPayment> getBranchBulkPaymentAppliedPaymentsByDepositDate(@Param("branchId") Long branchId,
                                                                          @Param("startDate") Date startDate,
                                                                          @Param("endDate") Date endDate);
    @Query(value = """
            SELECT
            `p`.`id` AS `paymentId`,
            `p`.`date` AS `paymentDate`,
            `p`.`deposit_date` AS `depositDate`,
            `rx`.`patient_id` AS `patientId`,
            `pt`.`primary_branch_id` AS `branchId`,
            `ptb`.`name` AS `branchName`,
            CONCAT(`pt`.`first_name`, ' ' , `pt`.`last_name`) AS `patientName`,
            `ic`.`name` AS `payerName`,
            `p`.`check_number` AS `checkNumber`,
            `p`.`payment_type` AS `paymentType`,
            `ap`.`applied_date` AS `appliedDate`,
            `p`.`adjustment` AS `adjustmentAmount`,
            `p`.`amount` AS `paymentAmount`,
            `ap`.`amount_applied` AS `paymentApplied`,
            `p`.`unapplied_amount` AS `paymentUnapplied`,
            `cu`.`first_name` AS `createdByFirstName`,
            `cu`.`last_name` AS `createdByLastName`
            FROM `applied_payment` `ap`
            JOIN `payment` `p` ON `ap`.`payment_id` = `p`.`id`
            LEFT JOIN `insurance_company` `ic` ON `p`.`insurance_company_id` = `ic`.`id`
            JOIN `claim` `c` ON `ap`.`claim_id` = `c`.`id`
            JOIN `prescription` `rx` ON `c`.`prescription_id` = `rx`.`id`
            JOIN `patient` pt ON `rx`.`patient_id` = `pt`.`id`
            JOIN `branch` `ptb` ON pt.`primary_branch_id` = `ptb`.`id`
            JOIN `nymbl_master`.`user` `cu` ON `p`.`created_by_id` = `cu`.`id`
            WHERE `pt`.`primary_branch_id` IN (:branchIds)
            AND `p`.`patient_id` IS NULL AND `p`.`adjustment_id` IS NULL
            AND (('payment' = :dateOption AND `p`.`date` between :startDate AND :endDate) OR ('payment' <> :dateOption AND `p`.`deposit_date` between :startDate AND :endDate))
            """, nativeQuery = true)
    List<DCPayment> getBranchesBulkPaymentAppliedPaymentsByDateOption(@Param("branchIds") List<Long> branchIds,
                                                                      @Param("startDate") Date startDate,
                                                                      @Param("endDate") Date endDate,
                                                                      @Param("dateOption") String dateOption);

    @Query(value = "SELECT SUM(ap.amount_applied) FROM applied_payment ap " +
        "JOIN payment p ON ap.payment_id = p.id " +
        "JOIN claim c ON ap.claim_id = c.id " +
        "JOIN prescription rx ON c.prescription_id = rx.id " +
        "JOIN patient pt ON rx.patient_id = pt.id " +
        "WHERE p.id IN (SELECT ap2.payment_id FROM applied_payment ap2) " +
        "AND p.claim_id is null  " +
        "AND p.patient_id is null " +
        "AND ( :branchId = 0 OR :branchId IS null OR c.billing_branch_id = :branchId ) " +
        "AND " +
        "( ( :dateOption = 'payment' AND p.date BETWEEN :startDate AND :endDate ) " +
        "OR ( :dateOption != 'payment' AND p.deposit_date BETWEEN :startDate AND :endDate ) ) ; ", nativeQuery = true)
    BigDecimal getAppliedPaymentsWithPaymentsThatCantBeAppliedToBranch(@Param(value = "startDate") String startDate,
                                                                       @Param(value = "endDate") String endDate,
                                                                       @Param(value = "dateOption") String dateOption,
                                                                       @Param(value = "branchId") Long branchId);

    @Query(value = "SELECT SUM(amount_applied) as amount , SUM(adjustment_applied) as adjustment " +
            "FROM applied_payment ap WHERE ap.claim_id = :claimId;", nativeQuery = true)
    List<Object[]> getPaymentTotalsAndAdjustmentsByClaimId(@Param("claimId") Long claimId);

    @Query(value = "SELECT SUM(COALESCE(arAdjustment, 0.00)) FROM ( " +
        "SELECT CASE WHEN aplc.adjustment <> 0 THEN CASE WHEN a.operation <> '+' THEN (aplc.adjustment * -1) ELSE aplc.adjustment END ELSE 0 END as arAdjustment  FROM applied_payment ap " +
        "INNER JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id " +
        "INNER JOIN payment p ON ap.payment_id = p.id " +
        "INNER JOIN adjustment a ON aplc.adjustment_type = a.id " +
        "INNER JOIN patient pt ON p.patient_id = pt.id " +
        "INNER JOIN claim c ON c.id = ap.claim_id " +
        "WHERE adjustment_type in (SELECT id FROM adjustment where withdraw = 1) " +
        "AND (( :dateOption = 'payment' AND p.date between :startDate and :endDate ) OR ( :dateOption != 'payment' AND p.deposit_date between :startDate and :endDate )) " +
        "AND c.billing_branch_id = :branchId ) x; ", nativeQuery = true)
    BigDecimal getCashAdjustmentForDailyCloseByBranch(@Param(value = "startDate") String startDate,
                                                      @Param(value = "endDate") String endDate,
                                                      @Param(value = "dateOption") String dateOption,
                                                      @Param(value = "branchId") Long branchId);


    @Query(value = "select a.orthotic_or_prosthetic, SUM(a.actualContractual) from ( \n" +
            "select dt.orthotic_or_prosthetic, ivlc.total_charge - ivlc.total_allowable - aplc.adjustment_amount1 actualContractual \n" +
            "from applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id=ap.id \n" +
            "JOIN claim c on ap.claim_id = c.id \n" +
            "JOIN prescription rx on c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN insurance_verification iv ON c.prescription_id = iv.prescription_id and c.patient_insurance_id = iv.patient_insurance_id \n" +
            "JOIN insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id and ivlc.prescription_l_code_id = aplc.prescription_l_code_id \n" +
            "where aplc.adjustment_type1 = 'CO-45' and p.payer_type = 'insurance_company' and (:branchId is null OR :branchId = 0 OR c.billing_branch_id = :branchId) and p.date between :startDate and :endDate and rx.id <> 0 and rx.active = 1 and dt.orthotic_or_prosthetic is not null \n" +
            "UNION ALL \n" +
            "select dt.orthotic_or_prosthetic, ivlc.total_charge - ivlc.total_allowable - aplc.adjustment_amount2 actualContractual \n" +
            "from applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id=ap.id \n" +
            "JOIN claim c on ap.claim_id = c.id \n" +
            "JOIN prescription rx on c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN insurance_verification iv ON c.prescription_id = iv.prescription_id and c.patient_insurance_id = iv.patient_insurance_id \n" +
            "JOIN insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id and ivlc.prescription_l_code_id = aplc.prescription_l_code_id \n" +
            "where aplc.adjustment_type2 = 'CO-45' and p.payer_type = 'insurance_company' and (:branchId is null OR :branchId = 0 OR c.billing_branch_id = :branchId) and p.date between :startDate and :endDate and rx.id <> 0 and rx.active = 1 and dt.orthotic_or_prosthetic is not null \n" +
            "UNION ALL \n" +
            "select dt.orthotic_or_prosthetic, ivlc.total_charge - ivlc.total_allowable - aplc.adjustment_amount3 actualContractual \n" +
            "from applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id=ap.id \n" +
            "JOIN claim c on ap.claim_id = c.id \n" +
            "JOIN prescription rx on c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
            "JOIN payment p ON ap.payment_id = p.id \n" +
            "JOIN insurance_verification iv ON c.prescription_id = iv.prescription_id and c.patient_insurance_id = iv.patient_insurance_id \n" +
            "JOIN insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id and ivlc.prescription_l_code_id = aplc.prescription_l_code_id \n" +
            "where aplc.adjustment_type3 = 'CO-45' and p.payer_type = 'insurance_company' and (:branchId is null OR :branchId = 0 OR c.billing_branch_id = :branchId) and p.date between :startDate and :endDate and rx.id <> 0 and rx.active = 1 and dt.orthotic_or_prosthetic is not null \n" +
            "UNION ALL \n" +
            "select dt.orthotic_or_prosthetic, ivlc.total_charge - ivlc.total_allowable - aplc.adjustment_amount5 actualContractual \n" +
            "from applied_payment_l_code aplc \n" +
            "JOIN applied_payment ap ON aplc.applied_payment_id=ap.id \n" +
            "JOIN claim c on ap.claim_id = c.id \n" +
            "JOIN prescription rx on c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
        "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
        "JOIN payment p ON ap.payment_id = p.id \n" +
        "JOIN insurance_verification iv ON c.prescription_id = iv.prescription_id and c.patient_insurance_id = iv.patient_insurance_id \n" +
        "JOIN insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id and ivlc.prescription_l_code_id = aplc.prescription_l_code_id \n" +
        "where aplc.adjustment_type4 = 'CO-45' and p.payer_type = 'insurance_company' and (:branchId is null OR :branchId = 0 OR c.billing_branch_id = :branchId) and p.date between :startDate and :endDate and rx.id <> 0 and rx.active = 1 and dt.orthotic_or_prosthetic is not null \n" +
        "UNION ALL \n" +
        "select dt.orthotic_or_prosthetic, ivlc.total_charge - ivlc.total_allowable - aplc.adjustment_amount5 actualContractual \n" +
        "from applied_payment_l_code aplc \n" +
        "JOIN applied_payment ap ON aplc.applied_payment_id=ap.id \n" +
        "JOIN claim c on ap.claim_id = c.id \n" +
        "JOIN prescription rx on c.prescription_id = rx.id \n" +
        "JOIN patient pt ON rx.patient_id = pt.id \n" +
        "JOIN device_type dt ON rx.device_type_id = dt.id \n" +
        "JOIN payment p ON ap.payment_id = p.id \n" +
        "JOIN insurance_verification iv ON c.prescription_id = iv.prescription_id and c.patient_insurance_id = iv.patient_insurance_id \n" +
        "JOIN insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id and ivlc.prescription_l_code_id = aplc.prescription_l_code_id \n" +
        "where aplc.adjustment_type5 = 'CO-45' and p.payer_type = 'insurance_company' and (:branchId is null OR :branchId = 0 OR c.billing_branch_id = :branchId) and p.date between :startDate and :endDate and rx.id <> 0 and rx.active = 1 and dt.orthotic_or_prosthetic is not null \n" +
        ") as a \n" +
        "group by a.orthotic_or_prosthetic ;", nativeQuery = true)
    List<Object[]> getContractualDifference(@Param(value = "branchId") Long branchId,
                                            @Param(value = "startDate") String startDate,
                                            @Param(value = "endDate") String endDate);

    @Query(value = "select max(ap.applied_date) from applied_payment ap where ap.claim_id = :claimId ;", nativeQuery = true)
    Date getLastAppliedDate(@Param(value = "claimId") Long claimId);

    @Query(value = "SELECT DISTINCT ap.* FROM applied_payment ap " +
        "JOIN payment p ON p.id = ap.payment_id " +
        "JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id " +
        "JOIN claim c ON ap.claim_id = c.id " +
        "JOIN claim_submission cs ON ap.claim_id = cs.claim_id " +
        "JOIN prescription rx ON c.prescription_id = rx.id " +
        "JOIN patient pt ON p.patient_id = pt.id " +
        "WHERE rx.active = 1 AND pt.active = 1 " +
        "AND p.payer_type like :payerType " +
        "AND( :branchId = 0 OR :branchId IS null OR pt.primary_branch_id = :branchId) " +
        "AND (( :dateOption = 'payment' AND p.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p.deposit_date BETWEEN :startDate AND :endDate )) " +
        "AND cs.submission_date <= :endDate ; ", nativeQuery = true)
    List<AppliedPayment> getAllAppliedPaymentsByDateAndPayerTypeAndBranch(@Param("startDate") String startDate,
                                                                          @Param("endDate") String endDate,
                                                                          @Param("dateOption") String dateOption,
                                                                          @Param("branchId") Long branchId,
                                                                          @Param("payerType") String payerType);

    @Query(value = "SELECT DISTINCT ap.* FROM applied_payment ap " +
            "JOIN payment p ON p.id = ap.payment_id " +
            "JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id " +
            "JOIN claim c ON ap.claim_id = c.id " +
            "JOIN claim_submission cs ON c.id = cs.claim_id " +
            "JOIN prescription rx ON c.prescription_id = rx.id " +
            "JOIN patient pt ON p.patient_id = pt.id " +
        "WHERE rx.active = 1 AND pt.active = 1 " +
        "AND p.payer_type like :payerType " +
        "AND( :branchId = 0 OR :branchId IS null OR pt.primary_branch_id = :branchId) " +
        "AND (( :dateOption = 'payment' AND p.date BETWEEN :startDate AND :endDate ) OR " +
        "        ( :dateOption <> 'payment' AND p.deposit_date BETWEEN :startDate AND :endDate )) " +
        "AND cs.submission_date <= :endDate " +
        "AND ap.id NOT IN ( " +
        "   SELECT DISTINCT ap1.id FROM applied_payment ap1 " +
        "   JOIN payment p1 ON p1.id = ap1.payment_id " +
        "   JOIN applied_payment_l_code aplc1 ON ap1.id = aplc1.applied_payment_id " +
        "   JOIN claim c1 ON ap1.claim_id = c1.id " +
        "   JOIN claim_submission cs1 ON ap1.claim_id = cs1.claim_id " +
        "   JOIN prescription rx1 ON c1.prescription_id = rx1.id " +
        "   JOIN patient pt1 ON p1.patient_id = pt1.id " +
        "   WHERE rx1.active = 1 AND pt1.active = 1 " +
        "   AND p1.payer_type like :payerType " +
        "   AND( :branchId = 0 OR :branchId IS null OR pt1.primary_branch_id = :branchId) " +
        "   AND (( :dateOption = 'payment' AND p1.date BETWEEN :startDate AND :endDate ) OR " +
        "       ( :dateOption <> 'payment' AND p1.deposit_date BETWEEN :startDate AND :endDate )) " +
        "   AND cs1.submission_date <= :endDate ) ;", nativeQuery = true)
    List<AppliedPayment> getAllAppliedPaymentsSalesSummaryDifferenceWithoutClaimSubmissions(@Param("startDate") String startDate,
                                                                                            @Param("endDate") String endDate,
                                                                                            @Param("dateOption") String dateOption,
                                                                                            @Param("branchId") Long branchId,
                                                                                            @Param("payerType") String payerType);

    @Query(value = "SELECT DISTINCT ap.* FROM applied_payment ap " +
        "JOIN payment p ON p.id = ap.payment_id " +
        "JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id " +
        "JOIN claim c ON ap.claim_id = c.id " +
        "JOIN claim_submission cs ON ap.claim_id = cs.claim_id " +
        "JOIN prescription rx ON c.prescription_id = rx.id " +
        "WHERE rx.active = 1 " +
        "AND p.payer_type like :payerType " +
        "AND (( :dateOption = 'payment' AND p.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p.deposit_date BETWEEN :startDate AND :endDate )) " +
        "AND cs.submission_date <= :endDate ; ", nativeQuery = true)
    List<AppliedPayment> getAllAppliedPaymentsByDateAndPayerTypeForBulkPayments(@Param("startDate") String startDate,
                                                                                @Param("endDate") String endDate,
                                                                                @Param("dateOption") String dateOption,
                                                                                @Param("payerType") String payerType);


    @Query(value = AppliedPaymentSQL.appliedPaymentByAppliedPaymentLCodeIdQuery, nativeQuery = true)
    List<AppliedPayment> appliedPaymentByAppliedPaymentLCodeIds(@Param("appliedPaymentLCodeIds") List<Long> appliedPaymentLCodeIds);

    @Query(value = "SELECT ap FROM AppliedPayment ap WHERE ap.paymentId = :paymentId AND ap.autopostPatientId = :autoPostPatientId AND ap.claimId = :claimId AND ap.amountApplied = :amount")
    List<AppliedPayment> findByAutoPostPatientClaimAmount(@Param("paymentId") Long paymentId, @Param("autoPostPatientId") Long autoPostPatientId, @Param("claimId") Long claimId, @Param("amount") BigDecimal amount);
}
