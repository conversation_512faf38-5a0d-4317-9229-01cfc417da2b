package com.nymbl.tenant.repository;

import com.nymbl.tenant.model.GeneralLedger;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

public interface ClinicalOperationsRepository extends JpaRepository<GeneralLedger, Long>, JpaSpecificationExecutor<GeneralLedger> {


    @Modifying
    @Transactional
    @Query(value = "DELETE FROM `multitenant`.`clinical_operations` WHERE tenant = :tenant", nativeQuery = true)
    void removeStaleTenantData(@Param("tenant") String tenant);

    @Modifying
    @Transactional
    @Query(value = importTenantQuery, nativeQuery = true)
    void importClinicalOperationsData(@Param("tenant") String tenant);

    String importTenantQuery = """
            INSERT INTO `multitenant`.`clinical_operations` (
            `tenant`,
            `patient_id`,
            `patient`,
            `patient_dob`,
            `patient_created_at`,
            `patient_created_by_id`,
            `patient_created_by`,
            `patient_primary_branch_id`,
            `patient_primary_branch`,
            `date_of_surgery`,
            `prescription_id`,
            `prescription_branch_id`,
            `prescription_branch`,
            `prescription_created_at`,
            `prescription_duplicated_from_id`,
            `prescription_created_by_id`,
            `prescription_created_by`,
            `prescription_assigned_to`,
            `prescription_date`,
            `prescription_projected_delivery_date`,
            `prescription_delivered_on`,
            `appointment_delivered_on`,
            `prescription_delivery_location`,
            `prescription_nymbl_status_id`,
            `prescription_nymbl_status`,
            `prescription_l_code_id`,
            `l_code_id`,
            `l_code`,
            `quantity`,
            `modifier1`,
            `modifier2`,
            `modifier3`,
            `modifier4`,
            `date_of_service`,
            `pos`,
            `device_type_id`,
            `device_type_category`,
            `device_type`,
            `claim_id`,
            `claim_date_of_service`,
            `sent_to_billing_date`,
            `date_resolved`,
            `sent_to_billing_user`,
            `claim_nymbl_status_id`,
            `claim_nymbl_status`,
            `claim_submission_date`,
            `claim_billing_branch_id`,
            `claim_billing_branch`,
            `claim_branch`,
            `claim_assigned_to`,
            `total_cost`,
            `total_applied_payment`,
            `insurance_company_id`,
            `insurance_company`,
            `allowable_fee`,
            `billing_fee`,
            `covered`,
            `bill`,
            `total_allowable`,
            `total_charge`,
            `verified_by_id`,
            `verified_by`,
            `verified_on`,
            `treating_practitioner_id`,
            `treating_practitioner`,
            `primary_care_physician_id`,
            `primary_care_physician`,
            `referring_physician_id`,
            `referring_physician`,
            `therapist_id`,
            `therapist`,
            `facility_id`,
            `facility`,
            `first_eval_date`,
            `eval_created_by`,
            `appointment_first_eval_date`,
            `last_submission_by`,
            `days_in_current_claim_status`,
            `days_in_current_rx_status`,
            `first_payment_date`)
            SELECT DISTINCT
            :tenant AS `tenant`,
            `rx`.`patient_id` AS `patient_id`,
            `nymbl_master`.`format_name`(`pt`.`first_name`, `pt`.`middle_name`, `pt`.`last_name`, NULL) AS `patient`,
            `pt`.`dob` AS `patient_dob`,
            `pt`.`created_at` AS `patient_created_at`,
            `pt`.`created_by_id` AS `patient_created_by_id`,
            `patu`.`username` AS `patient_created_by`,
            `pt`.`primary_branch_id` AS `patient_primary_branch_id`,
            `pb`.`name` AS `patient_primary_branch`,
            `rx`.`surgery_date` AS `date_of_surgery`,
            `rx`.`id` AS `prescription_id`,
            `rx`.`branch_id` AS `prescription_branch_id`,
            `rxb`.`name` AS `prescription_branch`,
            `rx`.`created_at` AS `prescription_created_at`,
            `rx`.`duplicated_from_id` AS `prescription_duplicated_from_id`,
            `rx`.`created_by_id` AS `prescription_created_by_id`,
            `rxu`.`username` AS `prescription_created_by`,
            `clerical`.`username` AS `prescription_assigned_to`,
            `rx`.`prescription_date` AS `prescription_date`,
            `rx`.`projected_delivery_date` AS `prescription_projected_delivery_date`,
            `rx`.`delivered_on` AS `prescription_delivered_on`,
            COALESCE(MAX(`delivery_apt`.`start_datetime`), null) AS `appointment_delivered_on`,	
            `rx`.`delivery_location` AS `prescription_delivery_location`,
            `rxns`.`id` AS `prescription_nymbl_status_id`,
            `rxns`.`name` AS `prescription_nymbl_status`,
            `plc`.`id` AS `prescription_l_code_id`,
            `lc`.`id` AS `l_code_id`,
            `lc`.`name` AS `l_code`,
            `plc`.`quantity` AS `quantity`,
            `plc`.`modifier1` AS `modifier1`,
            `plc`.`modifier2` AS `modifier2`,
            `plc`.`modifier3` AS `modifier3`,
            `plc`.`modifier4` AS `modifier4`,
            `plc`.`date_of_service` AS `l_code_date_of_service`,
            `plc`.`pos` AS `l_code_place_of_service`,
            `dt`.`id` AS `device_type_id`,
            `dt`.`orthotic_or_prosthetic` AS `device_type_category`,
            `dt`.`name` AS `device_name`,
            `c`.`id` AS `claim_id`,
            `c`.`date_of_service` AS `claim_date_of_service`,
            `c`.`created_at` AS `sent_to_billing_date`,
            `c`.`date_resolved` AS `date_resolved`,
            `sb`.`username` AS `sent_to_billing_user`,
            `c`.`nymbl_status_id` AS `claim_nymbl_status_id`,
            `cns`.`name` AS `claim_nymbl_status`,
            COALESCE(MIN(`cs`.`submission_date`), null) AS `claim_submission_date`,
            `c`.`billing_branch_id` AS `claim_billing_branch_id`,
            COALESCE(`cb`.`name`, '') AS `claim_billing_branch`,
            COALESCE(`cb`.`name`, '') AS `claim_branch`,
            `nymbl_master`.`format_name`(`c_user`.`first_name`, NULL, `c_user`.`last_name`, NULL) AS `claim_assigned_to`,
            `poi_sum`.`total_cost`,
            `aplc_sum`.`total_applied_payment`,
            `ic`.`id` AS `insurance_company_id`,
            `ic`.`name` AS `insurance_company`,
            `ivlc`.`allowable_fee` AS `allowable_fee`,
            `ivlc`.`billing_fee` AS `billing_fee`,
            `ivlc`.`covered` AS `covered`,
            `ivlc`.`bill` AS `bill`,
            `ivlc`.`total_allowable` AS `total_allowable`,
            `ivlc`.`total_charge` AS `total_charge`,
            `iv`.`verified_by_id` AS `verified_by_id`,
            `ivu`.`username` AS `verified_by`,
            `iv`.`verified_on` AS `verified_on`,
            `tp`.`id` AS `treating_practitioner_id`,
            `nymbl_master`.`format_name`(`tp`.`first_name`, `tp`.`middle_name`, `tp`.`last_name`, NULL) AS `treating_practitioner`,
            `pc`.`id` AS `primary_care_physician_id`,
            `nymbl_master`.`format_name`(`tp`.`first_name`, `tp`.`middle_name`, `tp`.`last_name`, NULL) AS `primary_care_physician`,
            `rp`.`id` AS `referring_physician_id`,
            `nymbl_master`.`format_name`(`rp`.`first_name`, `rp`.`middle_name`, `rp`.`last_name`, NULL) AS `referring_physician`,
            `th`.`id` AS `therapist_id`,
            `nymbl_master`.`format_name`(`th`.`first_name`, `th`.`middle_name`, `th`.`last_name`, NULL) AS `therapist`,
            `fac`.`id` AS `facility_id`,
            `fac`.`name` AS `facility_name`,
            `eval_audit`.`created_at` AS `first_eval_date`,
            # MIN(`eval_audit`.`created_at`) AS `first_eval_date`,
            MAX(`eval_audit`.`user`) AS `eval_created_by`,
            MIN(`init_eval_apt`.`start_datetime`) AS `appointment_first_eval_date`,
            (SELECT `csu`.`username` FROM `claim_submission` `cs1` LEFT JOIN `nymbl_master`.`user` `csu` ON `cs1`.`submitted_by_id` = `csu`.`id` WHERE `cs1`.`claim_id` = `c`.`id` ORDER BY `cs1`.`claim_file_id` DESC LIMIT 1) AS `last_submission_by`,
            DATEDIFF(FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), FROM_UNIXTIME(MIN(`c_audit_rev`.`rev_timestamp`) / 1000, '%Y-%m-%d'))  AS `days_in_current_claim_status`,
            DATEDIFF(FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), FROM_UNIXTIME(MIN(`rx_audit_rev`.`rev_timestamp`) / 1000, '%Y-%m-%d')) AS `days_in_current_rx_status`,
            MIN(`ap`.`applied_date`) AS `first_payment_date`
            FROM `prescription_l_code` `plc`
            JOIN `prescription` `rx` ON `plc`.`prescription_id` = `rx`.`id`
            JOIN `patient` `pt` ON `rx`.`patient_id` = `pt`.`id`
            LEFT JOIN `prescription_audit` `rx_audit` ON `rx`.`id` = `rx_audit`.`id` AND `rx`.`nymbl_status_id` = `rx_audit`.`nymbl_status_id`
            LEFT JOIN `audit_revision` `rx_audit_rev` ON `rx_audit`.`revision_id` = `rx_audit_rev`.`revision_id`
            LEFT JOIN `insurance_verification` `iv` ON `rx`.`id` = `iv`.`prescription_id` AND `iv`.`carrier_type` = 'primary'
            LEFT JOIN `insurance_verification_l_code` `ivlc` ON `plc`.`id` = `ivlc`.`prescription_l_code_id` AND `iv`.`id` = `ivlc`.`insurance_verification_id`
            LEFT JOIN `patient_insurance` `pi` ON `iv`.`patient_insurance_id` = `pi`.`id`
            LEFT JOIN `insurance_company` `ic` ON `pi`.`insurance_company_id` = `ic`.`id`
            LEFT JOIN (SELECT `prescription_l_code_id`, SUM(`total_cost`) `total_cost` FROM `purchase_order_item` WHERE `prescription_l_code_id` is not null GROUP BY `prescription_l_code_id`) AS `poi_sum` ON `plc`.`id` = `poi_sum`.`prescription_l_code_id`
            LEFT JOIN `claim` `c` ON `rx`.`id` = `c`.`prescription_id` AND `iv`.`patient_insurance_id` = `c`.`patient_insurance_id`
            LEFT JOIN `claim_audit` `c_audit` ON `c`.`id` = `c_audit`.`id` AND `c`.`nymbl_status_id` = `c_audit`.`nymbl_status_id`
            LEFT JOIN `audit_revision` `c_audit_rev` ON `c_audit`.`revision_id` = `c_audit_rev`.`revision_id`
            LEFT JOIN `applied_payment` `ap` ON `ap`.`claim_id` = `c`.`id`
            LEFT JOIN (SELECT `prescription_l_code_id`, SUM(`amount`) AS `total_applied_payment` FROM `applied_payment_l_code` GROUP BY `prescription_l_code_id`) AS `aplc_sum` ON `plc`.`id` = `aplc_sum`.`prescription_l_code_id`
            LEFT JOIN `claim_submission` `cs` ON `cs`.`claim_id` = `c`.`id`
            LEFT JOIN `l_code` `lc` ON `lc`.`id` = `plc`.`l_code_id`
            LEFT JOIN `device_type` `dt` ON `dt`.`id` = `rx`.`device_type_id`
            LEFT JOIN `branch` `pb` ON `pt`.`primary_branch_id` = `pb`.`id`
            LEFT JOIN `branch` `rxb` ON `rx`.`branch_id` = `rxb`.`id`
            LEFT JOIN `branch` `cb` ON `c`.`billing_branch_id` = `cb`.`id`
            LEFT JOIN `nymbl_master`.`user` `ivu` ON `iv`.`verified_by_id` = `ivu`.`id`
            LEFT JOIN `nymbl_master`.`user` `patu` ON `pt`.`created_by_id` = `patu`.`id`
            LEFT JOIN `nymbl_master`.`user` `rxu` ON `rx`.`created_by_id` = `rxu`.`id`
            LEFT JOIN `nymbl_master`.`user` `clerical` ON `rx`.`clerical_user_id` = `clerical`.`id`
            LEFT JOIN `nymbl_master`.`user` `sb` ON `c`.`created_by_id` = `sb`.`id`
            LEFT JOIN `nymbl_master`.`user` `pc` ON `pt`.`primary_practitioner_id` = `pc`.`id`
            LEFT JOIN `nymbl_master`.`user` `tp` ON `rx`.`treating_practitioner_id` = `tp`.`id`
            LEFT JOIN `physician` `rp` ON `rx`.`referring_physician_id` = `rp`.`id`
            LEFT JOIN `therapist` `th` ON `rx`.`therapist_id` = `th`.`id`
            LEFT JOIN `facility` `fac` ON `rx`.`facility_id` = `fac`.`id`
            LEFT JOIN `nymbl_status` `rxns` ON `rx`.`nymbl_status_id` = `rxns`.`id`
            LEFT JOIN `nymbl_status` `cns` ON `c`.`nymbl_status_id` = `cns`.`id`
            LEFT JOIN `nymbl_master`.`user` `c_user` ON `c`.`user_id` = `c_user`.`id`
            LEFT JOIN (
               SELECT `efa`.`prescription_id`, MIN(COALESCE(`efa`.`created_at`, null)) `created_at`, MIN(`ar`.`user`) `user`
               FROM `evaluation_form_audit` `efa`
               LEFT JOIN `audit_revision` `ar` ON `efa`.`revision_id` = `ar`.`revision_id`
               WHERE `efa`.`prescription_id` IS NOT NULL
               GROUP BY `efa`.`prescription_id`
            ) as `eval_audit` ON `rx`.`id` = `eval_audit`.`prescription_id`
            LEFT JOIN (
               SELECT `apt`.`prescription_id`,
               MAX(`apt`.`start_datetime`) AS `start_datetime`
               FROM `appointment` `apt`
               LEFT JOIN `appointment_type` `apt_type` ON `apt_type`.`id` = `apt`.`appointment_type_id`
               LEFT JOIN `appointment_type_status`  `apt_type_status` ON `apt_type_status`.`id` = `apt_type`.`appointment_type_status_id`
               WHERE `apt`.`prescription_id` IS NOT NULL AND `apt_type_status`.`key` = 'delivery'
               GROUP BY  `apt`.`prescription_id`
            ) AS `delivery_apt` ON `rx`.`id` = `delivery_apt`.`prescription_id`
            LEFT JOIN (
               SELECT `apt`.`prescription_id`,
               MAX(`apt`.`start_datetime`) AS `start_datetime`
               FROM `appointment` `apt`
               LEFT JOIN `appointment_type` `apt_type` ON `apt_type`.`id` = `apt`.`appointment_type_id`
               LEFT JOIN `appointment_type_status`  `apt_type_status` ON `apt_type_status`.`id` = `apt_type`.`appointment_type_status_id`
               WHERE `apt`.`prescription_id` IS NOT NULL AND `apt_type_status`.`key` = 'initial_eval'
               GROUP BY  `apt`.`prescription_id`
            ) as `init_eval_apt` ON `rx`.`id` = `init_eval_apt`.`prescription_id`
            WHERE `rx`.`active` = TRUE AND `pt`.`active` = TRUE
            GROUP BY `rx`.`patient_id`, `c`.`id`, `plc`.`id`, `ivlc`.`allowable_fee`, `ivlc`.`billing_fee`, `ivlc`.`covered`, `ivlc`.`bill`, `ivlc`.`total_allowable`, `ivlc`.`total_charge`, `iv`.`verified_by_id`, `iv`.`verified_on`, `ic`.`id`, `poi_sum`.`total_cost`, `aplc_sum`.`total_applied_payment`,`eval_audit`.`created_at`
            """;
}
