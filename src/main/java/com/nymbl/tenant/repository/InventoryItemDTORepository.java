package com.nymbl.tenant.repository;

import com.nymbl.tenant.model.InventoryItemDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface InventoryItemDTORepository extends JpaRepository<InventoryItemDTO, Long>, JpaSpecificationExecutor<InventoryItemDTO> {
    /**
     * itemCteClause contains the logic for item selection based on name, part number, SKU, and keywords.
     * It lacks the closing parenthesis because in one instance I need to add order by and limit clauses to it.
     */
    String itemCteClause = "i AS (SELECT ibv.`item_by_manufacturer_id`, ibm.`l_code_category_id` AS `item_category_id`,\n" +
            "ibm.`description` AS `item_description`, ibv.`id` AS `item_id`, ibm.`name` AS `item_name`, ibm.`part_number` AS `item_part_number`,\n" +
            "ibv.`price` AS `item_price`, ibv.`sku` AS `item_sku`, ibm.`manufacturer_id`, ibv.`vendor_id`\n" +
            "FROM `item_by_vendor` ibv\n" +
            "INNER JOIN `item_by_manufacturer` ibm on ibm.`id` = ibv.`item_by_manufacturer_id`\n" +
            "WHERE ibv.`active` = 1\n" +
            "AND (:partNumber IS NULL OR ibm.`part_number` LIKE :partNumber)\n" +
            // :sku is derived from :keywords, so they are both either NULL or not NULL
            "AND (((:keywords IS NULL OR :keywords = '') AND (:sku IS NULL OR :sku = ''))\n" +
            // :sku is not necessarily SKU but a literal value of the search string followed by '%'
            "  OR ibm.`description` LIKE :sku OR ibm.`name` LIKE :sku OR ibm.`keywords` LIKE :sku OR ibm.`part_number` LIKE :sku OR ibv.`sku` LIKE :sku\n" +
            // :keywords is a properly formatted full-text search string followed by '%' (e.g. "+orthopedic +shoe%")
            "  OR MATCH(ibm.`part_number`, ibm.`description`, ibm.`name`, ibm.`keywords`) AGAINST (:keywords IN BOOLEAN MODE))";
    /**
     * poiCteClause is just a count of PO items
     */
    String poiCteClause = "poi AS (SELECT `branch_id`, `item_id`, SUM(COALESCE(`quantity`, 0)) AS `quantity`\n" +
            "FROM `purchase_order_item`\n" +
            "WHERE `patient_id` IS NULL AND `prescription_id` IS NULL AND `status` IN ('open', 'ordered') GROUP BY `branch_id`, `item_id`)";
    /**
     * scCteClause is just a count of shopping cart items
     */
    String scCteClause = "sc AS (SELECT `branch_id`, `item_id`, SUM(COALESCE(`quantity`, 0)) AS `quantity`\n" +
            "FROM `shopping_cart`\n" +
            "WHERE `patient_id` IS NULL AND `prescription_id` IS NULL AND `status` = 'open' GROUP BY `branch_id`, `item_id`)";

    // includeUndefined is true and there are no numeric parameters
    String crossJoinCountQuery = "WITH " + itemCteClause + ")\n" + "SELECT COUNT(*) * :branchesCount\n" + "FROM i";
    String crossJoinSearchQuery = "WITH " + itemCteClause +
            "\nORDER BY ibm.`name`, ibv.`id` LIMIT :itemLimit OFFSET :itemOffset),\n" +
            poiCteClause + ",\n" + scCteClause + "\n" +
            // main select
            "SELECT COALESCE(ii.`id`, (i.`item_id` * 100000 + b.`id`) * -1) AS `id`, b.`id` AS `branch_id`, b.`name` AS `branch_name`,\n" +
            "COALESCE(sc.`quantity`, 0) AS `in_cart`, i.`item_by_manufacturer_id`, i.`item_category_id`, lc.`category` AS `item_category_name`,\n" +
            "i.`item_description`, i.`item_id`, i.`item_name`, i.`item_part_number`, i.`item_price`, i.`item_sku`,\n" +
            "ii.`low_stock`, i.`manufacturer_id`, m.`name` AS `manufacturer_name`, COALESCE(poi.`quantity`, 0) AS `ordered`,\n" +
            "ii.`preferred_stock`, COALESCE(ih.`quantity`, 0) AS `quantity`, i.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM i CROSS JOIN `branch` b\n" +
            "LEFT OUTER JOIN `inventory_item` ii ON ii.`branch_id` = b.`id` AND ii.`item_id` = i.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = i.`item_category_id`\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = b.`id` AND ih.`item_id` = i.`item_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = b.`id` AND poi.`item_id` = i.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = b.`id` AND sc.`item_id` = i.`item_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = i.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = i.`vendor_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR b.`id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR b.`id` NOT IN (:branchIdsExclude))\n" +
            "ORDER BY i.`item_name`, i.`item_id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset";

    String inventoryItemBranchCountQuery = "SELECT COUNT(*)\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN `item_by_vendor` ibv ON ibv.`active` = 1 AND ibv.`id` = ii.`item_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ii.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ii.`branch_id` NOT IN (:branchIdsExclude))";
    String inventoryItemBranchSearchQuery = "WITH " + poiCteClause + ",\n" + scCteClause + "\n" +
            // main select
            "SELECT ii.`id`, ii.`branch_id`, b.`name` AS `branch_name`, COALESCE(sc.`quantity`, 0) AS `in_cart`, ibv.`item_by_manufacturer_id`,\n" +
            "ibm.`l_code_category_id` AS `item_category_id`, lc.`category` AS `item_category_name`, ibm.`description` AS `item_description`, ii.`item_id`,\n" +
            "ibm.`name` AS `item_name`, ibm.`part_number` AS `item_part_number`, ibv.`price` AS `item_price`, ibv.`sku` AS `item_sku`,\n" +
            "ii.`low_stock`, ibm.`manufacturer_id`, m.`name` AS `manufacturer_name`, COALESCE(poi.`quantity`, 0) AS `ordered`,\n" +
            "ii.`preferred_stock`, COALESCE(ih.`quantity`, 0) AS `quantity`, ibv.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN `branch` b ON b.`id` = ii.`branch_id`\n" +
            "INNER JOIN `item_by_vendor` ibv ON ibv.`active` = 1 AND ibv.`id` = ii.`item_id`\n" +
            "INNER JOIN `item_by_manufacturer` ibm ON ibm.`id` = ibv.`item_by_manufacturer_id`\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = ii.`branch_id` AND ih.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = ibm.`l_code_category_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = ii.`branch_id` AND poi.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = ii.`branch_id` AND sc.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = ibm.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = ibv.`vendor_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR b.`id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR b.`id` NOT IN (:branchIdsExclude))\n" +
            "ORDER BY ibm.`name`, ibv.`id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset";

    // includeUndefined is false and there are no numeric parameters
    String inventoryItemCountQuery = "WITH " + itemCteClause + ")\n" +
            "SELECT COUNT(*)\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN i ON i.`item_id` = ii.`item_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ii.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ii.`branch_id` NOT IN (:branchIdsExclude))";
    String inventoryItemSearchQuery = "WITH " + poiCteClause + ",\n" + scCteClause + "\n" +
            // main select
            "SELECT ii.`id`, ii.`branch_id`, b.`name` AS `branch_name`, COALESCE(sc.`quantity`, 0) AS `in_cart`, ibv.`item_by_manufacturer_id`,\n" +
            "ibm.`l_code_category_id` AS `item_category_id`, lc.`category` AS `item_category_name`, ibm.`description` AS `item_description`,\n" +
            "ii.`item_id`, ibm.`name` AS `item_name`, ibm.`part_number` AS `item_part_number`,ibv.`price` AS `item_price`, ibv.`sku` AS `item_sku`,\n" +
            "ii.`low_stock`, ibm.`manufacturer_id`, m.`name` AS `manufacturer_name`, COALESCE(poi.`quantity`, 0) AS `ordered`,\n" +
            "ii.`preferred_stock`, COALESCE(ih.`quantity`, 0) AS `quantity`, ibv.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN `branch` b ON b.`id` = ii.`branch_id`\n" +
            "INNER JOIN `item_by_vendor` ibv ON ibv.`id` = ii.`item_id` AND ibv.`active` = 1\n" +
            "INNER JOIN `item_by_manufacturer` ibm ON ibm.`id` = ibv.`item_by_manufacturer_id`\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = ii.`branch_id` AND ih.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = ibm.`l_code_category_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = ii.`branch_id` AND poi.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = ii.`branch_id` AND sc.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = ibm.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = ibv.`vendor_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ii.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ii.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND (:partNumber IS NULL OR ibm.`part_number` LIKE :partNumber)\n" +
            // :sku is derived from :keywords, so they are both either NULL or not NULL
            "AND (((:keywords IS NULL OR :keywords = '') AND (:sku IS NULL OR :sku = ''))\n" +
            // :sku is not necessarily SKU but a literal value of the search string followed by '%'
            "  OR ibm.`description` LIKE :sku OR ibm.`name` LIKE :sku OR ibm.`keywords` LIKE :sku OR ibm.`part_number` LIKE :sku OR ibv.`sku` LIKE :sku\n" +
            // :keywords is a properly formatted full-text search string followed by '%' (e.g. "+orthopedic +shoe%")
            "  OR MATCH(ibm.`part_number`, ibm.`description`, ibm.`name`, ibm.`keywords`) AGAINST (:keywords IN BOOLEAN MODE))\n" +
            "ORDER BY ibm.`name`, ii.`item_id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset";

    // includeUndefined is false and min > 0
    String minMaxCountQuery = "WITH " + itemCteClause + ")\n" +
            "SELECT COUNT(*)\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN `inventory_on_hand` ih ON ih.branch_id = ii.branch_id AND ih.item_id = ii.item_id\n" +
            "INNER JOIN i ON i.`item_id` = ii.`item_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ii.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ii.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND (:min IS NULL OR ih.`quantity` >= :min)\n" +
            "AND (:max IS NULL OR :max = 0 OR ih.`quantity` <= :max)";
    String minMaxSearchQuery = "WITH " + poiCteClause + ",\n" + scCteClause + ",\n" +
            "x AS (SELECT ii.`id`, ih.`branch_id`, b.`name` AS `branch_name`, ibv.`item_by_manufacturer_id`, ibm.`l_code_category_id` AS `item_category_id`,\n" +
            "ibm.`description` as `item_description`, ih.`item_id`, ibm.`name` AS `item_name`, ibm.`part_number` AS `item_part_number`,\n" +
            "ibv.`price` AS `item_price`, ibv.`sku` AS `item_sku`, ii.`low_stock`, ibm.`manufacturer_id`, ii.`preferred_stock`, ih.`quantity`, ibv.`vendor_id`\n" +
            "FROM `inventory_on_hand` ih\n" +
            "INNER JOIN `inventory_item` ii ON ii.`branch_id` = ih.`branch_id` and ii.`item_id` = ih.`item_id`\n" +
            "INNER JOIN `item_by_vendor` ibv ON ibv.`id` = ih.`item_id` AND ibv.`active` = 1\n" +
            "INNER JOIN `item_by_manufacturer` ibm ON ibm.`id` = ibv.`item_by_manufacturer_id`\n" +
            "INNER JOIN `branch` b on b.id = ih.branch_id\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ih.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ih.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND (:partNumber IS NULL OR ibm.`part_number` LIKE :partNumber)\n" +
            "AND (((:keywords IS NULL OR :keywords = '') AND (:sku IS NULL OR :sku = ''))\n" +
            "  OR ibm.`description` LIKE :sku OR ibm.`name` LIKE :sku OR ibm.`keywords` LIKE :sku OR ibm.`part_number` LIKE :sku OR ibv.`sku` LIKE :sku\n" +
            "  OR MATCH(ibm.`part_number`, ibm.`description`, ibm.`name`, ibm.`keywords`) AGAINST (:keywords IN BOOLEAN MODE))\n" +
            "AND (:min IS NULL OR ih.`quantity` >= :min)\n" +
            "AND (:max IS NULL OR :max = 0 OR ih.`quantity` <= :max)\n" +
            "ORDER BY ibm.`name`, ih.`item_id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset)\n" +
            // main select
            "SELECT x.`id`, x.`branch_id`, x.`branch_name`, COALESCE(sc.`quantity`, 0) AS `in_cart`, x.`item_by_manufacturer_id`,\n" +
            "x.`item_category_id`, lc.`category` AS `item_category_name`, x.`item_description`, x.`item_id`, x.`item_name`,\n" +
            "x.`item_part_number`, x.`item_price`, x.`item_sku`, x.`low_stock`, x.`manufacturer_id`, m.`name` AS `manufacturer_name`,\n" +
            "COALESCE(poi.`quantity`, 0) AS `ordered`, x.`preferred_stock`, x.`quantity`, x.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM x\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = x.`branch_id` AND poi.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = x.`branch_id` AND sc.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = x.`item_category_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = x.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = x.`vendor_id`";

    // includeUndefined is true and min > 0
    String minMaxWithUndefinedCountQuery = "WITH " + itemCteClause + ")\n" +
            "SELECT COUNT(*)\n" +
            "FROM `inventory_on_hand` ih\n" +
            "INNER JOIN i ON i.`item_id` = ih.`item_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ih.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ih.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND (:min IS NULL OR ih.`quantity` >= :min)\n" +
            "AND (:max IS NULL OR :max = 0 OR ih.`quantity` <= :max)";
    String minMaxWithUndefinedSearchQuery = "WITH " + itemCteClause + "),\n" + poiCteClause + ",\n" + scCteClause + ",\n" +
            // I need this intermediate CTE to finalize filtering before joining in inventory_item, etc.
            "x AS (SELECT ih.`branch_id`, b.`name` AS `branch_name`, i.`item_by_manufacturer_id`, i.`item_category_id`,\n" +
            "i.`item_description`, i.`item_id`, i.`item_name`, i.`item_part_number`, i.`item_price`, i.`item_sku`,\n" +
            "i.`manufacturer_id`, ih.`quantity`, i.`vendor_id`\n" +
            "FROM `inventory_on_hand` ih\n" +
            "INNER JOIN `branch` b ON b.`id` = ih.`branch_id`\n" +
            "INNER JOIN i ON i.`item_id` = ih.`item_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ih.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ih.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND (:min IS NULL OR ih.`quantity` >= :min)\n" +
            "AND (:max IS NULL OR :max = 0 OR ih.`quantity` <= :max)\n" +
            "ORDER BY i.`item_name`, i.`item_id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset)\n" +
            // main select
            "SELECT COALESCE(ii.`id`, (x.`item_id` * 100000 + x.`branch_id`) * -1) AS `id`, x.`branch_id`, x.`branch_name`,\n" +
            "COALESCE(sc.`quantity`, 0) AS `in_cart`,x.`item_by_manufacturer_id`, x.`item_category_id`,\n" +
            "lc.`category` AS `item_category_name`, x.`item_description`, x.`item_id`, x.`item_name`, x.`item_part_number`,\n" +
            "x.`item_price`, x.`item_sku`, ii.`low_stock`, x.`manufacturer_id`, m.`name` AS `manufacturer_name`,\n" +
            "COALESCE(poi.`quantity`, 0) AS `ordered`, ii.`preferred_stock`, x.`quantity`, x.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM x\n" +
            "LEFT OUTER JOIN `inventory_item` ii ON ii.`branch_id` = x.`branch_id` AND ii.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = x.`item_category_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = x.`branch_id` AND poi.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = x.`branch_id` AND sc.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = x.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = x.`vendor_id`";

    // max = 0
    String outOfStockCountQuery = "WITH " + itemCteClause + ")\n" +
            "SELECT COUNT(*)\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN i ON i.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = ii.`branch_id` AND ih.`item_id` = ii.`item_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ii.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ii.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND ih.`quantity` IS NULL";
    String outOfStockSearchQuery = "WITH " + itemCteClause + "),\n" + poiCteClause + ",\n" + scCteClause + ",\n" +
            // intermediate CTE to finalize filtering before joining in inventory_item, etc.
            "x AS (SELECT ii.`id`, ii.`branch_id`, b.`name` AS `branch_name`,\n" +
            "i.`item_by_manufacturer_id`, i.`item_category_id`,\n" +
            "i.`item_description`, ii.`item_id`, i.`item_name`, i.`item_part_number`,\n" +
            "i.`item_price`, i.`item_sku`, ii.`low_stock`, i.`manufacturer_id`,\n" +
            "ii.`preferred_stock`, i.`vendor_id`\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN `branch` b ON b.`id` = ii.`branch_id`\n" +
            "INNER JOIN i ON i.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = ii.`branch_id` AND ih.`item_id` = ii.`item_id`\n" +
            "WHERE ih.`quantity` IS NULL\n" +
            "AND (CONCAT(:branchIds) IS NULL OR ii.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ii.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "ORDER BY i.`item_name`, ii.`item_id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset)\n" +
            // main select
            "SELECT x.`id`, x.`branch_id`, x.`branch_name`, COALESCE(sc.`quantity`, 0) AS `in_cart`,\n" +
            "x.`item_by_manufacturer_id`, x.`item_category_id`, lc.`category` AS `item_category_name`,\n" +
            "x.`item_description`, x.`item_id`, x.`item_name`, x.`item_part_number`,\n" +
            "x.`item_price`, x.`item_sku`, x.`low_stock`, x.`manufacturer_id`, m.`name` AS `manufacturer_name`,\n" +
            "COALESCE(poi.`quantity`, 0) AS `ordered`, x.`preferred_stock`, 0 AS `quantity`, x.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM x\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = x.`item_category_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = x.`branch_id` AND poi.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = x.`branch_id` AND sc.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = x.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = x.`vendor_id`";

    // max = 0 and includeUndefined is true
    String outOfStockWithUndefinedCountQuery = "WITH " + itemCteClause + ")\n" +
            "SELECT COUNT(*)\n" +
            "FROM i CROSS JOIN `branch` b\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = b.`id` AND ih.`item_id` = i.`item_id`\n" +
            "WHERE ih.`quantity` IS NULL\n" +
            "AND (CONCAT(:branchIds) IS NULL OR b.`id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR b.`id` NOT IN (:branchIdsExclude))";
    String outOfStockWithUndefinedSearchQuery = "WITH " + poiCteClause + ",\n" + scCteClause + ",\n" +
            // I don't use itemCteClause here because it makes the query 4 seconds slower in Prod
            "x AS (SELECT b.`id` AS `branch_id`, b.`name` AS `branch_name`, ibv.`item_by_manufacturer_id`,\n" +
            "ibm.`l_code_category_id` AS `item_category_id`, ibm.`description` AS `item_description`, ibv.`id` AS `item_id`,\n" +
            "ibm.`name` AS `item_name`, ibm.`part_number` AS `item_part_number`, ibv.`price` AS `item_price`,\n" +
            "ibv.`sku` AS `item_sku`,ibm.`manufacturer_id`, ibv.`vendor_id`\n" +
            "FROM `item_by_vendor` ibv\n" +
            "CROSS JOIN `branch` b ON (CONCAT(:branchIds) IS NULL OR b.`id` IN (:branchIds))\n" +
            "  AND (CONCAT(:branchIdsExclude) IS NULL OR b.`id` NOT IN (:branchIdsExclude))\n" +
            "INNER JOIN `item_by_manufacturer` ibm ON ibm.`id` = ibv.`item_by_manufacturer_id`\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = b.`id` AND ih.`item_id` = ibv.`id`\n" +
            "WHERE ih.`quantity` IS NULL AND ibv.`active` = 1 AND (:partNumber IS NULL OR ibm.`part_number` LIKE :partNumber)\n" +
            // :sku is derived from :keywords, so they are both either NULL or not NULL
            "AND (((:keywords IS NULL OR :keywords = '') AND (:sku IS NULL OR :sku = ''))\n" +
            // :sku is not necessarily SKU but a literal value of the search string followed by '%'
            "  OR ibm.`description` LIKE :sku OR ibm.`name` LIKE :sku OR ibm.`keywords` LIKE :sku OR ibm.`part_number` LIKE :sku OR ibv.`sku` LIKE :sku\n" +
            // :keywords is a properly formatted full-text search string followed by '%' (e.g. "+orthopedic +shoe%")
            "  OR MATCH(ibm.`part_number`, ibm.`description`, ibm.`name`, ibm.`keywords`) AGAINST (:keywords IN BOOLEAN MODE))\n" +
            "ORDER BY ibm.`name`, ibv.`id` LIMIT :pageLimit OFFSET :pageOffset)\n" +
            // main select
            "SELECT COALESCE(ii.`id`, (x.`item_id` * 100000 + x.`branch_id`) * -1) AS `id`, x.`branch_id`, x.`branch_name`,\n" +
            "COALESCE(sc.`quantity`, 0) AS `in_cart`, x.`item_by_manufacturer_id`, x.`item_category_id`,\n" +
            "lc.`category` AS `item_category_name`, x.`item_description`, x.`item_id`, x.`item_name`, x.`item_part_number`,\n" +
            "x.`item_price`, x.`item_sku`, ii.`low_stock`, x.`manufacturer_id`, m.`name` AS `manufacturer_name`,\n" +
            "COALESCE(poi.`quantity`, 0) AS `ordered`, ii.`preferred_stock`, 0 AS `quantity`, x.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM x\n" +
            "LEFT OUTER JOIN `inventory_item` ii ON ii.`branch_id` = x.`branch_id` AND ii.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = x.`item_category_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = x.`branch_id` AND poi.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = x.`branch_id` AND sc.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = x.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = x.`vendor_id`";

    // these two queries are called not from "All Inventory", so they don't use `inventory_on_hand`
    String nameInStockWithUndefinedSearchQuery = "WITH x AS (SELECT ip.`branch_id`, b.`name` AS `branch_name`,\n" +
            "ibv.`item_by_manufacturer_id`, ibm.`description` AS `item_description`, ibm.`l_code_category_id` AS `item_category_id`,\n" +
            "ip.`item_by_vendor_id` AS `item_id`, ibm.`name` AS `item_name`, ibm.`part_number` AS `item_part_number`,\n" +
            "ibv.`price` AS `item_price`, ibv.`sku` AS `item_sku`, ibm.`manufacturer_id`, COUNT(*) AS `quantity`, ibv.`vendor_id`\n" +
            "FROM `item_physical` ip\n" +
            "INNER JOIN `branch` b on b.`id` = ip.`branch_id`\n" +
            "INNER JOIN `item_by_vendor` ibv ON ibv.`id` = ip.`item_by_vendor_id` AND ibv.`active` = 1\n" +
            "INNER JOIN `item_by_manufacturer` ibm on ibm.`id` = ibv.`item_by_manufacturer_id`\n" +
            "WHERE ip.`status` = 'inventory'\n" +
            "AND (CONCAT(:branchIds) IS NULL OR ip.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ip.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND (((:keywords IS NULL OR :keywords = '') AND (:sku IS NULL OR :sku = ''))\n" +
            "  OR ibm.`description` LIKE :sku OR ibm.`name` LIKE :sku OR ibm.`keywords` LIKE :sku OR ibm.`part_number` LIKE :sku OR ibv.`sku` LIKE :sku\n" +
            "  OR MATCH(ibm.`part_number`, ibm.`description`, ibm.`name`, ibm.`keywords`) AGAINST (:keywords IN BOOLEAN MODE))\n" +
            "GROUP BY ip.`branch_id`, b.`name`, ip.`item_by_vendor_id`, ibv.`item_by_manufacturer_id`, ibm.`description`,\n" +
            "ibm.`l_code_category_id`, ibm.`name`, ibm.`part_number`, ibv.`price`, ibv.`sku`, ibm.`manufacturer_id`, ibv.`vendor_id`\n" +
            "ORDER BY ibm.`name`, ibv.`id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset)\n" +
            // main select
            "SELECT COALESCE(ii.`id`, (x.`item_id` * 100000 + x.`branch_id`) * -1) AS `id`, x.`branch_id`, x.`branch_name`,\n" +
            "NULL AS `in_cart`, x.`item_by_manufacturer_id`, x.`item_category_id`, lc.`category` AS `item_category_name`,\n" +
            "x.`item_description`, x. `item_id`, x.`item_name`, x.`item_part_number`, x.`item_price`, x.`item_sku`,\n" +
            "ii.`low_stock`, x.`manufacturer_id`, m.`name` AS `manufacturer_name`, NULL AS `ordered`,\n" +
            "ii.`preferred_stock`, x.`quantity`, x.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM x\n" +
            "LEFT OUTER JOIN `inventory_item` ii on ii.`branch_id` = x.`branch_id` AND ii.`item_id` = x.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = x.`item_category_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = x.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = x.`vendor_id`";
    String nameWithUndefinedSearchQuery = "WITH " + itemCteClause +
            "\nORDER BY ibm.`name`, ibv.`id` LIMIT :itemLimit OFFSET :itemOffset)\n" +
            // main select
            "SELECT COALESCE(ii.`id`, (i.`item_id` * 100000 + b.`id`) * -1) AS `id`, b.`id` AS `branch_id`, b.`name` AS `branch_name`,\n" +
            "NULL AS `in_cart`, i.`item_by_manufacturer_id`, i.`item_category_id`, lc.`category` AS `item_category_name`,\n" +
            "i.`item_description`, i.`item_id`, i.`item_name`, i.`item_part_number`, i.`item_price`, i.`item_sku`,\n" +
            "ii.`low_stock`, i.`manufacturer_id`, m.`name` AS `manufacturer_name`, NULL AS `ordered`,\n" +
            "ii.`preferred_stock`,\n" +
            "(SELECT COUNT(*) FROM item_physical WHERE `branch_id` = ii.`branch_id` AND `item_by_vendor_id` = ii.`item_id` AND `status` = 'inventory') AS `quantity`,\n" +
            "i.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM i CROSS JOIN `branch` b\n" +
            "LEFT OUTER JOIN `inventory_item` ii ON ii.`branch_id` = b.`id` AND ii.`item_id` = i.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = i.`item_category_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = i.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = i.`vendor_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR b.`id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR b.`id` NOT IN (:branchIdsExclude))\n" +
            "ORDER BY i.`item_name`, i.`item_id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset";

    String reloadInventoryOnHandQuery = "INSERT IGNORE INTO `inventory_on_hand` (`branch_id`, `item_id`, `quantity`)\n" +
            "SELECT `branch_id`, `item_by_vendor_id`, COUNT(*)\n" +
            "FROM `item_physical`\n" +
            "WHERE `branch_id` > 0 AND `item_by_vendor_id` > 0 AND `status` = 'inventory'\n" +
            "GROUP BY `branch_id`, `item_by_vendor_id`";

    // lowStock, preferredStock or needsRestock is true
    String stockValuesCountQuery = "WITH " + poiCteClause + ",\n" + scCteClause + "\n" +
            // main select
            "SELECT COUNT(*)\n" +
            "FROM `inventory_item` ii\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = ii.`branch_id` AND ih.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = ii.`branch_id` AND poi.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = ii.`branch_id` AND sc.`item_id` = ii.`item_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ii.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ii.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND (:min IS NULL OR COALESCE(ih.`quantity`, 0) >= :min)\n" +
            "AND (:max IS NULL OR :max = 0 OR COALESCE(ih.`quantity`, 0) <= :max)\n" +
            "AND (:lowStock IS NULL OR COALESCE(ih.`quantity`, 0) <= ii.`low_stock`)\n" +
            "AND (:preferredStock IS NULL OR ii.`preferred_stock` >= 0)\n" +
            "AND (:needsRestock IS NULL OR (COALESCE(sc.`quantity`, 0) + COALESCE(poi.`quantity`, 0) + COALESCE(ih.`quantity`, 0)) < ii.`preferred_stock`)";
    String stockValuesSearchQuery = "WITH " + poiCteClause + ",\n" + scCteClause + "\n" +
            // main select
            "SELECT ii.`id`, ii.`branch_id`, b.`name` AS `branch_name`, COALESCE(sc.`quantity`, 0) AS `in_cart`,\n" +
            "ibv.`item_by_manufacturer_id`, ibm.`l_code_category_id` AS `item_category_id`, lc.`category` AS `item_category_name`,\n" +
            "ibm.`description` AS `item_description`, ii.`item_id`, ibm.`name` AS `item_name`, ibm.`part_number` AS `item_part_number`,\n" +
            "ibv.`price` AS `item_price`, ibv.`sku` AS `item_sku`, ii.`low_stock`, ibm.`manufacturer_id`, m.`name` AS `manufacturer_name`,\n" +
            "COALESCE(poi.`quantity`, 0) AS `ordered`, ii.`preferred_stock`, COALESCE(ih.`quantity`, 0) AS `quantity`,\n" +
            "ibv.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN `branch` b ON b.`id` = ii.`branch_id`\n" +
            "INNER JOIN `item_by_vendor` ibv ON ibv.`id` = ii.`item_id` AND ibv.`active` = 1\n" +
            "INNER JOIN `item_by_manufacturer` ibm ON ibm.`id` = ibv.`item_by_manufacturer_id`\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = ii.`branch_id` AND ih.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = ii.`branch_id` AND poi.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = ii.`branch_id` AND sc.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = ibm.`l_code_category_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = ibm.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = ibv.`vendor_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR b.`id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR b.`id` NOT IN (:branchIdsExclude))\n" +
            "AND (:min IS NULL OR COALESCE(ih.`quantity`, 0) >= :min)\n" +
            "AND (:max IS NULL OR :max = 0 OR COALESCE(ih.`quantity`, 0) <= :max)\n" +
            "AND (:lowStock IS NULL OR (ii.`low_stock` >= 0 AND ii.`preferred_stock` >= 0 AND COALESCE(ih.`quantity`, 0) <= ii.`low_stock`))\n" +
            "AND (:preferredStock IS NULL OR ii.`preferred_stock` >= 0)\n" +
            "AND (:needsRestock IS NULL OR (COALESCE(sc.`quantity`, 0) + COALESCE(poi.`quantity`, 0) + COALESCE(ih.`quantity`, 0)) < ii.`preferred_stock`)\n" +
            "ORDER BY ibm.`name`, ii.`item_id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset";

    // same as above but have parameters to filter by item name, etc.
    String stockValuesFilterCountQuery = "WITH " + itemCteClause + "),\n" + poiCteClause + ",\n" + scCteClause + "\n" +
            // main select
            "SELECT COUNT(*)\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN i ON i.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = ii.`branch_id` AND ih.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = ii.`branch_id` AND poi.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = ii.`branch_id` AND sc.`item_id` = ii.`item_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ii.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ii.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND (:min IS NULL OR COALESCE(ih.`quantity`, 0) >= :min)\n" +
            "AND (:max IS NULL OR :max = 0 OR COALESCE(ih.`quantity`, 0) <= :max)\n" +
            "AND (:lowStock IS NULL OR COALESCE(ih.`quantity`, 0) <= ii.`low_stock`)\n" +
            "AND (:preferredStock IS NULL OR ii.`preferred_stock` >= 0)\n" +
            "AND (:needsRestock IS NULL OR (COALESCE(sc.`quantity`, 0) + COALESCE(poi.`quantity`, 0) + COALESCE(ih.`quantity`, 0)) < ii.`preferred_stock`)";
    String stockValuesFilterSearchQuery = "WITH " + itemCteClause + "),\n" + poiCteClause + ",\n" + scCteClause + "\n" +
            // main select
            "SELECT ii.`id`, ii.`branch_id`, b.`name` AS `branch_name`, COALESCE(sc.`quantity`, 0) AS `in_cart`,\n" +
            "i.`item_by_manufacturer_id`, i.`item_category_id`, lc.`category` AS `item_category_name`,\n" +
            "i.`item_description`, i.`item_id`, i.`item_name`, i.`item_part_number`,\n" +
            "i.`item_price`, i.`item_sku`, ii.`low_stock`, i.`manufacturer_id`, m.`name` AS `manufacturer_name`,\n" +
            "COALESCE(poi.`quantity`, 0) AS `ordered`, ii.`preferred_stock`, COALESCE(ih.`quantity`, 0) AS `quantity`,\n" +
            "i.`vendor_id`, v.`name` AS `vendor_name`\n" +
            "FROM `inventory_item` ii\n" +
            "INNER JOIN `branch` b ON b.`id` = ii.`branch_id`\n" +
            "INNER JOIN i ON i.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `inventory_on_hand` ih ON ih.`branch_id` = ii.`branch_id` AND ih.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN poi ON poi.`branch_id` = ii.`branch_id` AND poi.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN sc ON sc.`branch_id` = ii.`branch_id` AND sc.`item_id` = ii.`item_id`\n" +
            "LEFT OUTER JOIN `l_code_category` lc ON lc.`id` = i.`item_category_id`\n" +
            "LEFT OUTER JOIN `vendor` m ON m.`id` = i.`manufacturer_id`\n" +
            "LEFT OUTER JOIN `vendor` v ON v.`id` = i.`vendor_id`\n" +
            "WHERE (CONCAT(:branchIds) IS NULL OR ii.`branch_id` IN (:branchIds))\n" +
            "AND (CONCAT(:branchIdsExclude) IS NULL OR ii.`branch_id` NOT IN (:branchIdsExclude))\n" +
            "AND (:min IS NULL OR COALESCE(ih.`quantity`, 0) >= :min)\n" +
            "AND (:max IS NULL OR :max = 0 OR COALESCE(ih.`quantity`, 0) <= :max)\n" +
            "AND (:lowStock IS NULL OR (ii.`low_stock` >= 0 AND COALESCE(ih.`quantity`, 0) <= ii.`low_stock`))\n" +
            "AND (:preferredStock IS NULL OR ii.`preferred_stock` >= 0)\n" +
            "AND (:needsRestock IS NULL OR (COALESCE(sc.`quantity`, 0) + COALESCE(poi.`quantity`, 0) + COALESCE(ih.`quantity`, 0)) < ii.`preferred_stock`)\n" +
            "ORDER BY i.`item_name`, ii.`item_id`, b.`name` LIMIT :pageLimit OFFSET :pageOffset";
    String truncateInventoryOnHandQuery = "TRUNCATE TABLE `inventory_on_hand`";

    @Query(value = crossJoinCountQuery, nativeQuery = true)
    public Long countByCrossJoin(int branchesCount, String keywords, String partNumber, String sku);
    @Query(value = crossJoinSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByCrossJoin(List<Long> branchIds, List<Long> branchIdsExclude, Long itemLimit, Long itemOffset,
                                                    String keywords, Long pageLimit, Long pageOffset, String partNumber, String sku);
    @Query(value = inventoryItemBranchCountQuery, nativeQuery = true)
    public Long countByInventoryItemBranch(List<Long> branchIds, List<Long> branchIdsExclude);
    @Query(value = inventoryItemBranchSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByInventoryItemBranch(List<Long> branchIds, List<Long> branchIdsExclude, Long pageLimit, Long pageOffset);
    @Query(value = inventoryItemCountQuery, nativeQuery = true)
    public Long countByInventoryItem(List<Long> branchIds, List<Long> branchIdsExclude, String keywords, String partNumber, String sku);
    @Query(value = inventoryItemSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByInventoryItem(List<Long> branchIds, List<Long> branchIdsExclude, String keywords,
                                                        Long pageLimit, Long pageOffset, String partNumber, String sku);
    @Query(value = minMaxCountQuery, nativeQuery = true)
    public Long countByMinMax(List<Long> branchIds, List<Long> branchIdsExclude, String keywords, Long max, Long min,
                              String partNumber, String sku);
    @Query(value = minMaxSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByMinMax(List<Long> branchIds, List<Long> branchIdsExclude, String keywords,
                                                 Long max, Long min, Long pageLimit, Long pageOffset, String partNumber, String sku);
    @Query(value = minMaxWithUndefinedCountQuery, nativeQuery = true)
    public Long countByMinMaxWithUndefined(List<Long> branchIds, List<Long> branchIdsExclude, String keywords, Long max,
                                           Long min, String partNumber, String sku);
    @Query(value = minMaxWithUndefinedSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByMinMaxWithUndefined(List<Long> branchIds, List<Long> branchIdsExclude, String keywords,
                                                              Long max, Long min, Long pageLimit, Long pageOffset, String partNumber, String sku);
    @Query(value = outOfStockCountQuery, nativeQuery = true)
    public Long countByOutOfStock(List<Long> branchIds, List<Long> branchIdsExclude, String keywords, String partNumber, String sku);
    @Query(value = outOfStockSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByOutOfStock(List<Long> branchIds, List<Long> branchIdsExclude, String keywords,
                                                     Long pageLimit, Long pageOffset, String partNumber, String sku);
    @Query(value = outOfStockWithUndefinedCountQuery, nativeQuery = true)
    public Long countByOutOfStockWithUndefined(List<Long> branchIds, List<Long> branchIdsExclude, String keywords, String partNumber, String sku);
    @Query(value = outOfStockWithUndefinedSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByOutOfStockWithUndefined(List<Long> branchIds, List<Long> branchIdsExclude, String keywords,
                                                                  Long pageLimit, Long pageOffset, String partNumber, String sku);
    @Query(value = stockValuesCountQuery, nativeQuery = true)
    public Long countByStockValues(List<Long> branchIds, List<Long> branchIdsExclude, Boolean lowStock, Long max, Long min,
                                   Boolean needsRestock, Boolean preferredStock);
    @Query(value = stockValuesSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByStockValues(List<Long> branchIds, List<Long> branchIdsExclude, Boolean lowStock, Long max, Long min,
                                                      Boolean needsRestock, Long pageLimit, Long pageOffset, Boolean preferredStock);
    @Query(value = stockValuesFilterCountQuery, nativeQuery = true)
    public Long countByStockValuesFilter(List<Long> branchIds, List<Long> branchIdsExclude, String keywords, Boolean lowStock,
                                         Long max, Long min, Boolean needsRestock, String partNumber, Boolean preferredStock, String sku);
    @Query(value = stockValuesFilterSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByStockValuesFilter(List<Long> branchIds, List<Long> branchIdsExclude, String keywords,
                                                            Boolean lowStock, Long max, Long min, Boolean needsRestock, Long pageLimit,
                                                            Long pageOffset, String partNumber, Boolean preferredStock, String sku);

    @Query(value = nameInStockWithUndefinedSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByNameInStockWithUndefined(List<Long> branchIds, List<Long> branchIdsExclude, String keywords,
                                                                   Long pageLimit, Long pageOffset, String sku);
    @Query(value = nameWithUndefinedSearchQuery, nativeQuery = true)
    public List<InventoryItemDTO> searchByNameWithUndefined(List<Long> branchIds, List<Long> branchIdsExclude, Long itemLimit, Long itemOffset,
                                                            String keywords, Long pageLimit, Long pageOffset, String partNumber, String sku);
    @Transactional
    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query(value = reloadInventoryOnHandQuery, nativeQuery = true)
    public void reloadInventoryOnHand();
    @Transactional
    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query(value = truncateInventoryOnHandQuery, nativeQuery = true)
    public void truncateInventoryOnHand();
}
