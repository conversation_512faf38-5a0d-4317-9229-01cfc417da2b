package com.nymbl.tenant.repository;

import com.nymbl.config.dto.DailyCloseReport.DCPayment;
import com.nymbl.config.dto.reports.CashRow;
import com.nymbl.tenant.interfaces.IClaimTotals;
import com.nymbl.tenant.model.Payment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;

/**
 * Created by <PERSON> on 06/15/2017.
 */
public interface PaymentRepository extends JpaRepository<Payment, Long>, JpaSpecificationExecutor<Payment> {

    List<Payment> getByAutoPostPatientIdAndUnappliedAmountGreaterThan(Long autoPostPatientId, BigDecimal unappliedAmount);

    List<Payment> getByAutoPostPatient_AutoPostIdAndUnappliedAmountGreaterThan(Long autoPostId, BigDecimal unappliedAmount);

    Payment findTop1ByAutoPostPatientIdOrderByIdDesc(Long autoPostPatientId);

    List<Payment> findAllByAutoPostPatientIdIn(List<Long> autoPostPatientIds);

    List<Payment> findByInsuranceCompanyId(Long insuranceCompanyId);

    List<Payment> findByPatientId(Long patientId);

    List<Payment> findByPatientIdAndUnappliedAmountGreaterThan(Long patientId, BigDecimal unappliedAmount);

    List<Payment> findAllByDateBetween(Date startDate, Date endDate);

    List<Payment> findAllByDateBetweenAndPatientIdIsNull(Date startDate, Date endDate);

    List<Payment> findByClaimId(Long claimId);

    List<Payment> findByPrescriptionId(Long prescriptionId);

//    List<Payment> findAllByDateBetweenAndPayerTypeIsIn(Date startDate, Date endDate, String payerType);

    @Query(value = "SELECT * FROM payment p " +
        "WHERE p.date BETWEEN :startDate AND :endDate " +
        "AND p.id NOT IN (select DISTINCT payment_id from applied_payment) " +
        "AND p.payer_type != 'patient' ", nativeQuery = true)
    List<Payment> findAllUnappliedPaymentsBeforeDate(@Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    @Query(value = "SELECT * FROM payment p " +
            "WHERE p.date BETWEEN :startDate AND :endDate " +
            "AND p.deposit_date is null ", nativeQuery = true)
    List<Payment> findAllNullDepositDatesBetweenDate(@Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    //    TODO: BRM = billing_branch_id
    @Query(value = "SELECT * FROM payment p " +
        "LEFT JOIN patient pt ON p.patient_id = pt.id " +
        "WHERE ( :branchId = 0 OR :branchId IS null OR pt.primary_branch_id = :branchId) " +
        "AND (( :dateOption = 'payment' AND p.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p.deposit_date BETWEEN :startDate AND :endDate )) ", nativeQuery = true)
    List<Payment> getPaymentsByDateAndBranchId(@Param("startDate") String startDate,
                                               @Param("endDate") String endDate,
                                               @Param("dateOption") String dateOption,
                                               @Param("branchId") Long branchId);

    //    TODO: BRM = billing_branch_id
    @Query(value = "SELECT * FROM payment p " +
        "LEFT JOIN patient pt ON p.patient_id = pt.id " +
        "LEFT JOIN claim c ON c.id = p.claim_id " +
        "LEFT JOIN prescription rx ON c.prescription_id = rx.id " +
        "LEFT JOIN device_type dt ON rx.device_type_id = dt.id " +
        "WHERE ( :branchId = 0 OR :branchId IS null OR pt.primary_branch_id = :branchId) " +
        "AND (( :dateOption = 'payment' AND p.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p.deposit_date BETWEEN :startDate AND :endDate )) ", nativeQuery = true)
    List<Payment> getPaymentsByBranchAndDate(@Param("startDate") String startDate,
                                             @Param("endDate") String endDate,
                                             @Param("dateOption") String dateOption,
                                             @Param("branchId") Long branchId);

    @Query(value = "SELECT * from payment payments " +
        "WHERE payments.id IN (" +
        "SELECT DISTINCT(p.id) FROM payment p " +
        "JOIN applied_payment ap ON ap.payment_id = p.id " +
        "JOIN claim c ON ap.claim_id = c.id " +
        "JOIN prescription rx ON c.prescription_id = rx.id " +
        "JOIN patient pt ON rx.patient_id = pt.id " +
        "JOIN device_type dt ON rx.device_type_id = dt.id " +
        "WHERE ( :branchId = 0 OR :branchId IS null OR c.billing_branch_id = :branchId) " +
        "AND (  " +
        "CASE WHEN :deviceType = ' ' THEN dt.orthotic_or_prosthetic = ('orthotic' OR 'prosthetic' )" +
        "ELSE " +
        ":deviceType = dt.orthotic_or_prosthetic " +
        "END)   " +
        "AND (( :dateOption = 'payment' AND p.date BETWEEN :startDate AND :endDate ) " +
        "OR ( :dateOption <> 'payment' AND p.deposit_date BETWEEN :startDate AND :endDate ))) ", nativeQuery = true)
    List<Payment> getPaymentsForCashSummary(@Param("startDate") String startDate,
                                            @Param("endDate") String endDate,
                                            @Param("dateOption") String dateOption,
                                            @Param("deviceType") String deviceType,
                                            @Param("branchId") Long branchId);

//    @Query(value = "SELECT p.id as paymentId, " +
//            "c.billing_branch_id as branchId, " +
//            "p.patient_id as patientId, " +
//            "p.date as createdDate, " +
//            "p.payer_type as payerType, " +
//            "p.payment_type as paymentType, " +
//            "p.description, " +
//            "p.check_number as checkNumber, " +
//            "COALESCE(SUM(ap.amount_applied), 0.00) AS paymentApplied, " +
//            "COALESCE(p.unapplied_amount, 0.00) AS paymentUnApplied, " +
//            "COALESCE(p.amount, 0.00) AS amount, " +
//            "COALESCE(p.adjustment, 0.00) AS adjustment, " +
//            "p.adjustment_id AS adjustmentTypeId, " +
//            "pt.first_name, " +
//            "pt.last_name, " +
//            "ic.name, " +
//            "p.created_by_id " +
//            "FROM payment p " +
//            "INNER JOIN patient pt ON p.patient_id = pt.id " +
//            "LEFT JOIN applied_payment ap ON p.id = ap.payment_id " +
//            "LEFT JOIN claim c on ap.claim_id = c.id " +
//            "LEFT JOIN insurance_company ic ON p.insurance_company_id = ic.id " +
//            "WHERE p.date between :startDate AND :endDate " +
//            "AND (('%'=:payerType AND p.payer_type like :payerType) OR ('patient%'=:payerType AND p.payer_type like :payerType) OR ('insurance_company'=:payerType AND p.payer_type in ('insurance_company', 'adjustment')) )" +
//            "AND c.billing_branch_id IN (:branches) " +
//            "GROUP BY p.id",
//            nativeQuery = true)
//    List<Object[]> getAllDailyClosePaymentsForReportByPaymentDate(@Param("startDate") String startDate,
//                                                                  @Param("endDate") String endDate,
//                                                                  @Param("payerType") String payerType,
//                                                                  @Param("branches") List<Long> branches);
//
//    @Query(value = "SELECT p.id as paymentId, " +
//            "c.billing_branch_id as branchId, " +
//            "p.patient_id as patientId, " +
//            "p.deposit_date as createdDate, " +
//            "p.payer_type as payerType, " +
//            "p.payment_type as paymentType, " +
//            "p.description, " +
//            "p.check_number as checkNumber, " +
//            "COALESCE(SUM(ap.amount_applied), 0.00) AS paymentApplied, " +
//            "COALESCE(p.unapplied_amount, 0.00) AS paymentUnApplied, " +
//            "COALESCE(p.amount, 0.00) AS amount, " +
//            "COALESCE(p.adjustment, 0.00) AS adjustment, " +
//            "p.adjustment_id AS adjustmentTypeId, " +
//            "pt.first_name, " +
//            "pt.last_name, " +
//            "ic.name, " +
//            "p.created_by_id " +
//            "FROM payment p " +
//            "INNER JOIN patient pt ON p.patient_id = pt.id " +
//            "LEFT JOIN applied_payment ap ON p.id = ap.payment_id " +
//            "LEFT JOIN insurance_company ic ON p.insurance_company_id = ic.id " +
//            "WHERE p.deposit_date between :startDate AND :endDate " +
//            "AND (('%'=:payerType AND p.payer_type like :payerType) OR ('patient%'=:payerType AND p.payer_type like :payerType) OR ('insurance_company'=:payerType AND p.payer_type in ('insurance_company', 'adjustment')) )" +
//            "AND pt.primary_branch_id IN (:branches) " +
//            "GROUP BY p.id",
//            nativeQuery = true)
//    List<Object[]> getAllDailyClosePaymentsForReportByDepositDate(@Param("startDate") String startDate,
//                                                                  @Param("endDate") String endDate,
//                                                                  @Param("payerType") String payerType,
//                                                                  @Param("branches") List<Long> branches);

    String getAllDailyClosePaymentsForReportHeader = "paymentId,branchId,patientId,paymentDate,depositDate,payerType,paymentType,description,checkNumber,paymentApplied,paymentUnapplied" +
        ",amount,adjustment,adjustmentApplied,adjustmentUnapplied,adjustmentTypeId,first_name,last_name,name,created_by_id,appliedDate";
    String dailyCloseRecords = "select b.*\n" +
        ", b.amount - b.paymentApplied AS paymentUnApplied\n" +
        ", b.adjustment - b.adjustmentApplied as adjustmentUnapplied\n" +
        "from (\n" +
        "select a.*\n" +
            ", COALESCE((select sum(ap.amount_applied) from applied_payment ap where ap.payment_id=a.paymentId), 0.00) as paymentApplied\n" +
            ", COALESCE((select sum(ap.adjustment_applied) from applied_payment ap where ap.payment_id=a.paymentId), 0.00) as adjustmentApplied\n" +
            ", (select min(applied_date) from applied_payment ap where ap.payment_id=a.paymentId) as appliedDate\n" +
            "from (\n" +
            "SELECT distinct p.id as paymentId, \n" +
            "pt.primary_branch_id as branchId, \n" +
            "p.patient_id as patientId, \n" +
            "p.date as paymentDate, \n" +
            "p.deposit_date as depositDate, \n" +
            "p.payer_type as payerType, \n" +
            "p.payment_type as paymentType, \n" +
            "p.description, \n" +
            "p.check_number as checkNumber, \n" +
            "COALESCE(p.amount, 0.00) AS amount, \n" +
            "COALESCE(p.adjustment, 0.00) AS adjustment, \n" +
            "p.adjustment_id AS adjustmentTypeId, \n" +
        "pt.first_name, \n" +
            "pt.last_name, \n" +
            "ic.name, \n" +
            "p.created_by_id, \n" +
            "p.created_at \n" +
            "FROM payment p \n" +
            "LEFT JOIN patient pt ON p.patient_id = pt.id \n" +
            "LEFT JOIN applied_payment ap ON p.id = ap.payment_id \n" +
            "LEFT JOIN insurance_company ic ON p.insurance_company_id = ic.id \n" +
            "WHERE (( :dateOption = 'payment' AND p.date between :startDate AND :endDate) OR ( :dateOption <> 'payment' AND p.deposit_date between :startDate AND :endDate)) \n" +
            "AND (('%'=:payerType AND p.payer_type like :payerType) OR ('patient%'=:payerType AND p.payer_type like :payerType) OR ('insurance%'=:payerType AND p.payer_type like :payerType)) \n" +
            "AND pt.primary_branch_id IN (:branches) \n" +
            "AND pt.active = 1 \n" +
            ") as a\n" +
            ") as b\n";

    String sumDailyClosePayments = "select paymentId,branchId,patientId,paymentDate,depositDate,created_at,payerType,paymentType,description,checkNumber\n" +
        ",paymentApplied" +
        ",sum(paymentUnApplied) as paymentUnApplied\n" +
        ",amount" +
        ",adjustment" +
        ",sum(adjustmentApplied) as adjustmentApplied\n" +
        ",sum(adjustmentUnapplied) as adjustmentUnapplied\n" +
        ",adjustmentTypeId" +
        ",first_name" +
        ",last_name" +
        ",name,created_by_id,appliedDate \n" +
        "from (" +
        dailyCloseRecords +
        ") as p \n" +
        "GROUP BY paymentId,branchId,patientId,paymentDate,depositDate,created_at,payerType,paymentType,description,checkNumber,paymentApplied,amount,adjustment,adjustmentTypeId,first_name,last_name,name,created_by_id,appliedDate \n";
    String getAllDailyClosePaymentsForReportQuery = "select " + getAllDailyClosePaymentsForReportHeader + " from (\n" +
            sumDailyClosePayments +
            ") as a \n";

    @Deprecated
    @Query(value = getAllDailyClosePaymentsForReportQuery,
            nativeQuery = true)
    List<Object[]> getAllDailyClosePaymentsForReport(@Param("startDate") String startDate,
                                                     @Param("endDate") String endDate,
                                                     @Param("dateOption") String dateOption,
                                                     @Param("payerType") String payerType,
                                                     @Param("branches") List<Long> branches);

    @Query(value = """
                SELECT distinct
                    `p`.`id` AS `paymentId`,
                 	`pt`.`primary_branch_id` AS `branchId`,
                 	`ptb`.`name` AS `branchName`,
                 	`p`.`patient_id` AS `patientId`,
                 	CONCAT(`pt`.`first_name`, ' ' , `pt`.`last_name`) AS `patientName`,
                 	`p`.`date` AS `paymentDate`,
                 	`p`.`deposit_date` AS `depositDate`,
                 	`p`.`payer_type` AS `payerType`,
                 	CASE WHEN ((`p`.`payment_type` = 'adjustment' OR `p`.`payment_type` = 'patient_adjustment') AND `adj`.`withdraw` = 1) THEN
                 	    CONCAT(`p`.`payment_type`, ' - ', COALESCE(`adj`.`payment_type_origin`, ''), ' ZZ withdraw calculated')
                    ELSE
                 	    `p`.`payment_type`
                    END AS `paymentType`,
                 	`p`.`adjustment_id` AS `adjustmentTypeId`,
                 	`adj`.`withdraw` AS `withdraw`,
                 	`adj`.`payment_type_origin` AS `paymentTypeOrigin`,
                 	`p`.`description` AS `description`,
                 	`p`.`check_number` AS `checkNumber`,
                 	COALESCE(`p`.`amount`, 0.00) AS `paymentAmount`,
                 	COALESCE(SUM(`ap`.`amount_applied`), 0.00) AS `paymentApplied`,
                 	COALESCE(`p`.`amount`, 0.00) - COALESCE(SUM(`ap`.`amount_applied`), 0.00) AS `paymentUnapplied`,
                    CASE WHEN `adj`.`operation` = '-' THEN COALESCE(`p`.`adjustment` *-1, 0.00) ELSE COALESCE(`p`.`adjustment`, 0.00) END AS `adjustmentAmount`,
                    COALESCE(SUM(`ap`.`adjustment_applied`), 0.00) AS `adjustmentApplied`,
                    COALESCE(`p`.`adjustment`, 0.00) - COALESCE(SUM(`ap`.`adjustment_applied`), 0.00) AS `adjustmentUnapplied`,
                    CASE WHEN `p`.`payer_type` = 'patient' THEN CONCAT(`pt`.`first_name`, ' ' , `pt`.`last_name`) ELSE `ic`.`name` END AS `payerName`,
                 	`p`.`created_by_id` AS `createdById`,
                 	`cu`.`first_name` AS `createdByFirstName`,
                 	`cu`.`last_name` AS `createdByLastName`,
                 	`p`.`created_at` AS `createdAt`,
                 	MIN(ap.`applied_date`) AS `appliedDate`
                FROM `payment` `p`
                LEFT JOIN `adjustment` `adj` ON `p`.`adjustment_id` = `adj`.`id`
                LEFT JOIN `patient` `pt` ON `p`.`patient_id` = `pt`.`id`
                LEFT JOIN branch `ptb` ON `pt`.`primary_branch_id` = `ptb`.`id`
                LEFT JOIN `applied_payment` `ap` ON `p`.`id` = `ap`.`payment_id`
                LEFT JOIN `insurance_company` `ic` ON `p`.`insurance_company_id` = `ic`.`id`
                LEFT JOIN `nymbl_master`.`user` `cu` ON `p`.`created_by_id` = `cu`.`id`
                WHERE (( 'payment' = :dateOption AND `p`.`date` between :startDate AND :endDate) OR ( 'payment' <> :dateOption AND `p`.`deposit_date` between :startDate AND :endDate))
                AND (('%' = :payerType AND `p`.`payer_type` like :payerType) OR ('patient%' = :payerType AND `p`.`payer_type` like :payerType) OR ('insurance%' = :payerType AND `p`.`payer_type` like :payerType))
                AND `pt`.`primary_branch_id` IN (:branches)
                AND `pt`.`active` = 1
                GROUP BY `p`.`id`
            """, nativeQuery = true)
    List<DCPayment> getAllDailyClosePaymentsForReportNew(@Param("startDate") String startDate,
                                                      @Param("endDate") String endDate,
                                                      @Param("dateOption") String dateOption,
                                                      @Param("payerType") String payerType,
                                                      @Param("branches") List<Long> branches);

    @Query(name = "CashReportRecords", nativeQuery = true)
    List<CashRow> getDailyCloseV2Rows(@Param("startDate") Date startDate,
                                      @Param("endDate") Date endDate,
                                      @Param("dateOption") String dateOption,
                                      @Param("payerType") String payerType,
                                      @Param("branchId") Long branch);

    @Query(name = "CashReportSubRecords", nativeQuery = true)
    List<CashRow> getDailyCloseV2SubRows();

    @Query(value = "SELECT * FROM payment p where p.id not in (select payment_id from applied_payment) AND p.date between :startDate AND :endDate ;", nativeQuery = true)
    List<Payment> findUnappliedPatientPayments(@Param("startDate") String startDate,
                                               @Param("endDate") String endDate);

    //    TODO: BRM = billing_branch_id
    @Query(value = "SELECT * FROM payment " +
            "JOIN patient p ON payment.patient_id = p.id " +
            "WHERE payment.id not in (select payment_id from applied_payment) " +
            "AND (payment.date between :startDate AND :endDate) " +
        "AND p.primary_branch_id = :branchId ;", nativeQuery = true)
    List<Payment> findUnappliedPatientPaymentsByBranchId(@Param("startDate") String startDate,
                                                         @Param("endDate") String endDate,
                                                         @Param("branchId") Long branchId);

    @Query(value = "SELECT * FROM payment " +
        "JOIN patient p ON payment.patient_id = p.id " +
        "JOIN claim c ON c.id = payment.claim_id " +
        "JOIN prescription rx ON c.prescription_id = rx.id " +
        "JOIN device_type dt ON rx.device_type_id = dt.id " +
        "WHERE (payment.id not in (select payment_id from applied_payment)) " +
        "AND (payment.date between :startDate AND :endDate) " +
        "AND c.billing_branch_id = :branchId " +
        "AND dt.orthotic_or_prosthetic = :deviceType ;", nativeQuery = true)
    List<Payment> findUnappliedPatientPaymentsByBranchIdAndDeviceType(@Param("startDate") String startDate,
                                                                      @Param("endDate") String endDate,
                                                                      @Param("branchId") Long branchId,
                                                                      @Param("deviceType") String deviceType);

    @Query(value = "SELECT * FROM payment " +
        "JOIN patient p ON payment.patient_id = p.id " +
        "JOIN claim c ON c.id = payment.claim_id " +
        "JOIN prescription rx ON c.prescription_id = rx.id " +
        "JOIN device_type dt ON rx.device_type_id = dt.id " +
        "WHERE (payment.id not in (select payment_id from applied_payment)) " +
        "AND (payment.date between :startDate AND :endDate) " +
        "AND dt.orthotic_or_prosthetic = :deviceType ;", nativeQuery = true)
    List<Payment> findUnappliedPatientPaymentsByDeviceType(@Param("startDate") String startDate,
                                                           @Param("endDate") String endDate,
                                                           @Param("deviceType") String deviceType);

    @Query(value = "SELECT" +
        "(select SUM(amount) from payment where date between :startDate and :endDate ) as total_amount, " +
        "SUM(aplc.amount) as total_applied_amount, " +
        "(select SUM(amount) from payment where date between :startDate and :endDate ) - SUM(aplc.amount) as total_unapplied_amount, " +
        "SUM(CASE WHEN p.payment_type = 'patient_check' THEN aplc.amount ELSE 0 END) as total_patient_check, " +
        "(select SUM(amount) from payment WHERE payment_type = 'patient_check' AND date between :startDate and :endDate ) - SUM(CASE WHEN p.payment_type = 'patient_check' THEN aplc.amount ELSE 0 END) as total_unapplied_patient_check, " +
        "SUM(CASE WHEN p.payment_type = 'cash' THEN aplc.amount ELSE 0 END) as total_cash, " +
        "(select SUM(amount) from payment WHERE payment_type = 'cash' AND date between :startDate and :endDate ) - SUM(CASE WHEN p.payment_type = 'cash' THEN aplc.amount ELSE 0 END) as total_unapplied_cash, " +
        "SUM(CASE WHEN p.payment_type = 'patient_credit_card' THEN aplc.amount ELSE 0 END) as total_patient_credit_card, " +
        "(select SUM(amount) from payment WHERE payment_type = 'patient_credit_card' AND date between :startDate and :endDate ) - SUM(CASE WHEN p.payment_type = 'patient_credit_card' THEN aplc.amount ELSE 0 END) as total_unapplied_patient_credit_card, " +
        "SUM(CASE WHEN p.payment_type = 'insurance_payment_electronic' OR p.payment_type = 'insurance_payment_credit_card' OR p.payment_type = 'auto_post' OR p.payment_type = 'insurance_payment_check' THEN aplc.amount ELSE 0 END) as total_insurance, " +
        "(select SUM(amount) from payment WHERE find_in_set(payment_type, 'insurance_payment_electronic,insurance_payment_credit_card,auto_post,insurance_payment_check') AND date between :startDate and :endDate )  " +
        "    - SUM(CASE WHEN p.payment_type = 'insurance_payment_electronic' OR p.payment_type = 'insurance_payment_credit_card' OR p.payment_type = 'auto_post' OR p.payment_type = 'insurance_payment_check' THEN aplc.amount ELSE 0 END) as total_unapplied_insurance " +
        "FROM payment p " +
        "JOIN applied_payment ap ON ap.payment_id = p.id " +
        "JOIN applied_payment_l_code aplc ON aplc.applied_payment_id = ap.id " +
        "WHERE date between :startDate and :endDate ; ", nativeQuery = true)
    List<Object[]> getAppliedAndUnappliedPaymentTotals(@Param("startDate") String startDate,
                                                       @Param("endDate") String endDate);

    //    TODO: BRM = billing_branch_id
    @Query(value = "SELECT SUM(amount) FROM payment p " +
        "LEFT JOIN patient pt ON p.patient_id = pt.id " +
        "WHERE ( :branchId = 0 OR :branchId IS null OR pt.primary_branch_id = :branchId) " +
        "AND (( :dateOption = 'payment' AND p.date BETWEEN :startDate AND :endDate ) " +
        "OR ( :dateOption <>'payment' AND p.deposit_date BETWEEN :startDate AND :endDate )) ;", nativeQuery = true)
    BigDecimal getSumOfPaymentsByDateAndBranch(@Param("startDate") String startDate,
                                               @Param("endDate") String endDate,
                                               @Param("dateOption") String dateOption,
                                               @Param("branchId") Long branchId);

    @Query(value = "SELECT name, sum(total) from ( " +
            "SELECT aplc1.adjustment_type1 as name, sum(aplc1.adjustment_amount1) as total " +
            "FROM payment p1 " +
            "JOIN applied_payment ap1 ON ap1.payment_id = p1.id " +
            "JOIN applied_payment_l_code aplc1 ON aplc1.applied_payment_id = ap1.id " +
            "WHERE aplc1.adjustment_type1 is not null and aplc1.adjustment_amount1 is not null and aplc1.adjustment_amount1 <> 0 " +
            "AND (( :dateOption = 'payment' AND p1.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p1.deposit_date BETWEEN :startDate AND :endDate )) " +
            "GROUP BY aplc1.adjustment_type1 " +
            "UNION ALL " +
            "SELECT aplc2.adjustment_type2 as name, sum(aplc2.adjustment_amount2) as total " +
            "FROM payment p2 " +
            "JOIN applied_payment ap2 ON ap2.payment_id = p2.id " +
            "JOIN applied_payment_l_code aplc2 ON aplc2.applied_payment_id = ap2.id " +
            "WHERE aplc2.adjustment_type2 is not null and aplc2.adjustment_amount2 is not null and aplc2.adjustment_amount2 <> 0 " +
            "AND (( :dateOption = 'payment' AND p2.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p2.deposit_date BETWEEN :startDate AND :endDate )) " +
            "GROUP BY aplc2.adjustment_type2 " +
            "UNION ALL " +
            "SELECT aplc3.adjustment_type3 as name, sum(aplc3.adjustment_amount3) as total " +
            "FROM payment p3 " +
            "JOIN applied_payment ap3 ON ap3.payment_id = p3.id " +
            "JOIN applied_payment_l_code aplc3 ON aplc3.applied_payment_id = ap3.id " +
        "WHERE aplc3.adjustment_type3 is not null and aplc3.adjustment_amount3 is not null and aplc3.adjustment_amount3 <> 0 " +
        "AND (( :dateOption = 'payment' AND p3.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p3.deposit_date BETWEEN :startDate AND :endDate )) " +
        "GROUP BY aplc3.adjustment_type3 " +
        "UNION ALL " +
        "SELECT aplc4.adjustment_type4 as name, sum(aplc4.adjustment_amount4) as total " +
        "FROM payment p4 " +
        "JOIN applied_payment ap4 ON ap4.payment_id = p4.id " +
        "JOIN applied_payment_l_code aplc4 ON aplc4.applied_payment_id = ap4.id " +
        "WHERE aplc4.adjustment_type4 is not null and aplc4.adjustment_amount4 is not null and aplc4.adjustment_amount4 <> 0 " +
        "AND (( :dateOption = 'payment' AND p4.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p4.deposit_date BETWEEN :startDate AND :endDate )) " +
        "GROUP BY aplc4.adjustment_type4 " +
        "UNION ALL " +
        "SELECT aplc5.adjustment_type5 as name, sum(aplc5.adjustment_amount5) as total " +
        "FROM payment p5 " +
        "JOIN applied_payment ap5 ON ap5.payment_id = p5.id " +
        "JOIN applied_payment_l_code aplc5 ON aplc5.applied_payment_id = ap5.id " +
        "WHERE aplc5.adjustment_type5 is not null and aplc5.adjustment_amount5 is not null and aplc5.adjustment_amount5 <> 0 " +
        "AND (( :dateOption = 'payment' AND p5.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p5.deposit_date BETWEEN :startDate AND :endDate )) " +
        "GROUP BY aplc5.adjustment_type5) as ctr " +
        "group by name ; ", nativeQuery = true)
    List<Object[]> getLineAdjustmentTotalsByDate(@Param("startDate") String startDate,
                                                 @Param("endDate") String endDate,
                                                 @Param("dateOption") String dateOption);
    @Query(value = "SELECT a.name, sum(aplc.adjustment) as adjustment_total " +
        "FROM payment p " +
        "JOIN applied_payment ap ON ap.payment_id = p.id " +
        "JOIN applied_payment_l_code aplc ON aplc.applied_payment_id = ap.id " +
        "JOIN adjustment a on a.id = aplc.adjustment_type " +
        "where aplc.adjustment_type is not null " +
        "AND ( :dateOption = 'payment' AND p.date BETWEEN :startDate AND :endDate ) OR ( :dateOption <> 'payment' AND p.deposit_date BETWEEN :startDate AND :endDate ) " +
        "group by a.name ; ", nativeQuery = true)
    List<Object[]> getARAdjustmentTotalsByDate(@Param("startDate") String startDate,
                                               @Param("endDate") String endDate,
                                               @Param("dateOption") String dateOption);


    @Query(value = "select sum(amount) from payment " +
            "where claim_id IS NULL AND patient_id IS NOT NULL " +
            "AND ((:dateOption <> 'payment' AND deposit_date <= :endDate ) OR (:dateOption = 'payment' AND date <= :endDate)) " +
            "AND id in (" +
            "   select payment_id from applied_payment where claim_id in (" +
            "       select id from claim where id not in (select claim_id from claim_submission)" +
            "   )" +
            ")", nativeQuery = true)
    BigDecimal sumAmountPaymentWithoutClaim(@Param("endDate") String endDate,
                                            @Param("dateOption") String dateOption);

    @Query(value = "SELECT :claimId AS claimId, SUM(totalCharge) AS totalCharge, SUM(n.insurancePaid) * -1 AS insurancePaid, SUM(n.patientPaid) * -1 AS patientPaid, SUM(n.insuranceAdjustments) AS insuranceAdjustments, SUM(n.patientAdjustments) AS patientAdjustments, SUM(n.uncollected) as uncollected FROM (\n" +
            "\tSELECT :claimId AS claimId, SUM(ivlc.total_charge + ivlc.sales_tax) as totalCharge, 0.00 AS insurancePaid, 0.00 AS patientPaid, 0.00 AS insuranceAdjustments, 0.00 AS patientAdjustments, 0.00 as uncollected\n" +
            "\tFROM insurance_verification iv\n" +
            "\tJOIN insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id\n" +
            "\tJOIN patient_insurance pin ON pin.id = iv.patient_insurance_id AND pin.id = (\n" +
            "\t\tSELECT iv1.patient_insurance_id \n" +
            "\t\tFROM insurance_verification iv1 \n" +
            "\t\tWHERE iv1.prescription_id = :rxId\n" +
            "\t\tORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, \n" +
            "            iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1\n" +
            ") WHERE iv.prescription_id = :rxId\n" +
            "            \n" +
            "UNION ALL\n" +
            "            \n" +
            "SELECT :claimId AS claimId, \n" +
            "0.00 as totalCharge,\n" +
            "SUM(CASE WHEN pt = 'insurance' THEN amount * -1 ELSE 0.00 END) AS insurancePaid,\n" +
            "SUM(CASE WHEN pt = 'patient' THEN amount * -1 ELSE 0.00 END) AS patientPaid,\n" +
            "SUM(insuranceAdjustment + lineAdjustments) AS insuranceAdjustments,\n" +
            "SUM(patientAdjustment) AS patientAdjustments,\n" +
            "ROUND(AVG(uncollected), 2) AS uncollected\n" +
            "FROM (\n" +
            "\n" +
            "SELECT ap.claim_id as claimId, \n" +
            "\taplc.amount * -1 amount,\n" +
            "\t(\n" +
            "\t\tCOALESCE(IF (aplc.adjustment_amount1 <> 0.00 AND aplc.adjustment_amount1 IS NOT NULL\n" +
            "\t\t\t\t\t\t\t\tAND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end > ap.applied_date)\n" +
            "                                OR (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0\n" +
            "                                AND aplc.adjustment_type1 NOT LIKE 'PR-%', adjustment_amount1,0.00) )+\n" +
            "\t\tCOALESCE(IF (aplc.adjustment_amount2 <> 0.00 AND aplc.adjustment_amount2 IS NOT NULL\n" +
            "                                AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end > ap.applied_date)\n" +
            "                                OR (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0\n" +
            "                                AND aplc.adjustment_type2 NOT LIKE 'PR-%', adjustment_amount2,0.00)) +\n" +
            "\t\tCOALESCE(IF (aplc.adjustment_amount3 <> 0.00 AND aplc.adjustment_amount3 IS NOT NULL\n" +
            "                                AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end > ap.applied_date)\n" +
            "                                OR (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0\n" +
            "                                AND aplc.adjustment_type3 NOT LIKE 'PR-%', adjustment_amount3,0.00)) +\n" +
            "\t\tCOALESCE(IF (aplc.adjustment_amount4 <> 0.00 AND aplc.adjustment_amount4 IS NOT NULL\n" +
            "                                AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end > ap.applied_date)\n" +
            "                                OR (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0\n" +
            "                                AND aplc.adjustment_type4 NOT LIKE 'PR-%', adjustment_amount4,0.00)) +\n" +
            "\t\tCOALESCE(IF (aplc.adjustment_amount5 <> 0.00 AND aplc.adjustment_amount5 IS NOT NULL\n" +
            "                                AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end > ap.applied_date)\n" +
            "                                OR (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0\n" +
            "                                AND aplc.adjustment_type5 NOT LIKE 'PR-%', adjustment_amount5,0.00) )\n" +
            "                        \n" +
            "\t) * -1 as lineAdjustments,\n" +
            "    CASE WHEN payer_type LIKE 'insurance%'  OR payer_type = 'adjustment' THEN CASE WHEN adj.operation = '+' THEN COALESCE(aplc.adjustment, 0.00)  ELSE COALESCE(aplc.adjustment, 0.00) * -1 END ELSE 0.00 END AS insuranceAdjustment,\n" +
            "\tCASE WHEN payer_type LIKE 'patient%' THEN CASE WHEN adj.operation = '+' THEN COALESCE(aplc.adjustment, 0.00)  ELSE COALESCE(aplc.adjustment, 0.00) * -1 END ELSE 0.00 END AS patientAdjustment,\n" +
            "    CASE WHEN payer_type =  'insurance_company' OR payer_type = 'adjustment' THEN 'insurance' ELSE 'patient' END AS pt,\n" +
            "            clm.uncollected AS uncollected" +
            "            FROM applied_payment_l_code aplc\n" +
            "            JOIN applied_payment ap ON ap.id = aplc.applied_payment_id\n" +
            "            JOIN claim clm ON clm.id = ap.claim_id\n" +
            "            JOIN payment p ON ap.payment_id = p.id\n" +
            "            LEFT JOIN adjustment adj ON aplc.adjustment_type = adj.id\n" +
            "            WHERE ap.claim_id IN ( \n" +
            "            SELECT id FROM (\n" +
            "            SELECT c.id, c.patient_insurance_id, iv.carrier_type, \n" +
            "\t\t\t\tCASE WHEN iv.carrier_type = 'primary' THEN 1 \n" +
            "\t\t\t\tWHEN iv.carrier_type =  'secondary' THEN 2 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'tertiary' THEN 3 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'quaternary' THEN 4 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'quinary' THEN 5 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'senary' THEN 6 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'septenary' THEN 7 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'octonary' THEN 8 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'nonary' THEN 9 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'denary' THEN 10 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'other' THEN 11 WHEN iv.carrier_type = 'inactive' THEN 12 END AS 'place',\n" +
            "            (SELECT CASE WHEN iv.carrier_type = 'primary' THEN 1 \n" +
            "\t\t\t\tWHEN iv.carrier_type =  'secondary' THEN 2 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'tertiary' THEN 3 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'quaternary' THEN 4 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'quinary' THEN 5 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'senary' THEN 6 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'septenary' THEN 7 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'octonary' THEN 8 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'nonary' THEN 9 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'denary' THEN 10 \n" +
            "\t\t\t\tWHEN iv.carrier_type = 'other' THEN 11 WHEN iv.carrier_type = 'inactive' THEN 12 END AS 'caller'\n" +
            "\t\t\t\tFROM claim c \n" +
            "\t\t\t\tJOIN insurance_verification iv ON iv.patient_insurance_id = c.patient_insurance_id AND c.prescription_id = iv.prescription_id\n" +
            "\t\t\t\tWHERE c.prescription_id = :rxId\n" +
            "\t\t\t\tAND c.id = :claimId) as caller\n" +
            "            FROM claim c \n" +
            "            JOIN insurance_verification iv ON iv.patient_insurance_id = c.patient_insurance_id AND c.prescription_id = iv.prescription_id\n" +
            "            WHERE c.prescription_id = :rxId\n" +
            "            HAVING place <= caller\n" +
            "            ORDER BY  place ASC) as z\n" +
            "\t\t)\n" +
            "\t) as x  GROUP BY x.claimId \n" +
            ") AS n GROUP BY n.claimId", nativeQuery = true)
    IClaimTotals getClaimTotalsByClaimIdAndPrescriptionId(@Param("claimId") Long claimId, @Param("rxId") Long rxId);

}
