package com.nymbl.tenant.dashboard.dto;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

public interface PrescriptionDto extends PatientInfoDto {
    Long getId();
    String getDeviceType();
    BigDecimal getTotalAllowable();
    Date getProjectedDeliveryDate();
    Date getPrescriptionDate();
    Timestamp getCreatedAt();
    String getSection();
    String getOrthoticOrProsthetic();
}
