package com.nymbl.tenant;

import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON> on 11/05/2020.
 */
@AllArgsConstructor
@Aspect
@Slf4j
@Component
public class TimedAspect {

    private final UserService userService;
    private final Environment environment;

    @Around(value = "@annotation(Timed)")
    public Object measureExecutionTime(final ProceedingJoinPoint joinPoint) throws Throwable {
        String tenant = TenantContext.getCurrentTenant();
        String username = "UNKNOWN";
        if (Arrays.asList(environment.getActiveProfiles()).contains("cron")) {
            username = "SUPERADMIN";
        } else {
            User user = userService.getCurrentUser();
            if (user != null) {
                username = user.getUsername();
            }
        }
        long start = System.currentTimeMillis();
        Object proceed = joinPoint.proceed();
        long ms = System.currentTimeMillis() - start;
        String message = String.format("%d min, %d sec",
                TimeUnit.MILLISECONDS.toMinutes(ms),
                TimeUnit.MILLISECONDS.toSeconds(ms) - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(ms))
        );
        log.info("{} executed in {} on {} by {}", joinPoint.getSignature().toShortString(), message, tenant, username);
        return proceed;
    }
}
