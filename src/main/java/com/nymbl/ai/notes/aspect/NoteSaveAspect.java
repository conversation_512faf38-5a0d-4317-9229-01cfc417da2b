package com.nymbl.ai.notes.aspect;

import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.ai.notes.model.TranscriptionAppointmentNote;
import com.nymbl.ai.notes.service.TranscriptionAppointmentNoteService;
import com.nymbl.tenant.model.Note;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;


/**
 *
 * This aspect keeps the AI Note status aligned with changes made to the Note on the Patient Chart
 *
 */
@Aspect
@Component
@Slf4j
public class NoteSaveAspect {

    private final TranscriptionAppointmentNoteService transcriptionAppointmentNoteService;

    public NoteSaveAspect(TranscriptionAppointmentNoteService transcriptionAppointmentNoteService) {
        this.transcriptionAppointmentNoteService = transcriptionAppointmentNoteService;
    }

    @AfterReturning(
            pointcut = "execution(public org.springframework.http.ResponseEntity com.nymbl.tenant.controller.NoteController.save*(..))",
            returning = "response"
    )
    public void afterControllerSave(ResponseEntity<?> response) {
        try {
            if (response == null || response.getBody() == null) return;
            Object body = response.getBody();

            if (!(body instanceof Note note)) return;

            if (note.getTranscriptionDetailId() == null) return;

            TranscriptionAppointmentNote tapNote = transcriptionAppointmentNoteService
                    .findByTranscriptionDetailId(note.getTranscriptionDetailId());

            if (tapNote == null) return;

            boolean modified = updateModifiedOutsideAiNotesUI(tapNote, note);
            if (modified) {
                transcriptionAppointmentNoteService.saveTranscriptionChanges(
                        tapNote.getTranscriptionDetail(), tapNote
                );
            }
        } catch (Exception ex) {
            // log and swallow so controller isn’t affected
            log.error("Exception while updating ai note", ex);
        }
    }


    /***
     * This method checks if any changes have been made to the notes on the v1 screen
     *
     * @param transcriptionAppointmentNote transcriptionAppointmentNote
     * @return boolean
     */
    private boolean updateModifiedOutsideAiNotesUI(TranscriptionAppointmentNote transcriptionAppointmentNote, Note note) {
        boolean isModified = false;

        AINOTE currentStatus = transcriptionAppointmentNote.getTranscriptionDetail().getStatus();
        if (AINOTE.PUBLISHED.equals(currentStatus)) {
            // No changes will be made on the AI Notes side after the note has been published
            // TODO make sure this holds true
            return false;
        } else if (note.getPublished()) {
            // if it gets here it means the AI note is not in publish status, and publish was done in patient chart view
            transcriptionAppointmentNote.getTranscriptionDetail().setStatus(AINOTE.PUBLISHED);
            transcriptionAppointmentNote.setSubject(note.getSubject());
            isModified = true;
        }
        else if (note.getUserSignedAt() != null && AINOTE.DRAFT.equals(currentStatus)) {
            // Here we track we initially saved as draft on the AI notes UI, then sign action happened on the patient chart view
            transcriptionAppointmentNote.getTranscriptionDetail().setStatus(AINOTE.SIGNED);
            transcriptionAppointmentNote.setSubject(note.getSubject());
            isModified = true;
        }
        else if (note.getUserSignedAt() == null && AINOTE.SIGNED.equals(transcriptionAppointmentNote.getTranscriptionDetail().getStatus())){
            // If we had signed it at any point on the AI notes UI and unsigned the note in the patient chart view
            transcriptionAppointmentNote.getTranscriptionDetail().setStatus(AINOTE.DRAFT);
            transcriptionAppointmentNote.setSubject(note.getSubject());
            isModified = true;
        }
        return isModified;
    }

}
