package com.nymbl.ai.notes.repository;

import com.nymbl.ai.notes.model.TranscriptionAppointmentNote;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface TranscriptionAppointmentNoteRepository extends JpaRepository<TranscriptionAppointmentNote, Long>, JpaSpecificationExecutor<TranscriptionAppointmentNote> {
    TranscriptionAppointmentNote findByTranscriptionDetailId(Long transcriptionDetailsId);
}
