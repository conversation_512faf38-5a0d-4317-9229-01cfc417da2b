package com.nymbl.ai.notes.service;


import com.amazonaws.AmazonClientException;
import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.ai.notes.dto.TranscriptionUploadRequest;
import com.nymbl.ai.notes.model.TranscriptionAudio;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.repository.TranscriptionAudioRepository;
import com.nymbl.ai.notes.repository.TranscriptionDetailRepository;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.tenant.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.transcribe.TranscribeClient;
import software.amazon.awssdk.services.transcribe.model.*;

import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Service
@Slf4j
public class HealthScribeService {

    private final TranscribeClient transcribeClient;

    private final TranscriptionDetailRepository transcriptionDetailRepository;

    private final TranscriptionAudioRepository transcriptionAudioRepository;

    private final TranscriptionUtil transcriptionUtil;

    private final AwsUtil awsUtil;

    @Value("${healthscribe.audio.bucket}")
    private String audioBucket;

    @Value("${healthscribe.audio.prefix}")
    private String filePrefix;

    @Value("${healthscribe.output.bucket}")
    private String outputBucket;

    @Value("${healthscribe.iamrole}")
    private String iamrole;

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    public HealthScribeService(TranscribeClient transcribeClient,
                               TranscriptionDetailRepository transcriptionDetailRepository,
                               TranscriptionAudioRepository transcriptionAudioRepository,
                               TranscriptionUtil transcriptionUtil,
                               AwsUtil awsUtil) {
        this.transcribeClient = transcribeClient;
        this.transcriptionDetailRepository = transcriptionDetailRepository;
        this.transcriptionAudioRepository = transcriptionAudioRepository;
        this.transcriptionUtil = transcriptionUtil;
        this.awsUtil = awsUtil;
    }

    public Long initiateRecording(TranscriptionUploadRequest request) {

        request.setTenant(TenantContext.getCurrentTenant());
        log.info("Began recording request: {}", request);
        TranscriptionDetail transcriptionDetail;
        try
        {
            transcriptionDetail = makeTranscriptionDetail(request, AINOTE.STARTED);
            transcriptionDetail = transcriptionDetailRepository.save(transcriptionDetail);
            log.info("End recording request for id: {}", transcriptionDetail.getId());
            return transcriptionDetail.getId();
        }
        catch (Exception ex) {
            log.error("AWSConfig: Exception while creating transcription details: ", ex);
            throw ex;
        }
    }

    /**
     * Audio file will be saved to an S3 bucket within the tenant
     *
     */
    public String uploadAudioFileToS3(MultipartFile file, Long detailsId) {
        String response = "Exception";
        //ClassPathResource resource = new ClassPathResource("IE_Recording.m4a");
        //CustomMultipartFile testFile = new CustomMultipartFile(resource);

        Optional<TranscriptionDetail> transcriptionDetailsOptional = transcriptionDetailRepository.findById(detailsId);
        TranscriptionDetail transcriptionDetail = null;
        boolean uploaded = false;
        log.info("AWSConfig: Begin Upload to AWS");

        if (transcriptionDetailsOptional.isPresent()) {
            transcriptionDetail = transcriptionDetailsOptional.get();
            transcriptionDetail.setJobName(transcriptionUtil.makeJobName(transcriptionDetail));


            try
            {
                AWSS3Service awss3Service = awsUtil.getUsEast1Client();
                uploaded  = awss3Service.uploadFileToS3(file, audioBucket, transcriptionDetail.getAudioFileName());
                log.info("AWSConfig: Completed Upload to AWS");
            }
            catch (IOException | AmazonClientException | InterruptedException  ex)
            {
                response = "Exception: while attempting to create notes";
                log.error("AWSConfig: Exception while uploading file to S3 while attempting to transcribe id : {}", detailsId, ex);
                try
                {
                    // Prevent loss of audio data in case of S3 failure
                    TranscriptionAudio audio = new TranscriptionAudio();
                    audio.setTranscriptionDetailId(detailsId);
                    audio.setTranscriptionAudioData(file.getBytes());
                    transcriptionAudioRepository.save(audio);
                }
                catch (IOException ex1) {
                    response = "Exception: while attempting to save notes";
                    log.error("AWSConfig: Exception while saving file to database while attempting to transcribe id : {}", detailsId, ex1);
                }
                return response;
            }

        }


        if (uploaded) {
            log.info("AWSConfig: Begin Upload to HealthScribe");
            // Trigger transcription
            try
            {
                StartMedicalScribeJobResponse medicalScribeJobResponse = transcribeAudio(transcriptionDetail, transcriptionDetail.getJobName());
                MedicalScribeJob medicalScribeJob = medicalScribeJobResponse.medicalScribeJob();

                // update status, job start time
                transcriptionDetail.setStartTime(Timestamp.from(medicalScribeJob.startTime()));
                transcriptionDetail.setStatus(AINOTE.GENERATING);
                transcriptionDetail.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

            }
            catch(Exception ex) {
                log.error("AWSConfig: Error while trying to transcribe", ex);
            }

            response = "success";
            log.info("AWSConfig: End Upload to HealthScribe");
            transcriptionDetailRepository.save(transcriptionDetail);
        }


        return response;
    }

    public String formatTime(double seconds) {
        long wholeSeconds = (long) seconds; // Convert to long for easier calculations
        return transcriptionUtil.formatTime(wholeSeconds);
    }

    public TranscriptionDetail makeTranscriptionDetail(TranscriptionUploadRequest request, AINOTE status) {

        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setAppointmentId(request.getAppointmentId());
        transcriptionDetail.setAppointmentId(request.getAppointmentId());
        transcriptionDetail.setBranchId(request.getBranchId());
        transcriptionDetail.setJobName(String.valueOf(request.getPatientId()));
        String prefix = transcriptionUtil.replacePathVariables(filePrefix, transcriptionUtil.convertToMap(request));


        transcriptionDetail.setAudioFileName(prefix + request.getAppointmentId() + "_" + getCurrentDateTime() + request.getFileExtension());
        String s3_PREFIX = "s3://";
        transcriptionDetail.setAudioLength(request.getAudioLength());
        transcriptionDetail.setAudioTime(formatTime(request.getAudioLength()));
        transcriptionDetail.setMediaFileLocation(s3_PREFIX + audioBucket + "/" + transcriptionDetail.getAudioFileName());
        transcriptionDetail.setOutputBucketName(getOutputBucketName(request.getTenant()));
        transcriptionDetail.setPatientId(request.getPatientId());
        transcriptionDetail.setPrescriptionId(request.getPrescriptionId());
        transcriptionDetail.setPractitionerId(request.getPractitionerId());
        transcriptionDetail.setStatus(status);
        transcriptionDetail.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        transcriptionDetail.setCreatedById(request.getPractitionerId());
        transcriptionDetail.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        transcriptionDetail.setUpdatedById(request.getPractitionerId());


        return transcriptionDetail;
    }

    public String getOutputBucketName(String tenant) {
        String suffix = tenant.replace('_', '.');
        if (!"prod".equalsIgnoreCase(activeProfile)) {
            return outputBucket + "-" + suffix + "-" + activeProfile;
        }
        return outputBucket + "-" + suffix;
    }

    String getCurrentDateTime() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return now.format(formatter);
    }



    StartMedicalScribeJobResponse transcribeAudio(TranscriptionDetail transcriptionDetail, String jobName) {
        Media media = Media.builder()
                .mediaFileUri(transcriptionDetail.getMediaFileLocation())
                .build();

        MedicalScribeSettings settings = MedicalScribeSettings.builder()
                .showSpeakerLabels(true)
                .maxSpeakerLabels(4)
                .build();

        StartMedicalScribeJobRequest medicalScribeJobRequest =
                StartMedicalScribeJobRequest.builder()
                        .medicalScribeJobName(jobName)
                        .media(media)
                        .outputBucketName(transcriptionDetail.getOutputBucketName())
                        .dataAccessRoleArn(iamrole)
                        .settings(settings)
                        .build();
        return transcribeClient.startMedicalScribeJob(medicalScribeJobRequest);
    }
}
