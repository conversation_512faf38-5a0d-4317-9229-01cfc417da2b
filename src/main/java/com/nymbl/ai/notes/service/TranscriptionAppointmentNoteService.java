package com.nymbl.ai.notes.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentation;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentationContainer;
import com.nymbl.ai.notes.data.clinicaldoc.Section;
import com.nymbl.ai.notes.data.conversation.Conversation;
import com.nymbl.ai.notes.data.conversation.ConversationContainer;
import com.nymbl.ai.notes.data.conversation.TranscriptSegment;
import com.nymbl.ai.notes.dto.*;
import com.nymbl.ai.notes.exception.TranscriptionAppointmentNotesException;
import com.nymbl.ai.notes.model.TranscriptionAppointmentNote;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.repository.TranscriptionAppointmentNoteRepository;
import com.nymbl.ai.notes.repository.TranscriptionDetailRepository;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Appointment;
import com.nymbl.tenant.model.Note;
import com.nymbl.tenant.model.Prescription;
import com.nymbl.tenant.service.AppointmentService;
import com.nymbl.tenant.service.NoteService;
import com.nymbl.tenant.service.NotificationService;
import com.nymbl.tenant.service.PrescriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TranscriptionAppointmentNoteService extends AbstractTableService<TranscriptionAppointmentNote, Long> {

    private final TranscriptionDetailRepository transcriptionDetailRepository;
    private final TranscriptionAppointmentNoteRepository transcriptionAppointmentNoteRepository;
    private final TranscriptionUtil transcriptionUtil;
    private final NotificationService notificationService;
    private final PrescriptionService prescriptionService;
    private final AppointmentService appointmentService;
    private final NoteService noteService;
    private final AwsUtil awsUtil;
    private final UserService userService;

    public TranscriptionAppointmentNoteService(TranscriptionDetailRepository transcriptionDetailRepository, TranscriptionAppointmentNoteRepository transcriptionAppointmentNoteRepository, TranscriptionUtil transcriptionUtil, NotificationService notificationService, PrescriptionService prescriptionService, AppointmentService appointmentService, NoteService noteService, AwsUtil awsUtil, UserService userService) {
        super(transcriptionAppointmentNoteRepository);
        this.transcriptionDetailRepository = transcriptionDetailRepository;
        this.transcriptionAppointmentNoteRepository = transcriptionAppointmentNoteRepository;
        this.transcriptionUtil = transcriptionUtil;
        this.notificationService = notificationService;
        this.prescriptionService = prescriptionService;
        this.appointmentService = appointmentService;
        this.noteService = noteService;
        this.awsUtil = awsUtil;
        this.userService = userService;
    }

    /**
     * Search ai notes for the AI notes list page
     *
     * @param practitionerId practitionerId
     * @param patientId patientId
     * @param appointmentId appointmentId
     * @param startDate startDate
     * @param endDate endDate
     * @param status status
     * @param isArchived isArchived
     * @param pageable pageable
     * @return List<TranscriptionDetailsDto>
     */
    public Optional<List<TranscriptionDetailsDto>> search(List<Long> practitionerId, Long patientId, Long appointmentId, Date startDate, Date endDate, String status, boolean isArchived, Pageable pageable) {
        try
        {
            Specification<TranscriptionDetail> transcriptionDetailsSpecification = TranscriptionDetailSpecs.search(practitionerId, patientId, appointmentId, startDate, endDate, status, isArchived);
            List<TranscriptionDetail> results = transcriptionDetailRepository.findAll(transcriptionDetailsSpecification, pageable).getContent();
            return Optional.of(results.stream()
                    .map(transcriptionUtil::convertToDto)
                    .sorted(new TranscriptionStatusComparator())
                    .collect(Collectors.toList()));
        }
        catch(Exception ex) {
            log.error("Transcription: Exception searching for transcription list ", ex);
        }
        return Optional.empty();
    }

    /**
     * Finds Transcription details by Id
     *
     * @param detailsId detailsId
     * @return Optional<TranscriptionDetailsDto>
     */
    public Optional<TranscriptionDetailsDto> getDetailsById(Long detailsId) throws TranscriptionAppointmentNotesException {
        try
        {
            Optional<TranscriptionDetail> details = transcriptionDetailRepository.findById(detailsId);
            if(details.isPresent()) {
                return Optional.of(transcriptionUtil.convertToDto(details.get()));
            }
        }
        catch(Exception ex) {
            log.error("Transcription: Exception finding transcription by id {}", detailsId, ex);
            throw new TranscriptionAppointmentNotesException("Transcription: Exception finding transcription");
        }
        return Optional.empty();
    }

    /**
     * This method pulls in AI transcription results from S3
     *
     * @param aiNoteWebhookDto AiNoteWebhookDto
     * @return boolean
     * @throws TranscriptionAppointmentNotesException on error
     */
    public boolean writeCompletedNotesFromBucket(AiNoteWebhookDto aiNoteWebhookDto) throws TranscriptionAppointmentNotesException {

        AWSS3Service awss3Service = awsUtil.getUsEast1Client();
        Optional<byte[]> summaryBytes = awss3Service.getFile(aiNoteWebhookDto.getBucketName(), aiNoteWebhookDto.getJobName() + "/summary.json");
        Optional<byte[]> transcriptBytes = awss3Service.getFile(aiNoteWebhookDto.getBucketName(), aiNoteWebhookDto.getJobName() + "/transcript.json");

        Long detailId = transcriptionUtil.getTranscriptionDetailsId(aiNoteWebhookDto.getJobName());

        if (null != transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(detailId)) {
            log.info("Transcription: Transcription detail id {} already processed", detailId);
            return false;
        }

        Optional<TranscriptionDetail> transcriptionDetailsOptional = transcriptionDetailRepository.findById(detailId);

        TranscriptionAppointmentNote transcriptionAppointmentNote = new TranscriptionAppointmentNote();
        String summaryJson;
        String transcriptJson;

        if (summaryBytes.isPresent() && transcriptBytes.isPresent()) {
            summaryJson = new String(summaryBytes.get(), StandardCharsets.UTF_8);
            transcriptJson = new String(transcriptBytes.get(), StandardCharsets.UTF_8);
        }
        else
        {
            log.error("Transcription: Exception while pulling AI notes from S3 for job name{}", aiNoteWebhookDto.getJobName());
            throw new TranscriptionAppointmentNotesException("Transcription Detail not found!!!");
        }

        TranscriptionDetail transcriptionDetail = transcriptionDetailsOptional.get();
        transcriptionDetail.setStatus(AINOTE.READY);

        Instant instant = Instant.parse(aiNoteWebhookDto.getEventTime());
        Timestamp endTime = Timestamp.from(instant);
        transcriptionDetail.setEndTime(endTime);
        transcriptionDetail.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        transcriptionDetail.setUpdatedById(transcriptionDetail.getCreatedById());

        transcriptionAppointmentNote.setTranscriptionDetailId(transcriptionDetail.getId());
        transcriptionAppointmentNote.setGeneratedClinicalNotes(summaryJson);
        transcriptionAppointmentNote.setGeneratedConversation(transcriptJson);
        transcriptionAppointmentNote.setReviewed(false);
        transcriptionAppointmentNote.setCreatedAt(endTime);
        transcriptionAppointmentNote.setCreatedById(transcriptionDetail.getCreatedById());
        transcriptionAppointmentNote.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        transcriptionAppointmentNote.setUpdatedById(transcriptionDetail.getCreatedById());

        saveTranscriptionChanges(transcriptionDetail, transcriptionAppointmentNote);
        notificationService.createAINoteNotification(transcriptionDetail);

        return true;
    }

    public void saveTranscriptionChanges(TranscriptionDetail transcriptionDetail, TranscriptionAppointmentNote transcriptionAppointmentNote) {
        transcriptionAppointmentNoteRepository.save(transcriptionAppointmentNote);
        transcriptionDetailRepository.save(transcriptionDetail);
    }

    public TranscriptionAppointmentNote findByTranscriptionDetailId(Long transcriptionDetailsId) {
        TranscriptionAppointmentNote transcriptionAppointmentNote = transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(transcriptionDetailsId);
        loadForeignKeys(transcriptionAppointmentNote);
        return transcriptionAppointmentNote;
    }

    public TranscriptionAppointmentNote findByTranscriptionAppointmentNoteId(Long transcriptionAppointmentNoteId) {
        Optional<TranscriptionAppointmentNote> transcriptionAppointmentNote = transcriptionAppointmentNoteRepository.findById(transcriptionAppointmentNoteId);

        transcriptionAppointmentNote.ifPresent(this::loadForeignKeys);
        return transcriptionAppointmentNote.orElse(null);
    }

    /**
     * This is called to view the notes on the AI Notes edit screen
     * If called on initial view (isReviewed false) pull from AI notes table
     * When status is draft/signed/published pull from Notes table
     *
     *
     * @param transcriptionDetailsId identifier
     * @return TranscriptionAppointmentNotesDto
     */
    public Optional<TranscriptionAppointmentNotesDto> viewGeneratedNotes(Long transcriptionDetailsId) throws TranscriptionAppointmentNotesException {

        TranscriptionAppointmentNote transcriptionAppointmentNote = findByTranscriptionDetailId(transcriptionDetailsId);

        if (null == transcriptionAppointmentNote) {
            throw new TranscriptionAppointmentNotesException("Transcription Notes not found!!!");
        }

        ConversationContainer conversation;
        ClinicalDocumentationContainer clinicalDocumentation;
        TranscriptionAppointmentNotesDto transcriptionAppointmentNotesDto;
        try
        {
            conversation = transcriptionUtil.fromJson(transcriptionAppointmentNote.getGeneratedConversation(), ConversationContainer.class);
            clinicalDocumentation = transcriptionUtil.fromJson(transcriptionAppointmentNote.getGeneratedClinicalNotes(), ClinicalDocumentationContainer.class);

            transcriptionAppointmentNotesDto = decideNotesToDisplay(clinicalDocumentation.getClinicalDocumentation(), transcriptionAppointmentNote);

            transcriptionAppointmentNotesDto.setReviewed(transcriptionAppointmentNote.isReviewed());
            transcriptionAppointmentNotesDto.setTranscriptionDetailsId(transcriptionDetailsId);
            transcriptionAppointmentNotesDto.setTranscriptionAppointmentNotesId(transcriptionAppointmentNote.getId());
            transcriptionAppointmentNotesDto.setConversation(conversation.getConversation());
            // Turning this off until needed
            //transcriptionAppointmentNotesDto.setClinicalDocumentation(clinicalDocumentation.getClinicalDocumentation());
            transcriptionAppointmentNotesDto.setSubject(transcriptionAppointmentNote.getSubject());
            // The UI does not need this its uses the conversation segments
            //transcriptionAppointmentNotesDto.setTranscriptString(getTranscriptsString(conversation.getConversation()));

        } catch (JsonProcessingException e) {
            log.error("Transcription: Exception while retrieving notes for review {}", transcriptionDetailsId, e);
            throw new TranscriptionAppointmentNotesException("Error processing transcription notes: " + e.getMessage());
        }
        return Optional.of(transcriptionAppointmentNotesDto);
    }

    /***
     * Displays notes from the AI results for status ready, and publish
     * Displays from notes table for status draft and sign
     *
     * @param clinicalDocumentation clinicalDocumentation
     */
    private TranscriptionAppointmentNotesDto decideNotesToDisplay(ClinicalDocumentation clinicalDocumentation, TranscriptionAppointmentNote transcriptionAppointmentNote) {
        TranscriptionAppointmentNotesDto transcriptionAppointmentNotesDto = new TranscriptionAppointmentNotesDto();
        TranscriptionDetail transcriptionDetail = transcriptionAppointmentNote.getTranscriptionDetail();
        AINOTE status = transcriptionDetail.getStatus();
        if (transcriptionAppointmentNote.isReviewed()) {
            Note note = transcriptionAppointmentNote.getNote();
            transcriptionAppointmentNotesDto.setNoteId(transcriptionAppointmentNote.getNoteId());
            assert note != null;
            transcriptionAppointmentNotesDto.setReviewNoteString(note.getNote());
            transcriptionAppointmentNotesDto.setSubject(note.getSubject());
        }
        else
        {
            // Condition in which AI note has never been changed, READY Status
            transcriptionAppointmentNotesDto.setSubject(transcriptionAppointmentNote.getSubject());
            transcriptionAppointmentNotesDto.setReviewNoteString(getClinicalNotesString(clinicalDocumentation));
        }
        transcriptionAppointmentNotesDto.setStatus(status);
        transcriptionAppointmentNotesDto.setArchived(transcriptionDetail.isArchived());

        return transcriptionAppointmentNotesDto;
    }




    /**
     *
     * @param request Archive true or false
     * @return success
     * @throws TranscriptionAppointmentNotesException on error
     */
    public String archiveTranscription(ArchiveRequest request) throws TranscriptionAppointmentNotesException {
        Optional<TranscriptionDetail> transcriptionDetailsOptional = transcriptionDetailRepository.findById(request.getTranscriptionDetailId());

        if (transcriptionDetailsOptional.isEmpty()) {
            log.error("Transcription: Exception while archiving transcription note {}", request.getTranscriptionDetailId());
            throw new TranscriptionAppointmentNotesException("Transcription Detail not found!!!");
        }

        TranscriptionDetail transcriptionDetail = transcriptionDetailsOptional.get();
        transcriptionDetail.setArchived(request.archiveNote);
        transcriptionDetailRepository.save(transcriptionDetail);

        return "success";
    }

    /**

     * if save as draft,
     *  -> create initial note with draft action, update ai note status to draft, set is reviewed true
     *  -> if save as draft subsequently, update existing note status and action
     * if save and sign
     *  -> Never been drafted so no notes exist create initial note with sign action, update ai note status to sign, set isReviewed true
     *  -> if previously drafted, update existing note status and action, update ai note status to sign
     *  if save and publish
     *  -> Never been drafted or signed so no notes exist create initial note with sign action, update ai note status to publish, set isReviewed true
     *  -> if previously drafted or signed, update existing note status and action, update ai note status to publish
     *
     * @param request ReviewedNoteRequest
     * @return String response success or failure
     */
    public String reviewNotes(ReviewedNoteRequest request) throws TranscriptionAppointmentNotesException {
        User user = userService.getCurrentUser();

        if (!user.getIsSuperAdmin()) {
            if (request.getAction().equals(AINOTE.SIGNED) && isInValidCareExtenderOrResident(request, user.getId())) {
                throw new TranscriptionAppointmentNotesException("Sign action not allowed for user!!!");
            }
        }

        if (!user.getIsSuperAdmin()){
            if (request.getAction().equals(AINOTE.PUBLISHED) && isNotValidTreatingPractitioner(request, user.getId())) {
                throw new TranscriptionAppointmentNotesException("Publish action not allowed for user!!!");
            }
        }

        TranscriptionAppointmentNote transcriptionAppointmentNote = findByTranscriptionAppointmentNoteId(request.getTranscriptionAppointmentNoteId());

        if (null == transcriptionAppointmentNote) {
            log.error("Transcription: Exception while saving notes transcription note search {}", request.getTranscriptionAppointmentNoteId());
            throw new TranscriptionAppointmentNotesException("Transcription Note not found!!!");
        }

        TranscriptionDetail transcriptionDetail = transcriptionAppointmentNote.getTranscriptionDetail();

        if (transcriptionDetail.isArchived())
        {
            log.error("Transcription: Exception while saving notes transcription cannot edit archived notes {}", request.getTranscriptionAppointmentNoteId());
            throw new TranscriptionAppointmentNotesException("Cannot edit an archived note!!!");
        }

        if (AINOTE.PUBLISHED.equals(transcriptionDetail.getStatus()))
        {
            log.error("Transcription: Exception while saving notes transcription published notes cannot be edited {}", request.getTranscriptionAppointmentNoteId());
            throw new TranscriptionAppointmentNotesException("Cannot edit a published note!!!");
        }

        try
        {
            // This is the first time they modify the AI Note, can be saved as draft, signed, or published
            if (transcriptionAppointmentNote.getNoteId() == null) {
                Note savedNote = saveNote(request, transcriptionDetail.getId(), null, user.getId());
                transcriptionAppointmentNote.setNoteId(savedNote.getId());
                transcriptionAppointmentNote.setReviewed(true);
            }
            else
            {
                Note currentNote = transcriptionAppointmentNote.getNote();
                saveNote(request, transcriptionDetail.getId(), currentNote, user.getId());
            }

            transcriptionAppointmentNote.setSubject(request.getSubject());
            transcriptionAppointmentNoteRepository.save(transcriptionAppointmentNote);

            transcriptionDetail.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
            transcriptionDetail.setUpdatedById(user.getId());
            transcriptionDetail.setStatus(request.getAction());
            transcriptionDetailRepository.save(transcriptionDetail);
        }
        catch(Exception ex) {
            log.error("Transcription: Exception while saving notes {}", request.getTranscriptionAppointmentNoteId());
            throw new TranscriptionAppointmentNotesException("Error saving note!!!");
        }

        return "success";
    }

    /**
     * Check if user can perform publish action on the note
     *
     * @param request ReviewedNoteRequest
     * @param userId current user
     * @return boolean
     */
     boolean isNotValidTreatingPractitioner(ReviewedNoteRequest request, Long userId) throws TranscriptionAppointmentNotesException {
        Long defaultTreatingPractitionerId = null;

        Prescription prescription = prescriptionService.findOne(request.getPrescriptionId());
        Appointment appointment = appointmentService.findOne(request.getAppointmentId());

        if (appointment != null && appointment.getUserFourId() != null) {
            defaultTreatingPractitionerId = appointment.getUserFourId();
        } else if (appointment != null && appointment.getUserId() != null) {
            defaultTreatingPractitionerId = appointment.getUserId();
        } else if (prescription.getTreatingPractitionerId() != null) {
            defaultTreatingPractitionerId = prescription.getTreatingPractitionerId();
        }

        if (null == defaultTreatingPractitionerId)
        {
            throw new TranscriptionAppointmentNotesException("Error saving note, cannot validate treating practitioner!!!");
        }
        return !defaultTreatingPractitionerId.equals(userId);
    }

    /**
     * Check if user can perform sign action on the note
     * TODO revisit and clean up
     *
     * @param request ReviewedNoteRequest
     * @return boolean
     * @throws TranscriptionAppointmentNotesException on error
     */
    boolean isInValidCareExtenderOrResident(ReviewedNoteRequest request, Long userId) throws TranscriptionAppointmentNotesException {
        Note note = null;
        Prescription prescription = null;
        Appointment appointment = null;

        if (request.getNoteId() != null) {
            note = noteService.findOne(request.getNoteId());
        }
        else
        {
            prescription = prescriptionService.findOne(request.getPrescriptionId());
            appointment = appointmentService.findOne(request.getAppointmentId());
        }

        Long appointmentSupervisingPractitioner = null;
        Long appointmentAttendingId = null;
        Long prescriptionTreatingPractitioner = null;
        Long prescriptionResidentId = null;

        if (note != null) {
            if (note.getAppointment() != null) {
                appointmentSupervisingPractitioner = note.getAppointment().getUserFourId();
                appointmentAttendingId = note.getAppointment().getUserId();
            }
            if (note.getPrescription() != null) {
                prescriptionTreatingPractitioner = note.getPrescription().getTreatingPractitionerId();
                prescriptionResidentId = note.getPrescription().getResidentId();
            }
        } else if (prescription != null) {
            if (appointment != null) {
                appointmentSupervisingPractitioner = appointment.getUserFourId();
                appointmentAttendingId = appointment.getUserId();
            }
            prescriptionTreatingPractitioner = prescription.getTreatingPractitionerId();
            prescriptionResidentId = prescription.getResidentId();
        } else {
            throw new TranscriptionAppointmentNotesException("Error saving note - error determining Treating/Supervising Practitioner-");
        }

        if (appointmentSupervisingPractitioner != null) {
            return appointmentSupervisingPractitioner.equals(userId) || appointmentAttendingId == null || !appointmentAttendingId.equals(userId);
        } else if (appointmentAttendingId != null && appointmentAttendingId.equals(userId)) {
            return true; // can save and publish (old appointment or an actual practitioner)
        } else return prescriptionTreatingPractitioner == null || prescriptionTreatingPractitioner.equals(userId)
                || prescriptionResidentId == null || !prescriptionResidentId.equals(userId);
    }


    /**
     * Save AI Note to notes table
     *
     * @param request ReviewedNoteRequest
     * @param transcriptionDetailId transcriptionDetailId
     * @return Note
     */
    Note saveNote(ReviewedNoteRequest request, Long transcriptionDetailId, Note savedNote, Long currentUserId) {
        Note note = savedNote;
        if (null == savedNote) {
            note = new Note();
            note.setNoteType("clinical");
            note.setCreatedAt(Timestamp.from(Instant.now()));
            note.setCreatedById(currentUserId);
            note.setPrescriptionId(request.getPrescriptionId());
            note.setAppointmentId(request.getAppointmentId());
            note.setPatientId(request.getPatientId());
            note.setTreatingPractitionerId(request.getTreatingPractitionerId());
            note.setTranscriptionDetailId(transcriptionDetailId);
        }

         // When modifications come from the AI Notes UI we do not need to reconcile changes to the notes
        note.setNote(request.getReviewedNoteString());
        note.setUpdatedAt(Timestamp.from(Instant.now()));
        note.setSubject(request.getSubject());
        note.setUserId(currentUserId);

        String action = request.getAction().name().toLowerCase();

        if (AINOTE.PUBLISHED.equals(request.getAction())) {
            note.setPublished(true);
            action = "publish";
        }
        else if(AINOTE.SIGNED.equals(request.getAction())) {
            action = "sign";
        }

        Map<String, Object> noteMap = noteService.saveNote(note, action);
        return  (Note) noteMap.get(OptimisticLockingUtil.SAVED);
    }



    /**
     *
     *
     * @param clinicalDocumentation clinical documents
     * @return String of output document
     */
    public String getClinicalNotesString(ClinicalDocumentation clinicalDocumentation) {
        StringBuilder stringBuilder = new StringBuilder();
        List<Section> sections = clinicalDocumentation.getSections();

        // Define the desired order of sections
        List<String> orderedSectionNames = List.of(
                "CHIEF_COMPLAINT",
                "HISTORY_OF_PRESENT_ILLNESS",
                "PAST_MEDICAL_HISTORY",
                "REVIEW_OF_SYSTEMS",
                "PHYSICAL_EXAMINATION",
                "PLAN"
        );

        // Build a map for fast lookup
        Map<String, Section> sectionMap = sections.stream()
                .collect(Collectors.toMap(Section::getSectionName, s -> s, (a, b) -> a));

        // Append in the required order
        for (String sectionName : orderedSectionNames) {
            Section section = sectionMap.get(sectionName);
            if (section != null) {
                stringBuilder.append(section.printSectionDetails());
                stringBuilder.append("<br />");
            }
        }

        return stringBuilder.toString();
    }

    /**
     *
     * @param conversation conversation
     * @return Transcript string
     */
    public String getTranscriptsString(Conversation conversation) {
        StringBuilder stringBuilder = new StringBuilder();
        List<TranscriptSegment> segments = conversation.getTranscriptSegments();

        for (TranscriptSegment segment : segments) {
            stringBuilder.append(segment.printTranscriptSegment());
        }
        return stringBuilder.toString();
    }

    @Override
    public void loadForeignKeys(TranscriptionAppointmentNote transcriptionAppointmentNote) {

        if (null != transcriptionAppointmentNote) {
            Long noteId = transcriptionAppointmentNote.getNoteId();
            if (null != noteId) {
                Note note = noteService.findOne(noteId);
                transcriptionAppointmentNote.setNote(note);
            }

            Optional<TranscriptionDetail> transcriptionDetailOptional = transcriptionDetailRepository.findById(transcriptionAppointmentNote.getTranscriptionDetailId());
            transcriptionDetailOptional.ifPresent(transcriptionAppointmentNote::setTranscriptionDetail);
        }
    }
}
