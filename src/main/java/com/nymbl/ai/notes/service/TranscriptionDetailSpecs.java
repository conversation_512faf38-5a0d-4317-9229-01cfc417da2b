package com.nymbl.ai.notes.service;

import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.tenant.model.Appointment;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TranscriptionDetailSpecs {

    public static Specification<TranscriptionDetail> search(List<Long> practitionerId, Long patientId, Long appointmentId, Date startDate, Date endDate, String status, boolean isArchived) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (patientId != null) {
                predicates.add(criteriaBuilder.equal(root.get("patientId"), patientId));
            }

            if (practitionerId != null && !practitionerId.isEmpty()) {
                CriteriaBuilder.In<Long> inClause = criteriaBuilder.in(root.get("practitionerId"));
                for (Long id : practitionerId) {
                    inClause.value(id);
                }
                predicates.add(inClause);
            }

            if (appointmentId != null) {
                predicates.add(criteriaBuilder.equal(root.get("appointmentId"), appointmentId));
            }

            if (startDate != null || endDate != null) {
                Join<TranscriptionDetail, Appointment> appointmentJoin = root.join("appointment", JoinType.INNER);

                if (startDate != null) {
                    Timestamp startDateTimestamp = new Timestamp(startDate.getTime());
                    predicates.add(criteriaBuilder.greaterThanOrEqualTo(appointmentJoin.get("startDateTime"), startDateTimestamp));
                }

                if (endDate != null) {
                    Timestamp endDateTimestamp = new Timestamp(endDate.getTime());
                    predicates.add(criteriaBuilder.lessThanOrEqualTo(appointmentJoin.get("endDateTime"), endDateTimestamp));
                }
            }

            if (StringUtils.isNotBlank(status)) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            }

            predicates.add(criteriaBuilder.equal(root.get("isArchived"), isArchived));

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
