package com.nymbl.ai.notes.model;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.config.utils.JsonDateTimeDeserializer;
import com.nymbl.config.utils.JsonDateTimeSerializer;
import com.nymbl.master.model.User;
import com.nymbl.tenant.model.Appointment;
import com.nymbl.tenant.model.Branch;
import com.nymbl.tenant.model.Patient;
import com.nymbl.tenant.model.Prescription;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
@Table(name = "transcription_detail")
@Audited
public class TranscriptionDetail {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "job_name")
    private String jobName;

    @Column(name = "patient_id")
    private Long patientId;

    @Column(name = "practitioner_id")
    private Long practitionerId;

    @Column(name = "branch_id")
    private Long branchId;

    @Column(name = "appointment_id")
    private Long appointmentId;

    @Column(name = "prescription_id")
    private Long prescriptionId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private AINOTE status;

    @Column(name = "created_by_id", nullable = false)
    private Long createdById;

    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    @Column(name = "created_at", columnDefinition = "TIMESTAMP")
    private Timestamp createdAt;

    @Column(name = "updated_by_id", nullable = false)
    private Long updatedById;

    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    @Column(name = "updated_at", columnDefinition = "TIMESTAMP")
    private Timestamp updatedAt;

    @Column(name = "media_file_location")
    private String mediaFileLocation;

    @Column(name = "output_bucket_name")
    private String outputBucketName;

    @Column(name = "audio_file_name")
    private String audioFileName;

    @Column(name = "conversation_id")
    private String conversationId;

    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    @Column(name = "start_time", columnDefinition = "TIMESTAMP")
    private Timestamp startTime;

    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    @Column(name = "end_time", columnDefinition = "TIMESTAMP")
    private Timestamp endTime;

    @Column(name = "audio_length")
    private Double audioLength;

    @Column(name = "audio_time")
    private String audioTime;

    @Column(name = "is_archived")
    private boolean isArchived;

    @ManyToOne
    @JoinColumn(name = "patient_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptiondetailpatientid"))
    private Patient patient;

    @NotAudited
    @Transient
    @ManyToOne
//    @JoinColumn(name = "practitioner_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptiondetailpractitionerid"))
    private User practitioner;


    @ManyToOne
    @JoinColumn(name = "branch_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptiondetailbranchid"))
    private Branch branch;

    @ManyToOne
    @JoinColumn(name = "appointment_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptiondetailappointmentid"))
    private Appointment appointment;

    @ManyToOne
    @JoinColumn(name = "prescription_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptiondetailprescriptionid"))
    private Prescription prescription;

    @NotAudited
    @Transient
    @ManyToOne
//    @JoinColumn(name = "created_by_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptiondetailcreatedbyid"))
    private User createdBy;

    @NotAudited
    @Transient
    @ManyToOne
//    @JoinColumn(name = "updated_by_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptiondetailupdatedbyid"))
    private User updatedBy;

}
