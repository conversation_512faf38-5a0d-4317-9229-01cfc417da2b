package com.nymbl.ai.notes.controller;

import com.nymbl.ai.notes.dto.AiNoteWebhookDto;
import com.nymbl.ai.notes.service.TranscriptionAppointmentNoteService;
import com.nymbl.tenant.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/****
 * This method pulls in the transcription results from s3.
 *
 */
@RestController
@RequestMapping("v1/transcribe/webhook")
@Slf4j
public class TranscriptionWebHookController
{
    private final TranscriptionAppointmentNoteService transcriptionAppointmentNoteService;

    @Value("${healthscribe.authToken}")
    String authToken;

    public TranscriptionWebHookController(TranscriptionAppointmentNoteService transcriptionAppointmentNoteService) {
        this.transcriptionAppointmentNoteService = transcriptionAppointmentNoteService;
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> generatedNotes(@RequestBody AiNoteWebhookDto aiNoteWebhookDto) {
        String AWS_EVENT_NAME = "ObjectCreated:Put";
        log.info("Transcription: Beginning webhook call");
        if (isValidRequest(aiNoteWebhookDto) && AWS_EVENT_NAME.equals(aiNoteWebhookDto.getAwsEventName()))
        {
            String tenant = getBucketName(aiNoteWebhookDto.getTenant());
            log.info("Transcription: tenant {}", tenant);
            TenantContext.setCurrentTenant(tenant);
            try
            {
                transcriptionAppointmentNoteService.writeCompletedNotesFromBucket(aiNoteWebhookDto);
            }
            catch(Exception ex) {
                log.error("Transcription: Exception while copying notes over ", ex);
            }
            TenantContext.clear();
            log.info("Transcription: Ending webhook call");
            return ResponseEntity.ok("success");
        }
        else
        {
            return ResponseEntity.badRequest().body("Invalid Signature!!");
        }

    }

    private String getBucketName(String tenant) {
        return tenant.replace('.', '_');
    }

    private boolean isValidRequest(AiNoteWebhookDto aiNoteWebhookDto) {
        return aiNoteWebhookDto.getAuthToken().equals(authToken);
    }
}
