package com.nymbl.ai.notes.controller;

import com.nymbl.ai.lmn.controller.request.EvaluationRequest;
import com.nymbl.ai.notes.dto.*;
import com.nymbl.ai.notes.exception.TranscriptionAppointmentNotesException;
import com.nymbl.ai.notes.service.TranscriptionAppointmentNoteService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("api/transcribe/")
public class TranscriptionAppointmentNoteController {

    private final TranscriptionAppointmentNoteService transcriptionAppointmentNoteService;

    public TranscriptionAppointmentNoteController(TranscriptionAppointmentNoteService transcriptionAppointmentNoteService) {
        this.transcriptionAppointmentNoteService = transcriptionAppointmentNoteService;
    }

    /**
     * Search for ai note records
     *
     * @param practitionerIds practitioner id
     * @param patientId       patient id
     * @param startDate       evaluation created at search start
     * @param endDate         evaluation created at search end range
     * @param appointmentId   appointmentId id
     * @param status          Note Status
     * @param pageable        pageable
     * @return TranscriptionDetailsDto Object
     */
    @Operation(
            summary = "Search for AI Notes list",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "List of AI Notes",
                            content = @Content(
                                    mediaType = "application/json",
                                    array = @ArraySchema(schema = @Schema(implementation = TranscriptionDetailsDto.class))
                            )
                    ),
                    @ApiResponse(responseCode = "400", description = "No Content")
            }
    )
    @GetMapping(value = "search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> search(@RequestParam(name = "practitionerId", required = false) List<Long> practitionerIds,
                                    @RequestParam(name = "patientId", required = false) Long patientId,
                                    @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date startDate,
                                    @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date endDate,
                                    @RequestParam(name = "appointmentId", required = false) Long appointmentId,
                                    @RequestParam(name = "status", required = false) String status,
                                    @PageableDefault(page = 0, value = 1000) Pageable pageable,
                                    HttpServletRequest request) {

        boolean isArchived = AINOTE.ARCHIVED.name().equalsIgnoreCase(status);
        if(isArchived) status = null;
        Optional<List<TranscriptionDetailsDto>> results = transcriptionAppointmentNoteService.search(practitionerIds, patientId, appointmentId, startDate, endDate, status, isArchived, pageable);
        if (results.isPresent())
            return ResponseEntity.ok(results.get());

        return ResponseEntity.noContent().build();
    }

    /**
     * view generated ai note
     *
     * @param transcriptionDetailsId - transcriptionDetailsId
     * @return Response TranscriptionAppointmentNotesDto
     */
    @Operation(
            summary = "Get generated ai note id",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Successfully retrieved the ai note",
                            content = @Content(
                                    schema = @Schema(implementation = TranscriptionAppointmentNotesDto.class)
                            )
                    ),
                    @ApiResponse(responseCode = "404", description = "Note not found"),
                    @ApiResponse(responseCode = "500", description = "Server error", content = @Content)
            }
    )
    @GetMapping(value = "/{transcriptionDetailsId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> view(@PathVariable Long transcriptionDetailsId) {
        try {
            Optional<TranscriptionAppointmentNotesDto> results = transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);
            if (results.isPresent())
                return ResponseEntity.ok(results.get());

        } catch (TranscriptionAppointmentNotesException e) {
            if (e.getMessage().contains("not found"))
                return ResponseEntity.notFound().build();

            return ResponseEntity.internalServerError().body(e.getMessage());
        }
        return ResponseEntity.internalServerError().build();
    }

    /**
     * view transcription detail
     *
     * @param transcriptionDetailId - transcriptionDetailId
     * @return Response TranscriptionAppointmentNotesDto
     */
    @Operation(
            summary = "Get transcription detail",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Successfully retrieved the transcription detail",
                            content = @Content(
                                    schema = @Schema(implementation = TranscriptionDetailsDto.class)
                            )
                    ),
                    @ApiResponse(responseCode = "404", description = "Note not found"),
                    @ApiResponse(responseCode = "500", description = "Server error", content = @Content)
            }
    )
    @GetMapping(value = "/details/{transcriptionDetailId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> details(@PathVariable Long transcriptionDetailId) {
        try
        {
            Optional<TranscriptionDetailsDto> results = transcriptionAppointmentNoteService.getDetailsById(transcriptionDetailId);
            if (results.isPresent())
                return ResponseEntity.ok(results.get());
            else
                return ResponseEntity.notFound().build();
        }
        catch (TranscriptionAppointmentNotesException e)
        {
            return ResponseEntity.internalServerError().body(e.getMessage());
        }
    }

    @GetMapping(value = "/note/{transcriptionDetailsId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Hidden
    public ResponseEntity<?> notes(@PathVariable Long transcriptionDetailsId) {
        try
        {
            Optional<TranscriptionAppointmentNotesDto> results = transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);
            if (results.isPresent()) {
                TranscriptionAppointmentNotesDto notesDto = results.get();
                return ResponseEntity.ok(transcriptionAppointmentNoteService.getClinicalNotesString(notesDto.getClinicalDocumentation()));
            }
        } catch (TranscriptionAppointmentNotesException e) {
            return ResponseEntity.internalServerError().body(e.getMessage());
        }
        return ResponseEntity.internalServerError().build();
    }

    @GetMapping(value = "/transcript/{transcriptionDetailsId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Hidden
    public ResponseEntity<?> transcript(@PathVariable Long transcriptionDetailsId) {

        try
        {
            Optional<TranscriptionAppointmentNotesDto> results = transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);
            if (results.isPresent()) {
                TranscriptionAppointmentNotesDto notesDto = results.get();
                return ResponseEntity.ok(transcriptionAppointmentNoteService.getTranscriptsString(notesDto.getConversation()));
            }

        } catch (TranscriptionAppointmentNotesException e) {
            return ResponseEntity.internalServerError().body(e.getMessage());
        }
        return ResponseEntity.internalServerError().build();
    }

    /**
     * Archives an AI Note
     *
     * @param request - Object containing note changes
     * @return Response response message
     */
    @Operation(
            summary = "Archive AI Notes",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ArchiveRequest.class),
                            examples = {
                                    @ExampleObject(
                                            name = "ArchiveRequest Example",
                                            summary = "Example ArchiveRequest",
                                            value = "{\"transcriptionDetailId\": 2, \"archiveNote\": true }"
                                    )
                            }
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Successfully sent invite",
                            content = @Content(
                                    schema = @Schema(implementation = String.class)
                            )
                    ),
                    @ApiResponse(responseCode = "400", description = "Invalid request", content = @Content),
                    @ApiResponse(responseCode = "500", description = "Server error", content = @Content)
            }
    )
    @PutMapping(value = "archive", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> archive(@RequestBody ArchiveRequest request) {

        try
        {
            String response = transcriptionAppointmentNoteService.archiveTranscription(request);
            if ("success".equals(response))
                return ResponseEntity.ok(response);
        }
        catch (TranscriptionAppointmentNotesException e)
        {
                return ResponseEntity.notFound().build();
        }
        return ResponseEntity.internalServerError().body("Unknown Error");
    }

    /**
     * Saves user changes to the patient chart note table
     *
     * @param request - Object containing note changes
     * @return Response response message
     */
    @Operation(
            summary = "Save/Update AI Note to Patient Chart",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = EvaluationRequest.class),
                            examples = {
                                    @ExampleObject(
                                            name = "ReviewedNoteRequest Example",
                                            summary = "Example ReviewedNoteRequest",
                                            value = "{\"reviewedNoteString\": \"This is a patient note\", \"transcriptionAppointmentNoteId\": 2, \"treatingPractitionerId\": 2, \"patientId\": 2, \"appointmentId\": 2, \"prescriptionId\": 2, \"noteId\": 2, \"action\": \"AINOTE.Ready\", \"subject\": \"Clinical Subject\"}"
                                    )
                            }
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Successfully sent invite",
                            content = @Content(
                                    schema = @Schema(implementation = String.class)
                            )
                    ),
                    @ApiResponse(responseCode = "400", description = "Invalid request", content = @Content),
                    @ApiResponse(responseCode = "500", description = "Server error", content = @Content)
            }
    )
    @PostMapping(value = "save-review", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> saveReviewedNotes(@RequestBody ReviewedNoteRequest request) {
        try {
            String response = transcriptionAppointmentNoteService.reviewNotes(request);
            if ("success".equals(response))
                return ResponseEntity.ok(response);
            return ResponseEntity.internalServerError().body(response);
        } catch (TranscriptionAppointmentNotesException e) {
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("not found"))
                return ResponseEntity.notFound().build();

            return ResponseEntity.internalServerError().body(errorMessage != null ? errorMessage : "Unknown error occurred");
        }
    }
}
