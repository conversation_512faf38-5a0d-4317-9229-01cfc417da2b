package com.nymbl.ai.notes.dto;


import com.nymbl.master.model.UserDto;
import com.nymbl.tenant.dashboard.dto.AppointmentDto;
import com.nymbl.tenant.dashboard.dto.PatientDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@Schema(description = "Transcription Details DTO")
public class TranscriptionDetailsDto {
    @Schema(description = "Transcription Detail Id", implementation = Long.class)
    private Long id;
    @Schema(description = "Transcription job name")
    private String jobName;
    @Schema(description = "Branch Id", implementation = Long.class)
    private Long branchId;
    @Schema(description = "AI Note status", implementation = AINOTE.class)
    private AINOTE status;
    @Schema(description = "User created info", implementation = UserDto.class)
    private UserDto createdBy;
    /**
     * When transcription starts.
     */
    @Schema(description = "Transcription start timestamp")
    private Timestamp startTime;
    @Schema(description = "Transcription end timestamp")
    private Timestamp endTime;
    @Schema(description = "Transcription update timestamp")
    private Timestamp updatedAt;
    @Schema(description = "Patient details", implementation = PatientDto.class)
    private PatientDto patient;
    @Schema(description = "Practitioner information", implementation = UserDto.class)
    private UserDto practitioner;
    @Schema(description = "Appointment information", implementation = AppointmentDto.class)
    private AppointmentDto appointment;
    @Schema(description = "Prescription Id", implementation = Long.class)
    private Long prescriptionId;
    @Schema(description = "Audio length time")
    private String audioTime;
    @Schema(description = "Resident Id", implementation = Long.class)
    private Long residentId;
    @Schema(description = "is Archived")
    private boolean isArchived;
}
