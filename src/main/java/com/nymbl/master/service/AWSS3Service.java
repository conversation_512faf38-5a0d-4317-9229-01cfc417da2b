package com.nymbl.master.service;

import com.amazonaws.AmazonClientException;
import com.amazonaws.HttpMethod;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import com.nymbl.master.dto.NymblS3Object;
import com.nymbl.tenant.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AWSS3Service {

    String LIST_EXCEPTION = "S3: Exception listing all items within bucket, for company {}, bucket {}, exception message is {}";

    String DOWNLOAD_EXCEPTION = "S3: Exception downloading object from, bucket:key {}, exception message is {}";
    private final AmazonS3 amazonS3;
    private final int MAX_UPLOAD_THREADS = 15;

    public AWSS3Service(AmazonS3 amazonS3) {
        this.amazonS3 = amazonS3;
    }

    /**
     * Upload files  to s3 bucket
     *
     * @param multipartFile
     * @param key
     * @throws IOException
     */
    public boolean uploadFileToS3(MultipartFile multipartFile, String bucket, String key) throws IOException, AmazonClientException, InterruptedException {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(multipartFile.getContentType());
        metadata.setContentLength(multipartFile.getSize());

        TransferManager transferManager = TransferManagerBuilder.standard().
                withS3Client(amazonS3).
                withMultipartUploadThreshold((long) (5 * 1024 * 1025)).
                withExecutorFactory(() -> Executors.newFixedThreadPool(MAX_UPLOAD_THREADS)).
                build();

        Upload upload = transferManager.upload(bucket, key, multipartFile.getInputStream(), metadata);
        upload.waitForCompletion();
        log.info("AWSConfig: Completed Upload to AWS");
        return true;
    }

    /**
     * @param bucketName
     * @param prefix
     * @return
     */
    public Optional<List<NymblS3Object>> listObjects(String bucketName, String prefix) {

        try {
            final ListObjectsRequest listObjectRequest = new ListObjectsRequest()
                    .withBucketName(bucketName)
                    .withPrefix(prefix)
                    .withDelimiter("/");
            ObjectListing objectListing = amazonS3.listObjects(listObjectRequest);
            return Optional.of(objectListing.getObjectSummaries().stream().map(this::convertToNymblS3).filter(x -> StringUtils.isNotBlank(x.getFileName())).collect(Collectors.toList()));
        } catch (SdkClientException exception) {
            log.error(LIST_EXCEPTION, TenantContext.getCurrentTenant(), bucketName, exception.getMessage());
        }

        return Optional.empty();
    }

    /**
     * Download object
     *
     * @param bucket
     * @param key
     * @return
     */
    public Optional<byte[]> getFile(String bucket, String key) {

        try {
            S3Object object = amazonS3.getObject(bucket, key);
            S3ObjectInputStream stream = object.getObjectContent();
            byte[] content = IOUtils.toByteArray(stream);
            object.close();
            return Optional.ofNullable(content);
        } catch (SdkClientException | IOException e) {
            log.error(DOWNLOAD_EXCEPTION, TenantContext.getCurrentTenant(), bucket + ":" + key, e.getMessage());
        }
        return Optional.empty();
    }

    /**
     * @param s3ObjectSummary
     * @return
     */
    private NymblS3Object convertToNymblS3(S3ObjectSummary s3ObjectSummary) {
        NymblS3Object nymblS3Object = new NymblS3Object();
        nymblS3Object.setBucket(s3ObjectSummary.getBucketName());
        nymblS3Object.setFileName(deriveFileName(s3ObjectSummary.getKey()));
        nymblS3Object.setKey(s3ObjectSummary.getKey());
        nymblS3Object.setSize((s3ObjectSummary.getSize() / (1024l * 1024l)) + " MB");
        nymblS3Object.setLastUpdated(s3ObjectSummary.getLastModified());

        return nymblS3Object;
    }

    /**
     * @param key
     * @return
     */
    public String deriveFileName(String key) {
        return key.substring(key.lastIndexOf("/") + 1);
    }

    public boolean doesBucketExist(String bucketName) {
        return amazonS3.doesBucketExistV2(bucketName);
    }


    // Method to check if a bucket exists, create it if not, and add a Lambda event notification
    public void createBucketAndAddLambdaTrigger(String bucketName, String lambdaFunctionArn) {
        // Create an S3 client
        try
        {
            // Create the bucket if it does not exist
            CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
            amazonS3.createBucket(createBucketRequest);
            log.info("Bucket created successfully:  {}", bucketName);

            // Add event notification for the Lambda function
            addLambdaEventNotification(amazonS3, bucketName, lambdaFunctionArn);
            log.info("Event notification added to trigger Lambda function: {}", lambdaFunctionArn);

        } catch (AmazonS3Exception e) {
            log.error("Error occurred while checking or creating the bucket: {} ", e.getMessage(), e);
            throw e;
        }
    }

    // Method to add an event notification to the S3 bucket for a Lambda function
    private void addLambdaEventNotification(AmazonS3 s3Client, String bucketName, String lambdaFunctionArn) {
        // Define the Lambda function configuration for S3 event notification
        LambdaConfiguration lambdaConfig = new LambdaConfiguration(lambdaFunctionArn, "s3:ObjectCreated:*");

        // Create a filter rule for the suffix "summary.json"
        FilterRule suffixFilterRule = new FilterRule().withName("suffix").withValue("summary.json");

        // Create an S3 key filter with the filter rule
        S3KeyFilter keyFilter = new S3KeyFilter().withFilterRules(suffixFilterRule);

        // Set the filter on the Lambda configuration
        lambdaConfig.setFilter(new Filter().withS3KeyFilter(keyFilter));
        BucketNotificationConfiguration bucketNotificationConfig = new BucketNotificationConfiguration();
        bucketNotificationConfig.addConfiguration("TriggerTranscribeCompleteEvt", lambdaConfig);

        // Set the bucket notification configuration
        s3Client.setBucketNotificationConfiguration(bucketName, bucketNotificationConfig);
    }

    /**
     * Generates a presigned url for display on UI
     *
     * @param bucketName         bucket name
     * @param objectKey          object key includes name
     * @param expirationInMillis how long it should be alive.
     * @return S3 Presigned URL
     */
    public URL generatePresignedUrl(String bucketName, String objectKey, long expirationInMillis) {
        Date expiration = new Date(System.currentTimeMillis() + expirationInMillis);
        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucketName, objectKey)
                .withMethod(HttpMethod.GET)
                .withExpiration(expiration);
        return amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
    }


    public void deleteFile(String bucketName, String key) {
        // Create a DeleteObjectRequest
        DeleteObjectRequest request = new DeleteObjectRequest(bucketName, key);

        // Delete the object
        amazonS3.deleteObject(request);
        log.info("S3 File deleted successfully: {}", key);
    }


    public void deleteFolderContents(String bucketName, String folderKey) {
        if (!folderKey.endsWith("/")) {
            folderKey += "/";
        }

        // Loop through all the objects in the folder
        List<DeleteObjectsRequest.KeyVersion> keysToDelete = new ArrayList<>();
        String nextMarker = null;

        do {
            ListObjectsRequest listRequest = new ListObjectsRequest()
                    .withBucketName(bucketName)
                    .withPrefix(folderKey)
                    .withMarker(nextMarker);

            ObjectListing objectListing = amazonS3.listObjects(listRequest);
            for (S3ObjectSummary summary : objectListing.getObjectSummaries()) {
                keysToDelete.add(new DeleteObjectsRequest.KeyVersion(summary.getKey()));
                System.out.println("Prepared for deletion: " + summary.getKey());
            }

            nextMarker = objectListing.getNextMarker();
        } while (nextMarker != null);

        // Delete all collected objects
        if (!keysToDelete.isEmpty()) {
            DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest(bucketName)
                    .withKeys(keysToDelete);
            amazonS3.deleteObjects(deleteRequest);
            log.info("All files deleted under folder: {}", folderKey);
        } else {
            log.info("No files found to delete.");
        }
    }
}
