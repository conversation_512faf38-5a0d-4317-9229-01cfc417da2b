package com.nymbl.payment.utils;

import com.nymbl.master.service.CompanyService;
import com.nymbl.master.service.UserService;
import com.nymbl.payment.dto.ChargeRequest;
import com.nymbl.payment.dto.StripeBranchDetail;
import com.nymbl.payment.dto.StripeEvent;
import com.nymbl.payment.dto.StripeTransactionDto;
import com.nymbl.payment.service.StripeNotificationService;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.dashboard.dto.BranchDto;
import com.nymbl.tenant.dashboard.dto.PatientInfoDto;
import com.nymbl.tenant.dashboard.repository.BranchDtoRepository;
import com.nymbl.tenant.dashboard.repository.PatientDtoRepository;
import com.nymbl.tenant.model.Branch;
import com.nymbl.tenant.model.StripeTransaction;
import com.nymbl.tenant.repository.StripeTransactionRepository;
import com.nymbl.tenant.service.BranchService;
import com.nymbl.tenant.service.NotificationService;
import com.stripe.exception.StripeException;
import com.stripe.model.Account;
import com.stripe.model.Event;
import com.stripe.model.PaymentIntent;
import com.stripe.model.StripeObject;
import com.stripe.model.checkout.Session;
import com.stripe.param.checkout.SessionCreateParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class StripeUtil {

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Value("${nymbl.posted.payments.internal}")
    private String browserCallbackUrl;

    @Value("${nymbl.posted.payments.terminal}")
    private String terminalCallbackUrl;

    @Value("${nymbl.patient.payments.public}")
    private String paySuccessUrl;

    @Value("${stripe.account.change.notification}")
    private String accountUpdateMsg;


    @Value("${base.url}")
    private String baseURL;

    private final Environment environment;


    private final NotificationService notificationService;
    private final StripeTransactionRepository stripeRepository;
    private final BranchDtoRepository branchDtoRepository;
    private final PatientDtoRepository patientDtoRepository;
    private final CompanyService companyService;
    private final BranchService branchService;

    private final StripeNotificationService stripeNotificationService;

    private final UserService userService;

    public StripeUtil(Environment environment, NotificationService notificationService, StripeTransactionRepository stripeRepository, BranchDtoRepository branchDtoRepository, PatientDtoRepository patientDtoRepository, CompanyService companyService, BranchService branchService, StripeNotificationService stripeNotificationService, UserService userService) {
        this.environment = environment;
        this.notificationService = notificationService;
        this.stripeRepository = stripeRepository;
        this.branchDtoRepository = branchDtoRepository;
        this.patientDtoRepository = patientDtoRepository;
        this.companyService = companyService;
        this.branchService = branchService;
        this.stripeNotificationService = stripeNotificationService;
        this.userService = userService;
    }

    public String getStripeAccount(Long branchId) {
        BranchDto branchDto = branchDtoRepository.findBranchDtoByIdDto(branchId);
        return branchDto.getStripeAccountId();
    }

    public BranchDto getBranchDto(Long branchId) {
        return branchDtoRepository.findBranchDtoByIdDto(branchId);
    }

    /**
     * Process right event for right environment, stripe triggers test events for prod webhooks as well
     *
     * @return
     */
    public boolean filterLiveMode(Event event, String requestUrl) {
        boolean isProduction = Arrays.asList(environment.getActiveProfiles()).contains("prod");

        if (event.getLivemode() && isProduction) {
            log.info("Stripe: Triggered event for the following environment {}", environment.getActiveProfiles()[0]);
            return true;
        } else if (!event.getLivemode() && (!isProduction) && requestUrl.contains(activeProfile)) {
            log.info("Stripe: Triggered event for the following environment test {}", activeProfile);
            return true;
        } else {
            return false;
        }
    }


    /**
     * Save stripe payment transaction
     *
     * @param chargeRequest
     */
    public StripeTransaction saveStripePayment(ChargeRequest chargeRequest, BigDecimal applicationFee) {
        StripeTransaction payment = new StripeTransaction();
        payment.setPaymentType("stripe");
        payment.setAmount(chargeRequest.getAmount());
        payment.setUnappliedAmount(chargeRequest.getUnappliedAmount());
        payment.setUnappliedAdjustment(chargeRequest.getUnappliedAdjustment());
        payment.setPayerType("patient");
        payment.setCreatedById(chargeRequest.getCreatedById());
        payment.setNymblFee(applicationFee);
        payment.setDate(Date.valueOf(chargeRequest.getDate()));
        if (StringUtils.isNotBlank(chargeRequest.getPatientEmail()))
            payment.setReceiptEmail(chargeRequest.getPatientEmail());
        payment.setDepositDate(chargeRequest.getDepositDate() != null ? Date.valueOf(chargeRequest.getDepositDate()) : null);
        payment.setNymblStripeId("default");
        payment.setPatientId(chargeRequest.getPatientId());
        payment.setStripeAccountId(getStripeAccount(chargeRequest.getBranchId()));
        payment.setClaimId(chargeRequest.getClaimId());
        payment.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        payment.setStatus("initialize");
        payment.setDescription(chargeRequest.getDescription());
        payment.setPrescriptionId(chargeRequest.getPrescriptionId());
        payment.setNymblPaymentMethod(chargeRequest.getNymblPaymentType());
        payment.setPatientFirstName(chargeRequest.getPatientFirstName());
        payment.setPatientLastName(chargeRequest.getPatientLastName());
        payment.setPatientDob(Date.valueOf(chargeRequest.getPatientDob()));
        payment.setBranchId(chargeRequest.getBranchId());

        return stripeRepository.save(payment);
    }


    /**
     * Delete stripe transaction record
     *
     * @param stripeTransaction
     */
    public void deleteStripeTransaction(StripeTransaction stripeTransaction) {
        stripeRepository.delete(stripeTransaction);
    }


    public StripeEvent makePaymentIntentEvent(String connectedAccountId, StripeObject stripeObject) {

        StripeEvent stripeEvent = new StripeEvent();
        PaymentIntent paymentIntent = (PaymentIntent) stripeObject;
        stripeEvent.setCheckOutStatus(paymentIntent.getStatus());
        boolean paid = paymentIntent.getCharges().getData().get(0).getPaid();
        stripeEvent.setPaymentStatus(paid ? "paid" : "incomplete");
        stripeEvent.setTenant(paymentIntent.getMetadata().get("tenant"));
        stripeEvent.setClientReferenceId(paymentIntent.getMetadata().get("clientReferenceId"));
        stripeEvent.setConnectedAccountId(connectedAccountId);
        stripeEvent.setStripePaymentId(paymentIntent.getId());

        return stripeEvent;
    }

    public StripeEvent makeSessionEvent(String connectedAccountId, StripeObject stripeObject) {

        StripeEvent stripeEvent = new StripeEvent();
        Session checkoutSession = (Session) stripeObject;
        stripeEvent.setCheckOutStatus(checkoutSession.getStatus());
        stripeEvent.setPaymentStatus(checkoutSession.getPaymentStatus());
        stripeEvent.setTenant(checkoutSession.getMetadata().get("tenant"));
        stripeEvent.setNymblPaymentMethod(checkoutSession.getMetadata().get("nymblPaymentMethod"));
        stripeEvent.setClientReferenceId(checkoutSession.getClientReferenceId());
        stripeEvent.setConnectedAccountId(connectedAccountId);
        stripeEvent.setStripePaymentId(checkoutSession.getPaymentIntent());

        return stripeEvent;
    }


    public Long deconstructStripeIdentifier(String nymblTransactionId) {
        String numericString = nymblTransactionId.substring(3);
        numericString = removeLeadingZeroes(numericString);
        return Long.valueOf(numericString);
    }

    public String removeLeadingZeroes(String str) {
        String strPattern = "^0+(?!$)";
        str = str.replaceAll(strPattern, "");
        return str;
    }

    public String makeStripeIdentifier(Long id) {
        return "ST-" + StringUtils.leftPad(String.valueOf(id), 10, "0");
    }

    /**
     * Process connected account creation and events
     *
     * @param stripeObject
     */
    public void processAccountEvent(StripeObject stripeObject) {

        Account account = (Account) stripeObject;
        log.info("Stripe: Beginning processAccountEvent for account {}", account.getId());
        try
        {
            String tenant = account.getMetadata().get("tenant");
            TenantContext.setCurrentTenant(tenant);
            saveBranchDetails(account);
            TenantContext.clear();

        } catch (Exception ex) {
            log.error("Stripe: Exception processing account updates for account {}", account.getId(), ex);
        }

        log.info("Stripe: End processAccountEvent for account {}", account.getId());
    }

    public String getCustomerEmail(Long patientId) {
        PatientInfoDto patientInfoDto = patientDtoRepository.findPatientInfoById(patientId);
        if (null == patientInfoDto)
            return "";

        return patientInfoDto.getEmail();
    }

    /**
     * Websocket to redirect to success page
     *
     * @param stripeIdentifier
     * @param source
     * @param userId
     * @return
     * @throws StripeException
     */
    public void triggerNotification(String stripeIdentifier, String source, Long userId) {

        String baseUrl = getUrl() + "billing/success/";
        String stringBuilder = baseUrl + stripeIdentifier +
                "/" + source;

        notificationService.createRedirectNotificationForWebhook(stringBuilder, userId);
    }

    /**
     * Redirect to success page after payment
     *
     * @return
     */
    public String getUrl() {

        return getBaseUrl() + "/#/app/";
    }

    public String getBaseUrl() {
        boolean isLocal = Arrays.asList(environment.getActiveProfiles()).contains("local");
        if (isLocal)
            return ServletUriComponentsBuilder.fromCurrentContextPath().build().toUriString();

        return baseURL;
    }

    private StripeBranchDetail makeDetail(String[] detailsArray, Long id) {

        Map<String, String> detailsMap = Arrays.stream(detailsArray).map(detail -> detail.split(":"))
                .collect(Collectors.toMap(det -> det[0], det -> det[1]));

        StripeBranchDetail stripeBranchDetail = new StripeBranchDetail();
        stripeBranchDetail.setBillingEmail(detailsMap.get("billingEmail"));
        stripeBranchDetail.setBranchId(id);

        return stripeBranchDetail;
    }

    private void saveBranchDetails(Account account) {

        log.info("Stripe: Beginning saveBranchDetails for account " + account.getId());
        List<Branch> activeBranch = branchService.findAllByActiveTrue();
        String allBranches = account.getMetadata().get("allBranches");

        if (null != allBranches) {
            String[] allBranchDetails = allBranches.split(";");
            StripeBranchDetail stripeBranchDetail = makeDetail(allBranchDetails, null);
            branchDtoRepository.updateStripeAccountIdForAll(account.getId(), stripeBranchDetail.getBillingEmail());

            if (StringUtils.isNotBlank(stripeBranchDetail.getBillingEmail())) {
                stripeNotificationService.sendEmail(accountUpdateMsg + "/n" + "/n", stripeBranchDetail.getBillingEmail());
                stripeNotificationService.sendSnsNotification("An update has been made for account belonging to ", TenantContext.getCurrentTenant());
                log.info("Stripe: Processed account update notification for account and branch {}", account.getId());
            }

            log.info("Stripe: Updated branch Details for account {}", account.getId());
        }

        if (null != activeBranch && !activeBranch.isEmpty()) {
            activeBranch.forEach(branch -> {
                String branchDetails = account.getMetadata().get("branchDetails:" + branch.getId());

                if (StringUtils.isNotBlank(branchDetails)) {
                    String[] branchDetailsArray = branchDetails.split(";");
                    StripeBranchDetail stripeBranchDetail = makeDetail(branchDetailsArray, branch.getId());

                    branchDtoRepository.updateStripeAccountId(branch.getId(), account.getId(), stripeBranchDetail.getBillingEmail());
                    log.info("Stripe: Updated branch Details for account and branchId" + account.getId() + " : " + branch.getId());
                    // Notify when changes made to account
                    if (StringUtils.isNotBlank(stripeBranchDetail.getBillingEmail())) {
                        stripeNotificationService.sendEmail(accountUpdateMsg + "/n" + "/n", stripeBranchDetail.getBillingEmail());
                        stripeNotificationService.sendSnsNotification("An update has been made for account belonging to ", TenantContext.getCurrentTenant());

                        log.info("Stripe: Processed account update notification for account and branch {} : {}", account.getId(), branch.getId());
                    }
                }
            });
        }
    }

    public void saveLocationId(List<Long> branchId, String updateStripeLocationId) {
        branchDtoRepository.updateStripeLocationId(branchId, updateStripeLocationId);
    }

    /**
     * Create Payment Intent for Session Object, 3 represent fee percentage
     *
     * @param request
     * @return
     */
    public SessionCreateParams.PaymentIntentData calcNymblCharge(ChargeRequest request) {

        long amount = request.getAmount().setScale(0, RoundingMode.HALF_UP).longValue();
        long percentage = 3 * amount;

        String companyName = userService.getCurrentCompany().getName();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(companyName) && companyName.length() > 21) {
            companyName = companyName.substring(0, 21);
        }


        SessionCreateParams.PaymentIntentData.Builder paymentIntentBuilder = SessionCreateParams.PaymentIntentData.builder()
                .setStatementDescriptor(companyName)  // This might need to be in a table somewhere
                .putMetadata("tenant", TenantContext.getCurrentTenant())
                .setApplicationFeeAmount(percentage);

        if (StringUtils.isNotBlank(request.getPatientEmail())) {
            paymentIntentBuilder.setReceiptEmail(request.getPatientEmail());
        }

        return paymentIntentBuilder.build();
    }

    /**
     * Description on payment page
     *
     * @param request
     * @return
     */
    public String makeDescription(ChargeRequest request) {

        String patientIdString = "";
        if (request.getPatientId() != null) {
            patientIdString = " (#" + request.getPatientId() + ") to ";
        }

        StringBuilder builder = new StringBuilder();
        String companyName = userService.getCurrentCompany().getName();
        builder.append("Payment made by: Patient Id: ").append(patientIdString).append(" Company Name: ").append(companyName);

        return builder.toString();
    }

    /**
     *
     * @param stripeTransaction
     * @return
     */
    public StripeTransactionDto convertToDto(StripeTransaction stripeTransaction) {
        StripeTransactionDto dto = new StripeTransactionDto();
        dto.setPatientName(stripeTransaction.getPatientFirstName() + " " + stripeTransaction.getPatientLastName());

        dto.setStatus(stripeTransaction.getStatus());
        dto.setPaymentMethod(stripeTransaction.getPaymentType());
        dto.setPatientId(stripeTransaction.getPatientId());
        dto.setReferenceNumber(stripeTransaction.getNymblStripeId());
        dto.setTotalAmount(stripeTransaction.getAmount());
        dto.setUnappliedAmount(stripeTransaction.getUnappliedAmount());
        dto.setPaymentDate(stripeTransaction.getCreatedAt());
//        dto.setPaymentDate(stripeTransaction.getDate().toLocalDate());
        dto.setPatientUnmatched(stripeTransaction.getPatientId() == null);
        dto.setStripeIdentifier(stripeTransaction.getCheckoutSessionId());
        dto.setPatientDob(stripeTransaction.getPatientDob().toLocalDate());

        return dto;
    }

    public String getCallbackUrl(ChargeRequest var) {
        switch (var.getNymblPaymentType()) {
            case "PPL": {
                return getPatientCallbackUrl(var.getCompanyKey(), var.getStripeIdentifier(), var.getPrimaryBranchName());
            }
            case "PPT": {
                return getUrl() + terminalCallbackUrl.replace("{stripeIdentifier}", var.getStripeIdentifier());
            }
            default: {
                return getUrl() + browserCallbackUrl.replace("{stripeIdentifier}", var.getStripeIdentifier());
            }
        }

    }

    public boolean sendNotifications(String message, String tenant, String tenantEmail) {
        // trigger success SNS topic to nymbl billing
        stripeNotificationService.sendSnsNotification(message, tenant);
        return stripeNotificationService.sendEmail(message, tenantEmail);
    }

    private String getPatientCallbackUrl(String company, String nymblPaymentId, String branchName) {
        return getBaseUrl() + paySuccessUrl + "?company=" + company + "&nymblPaymentId=" + nymblPaymentId + "&branch=" + branchName;
    }
}
