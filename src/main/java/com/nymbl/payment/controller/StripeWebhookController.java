package com.nymbl.payment.controller;

import com.nymbl.payment.service.StripeWebhookService;
import com.nymbl.payment.utils.StripeUtil;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.model.Event;
import com.stripe.net.Webhook;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("v1/stripe/webhook")
@Slf4j
public class StripeWebhookController {

    @Value("${stripe.secret.webhook}")
    private String webHookSecret;
    @Value("${stripe.secret.webhook_con}")
    private String webHookSecretConnected;

    public final StripeUtil stripeUtil;

    private final StripeWebhookService stripeWebhookService;

    public StripeWebhookController(StripeUtil stripeUtil, StripeWebhookService stripeWebhookService) {
        this.stripeUtil = stripeUtil;
        this.stripeWebhookService = stripeWebhookService;
    }

    @PostMapping(value = "/account", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> webhook(@RequestBody String eventString, HttpServletRequest request) {
        Event event = validateSignature(eventString, request, false);
        if (null != event) {
            String requestUrl = String.valueOf(request.getRequestURL());
            if(stripeUtil.filterLiveMode(event, requestUrl)) {
                stripeWebhookService.processEvents(event);
            }
            else {
                return ResponseEntity.ok("Not Live Mode");
            }
        }
        else
        {
            return ResponseEntity.badRequest().body("Invalid Signature!!");
        }
        return ResponseEntity.ok("Thanks");
    }

    @PostMapping(value = "/connected", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> webhookConnected(@RequestBody String eventString, HttpServletRequest request) {
        Event event = validateSignature(eventString, request, true);
        if (null != event) {
            String requestUrl = String.valueOf(request.getRequestURL());
            log.info("Stripe: Got a request for an event {} in mode {} for account {}", event.getType(), event.getLivemode(), event.getAccount());
            if(stripeUtil.filterLiveMode(event, requestUrl)) {
                stripeWebhookService.processEvents(event);
            }
            else {
                return ResponseEntity.ok("Not Live Mode");
            }
        }
        else
        {
            return ResponseEntity.badRequest().body("Invalid Signature!!");
        }
        return ResponseEntity.ok("Thanks");
    }

    private Event validateSignature(String eventString, HttpServletRequest request, boolean isConnected) {
        String sigHeader = request.getHeader("Stripe-Signature");
        Event event;
        try
        {
            event = Webhook.constructEvent(eventString, sigHeader, isConnected ? webHookSecretConnected : webHookSecret);
        } catch (SignatureVerificationException e) {
            log.error("Exception verifying signature!!! {}", e.getMessage());
            return null;
        }
        return event;
    }


}
