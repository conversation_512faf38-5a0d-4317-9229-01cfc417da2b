{"groups": [{"name": "sentry", "type": "io.sentry.spring.boot.SentryProperties", "sourceType": "io.sentry.spring.boot.SentryProperties"}, {"name": "sentry.session-replay", "type": "io.sentry.spring.boot.SentrySessionReplayProperties", "sourceType": "io.sentry.spring.boot.SentrySessionReplayProperties"}], "properties": [{"name": "sentry.session-replay.sample-rate", "type": "java.lang.Double", "description": "Sample rate for session replay capture.", "defaultValue": 0.1}, {"name": "sentry.session-replay.on-error-sample-rate", "type": "java.lang.Double", "description": "Sample rate for session replay capture when an error occurs.", "defaultValue": 1.0}]}