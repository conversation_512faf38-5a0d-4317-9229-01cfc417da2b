<!doctype html>
<html ng-app="minovateApp" ng-controller="MainCtrl" class="no-js {{containerClass}}"
      xmlns:th="http://www.thymeleaf.org">
<head>

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-*********-2"></script>

    <!-- Sentry config -->
    <script
            src="https://browser.sentry-cdn.com/9.13.0/bundle.tracing.min.js"
            integrity="sha384-By0OPQsiDfFrqktodOeUhnJ3mZ/5U2207JDXjozMFqFJSHLkhpxZ5rElOKTdM/HZ"
            crossorigin="anonymous"
    ></script>
    <script th:inline="javascript">
        Sentry.init({
            dsn: /*[[${@environment.getProperty('sentry.dsn')}]]*/,
            environment: /*[[${@environment.getProperty('sentry.environment')}]]*/,
            // Adds request headers and IP for users, for more info visit:
            // https://docs.sentry.io/platforms/javascript/configuration/options/#sendDefaultPii
            sendDefaultPii: true,
            integrations: [
                Sentry.browserTracingIntegration(),
                Sentry.replayIntegration(),
            ],
            // Set tracesSampleRate to 1.0 to capture 100%
            // of transactions for tracing.
            // We recommend adjusting this value in production
            // Learn more at
            // https://docs.sentry.io/platforms/javascript/configuration/options/#traces-sample-rate
            tracesSampleRate: /*[[${@environment.getProperty('sentry.traces-sample-rate', 1.0)}]]*/,
            // Set `tracePropagationTargets` to control for which URLs trace propagation should be enabled
            tracePropagationTargets: [location.host],
            // Capture Replay for 10% of all sessions,
            // plus for 100% of sessions with an error
            // Learn more at
            // https://docs.sentry.io/platforms/javascript/session-replay/configuration/#general-integration-configuration
            replaysSessionSampleRate: /*[[${@environment.getProperty('sentry.session-replay.sample-rate', 0.1)}]]*/,
            replaysOnErrorSampleRate: /*[[${@environment.getProperty('sentry.session-replay.on-error-sample-rate', 1.0)}]]*/,
        });
    </script>
    <!-- -->

    <!--- Stripe scripts -->
    <script src="https://js.stripe.com/v3/"></script>
    <script th:if="${#arrays.contains(@environment.getActiveProfiles(),'prod')}">
	    window.dataLayer = window.dataLayer || [];

	    function gtag() {
		    dataLayer.push(arguments);
	    }

	    gtag('js', new Date());
	    gtag('config', 'UA-*********-2');
    </script>

    <!-- Start of nymblsystems Zendesk Widget script -->
    <script id="ze-snippet"
            src="https://static.zdassets.com/ekr/snippet.js?key=04c894c3-b8f8-4a14-8c2f-8b8bbc79e129"></script>
    <script type="text/javascript">
	    zE('webWidget', 'hide');
	    window.zESettings = {
		    webWidget: {
			    authenticate: {
				    chat: {
					    jwtFn: function (callback) {
						    angular.element(document.body).injector().get('UserFactory').getZendeskToken().$promise.then(function (res) {
							    callback(res.data);
						    });
					    }
				    }
			    }
		    }
	    };
    </script>
    <!-- End of nymblsystems Zendesk Widget script -->

    <!-- Start of nymblsystems Pendo.io script -->
    <script>
			(function(apiKey){
				(function(p,e,n,d,o){var v,w,x,y,z;o=p[d]=p[d]||{};o._q=o._q||[];
					v=['initialize','identify','updateOptions','pageLoad','track'];for(w=0,x=v.length;w<x;++w)(function(m){
						o[m]=o[m]||function(){o._q[m===v[0]?'unshift':'push']([m].concat([].slice.call(arguments,0)));};})(v[w]);
					y=e.createElement(n);y.async=!0;y.src='https://cdn.pendo.io/agent/static/'+apiKey+'/pendo.js';
					z=e.getElementsByTagName(n)[0];z.parentNode.insertBefore(y,z);})(window,document,'script','pendo');
			})('9136b0dd-4ba9-4ec3-4490-4efe1f382558');
    </script>
    <!-- End of nymblsystems Pendo.io script -->

    <meta charset="utf-8">
    <!--<title ng-cloak>{{main.title}} &mdash; {{page.subtitle ? page.subtitle : page.title}}</title>-->
    <title ng-cloak ng-bind-html="main.title+' - '+(page.subtitle ? page.subtitle : page.title)">Nymbl</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1,user-scalable=yes">
    <meta name="robots" content="noindex">
    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->
    <link rel="icon" type="image/ico" href="images/favicon.png"/>

    <!--    Overall css stylesheets are colliding with evaluation form printing. Will need to revise when we refactor evaluation forms-->
    <link rel="stylesheet" href="styles/vendor.css" media="screen">
    <link rel="stylesheet" href="styles/vendor.css" media="print">
    <link rel="stylesheet" href="styles/app.css" media="screen">
    <link rel="stylesheet" href="styles/app.css" media="print">
    <link rel="stylesheet" href="styles/main.css" media="all">

    <!--If you remove the line below this the Nymbl Bird branch logo will be messed up and the menus will have
    strange formatting.-->
    <link rel="stylesheet" href="styles/app.css" media="screen" data-ng-if="!evaluationFormId">

    <ng-include src="'views/tmpl/_view_release_note_modal.html'"></ng-include>
</head>
<body id="minovate"
      class="{{main.settings.navbarHeaderColor}} {{main.settings.activeColor}} {{containerClass}} header-fixed aside-fixed rightbar-hidden appWrapper"
      ng-class="{'header-fixed': main.settings.headerFixed, 'header-static': !main.settings.headerFixed, 'aside-fixed': main.settings.asideFixed, 'aside-static': !main.settings.asideFixed, 'rightbar-show': main.settings.rightbarShow, 'rightbar-hidden': !main.settings.rightbarShow}">

<!--[if lt IE 7]>
<p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade
    your browser</a> to improve your experience.</p>
<![endif]-->

<!-- Environment Properties -->
<div id="nymblV2Url" th:value="${@environment.getProperty('nymbl.v2.url')}"></div>
<div id="nymbl.token.expiration" th:value="${@environment.getProperty('nymbl.token.expiration')}"></div>
<!-- TODO replace with UI Runtime Config reference for swapping config without rebuilding -->
<div id="pspdfkitLicense" th:value="${@environment.getProperty('pspdfkit.license')}"></div>
<div id="empireClientId" th:value="${@environment.getProperty('empire.client.id')}"></div>
<div id="empireAuthorizeUrl" th:value="${@environment.getProperty('empire.authorize.url')}"></div>
<div id="baseUrl" th:value="${@environment.getProperty('base.url')}"></div>
<div id="codedReasons" th:value="${@environment.getProperty('nymbl.itemPhysical.codedReasons')}"></div>

<!-- Application content -->
<div id="wrap" ui-view autoscroll="false"></div>

<!-- Page Loader -->
<!--<div id="pageloader" page-loader></div>-->

<!--[if lt IE 9]>
<script th:src="@{scripts/oldieshim.js}"></script>
<![endif]-->

<script th:src="@{scripts/vendor.min.js}" data-auto-replace-svg="nest"></script>
<script th:src="@{scripts/assets/nutrient-viewer.js}" th:type="module"></script>

<script th:if="${#arrays.contains(@environment.getActiveProfiles(),'prod')}"
        th:src="@{scripts/script.min.js}"></script>
<script th:src="@{scripts/templatecache.js}" data-auto-replace-svg="nest"></script>
<script th:if="${!#arrays.contains(@environment.getActiveProfiles(), 'prod')}" th:src="@{scripts/script.js}"></script>
<div class="hidden" th:value="${!#arrays.contains(@environment.getActiveProfiles(), 'prod')}"></div>
</body>
</html>
