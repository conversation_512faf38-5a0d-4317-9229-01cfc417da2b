environment=production
#retail.cascade.company=COS
## ESTORE = test, ESTORE2 = production
#retail.cascade.taker=ESTORE2
#retail.cascade.po_start_url=https://v2backend.cascade-usa.com/api/punchout/startpage
#retail.cascade.po_login_url=https://www.cascade-usa.com/auth/login
# 7443 = test, 6443 = production
#retail.cascade.api_url=https://api.cascade-usa.com:6443
#retail.cascade.api_username=nymbl
#retail.cascade.api_password=GXjskyW2zv
#retail.cascade.api_grant_type=password
#retail.sps.company=spsco_store_view
#retail.sps.taker=ESTORE2
#retail.sps.po_start_url=https://connect.punchout2go.com/gateway/punchout/request/catalog/eF6148b08325593
#retail.sps.po_login_url=https://connect.punchout2go.com/gateway/order/request/id/Fq6148b09252af0
#retail.sps.api_url=https://connect.punchout2go.com/gateway/order/request/id/Fq6148b09252af0
#DB Connection
#db.url=*****************************************************************************************************************************************************
# Empire
#empire.token.url=https://api.empire-medical.com/oauth/token
#empire.authorize.url=https://api.empire-medical.com/oauth/authorize
#empire.order.lines.url=https://api.empire-medical.com/my/facility/order-lines
# Redis Config
spring.cache.type=redis
#spring.redis.host=redis-cache-1.t16ulg.ng.0001.use1.cache.amazonaws.com
spring.redis.port=6379

#Sentry
sentry.environment=prod
sentry.traces-sample-rate=1.0
sentry.profiles-sample-rate=1.0
sentry.trace-propagation-targets=prod.nymbl.live
sentry.enable-auto-session-tracking=true
sentry.session-tracking-interval-millis=30000
sentry.enable-user-interaction-tracing=true
sentry.enable-user-interaction-breadcrumbs=true

#stripe.env=prod
#stripe.secret.api-key=***********************************************************************************************************
#stripe.secret.webhook=whsec_Ufx8F4WlkNmnFr9FbUzRtzvOaSoJfREA
#stripe.secret.webhook_con=whsec_A3KKLDsHn6cQUxHcpPPIfxlG1pVXPHmp
#stripe.secret.webhook_con=whsec_A3KKLDsHn6cQUxHcpPPIfxlG1pVXPHmp
#
empire.client.id=10
empire.client.secret=errErIKkBnbbxML31DnKbZnf1QBtYgTyAz9ghEBy

# CORS Domains
#management.endpoints.web.cors.allowed-origins=https://prod.nymbl.live/,https://prod2.nymbl.live/

#TODO: Move this to parameter store
# LMN PDF Generator Lambda Function
lmn.pdf.lambda.function=arn:aws:lambda:us-east-2:086881067392:function:lmn-pdf-generator-prod-function
