{"info": {"_postman_id": "419f5df9-a206-444d-9652-75b27a38de2a", "name": "Note Controller", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "2541785"}, "item": [{"name": "Save Note - Unsign", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": 1553,\n    \"subject\": \"This is a subject direct unsign\",\n    \"noteType\": \"clinical\",\n    \"published\": false,\n    \"appointmenId\" : 3520,\n    \"patientId\": 360,\n    \"prescriptionId\": 818,\n    \"action\": \"unsign\",\n    \"note\": \"This is a test note string for draft being unsigned\",\n    \"version\": 1,\n    \"transcriptionDetailId\": 52\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/note/save/dto", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "note", "save", "dto"]}}, "response": []}, {"name": "Save Note - Sign", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": 1553,\n    \"subject\": \"This is a subject direct sign\",\n    \"noteType\": \"clinical\",\n    \"published\": false,\n    \"appointmenId\" : 3520,\n    \"patientId\": 360,\n    \"prescriptionId\": 818,\n    \"action\": \"sign\",\n    \"note\": \"This is a test note string for draft being sign\",\n    \"version\": 2,\n    \"transcriptionDetailId\": 52\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/note/save/dto", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "note", "save", "dto"]}}, "response": []}, {"name": "Save Note - Publish", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": 1553,\n    \"subject\": \"This is a subject direct published\",\n    \"noteType\": \"clinical\",\n    \"published\": true,\n    \"appointmenId\" : 3520,\n    \"patientId\": 360,\n    \"prescriptionId\": 818,\n    \"action\": \"publish\",\n    \"note\": \"This is a test note string for draft being published\",\n    \"version\": 3,\n    \"transcriptionDetailId\": 52\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/note/save/dto", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "note", "save", "dto"]}}, "response": []}, {"name": "Save Note - Unpublish", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": 1553,\n    \"subject\": \"This is a subject direct published\",\n    \"noteType\": \"clinical\",\n    \"published\": false,\n    \"appointmenId\" : 3520,\n    \"patientId\": 360,\n    \"prescriptionId\": 818,\n    \"action\": \"unpublish\",\n    \"note\": \"This is a test note string for draft being published\",\n    \"version\": 4,\n    \"transcriptionDetailId\": 52\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/note/save/dto", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "note", "save", "dto"]}}, "response": []}]}