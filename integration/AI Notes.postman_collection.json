{"info": {"_postman_id": "39ae7d89-5699-42d4-873a-ae0bec9c03a4", "name": "AI Notes", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "2541785"}, "item": [{"name": "find transcription detail by id", "event": [{"listen": "test", "script": {"exec": ["if (postman.get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "Transcript-0000000007", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/transcribe/details/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "details", "1"]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "find transcription note by detail id for view = ready status", "event": [{"listen": "test", "script": {"exec": ["if (postman.get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "Transcript-0000000007", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/transcribe/51", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "51"]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "find transcription note by detail id for view = published status", "event": [{"listen": "test", "script": {"exec": ["if (postman.get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "Transcript-0000000007", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/transcribe/63", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "63"]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "find transcription note string", "event": [{"listen": "test", "script": {"exec": ["if (postman.get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "Transcript-0000000007", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/transcribe/note/61", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "note", "61"]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "Archive Note", "event": [{"listen": "test", "script": {"exec": ["if (postman.get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"transcriptionDetailId\": 49,\n    \"archiveNote\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/transcribe/archive", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "archive"]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "Save/Edit Note - Draft", "event": [{"listen": "test", "script": {"exec": ["if (postman.get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"transcriptionAppointmentNoteId\": 7,\n    \"subject\": \"This is a subject\",\n    \"treatingPractitionerId\": 3240,\n    \"appointmenId\" : 3520,\n    \"patientId\": 360,\n    \"prescriptionId\": 818,\n    \"action\": \"DRAFT\",\n    \"reviewedNoteString\": \"This is a test note string for draft\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/transcribe/save-review", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "save-review"]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "Save/Edit Note - Signed", "event": [{"listen": "test", "script": {"exec": ["if (postman.get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"transcriptionAppointmentNoteId\": 7,\n    \"subject\": \"This is a subject - signed\",\n    \"treatingPractitionerId\": 3240,\n    \"appointmenId\" : 3520,\n    \"patientId\": 360,\n    \"prescriptionId\": 818,\n    \"action\": \"SIGNED\",\n    \"reviewedNoteString\": \"This is a test note string for draft - Note got signed\",\n    \"noteId\": 1553\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/transcribe/save-review", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "save-review"]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "Save/Edit Note - Signed Publish", "event": [{"listen": "test", "script": {"exec": ["if (postman.get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"transcriptionAppointmentNoteId\": 7,\n    \"subject\": \"This is a subject - published\",\n    \"treatingPractitionerId\": 3240,\n    \"appointmenId\" : 3520,\n    \"patientId\": 360,\n    \"prescriptionId\": 818,\n    \"action\": \"PUBLISHED\",\n    \"reviewedNoteString\": \"This is a test note string for draft - Note got publish\",\n    \"note_id\": 1553\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/transcribe/save-review", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "save-review"]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "find transcription note transcript", "event": [{"listen": "test", "script": {"exec": ["if (postman.get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "Transcript-0000000007", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/api/transcribe/transcript/51", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "transcript", "51"]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "search transcription details list", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.headers.get(\"X-Auth-Token\")) {", "    pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "X-Auth-Token", "value": "{{apiToken}}", "type": "default"}], "url": {"raw": "http://localhost:8080/api/transcribe/search?practitionerId=3240&patientId=360&appointmentId=3520&status=READY&startDate=2024-09-17&endDate=2025-03-18", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "transcribe", "search"], "query": [{"key": "practitionerId", "value": "3240"}, {"key": "patientId", "value": "360"}, {"key": "appointmentId", "value": "3520"}, {"key": "status", "value": "READY"}, {"key": "startDate", "value": "2024-09-17"}, {"key": "endDate", "value": "2025-03-18"}]}, "description": "Find transcription Detail by Id"}, "response": []}, {"name": "Authenticate Local", "event": [{"listen": "test", "script": {"exec": ["pm.environment.set(\"apiT<PERSON>\", postman.getResponseHeader(\"X-Auth-Token\"));"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"username\":\"<EMAIL>\",\n  \"password\":\"Faradel@82\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/auth", "host": ["{{url}}"], "path": ["auth"]}}, "response": []}]}